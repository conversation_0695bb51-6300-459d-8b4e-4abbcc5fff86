-- -----------------------------------
-- 拓展代码生成配置表
-- -----------------------------------
ALTER TABLE [blade_code_setting] ADD [name] varchar(32)
    GO

ALTER TABLE [blade_code_setting] ADD [code] varchar(16)
    GO

ALTER TABLE [blade_code_setting] ADD [category] int DEFAULT 1
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'名称',
    'SCHEMA', N'dbo',
    'TABLE', N'blade_code_setting',
    'COLUMN', N'name'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'编号',
    'SCHEMA', N'dbo',
    'TABLE', N'blade_code_setting',
    'COLUMN', N'code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'分类:1 默认配置 2 表单设计',
    'SCHEMA', N'dbo',
    'TABLE', N'blade_code_setting',
    'COLUMN', N'category';
