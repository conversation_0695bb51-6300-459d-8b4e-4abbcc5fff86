<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.job.mapper.JobServerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="jobServerResultMap" type="org.springblade.job.pojo.entity.JobServer">
        <result column="id" property="id"/>
        <result column="job_server_name" property="jobServerName"/>
        <result column="job_server_url" property="jobServerUrl"/>
        <result column="job_app_name" property="jobAppName"/>
        <result column="job_app_password" property="jobAppPassword"/>
        <result column="job_remark" property="jobRemark"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectJobServerPage" resultMap="jobServerResultMap">
        select * from blade_job_server where is_deleted = 0
    </select>

</mapper>
