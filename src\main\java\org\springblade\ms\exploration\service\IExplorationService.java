/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.ms.exploration.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.ms.exploration.pojo.entity.ExplorationEntity;
import org.springblade.ms.exploration.pojo.vo.ExplorationVO;

/**
 * 勘查记录 服务类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
public interface IExplorationService extends BaseService<ExplorationEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page 分页参数
	 * @param exploration 查询参数
	 * @return IPage<ExplorationVO>
	 */
	IPage<ExplorationVO> selectExplorationPage(IPage<ExplorationVO> page, ExplorationVO exploration);

	/**
	 * 根据参数查询
	 * @param explorationVO
	 * @return
	 */
	ExplorationVO getExplorationByParam(ExplorationVO explorationVO);

}
