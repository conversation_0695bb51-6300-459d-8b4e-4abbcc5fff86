<view class="xkzMain">
  <view>
    <view class="xkz-title">
      {{yhytData.companyName}}
    </view>
    <image mode="scaleToFill" class="images" src="{{filePath}}" />
  </view>
  <ant-divider />
  <view class="text">
    <view class="btn-text mtb10">
      <view class="flex ">
        <view>
          许可证号：
        </view>
        <view class="text-line">
          {{yhytData.licNo}}
        </view>
      </view>
      <!-- <view class="btn" onTap="handleCreateCheckDialog">
        比对
      </view> -->
    </view>
    <view class="btn-text mtb10">

      <view class="flex ">
        <view >
          企业名称：
        </view>
        <view class="text-line" style="width:60vw">
          {{yhytData.companyName}}
        </view>
      </view>
      <view class="btn" onTap="handlePositionClick" a:if="{{ isView=='false' }}"> 
        定位
      </view>
    </view>

    
    <view class="flex mtb10">
      <view>
        企业类型：
      </view>
      <view class="text-line">
        {{yhytData.bizFormat}}
      </view>
    </view>
      <view class="flex mtb10">
        <view>
          经营场所：
        </view>
        <view class="text-line">
          {{yhytData.businessAddr}}
        </view>
      </view>
      
    <view class="flex mtb10">
      <view>
        许可范围：
      </view>
      <view class="text-line">
        {{yhytData.managerScopeName}}
      </view>
    </view>
    
    
    <view class="flex mtb10">
      <view>
        客户编码：
      </view>
      <view class="text-line">
        {{yhytData.custCode}}
      </view>
    </view>
    <view class="flex mtb10">
      <view>
        客户档位：
      </view>
      <view class="text-line">
        {{yhytData.tapPosition}}
      </view>
    </view>
   
    <view class="flex mtb10">
      <view>
        供货单位：
      </view>
      <view class="text-line">
        {{yhytData.supplyCompanyName}}
      </view>
    </view>
    <view class="flex mtb10">
      <view>
        有效期限：
      </view>
      <view class="text-line" a:if="{{ yhytData.validateStart && yhytData.validateEnd }}">
        {{yhytData.validateStart}}至{{yhytData.validateEnd}}
      </view>
      <view class="text-line" a:if="{{ !(yhytData.validateStart && yhytData.validateEnd) }}">
        无
      </view>
    </view>
    
    <view class="flex mtb10">
      <view>
        营业执照编码：
      </view>
      <view class="text-line">
        {{yhytData.businessLicNo}}
      </view>
    </view>
    
    <!-- <view class="flex mtb10">
      <view>
        营业执照类型：
      </view>
      <view class="text-line">
        {{yhytData.businessValidType}}
      </view>
    </view> -->
    
    <view class="flex mtb10">
      <view>
        营业执照有效期：
      </view>
      <view class="text-line" a:if="{{ yhytData.businessValidStart && yhytData.businessValidEnd }}">
        {{yhytData.businessValidStart}}至{{yhytData.businessValidEnd}}
      </view>
      <view class="text-line" a:if="{{ !(yhytData.businessValidStart && yhytData.businessValidEnd) }}">
        无
      </view>
    </view>
    
  </view>

</view>
<ant-dialog 
    title="许可证号比对"
    visible={{isShowCheckDialog}}
    onClose="handleCheckDialogClose"
    >
        <view
            slot="footer"
            class="activity-dialog-footer"
        >
            <ant-input 
            className="searchBar" 
                style="background: #f7f9fa;"
                placeholder="请输入许可证号"
                confirm-type="search"
                onChange="handleLicChange"
            >
                <ant-icon slot="prefix" type="SearchOutline" style="color: black;" />
                <ant-icon slot="suffix" type="ScanningOutline" style="color: #1486FF; font-size: 30px;"  />
            </ant-input>
            <view class="searchBtn" onTap="handleCheckBtnTap">
              比 对
            </view>
        </view>
</ant-dialog>
