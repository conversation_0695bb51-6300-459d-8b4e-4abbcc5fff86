#数据源配置
spring:
  data:
    redis:
      ##redis 单机环境配置
      host: 127.0.0.1
      port: 6379
      password:
      database: 10
      ssl:
        enabled: false
      ##redis 集群环境配置
      #cluster:
      #  nodes: 127.0.0.1:7001,127.0.0.1:7002,127.0.0.1:7003
      #  commandTimeout: 5000
  datasource:
    # MySql
#    url: *********************************************************************************************************************************************************************************************************************************************
#    username: root
#    password: root
    # PostgreSQL
#    url: *********************************************
#    username: postgres
#    password: postgresql
    url: *******************************************************************
    username: gzyc
    password: gzyc1234
    # Oracle
    #url: *************************************
    #username: BLADEX_BOOT
    #password: BLADEX_BOOT
    # SqlServer
    #url: ********************************************************
    #username: bladex_boot
    #password: bladex_boot
    # DaMeng
    #url: jdbc:dm://127.0.0.1:5236/BLADEX_BOOT?zeroDateTimeBehavior=convertToNull&useUnicode=true&characterEncoding=utf-8
    #username: BLADEX_BOOT
    #password: BLADEX_BOOT
    # YashanDB
    #url: ***************************************
    #username: BLADEX_BOOT
    #password: BLADEX_BOOT

#第三方登陆
social:
  enabled: true
  domain: http://127.0.0.1:1888

#blade配置
blade:
  #分布式锁配置
  lock:
    ##是否启用分布式锁
    enabled: false
    ##redis服务地址
    address: redis://127.0.0.1:6379
  #本地文件上传
  file:
    remote-mode: true
    upload-domain: http://localhost:8999
    remote-path: /usr/share/nginx/html

#oss默认配置
oss:
  #开启oss配置
  enabled: true
  #开启oss类型
  #minio、s3、qiniu、alioss、huaweiobs、tencentcos
  name: minio
  #租户模式
  tenant-mode: false
  #oss服务地址
  endpoint: http://localhost:9000
#  endpoint: http://**************:9000
  #minio转换服务地址，用于内网上传后将返回地址改为转换的外网地址
  transform-endpoint: https://tcinspect.foshantc.com/webfile
  #访问key
  access-key: BJEA6hXrehmRYIrGvxKk
  #密钥key
  secret-key: KiQ1NPjX5bbgQVYCGy2X19SVIErQLiQV7OKujscT
  #存储桶
  bucket-name: images

# 共享中心接口配置
share:
  api:
    url: https://*************:8443/GatewayMsg/http/api/proxy/invoke
    paasid: ap25022LRCZH
    token: 2718549fe9f94bb8b4d62444f18c891c

ding-app:
  agent-id: 3589732301
  mini-app-id: 5000000006276235
  app-key: ding0nsdoc8pqxfktrs2
  app-secret: Eu63PK7We9S6oN46IUEcsoSw6P_mmZ1kYtl7wq5oc4FOnU0QH3nnpGWWDxnGd9b5
  corp-id: ding018ee9ae0871976df2c783f7214b6d69
  api-token: 755fd9362ed73ad8a74e52160ff2a988
  #品规识别地址
  detect-url: http://localhost:8088/detect
  base-url: http://*************:3000

# 12345平台配置
platform:
  12345:
    api-root: http://************:8020