package org.springblade.ms.common.utils;

import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * 通用三级数据获取工具类
 * 实现 Redis > API > 数据库 的查询模式，支持异步刷新缓存
 *
 * @param <T> 返回的数据类型
 * @param <K> 查询的键类型
 */
@Slf4j
public class ThreeTierDataFetcher<T, K> {

    /**
     * 缓存配置
     */
    @Data
    @Builder
    public static class CacheConfig {
        /**
         * Redis键前缀
         */
        private String keyPrefix;
        
        /**
         * 缓存过期天数
         */
        private int expireDays;
        
        /**
         * 缓存刷新间隔（毫秒）
         */
        private long refreshIntervalMs;
        
        /**
         * API查询超时时间（秒）
         */
        private int apiTimeoutSeconds;
        
        /**
         * 数据库查询超时时间（秒）
         */
        private int dbTimeoutSeconds;
    }

    private final RedisTemplate<String, Object> redisTemplate;
    private final CacheConfig cacheConfig;
    private final Function<K, String> keyGenerator;
    private final Function<K, List<T>> apiFetcher;
    private final Function<K, List<T>> dbFetcher;
    
    /**
     * 最后一次缓存刷新时间映射
     */
    private final Map<String, Long> lastRefreshTimeMap = new ConcurrentHashMap<>();

    /**
     * 构造函数
     *
     * @param redisTemplate Redis模板
     * @param cacheConfig 缓存配置
     * @param keyGenerator 键生成器，用于生成Redis键
     * @param apiFetcher API数据获取函数
     * @param dbFetcher 数据库数据获取函数
     */
    public ThreeTierDataFetcher(
            RedisTemplate<String, Object> redisTemplate,
            CacheConfig cacheConfig,
            Function<K, String> keyGenerator,
            Function<K, List<T>> apiFetcher,
            Function<K, List<T>> dbFetcher) {
        this.redisTemplate = redisTemplate;
        this.cacheConfig = cacheConfig;
        this.keyGenerator = keyGenerator;
        this.apiFetcher = apiFetcher;
        this.dbFetcher = dbFetcher;
    }

    /**
     * 获取数据，遵循 Redis > API > 数据库 的查询模式
     *
     * @param key 查询键
     * @return 数据列表
     */
    public List<T> fetchData(K key) {
        if (key == null) {
            return new ArrayList<>();
        }

        String redisKey = keyGenerator.apply(key);
        List<T> resultList;

        // 1. 先从 Redis 中查询
        resultList = getFromRedis(redisKey);
        if (!resultList.isEmpty()) {
            // 异步刷新缓存策略：如果缓存已过期时间间隔，则异步刷新
            asyncRefreshCacheIfNeeded(key, redisKey);
            return resultList;
        }

        // 2. 如果 Redis 中没有，则同时查询 API 和数据库，使用先返回的结果
        CompletableFuture<List<T>> apiFuture = CompletableFuture.supplyAsync(() -> apiFetcher.apply(key));
        CompletableFuture<List<T>> dbFuture = CompletableFuture.supplyAsync(() -> dbFetcher.apply(key));

        // 等待任一查询完成，优先使用 API 结果
        try {
            resultList = apiFuture.get(cacheConfig.getApiTimeoutSeconds(), TimeUnit.SECONDS);
            if (resultList.isEmpty()) {
                resultList = dbFuture.get(cacheConfig.getDbTimeoutSeconds(), TimeUnit.SECONDS);
            }
        } catch (Exception e) {
            log.warn("API查询失败或超时，回退到数据库查询，key: {}", key);
            try {
                resultList = dbFuture.get(cacheConfig.getDbTimeoutSeconds(), TimeUnit.SECONDS);
            } catch (Exception ex) {
                log.error("API和数据库查询均失败，key: {}", key, ex);
                return new ArrayList<>();
            }
        }

        // 3. 将查询结果存入 Redis
        if (!resultList.isEmpty()) {
            try {
                redisTemplate.opsForValue().set(redisKey, resultList, cacheConfig.getExpireDays(), TimeUnit.DAYS);
                lastRefreshTimeMap.put(redisKey, System.currentTimeMillis());
            } catch (Exception e) {
                log.error("缓存结果到Redis失败，key: {}", key, e);
            }
        }

        return resultList;
    }

    /**
     * 从 Redis 中获取数据
     */
    private List<T> getFromRedis(String redisKey) {
        try {
            Object cachedData = redisTemplate.opsForValue().get(redisKey);
            if (cachedData != null) {
                @SuppressWarnings("unchecked")
                List<T> result = (List<T>) cachedData;
                return result;
            }
        } catch (Exception e) {
            log.error("从Redis获取数据失败，key: {}", redisKey, e);
        }
        return new ArrayList<>();
    }

    /**
     * 异步刷新缓存（如果需要）
     */
    private void asyncRefreshCacheIfNeeded(K key, String redisKey) {
        Long lastRefreshTime = lastRefreshTimeMap.get(redisKey);
        long currentTime = System.currentTimeMillis();
        
        // 如果上次刷新时间距离现在超过了刷新间隔，则异步刷新缓存
        if (lastRefreshTime == null || (currentTime - lastRefreshTime) > cacheConfig.getRefreshIntervalMs()) {
            CompletableFuture.runAsync(() -> {
                try {
                    // 先尝试从 API 获取最新数据
                    List<T> apiResults = apiFetcher.apply(key);
                    if (!apiResults.isEmpty()) {
                        redisTemplate.opsForValue().set(redisKey, apiResults, cacheConfig.getExpireDays(), TimeUnit.DAYS);
                        lastRefreshTimeMap.put(redisKey, currentTime);
                        log.debug("异步从API刷新缓存成功，key: {}", key);
                        return;
                    }
                    
                    // 如果 API 没有数据，则从数据库获取
                    List<T> dbResults = dbFetcher.apply(key);
                    if (!dbResults.isEmpty()) {
                        redisTemplate.opsForValue().set(redisKey, dbResults, cacheConfig.getExpireDays(), TimeUnit.DAYS);
                        lastRefreshTimeMap.put(redisKey, currentTime);
                        log.debug("异步从数据库刷新缓存成功，key: {}", key);
                    }
                } catch (Exception e) {
                    log.error("异步刷新缓存失败，key: {}", key, e);
                }
            });
        }
    }
}
