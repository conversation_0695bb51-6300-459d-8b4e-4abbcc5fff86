package org.springblade.ms.dingapp.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 人员VO
 *
 * <AUTHOR> Name
 * @since 2025-05-01
 */
@Data
@Schema(description = "人员VO")
public class PersonnelVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 人员ID
     */
    @Schema(description = "人员ID")
    private Long id;

    /**
     * 人员姓名
     */
    @Schema(description = "人员姓名")
    private String name;
}
