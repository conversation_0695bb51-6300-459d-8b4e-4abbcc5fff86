package org.springblade.ms.priceStandards.pojo.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.ms.priceStandards.pojo.entity.MsEvidenceYhyt;

import java.io.Serial;

/**
 * 涉案物品导出VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MsEvidenceYhytExportVO extends MsEvidenceYhyt {
    @Serial
    private static final long serialVersionUID = 1L;
    
    /**
     * 条形码
     */
    private String barcode;
    private Long packageQty2;
}
