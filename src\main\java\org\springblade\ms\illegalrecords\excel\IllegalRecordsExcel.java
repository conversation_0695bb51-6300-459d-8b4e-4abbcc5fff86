/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.ms.illegalrecords.excel;


import lombok.Data;

import java.util.Date;
import java.math.BigDecimal;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;
import java.io.Serial;


/**
 * 案件信息 Excel实体类
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class IllegalRecordsExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("主键")
	private Long id;
	/**
	 * 案件uuid
	 */
	@ColumnWidth(20)
	@ExcelProperty("案件uuid")
	private String caseUuid;
	/**
	 * 案件名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("案件名称")
	private String caseName;
	/**
	 * 案件编号
	 */
	@ColumnWidth(20)
	@ExcelProperty("案件编号")
	private String caseCode;
	/**
	 * 案件所属年度
	 */
	@ColumnWidth(20)
	@ExcelProperty("案件所属年度")
	private Integer caseYear;
	/**
	 * 案发时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("案发时间")
	private Date caseDate;
	/**
	 * 案发地点
	 */
	@ColumnWidth(20)
	@ExcelProperty("案发地点")
	private String casePlace;
	/**
	 * gis经度
	 */
	@ColumnWidth(20)
	@ExcelProperty("gis经度")
	private BigDecimal placeGisX;
	/**
	 * gis纬度
	 */
	@ColumnWidth(20)
	@ExcelProperty("gis纬度")
	private BigDecimal placeGisY;
	/**
	 * 案由
	 */
	@ColumnWidth(20)
	@ExcelProperty("案由")
	private String caseOfAction;
	/**
	 * 案件性质
	 */
	@ColumnWidth(20)
	@ExcelProperty("案件性质")
	private String caseProperty;
	/**
	 * 立案号
	 */
	@ColumnWidth(20)
	@ExcelProperty("立案号")
	private String regDocNo;
	/**
	 * 立案时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("立案时间")
	private Date regTime;
	/**
	 * 上报日期
	 */
	@ColumnWidth(20)
	@ExcelProperty("上报日期")
	private Date reportDate;
	/**
	 * 案件类型
	 */
	@ColumnWidth(20)
	@ExcelProperty("案件类型")
	private String caseType;
	/**
	 * 是否结案
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否结案")
	private Integer isFinshed;
	/**
	 * 结案日期
	 */
	@ColumnWidth(20)
	@ExcelProperty("结案日期")
	private Date finshDate;
	/**
	 * 是否12313
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否12313")
	private Integer isReportHotline;
	/**
	 * 12313受理编号
	 */
	@ColumnWidth(20)
	@ExcelProperty("12313受理编号")
	private String applyCode;
	/**
	 * 行政处罚日期
	 */
	@ColumnWidth(20)
	@ExcelProperty("行政处罚日期")
	private Date punishDecideDate;
	/**
	 * 是否已删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否已删除")
	private Integer isDeleted;
	/**
	 * 租户ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户ID")
	private String tenantId;

}
