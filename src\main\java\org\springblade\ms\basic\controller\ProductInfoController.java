/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.ms.basic.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.ms.basic.pojo.entity.YhytLicenseEntity;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.ms.basic.pojo.entity.ProductInfoEntity;
import org.springblade.ms.basic.pojo.vo.ProductInfoVO;
import org.springblade.ms.basic.excel.ProductInfoExcel;
import org.springblade.ms.basic.wrapper.ProductInfoWrapper;
import org.springblade.ms.basic.service.IProductInfoService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 品规信息 控制器
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@RestController
@AllArgsConstructor
@RequestMapping("ms-productinfo/productInfo")
@Tag(name = "品规信息", description = "品规信息接口")
public class ProductInfoController extends BladeController {

	private final IProductInfoService productInfoService;

	/**
	 * 品规信息 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入productInfo")
	public R<ProductInfoVO> detail(ProductInfoEntity productInfo) {
		ProductInfoEntity detail = productInfoService.getOne(Condition.getQueryWrapper(productInfo));
		return R.data(ProductInfoWrapper.build().entityVO(detail));
	}
	/**
	 * 品规信息 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入productInfo")
	public R<IPage<ProductInfoVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> productInfo, Query query) {
		IPage<ProductInfoEntity> pages = productInfoService.page(Condition.getPage(query), Condition.getQueryWrapper(productInfo, ProductInfoEntity.class));
		return R.data(ProductInfoWrapper.build().pageVO(pages));
	}

	/**
	 * 品规信息 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入productInfo")
	public R<IPage<ProductInfoVO>> page(ProductInfoVO productInfo, Query query) {
		IPage<ProductInfoVO> pages = productInfoService.selectProductInfoPage(Condition.getPage(query), productInfo);
		return R.data(pages);
	}

	/**
	 * 品规信息 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入productInfo")
	public R save(@Valid @RequestBody ProductInfoEntity productInfo) {
		return R.status(productInfoService.save(productInfo));
	}

	/**
	 * 品规信息 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入productInfo")
	public R update(@Valid @RequestBody ProductInfoEntity productInfo) {
		return R.status(productInfoService.updateById(productInfo));
	}

	/**
	 * 品规信息 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入productInfo")
	public R submit(@Valid @RequestBody ProductInfoEntity productInfo) {
		return R.status(productInfoService.saveOrUpdate(productInfo));
	}

	/**
	 * 品规信息 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(productInfoService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@GetMapping("/export-productInfo")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入productInfo")
	public void exportProductInfo(@Parameter(hidden = true) @RequestParam Map<String, Object> productInfo, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<ProductInfoEntity> queryWrapper = Condition.getQueryWrapper(productInfo, ProductInfoEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(ProductInfo::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(ProductInfoEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<ProductInfoExcel> list = productInfoService.exportProductInfo(queryWrapper);
		ExcelUtil.export(response, "品规信息数据" + DateUtil.time(), "品规信息数据表", list, ProductInfoExcel.class);
	}

	@GetMapping("/getSelectionList")
	public R<List<ProductInfoEntity>> getSelectionList(@RequestParam("name") String name) {
		return R.data(productInfoService.getSelectionList(name));
	}

}
