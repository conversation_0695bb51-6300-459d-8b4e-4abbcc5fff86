import httpApi from "/utils/http/httpApi";


Component({
  mixins: [],
  data: {
    lshInfo:{
      title:'xxx益民超市',
      xkzh:'xxx',
      qymc:'xxx',
      qylx:'',
      jycs:'',
      xkfw:'',
      khbm:'',
      khdw:'',
      ghdw:'',
      yxqk:'',
      yyzzbm:'',
      yyzzlx:'',
      yyzzyxq:'',
    },
    isShowCheckDialog:false,
    inputLic:'',
    licData:{
      licId:'',
      explorationId:'',
    },
  },
  props: {
    yhytData:{},
    isView:false,
},
  async didMount() {
    await dd.getStorage(
      {
        key:"licData",
        success:res=>{
          this.setData({
            licData:JSON.parse(res.data)
          })
        }
    })
    console.log(this.data.licData,"xkz-data")
    // const res = await httpApi.request({
    //   url: `/api/dingapp/license/getDingMapLicense?licNo=${this.data.licData.licNo}&yhytId=${this.data.licData.licId}`,
    //   method: 'get',
    // })
    // this.setData({yhytData:res.data.yhytLicenseVO})
    // console.log(res)
  },
  didUpdate() {},
  didUnmount() {},
  methods: {
    handleCheckDialogClose(){
      this.setData({
        isShowCheckDialog:false
      })
    },
    handleCreateCheckDialog() {
      this.setData({
        isShowCheckDialog: true
      })
    },
    handleLicChange(value, e) {
      this.setData({
          inputLic: value
      })
    },
    handlePositionClick(){
      console.log("定位")
      this.uploadLocation();
    },
    handleCheckBtnTap(){
      let data  = {};
      //todo   存储数据
      
      data = this.parseXkzText(`许可证号:440811106906发证机关：广东省湛江市烟草专卖局
      制证日期：2024年05月11日
      企业（字号）名称：湛江市麻章区妹姐商店
      负责人：吴益华
      企业类型：个体(个人经营)
      经营场所：湛江市麻章区湖光镇金兴村委会金兴市场西侧旁边铺面
      许可范围：消费类烟丝本店零售、卷烟本店零售、雪茄烟本店零售
      供货单位：广东烟草湛江市有限公司
      有效期限：自2024年05月11日 至 2028年11月30日`);
      console.log(data)
      const jsonString = JSON.stringify(data);
      dd.setStorageSync({key:'comparisionData', data:jsonString});

      dd.navigateTo({
        url: '/pages/comparison/index'
      });
    },
    parseXkzText(text){
      const patterns = {
        licenseNumber: /许可证号:(\d+)/,
        issuingAuthority: /发证机关：([\u4e00-\u9fa5]+)/,
        issueDate: /制证日期：(\d{4}年\d{2}月\d{2}日)/,
        enterpriseName: /企业（字号）名称：([\u4e00-\u9fa5]+)/,
        responsiblePerson: /负责人：([\u4e00-\u9fa5]+)/,
        enterpriseType: /企业类型：([\u4e00-\u9fa5()]+)/,
        businessAddress: /经营场所：([\u4e00-\u9fa5]+)/,
        businessScope: /许可范围：([\u4e00-\u9fa5、]+)/,
        supplier: /供货单位：([\u4e00-\u9fa5]+)/,
        validityPeriod: /有效期限：自(\d{4}年\d{2}月\d{2}日) 至 (\d{4}年\d{2}月\d{2}日)/
      };
      const result = {};

      for (const key in patterns) {
        const match = text.match(patterns[key]);
        if (match) {
          // 提取匹配的内容
          if (key === 'validityPeriod') {
            result.startDate = match[1];
            result.endDate = match[2];
          } else {
            result[key] = match[1];
          }
        }
      }
      return result;
    },
    uploadLocation(){
      dd.getLocation({
        type: 1,
        useCache: false,
        coordinate: '1',
        cacheTimeout: 1,
        withReGeocode: true,
        targetAccuracy: '100',
        success: (res) => {
          httpApi.request({
            url: '/api/dingapp/exploration/submitExplorationCoordinate',
            method: 'POST',
            data:{
              "explorationId": this.data.licData.explorationId,
              "licenseId": this.data.licData.licNo,
              "longitude": res.longitude,
              "latitude": res.latitude
            }
          }).then(res1=>{
            if(res1.success){
              dd.showToast({
                type: 'success',
                content: '定位 成功',
                duration: 2000,
              });
            }
          })
        },
        fail: (err) => {
        }
      })
    },
    
  },
  
});
