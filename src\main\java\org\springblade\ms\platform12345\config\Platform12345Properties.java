package org.springblade.ms.platform12345.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 12345平台配置属性
 */
@Data
@Component
@ConfigurationProperties(prefix = "platform.12345")
public class Platform12345Properties {
    
    /**
     * 平台API根路径
     */
    private String apiRoot;
    
    /**
     * 账号
     */
    private String account;
    
    /**
     * 密码
     */
    private String password;
    
    /**
     * 应用ID
     */
    private String appid;

    /**
     * 用户帐号
     */
    private String dealUserNo;
}
