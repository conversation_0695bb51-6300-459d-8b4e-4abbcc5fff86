package org.springblade.ms.dingapp.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 人员排名数据VO
 *
 * <AUTHOR> Name
 * @since 2025-05-01
 */
@Data
@Schema(description = "人员排名数据VO")
public class PersonnelRankingVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 排名
     */
    @Schema(description = "排名")
    private Integer ranking;

    /**
     * 中队
     */
    @Schema(description = "中队")
    private String deptName;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private String userId;

    /**
     * 姓名
     */
    @Schema(description = "姓名")
    private String name;

    /**
     * 登录次数
     */
    @Schema(description = "登录次数")
    private Integer loginCount;

    /**
     * 检查零售户数
     */
    @Schema(description = "检查零售户数")
    private Integer inspectionCount;

    /**
     * 品规识别次数
     */
    @Schema(description = "品规识别次数")
    private Integer recognitionCount;

    /**
     * 识别品规数
     */
    @Schema(description = "识别品规数")
    private Integer specCount;
}
