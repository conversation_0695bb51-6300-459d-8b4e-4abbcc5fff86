import http from '/utils/http/httpApi';
import {setting} from "/common/setting";

/**
 * 获取企业access_token
 * @returns {Promise<string>} access_token
 */
export const getAccessToken = async () => {
  try {
    const response = await http.get(`/blade-system/dingtalk/getAccessToken?appKey=${setting.appKey}&appSecret=${setting.appSecret}`);
    if (response.success && response.data) {
      return response.data.access_token;
    } else {
      console.error('获取access_token失败：', response.msg);
      return null;
    }
  } catch (error) {
    console.error('获取access_token异常：', error);
    return null;
  }
};

/**
 * 获取jsapi_ticket
 * @returns {Promise<Object>} jsapi_ticket对象
 */
export const getJsapiTicket = async () => {
  try {
    // 先获取access_token
    const accessToken = await getAccessToken();
    if (!accessToken) {
      return {
        success: false,
        msg: '获取access_token失败'
      };
    }
    
    // 获取jsapi_ticket
    const response = await http.get(`/blade-system/dingtalk/getJsapiTicket?accessToken=${accessToken}`);
    if (response.success && response.data) {
      return {
        success: true,
        data: {
          ticket: response.data.ticket,
          expires_in: response.data.expires_in
        }
      };
    } else {
      return {
        success: false,
        msg: response.msg || '获取jsapi_ticket失败'
      };
    }
  } catch (error) {
    console.error('获取jsapi_ticket异常：', error);
    return {
      success: false,
      msg: '获取jsapi_ticket异常'
    };
  }
};
