<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.ms.basic.mapper.ProductInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="productInfoResultMap" type="org.springblade.ms.basic.pojo.entity.ProductInfoEntity">
        <result column="id" property="id"/>
        <result column="product_uuid" property="productUuid"/>
        <result column="product_code" property="productCode"/>
        <result column="product_name" property="productName"/>
        <result column="gj_product_uuid" property="gjProductUuid"/>
        <result column="gj_product_name" property="gjProductName"/>
        <result column="product_type" property="productType"/>
        <result column="supply_short_name" property="supplyShortName"/>
        <result column="brand_code" property="brandCode"/>
        <result column="brand_name" property="brandName"/>
        <result column="brand_type" property="brandType"/>
        <result column="length" property="length"/>
        <result column="width" property="width"/>
        <result column="height" property="height"/>
        <result column="strip_code" property="stripCode"/>
        <result column="is_offten" property="isOfften"/>
        <result column="import_flag" property="importFlag"/>
        <result column="is_main_product" property="isMainProduct"/>
        <result column="is_famous" property="isFamous"/>
        <result column="is_province" property="isProvince"/>
        <result column="is_loc_sale" property="isLocSale"/>
        <result column="is_seized" property="isSeized"/>
        <result column="is_special" property="isSpecial"/>
        <result column="is_cigar" property="isCigar"/>
        <result column="is_high_tier_brand" property="isHighTierBrand"/>
        <result column="is_active" property="isActive"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectProductInfoPage" resultMap="productInfoResultMap">
        select * from ms_product_info where is_deleted = 0
    </select>


    <select id="exportProductInfo" resultType="org.springblade.ms.basic.excel.ProductInfoExcel">
        SELECT * FROM ms_product_info ${ew.customSqlSegment}
    </select>

    <select id="getSelectionList" resultMap="productInfoResultMap">
        SELECT * FROM ms_product_info
        <where>
            <if test="true">
                is_deleted = 0
            </if>
            <if test="name != null and name != ''">
                AND product_name LIKE concat('%',#{name},'%')
            </if>
        </where>
    </select>
</mapper>
