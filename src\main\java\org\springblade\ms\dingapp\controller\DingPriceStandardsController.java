package org.springblade.ms.dingapp.controller;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.ms.itemidentify.pojo.entity.ItemIdentifyEntity;
import org.springblade.ms.itemidentify.pojo.vo.ItemIdentifyVO;
import org.springblade.ms.itemidentify.wrapper.ItemIdentifyWrapper;
import org.springblade.ms.priceStandards.pojo.entity.MsEvidenceYhyt;
import org.springblade.ms.priceStandards.pojo.entity.MsPriceStandards;
import org.springblade.ms.priceStandards.pojo.entity.ProductInfoYangjiangEntity;
import org.springblade.ms.priceStandards.service.MsPriceStandardsService;
import org.springblade.ms.priceStandards.service.ProductInfoYangjiangEntityService;
import org.springblade.ms.reportcomplaint.pojo.vo.ReportIllegalLabelVO;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@RestController
@AllArgsConstructor
@RequestMapping("/dingapp/priceStandards")
public class DingPriceStandardsController extends BladeController {
    private final MsPriceStandardsService priceStandardsService;
    private final ProductInfoYangjiangEntityService productInfoYangjiangService;


    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description  = "传入itemIdentify")
    public R<IPage<MsPriceStandards>> list(MsPriceStandards dto, Query query) {
        QueryWrapper<MsPriceStandards> qw = new QueryWrapper<>();
        if(ObjUtil.isNotNull(dto.getStdType())){
            qw.eq("std_type",dto.getStdType());
        }
        if(ObjUtil.isNotNull(dto.getProductName())){
            qw.nested(i -> {
                i.like("product_name",dto.getProductName())
                        .or().like("barcode",dto.getProductName());
            });
        }
        IPage<MsPriceStandards> pages = priceStandardsService.page(Condition.getPage(query), qw);
        List<MsPriceStandards> records = pages.getRecords();



        //附件查不到查阳江表数据
        // 检查ProductName是否是纯数字（可能是条形码）且结果为空
        if (pages.getRecords().isEmpty() && ObjUtil.isNotNull(dto.getProductName()) && StrUtil.isNumeric(dto.getProductName())) {
            String barcode = dto.getProductName();

            // 查询杨江产品信息表
            QueryWrapper<ProductInfoYangjiangEntity> yangjiangQw = new QueryWrapper<>();
            yangjiangQw.eq("barcode", barcode).or().eq("barcode2", barcode);
            List<ProductInfoYangjiangEntity> yangjiangProducts = productInfoYangjiangService.list(yangjiangQw);

            if (!yangjiangProducts.isEmpty()) {
                // 创建新的结果集
                List<MsPriceStandards> newRecords = new ArrayList<>();

                // 查询所有品牌数据，后面会根据提取的中文品牌名匹配
                QueryWrapper<MsPriceStandards> allBrandQw = new QueryWrapper<>();
                allBrandQw.eq("std_type", "品牌");
                List<MsPriceStandards> allBrandStandards = priceStandardsService.list(allBrandQw);

                // 遍历所有产品，找到包含中文的产品名称来提取品牌名
                String chineseBrandName = "";
                for (ProductInfoYangjiangEntity product : yangjiangProducts) {
                    String productName = product.getName();
                    if (productName != null && containsChineseChar(productName)) {
                        chineseBrandName = extractChineseBrandName(productName);
                        if (!chineseBrandName.isEmpty()) {
                            break; // 找到中文品牌名就退出循环
                        }
                    }
                }

                // 如果没有找到中文品牌名，结束
                if (chineseBrandName.isEmpty() && !yangjiangProducts.isEmpty()) {
                    return R.data(null);
                }

                // 根据提取的中文品牌名查找对应的品牌数据
                MsPriceStandards matchedBrand = null;
                for (MsPriceStandards brand : allBrandStandards) {
                    if (brand.getProductName().equals(chineseBrandName) ||
                        brand.getProductName().contains(chineseBrandName) ||
                        chineseBrandName.contains(brand.getProductName())) {
                        matchedBrand = brand;
                        break;
                    }
                }



                // 如果有品牌数据，为每个杨江产品创建新的价格标准
                if (matchedBrand != null) {
                    for (ProductInfoYangjiangEntity yangjiangProduct : yangjiangProducts) {
                        MsPriceStandards newStandard = new MsPriceStandards();
                        BeanUtil.copyProperties(matchedBrand, newStandard);
                        newStandard.setStdType("品牌"); // 设置为品牌类型
                        newStandard.setProductName(yangjiangProduct.getName()); // 使用杨江产品的完整名称
                        newStandard.setEvidenceType(yangjiangProduct.getEvidenceType()); // 设置涉案物品类型
                        newRecords.add(newStandard);
                    }
                }else{ //没有使用附件3
                    QueryWrapper<MsPriceStandards> priceQw = new QueryWrapper<>();
                    priceQw.eq("product_name", "卷烟型雪茄烟");
                    List<MsPriceStandards> juanyanList = priceStandardsService.list(priceQw);
                    for (ProductInfoYangjiangEntity yangjiangProduct : yangjiangProducts) {
                        MsPriceStandards newStandard = new MsPriceStandards();
                        BeanUtil.copyProperties(juanyanList.get(0), newStandard);
                        newStandard.setProductName(yangjiangProduct.getName()); // 使用杨江产品的完整名称
                        newStandard.setId(Long.valueOf(yangjiangProduct.getId()));
                        newStandard.setEvidenceType(yangjiangProduct.getEvidenceType()); // 设置涉案物品类型
                        newRecords.add(newStandard);
                    }
                }

                // 更新结果集
                if (!newRecords.isEmpty()) {
                    pages.setRecords(newRecords);
                    pages.setTotal(newRecords.size());
                }
            }
        }

        //处理价格和设置evidenceType
        if(!pages.getRecords().isEmpty()){
            List<String> barcodes = new ArrayList<>();
            // 收集所有记录的barcode，不仅仅是价格单位为"元/支"的记录
            for (MsPriceStandards record : records) {
                if (record.getBarcode() != null) {
                    barcodes.add(record.getBarcode());
                }
            }

            if(!barcodes.isEmpty()){
                QueryWrapper<ProductInfoYangjiangEntity> queryWrapper = new QueryWrapper<>();
                queryWrapper.in("barcode", barcodes); // 将 barcode 集合传入查询条件
                List<ProductInfoYangjiangEntity> yangjiangProducts = productInfoYangjiangService.list(queryWrapper);
                Map<String, ProductInfoYangjiangEntity> productMap = yangjiangProducts.stream()
                        .collect(Collectors.toMap(
                                ProductInfoYangjiangEntity::getBarcode,
                                Function.identity(),
                                (existing, replacement) -> existing // 或者选择 replacement
                        ));

                records.forEach(item->{
                    // 先设置涉案物品类型，对所有记录都尝试设置
                    ProductInfoYangjiangEntity productInfoYangjiangEntity = productMap.get(item.getBarcode());
                    if (productInfoYangjiangEntity != null) {
                        item.setEvidenceType(productInfoYangjiangEntity.getEvidenceType());
                    }

                    // 然后处理价格转换，只对价格单位为"元/支"的记录进行处理
                    if(item.getPriceUnit() != null && item.getPriceUnit().equals("元/支")){
                        BigDecimal multiply = null;
                        if (ObjUtil.isEmpty(productInfoYangjiangEntity)) {
                            // 如果 productInfoYangjiangEntity 为空，则默认 200 支
                            multiply = item.getPrice().multiply(new BigDecimal(200));
                        } else if (productInfoYangjiangEntity.getPackageQty2() == null) {
                            // 如果 packageQty2 为空，但 type2 不为空，则使用 type2
                            if (productInfoYangjiangEntity.getType2() != null) {
                                multiply = item.getPrice().multiply(new BigDecimal(productInfoYangjiangEntity.getType2()));
                            } else {
                                multiply = item.getPrice().multiply(new BigDecimal(200)); // 默认值兜底
                            }
                        } else {
                            // 使用 packageQty2
                            multiply = item.getPrice().multiply(new BigDecimal(productInfoYangjiangEntity.getPackageQty2()));
                        }

                        item.setPrice(multiply);
                        item.setPriceUnit("元/条");
                    }
                });
            }
        }

        return R.data(pages);
    }

    /**
     * 从产品全名中提取中文品牌名
     * 例如：爱喜（幻变双桔子味） -> 爱喜
     * ESSE（CHANGEDOUBLEApplemintOrange） -> ESSE
     *
     * @param fullName 产品全名
     * @return 提取的中文品牌名
     */
    private String extractChineseBrandName(String fullName) {
        if (fullName == null || fullName.isEmpty()) {
            return "";
        }

        // 如果有括号，取括号前的部分作为品牌名
        int bracketIndex = fullName.indexOf('（');
        if (bracketIndex == -1) {
            bracketIndex = fullName.indexOf('(');
        }

        if (bracketIndex != -1) {
            String brandName = fullName.substring(0, bracketIndex).trim();

            // 如果品牌名是纯英文，尝试在数据库中查找对应的中文名
            if (isEnglishOnly(brandName)) {
                // 这里可以添加查找英文品牌对应中文名的逻辑
                // 例如：ESSE -> 爱喜
                // 由于没有具体的映射关系，这里只返回原始品牌名
                return brandName;
            }

            return brandName;
        }

        // 如果没有括号，尝试提取中文部分
        StringBuilder chinesePart = new StringBuilder();
        for (char c : fullName.toCharArray()) {
            if (isChineseChar(c)) {
                chinesePart.append(c);
            }
        }

        // 如果有中文部分，返回中文部分
        if (chinesePart.length() > 0) {
            return chinesePart.toString();
        }

        // 如果没有中文部分，返回原始名称
        return fullName;
    }

    /**
     * 判断字符是否是中文字符
     */
    private boolean isChineseChar(char c) {
        return c >= 0x4E00 && c <= 0x9FA5;
    }

    /**
     * 判断字符串是否包含中文字符
     */
    private boolean containsChineseChar(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        for (char c : str.toCharArray()) {
            if (isChineseChar(c)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断字符串是否只包含英文字符
     */
    private boolean isEnglishOnly(String str) {
        for (char c : str.toCharArray()) {
            if (!((c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z') || c == ' ')) {
                return false;
            }
        }
        return true;
    }
}
