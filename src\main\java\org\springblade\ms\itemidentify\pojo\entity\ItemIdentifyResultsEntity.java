/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.ms.itemidentify.pojo.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;

/**
 * 品规识别结果 实体类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@TableName("ms_item_identify_results")
@Schema(description = "ItemIdentifyResults对象")
@EqualsAndHashCode(callSuper = true)
public class ItemIdentifyResultsEntity extends TenantEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 品规名称
	 */
	@Schema(description = "品规名称")
	private String itemName;
	/**
	 * 品规码
	 */
	@Schema(description = "品规码")
	private String itemCode;
	/**
	 * 准确率
	 */
	@Schema(description = "准确率")
	private BigDecimal accuracy;
	/**
	 * 数量
	 */
	@Schema(description = "数量")
	private Integer quantity;
	/**
	 * 对碰类别（非烟、假烟、私烟、正常）
	 */
	@Schema(description = "对碰类别（非烟、假烟、私烟、正常）")
	private String collisionType;
	/**
	 * 识别日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@Schema(description = "识别日期")
	private LocalDate identifyDate;
	/**
	 * 勘查表 ID
	 */
	@Schema(description = "勘查表 ID")
	private Long explorationId;
	/**
	 * 品规识别 ID
	 */
	@Schema(description = "品规识别 ID")
	private Long identifyId;

}
