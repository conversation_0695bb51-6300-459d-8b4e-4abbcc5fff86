package org.springblade.ms.dingapp.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.StringEscapeUtils;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.utils.Func;
import org.springblade.ms.basic.pojo.entity.FakeCigarettesEntity;
import org.springblade.ms.basic.pojo.entity.UploadFileEntity;
import org.springblade.ms.basic.service.IFakeCigarettesService;
import org.springblade.ms.basic.service.impl.UploadFileServiceImpl;
import org.springblade.ms.dingapp.config.DingAppConfig;
import org.springblade.ms.dingapp.service.IDingItemIdentifyService;
import org.springblade.ms.itemidentify.pojo.dto.ItemIdentifyResultsDTO;
import org.springblade.ms.itemidentify.pojo.entity.ItemIdentifyEntity;
import org.springblade.ms.itemidentify.pojo.entity.ItemIdentifyResultsEntity;
import org.springblade.ms.itemidentify.pojo.entity.MsRecentRetailerOrderProduct;
import org.springblade.ms.itemidentify.pojo.vo.ItemIdentifyResultsVO;
import org.springblade.ms.itemidentify.service.IItemIdentifyResultsService;
import org.springblade.ms.itemidentify.service.IItemIdentifyService;
import org.springblade.ms.itemidentify.service.MsRecentRetailerOrderProductService;
import org.springblade.modules.system.service.IDictBizService;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.URL;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class DingItemIdentifyService implements IDingItemIdentifyService {

    private final DingAppConfig dingAppConfig;
    private final IItemIdentifyService itemIdentifyService;
    private final IItemIdentifyResultsService iItemIdentifyResultsService;
    private final UploadFileServiceImpl uploadFileService;
    private final MsRecentRetailerOrderProductService recentRetailerOrderProductService;
    private final IFakeCigarettesService fakeCigarettesService;
    private final IDictBizService dictBizService;

    /**
     * 检测网络连通性
     * @param url 要检测的URL
     * @return 是否连通
     */
    private boolean isUrlReachable(String url) {
        try {
            // 从URL中提取主机名
            URL urlObj = new URL(url);
            String host = urlObj.getHost();

            // 使用InetAddress进行连通性检测，超时时间设为3秒
            InetAddress address = InetAddress.getByName(host);
            return address.isReachable(3000);
        } catch (IOException e) {
            log.warn("检测URL连通性失败: {}, 错误信息: {}", url, e.getMessage());
            return false;
        }
    }

    private boolean isHostPortReachable(String host, int port) {
        try (Socket socket = new Socket()) {
            // 设置连接超时时间（毫秒）
            socket.connect(new InetSocketAddress(host, port), 3000);
            return true; // 连接成功
        } catch (IOException e) {
            log.warn("检测 host:{} port:{} 连通性失败: {}", host, port, e.getMessage());
            return false;
        }
    }

    private boolean isUrlHostPortReachable(String url) {
        try {
            URL urlObj = new URL(url);
            String host = urlObj.getHost();
            int port = urlObj.getPort(); // 如果 URL 中没有显式指定端口，则返回 -1
            if (port == -1) {
                port = 80; // 使用协议默认端口（如 HTTP 80, HTTPS 443）
            }

            return isHostPortReachable(host, port);
        } catch (IOException e) {
            log.warn("解析 URL 失败或检测连通性失败: {}, 错误信息: {}", url, e.getMessage());
            return false;
        }
    }

    /**
     * 获取检测服务URL
     * 优先从业务字典获取detect_url，如果获取失败或连通性检测失败，则使用配置文件中的默认URL
     * @return 检测服务URL
     */
    private String getDetectUrl() {
        try {
            // 从业务字典获取detect_url
            String dictUrl = dictBizService.getValue2("detect_url", "url");

            if (dictUrl != null && !dictUrl.trim().isEmpty()) {
                log.info("从业务字典获取到detect_url: {}", dictUrl);

                // 检测连通性
                if (isUrlHostPortReachable(dictUrl)) {
                    log.info("业务字典中的URL连通性检测成功，使用字典URL: {}", dictUrl);
                    return dictUrl;
                } else {
                    log.warn("业务字典中的URL连通性检测失败: {}, 将使用配置文件中的默认URL", dictUrl);
                }
            } else {
                log.info("业务字典中未找到detect_url配置，将使用配置文件中的默认URL");
            }
        } catch (Exception e) {
            log.error("从业务字典获取detect_url时发生异常: {}, 将使用配置文件中的默认URL", e.getMessage());
        }

        // 使用配置文件中的默认URL
        String defaultUrl = dingAppConfig.getDetectUrl();
        log.info("使用配置文件中的默认detect_url: {}", defaultUrl);
        return defaultUrl;
    }

    @Override
    @Transactional
    public List<ItemIdentifyResultsEntity> getDetect(MultipartFile file,Long explorationId,String type,Long uploadImgId,
                            Long objId, String objName, Boolean onlyPingui, Boolean ocr, Integer imgSize, String customerCode, HttpServletRequest httpServletRequest) {
        RestTemplate restTemplate = new RestTemplate();

        // 获取检测服务URL：优先从业务字典获取detect_url，检测连通性后决定使用哪个URL
        String url = getDetectUrl();

        // 定义请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        // 定义请求体
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        try {
            ByteArrayResource resource = new ByteArrayResource(file.getBytes()) {
                @Override
                public String getFilename() {
                    return file.getOriginalFilename();
                }
            };
            body.add("file_list", resource);
            body.add("download_image", true);
            body.add("model_name", "custom");
            body.add("ocr", ocr);
            body.add("only_pingui", onlyPingui);
            body.add("img_size", imgSize);

            // 获取近一年零售订单产品列表
            // List<MsRecentRetailerOrderProduct> recentRetailerOrderProductList = RecentRetailerOrderProductService.listByMap(MapUtil.of("customer_code", customerCode));
            List<MsRecentRetailerOrderProduct> recentRetailerOrderProductList = recentRetailerOrderProductService.listByCustomerCode(customerCode);
            List<String> orderProductList = recentRetailerOrderProductList.stream().map(x -> x.getProductCode()).collect(Collectors.toList());
            body.add("order_product_list", String.join(",", orderProductList));


            // 获取查获假烟列表
            List<FakeCigarettesEntity> fakeCigarettesList = fakeCigarettesService.list();
            List<String> fakeCigarettesProductList = fakeCigarettesList.stream().map(x -> x.getGoodsUuid()).collect(Collectors.toList());
            body.add("fake_cigarettes_list", String.join(",", fakeCigarettesProductList));

            // 将请求头和请求体封装到 HttpEntity
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            // 发起请求
            String result = restTemplate.postForObject(
                    url,
                    requestEntity,
                    String.class);

            if (result.startsWith("\"") && result.endsWith("\"")) {
                result =  result.substring(1, result.length() - 1);
            }
            result = StringEscapeUtils.unescapeJson(result);

            //处理识别结果
            JSONArray array = JSONUtil.parseArray(result);

            //处理itemIdentify
            JSONObject result_text = JSONUtil.parseObj(array.getByPath("[0]"));
            UploadFileEntity resultImgUploadFileEntity = uploadFileService.uploadBase64ImgFileEntity(result_text.get("image_base64").toString(), objId, objName, httpServletRequest);
            ItemIdentifyEntity itemIdentifyEntity = new ItemIdentifyEntity();
            if(type.equals("yangui"))itemIdentifyEntity.setType("烟柜"); //钉钉uploadfile无法带中文参数
            if(type.equals("yanjia"))itemIdentifyEntity.setType("烟架");
            itemIdentifyEntity.setFileId(uploadImgId);
            itemIdentifyEntity.setExplorationId(explorationId);
            result_text.remove("image_base64");
            itemIdentifyEntity.setResultText(result_text.toString());
            itemIdentifyEntity.setIdentifyDate(LocalDate.now());
            itemIdentifyEntity.setResultFileId(resultImgUploadFileEntity.getId());
            itemIdentifyService.saveOrUpdate(itemIdentifyEntity);

            //处理itemIdentifyResult
            List<ItemIdentifyResultsEntity> resultsEntities = new ArrayList<>();
            Object info = result_text.get("info");
            Map<String, Object> infoMap = JSONUtil.toBean(info.toString(), Map.class);
            JSONArray pinguiList = JSONUtil.parseArray(infoMap.get("pingui_detail").toString());
            for (Object item : pinguiList) {
                JSONObject detail = JSONUtil.parseObj(item);
                //过滤无效信息
                if(detail.get("item_id").equals("tag")){
                    continue;
                }
                if(detail.get("item_id").equals("hezi")){
                    continue;
                }
                if(detail.get("item_id").toString().equals("0")){
                    continue;
                }
                ItemIdentifyResultsEntity itemIdentifyResultsEntity = new ItemIdentifyResultsEntity();
                itemIdentifyResultsEntity.setItemName(detail.get("item_name").toString());
                itemIdentifyResultsEntity.setItemCode(detail.get("item_id").toString());
                itemIdentifyResultsEntity.setCollisionType(detail.get("collision_type").toString());
                itemIdentifyResultsEntity.setQuantity((int)detail.get("count"));
                itemIdentifyResultsEntity.setExplorationId(explorationId);
                itemIdentifyResultsEntity.setIdentifyDate(LocalDate.now());
                itemIdentifyResultsEntity.setIdentifyId(itemIdentifyEntity.getId());
                resultsEntities.add(itemIdentifyResultsEntity);
            }

            iItemIdentifyResultsService.saveOrUpdateBatch(resultsEntities);
            return resultsEntities;
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    @Override
    public IPage<ItemIdentifyResultsVO> ListGroupByDate(ItemIdentifyResultsDTO dto, Query query) {
        IPage<ItemIdentifyResultsVO> itemIdentifyResultsVOS = iItemIdentifyResultsService.countListGroupByDate(Condition.getPage(query), dto);
        return itemIdentifyResultsVOS;
    }

    @Override
    @Transactional
    public Boolean removeById(Long itemIdentifyId) {
        boolean b1 = itemIdentifyService.deleteLogic(Func.toLongList(itemIdentifyId.toString()));

        QueryWrapper<ItemIdentifyResultsEntity> qw1 = new QueryWrapper<>();
        qw1.eq("identify_id", itemIdentifyId)
                .select("id","is_deleted","identify_id");
        List<ItemIdentifyResultsEntity> identifyResultList = iItemIdentifyResultsService.list(qw1);
        List<Long> idList = identifyResultList.stream()
                .map(ItemIdentifyResultsEntity::getId)
                .toList();
        boolean b2 = iItemIdentifyResultsService.deleteLogic(idList);


        return b1 && b2;
    }


}
