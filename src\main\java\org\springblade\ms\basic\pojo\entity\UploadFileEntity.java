package org.springblade.ms.basic.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.math.BigDecimal;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-02-05 15:45
 */
@Data
@TableName("ms_upload_file")
@EqualsAndHashCode(callSuper = true)
public class UploadFileEntity extends TenantEntity {

    private Long objId;
    private String fileName;
    private String filthPath;
    private BigDecimal fileSize;
    private String objName;

}
