package org.springblade.ms.dingapp.controller;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.ms.dingapp.dto.EvidenceSubmitDTO;
import org.springblade.ms.dingapp.dto.TextImportDTO;
import org.springblade.ms.priceStandards.pojo.entity.MsEvidenceYhyt;
import org.springblade.ms.priceStandards.pojo.entity.MsPriceStandards;
import org.springblade.ms.priceStandards.pojo.entity.ProductInfoYangjiangEntity;
import org.springblade.ms.priceStandards.service.MsEvidenceYhytService;
import org.springblade.ms.priceStandards.service.MsPriceStandardsService;
import org.springblade.ms.priceStandards.service.ProductInfoYangjiangEntityService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@RestController
@AllArgsConstructor
@RequestMapping("/dingapp/ecidenceYhytV2")
public class DingEvidenceYhytControllerV2 extends BladeController {
    private final MsEvidenceYhytService evidenceYhytService;
    private final MsPriceStandardsService priceStandardsService;
    private final ProductInfoYangjiangEntityService productInfoYangjiangService;

    /**
     * 价格标准匹配结果，包含匹配到的价格标准和匹配类型
     */
    @Data
    private static class PriceStandardMatchResult {
        /**
         * 匹配到的价格标准
         */
        private MsPriceStandards priceStandard;

        /**
         * 匹配类型：1-直接匹配品规，2-通过品牌匹配
         */
        private int matchType;

        public PriceStandardMatchResult(MsPriceStandards priceStandard, int matchType) {
            this.priceStandard = priceStandard;
            this.matchType = matchType;
        }
    }

    /**
     * 从产品全名中提取中文品牌名
     * 例如：爱喜（幻变双桔子味） -> 爱喜
     * ESSE（CHANGEDOUBLEApplemintOrange） -> ESSE
     *
     * @param fullName 产品全名
     * @return 提取的中文品牌名
     */
    private String extractChineseBrandName(String fullName) {
        if (fullName == null || fullName.isEmpty()) {
            return "";
        }

        // 如果有括号，取括号前的部分作为品牌名
        int bracketIndex = fullName.indexOf('（');
        if (bracketIndex == -1) {
            bracketIndex = fullName.indexOf('(');
        }

        if (bracketIndex != -1) {
            String brandName = fullName.substring(0, bracketIndex).trim();

            // 如果品牌名是纯英文，尝试在数据库中查找对应的中文名
            if (isEnglishOnly(brandName)) {
                // 这里可以添加查找英文品牌对应中文名的逻辑
                // 例如：ESSE -> 爱喜
                // 由于没有具体的映射关系，这里只返回原始品牌名
                return brandName;
            }

            return brandName;
        }

        // 如果没有括号，尝试提取中文部分
        StringBuilder chinesePart = new StringBuilder();
        for (char c : fullName.toCharArray()) {
            if (isChineseChar(c)) {
                chinesePart.append(c);
            }
        }

        // 如果有中文部分，返回中文部分
        if (chinesePart.length() > 0) {
            return chinesePart.toString();
        }

        // 如果没有中文部分，返回原始名称
        return fullName;
    }

    /**
     * 判断字符是否是中文字符
     */
    private boolean isChineseChar(char c) {
        return c >= 0x4E00 && c <= 0x9FA5;
    }

    /**
     * 判断字符串是否包含中文字符
     */
    private boolean containsChineseChar(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        for (char c : str.toCharArray()) {
            if (isChineseChar(c)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断字符串是否只包含英文字符
     */
    private boolean isEnglishOnly(String str) {
        for (char c : str.toCharArray()) {
            if (!((c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z') || c == ' ')) {
                return false;
            }
        }
        return true;
    }

    /**
     * 规范化产品名称，忽略中英文括号的差异
     * 例如：爱喜（幻变双桔子味） 和 爱喜(幻变双桔子味) 会被规范为相同的字符串
     *
     * @param productName 产品名称
     * @return 规范化后的产品名称
     */
    private String normalizeProductName(String productName) {
        if (productName == null || productName.isEmpty()) {
            return "";
        }

        // 将所有中文括号替换为英文括号
        String normalized = productName.replace('（', '(').replace('）', ')');

        // 去除所有空格
        normalized = normalized.replaceAll("\\s+", "");

        return normalized;
    }

    /**
     * 根据产品名称匹配最佳的产品，忽略中英文括号的差异
     *
     * @param products 产品列表
     * @param targetName 目标产品名称
     * @return 最匹配的产品，如果没有匹配项则返回null
     */
    private ProductInfoYangjiangEntity findBestMatchByName(List<ProductInfoYangjiangEntity> products, String targetName) {
        if (products == null || products.isEmpty() || targetName == null || targetName.isEmpty()) {
            return null;
        }

        // 如果只有一个产品，直接返回
        if (products.size() == 1) {
            return products.get(0);
        }

        // 规范化目标名称
        String normalizedTargetName = normalizeProductName(targetName);

        // 首先尝试精确匹配（忽略括号差异）
        for (ProductInfoYangjiangEntity product : products) {
            String normalizedProductName = normalizeProductName(product.getName());
            if (normalizedProductName.equals(normalizedTargetName)) {
                return product;
            }
        }

        // 如果没有精确匹配，尝试部分匹配
        // 按照匹配度排序，返回最匹配的产品
        return products.stream()
                .sorted(Comparator.comparingInt(p -> {
                    String normalizedName = normalizeProductName(p.getName());
                    // 计算相似度（这里简单使用包含关系作为相似度指标）
                    if (normalizedName.contains(normalizedTargetName) || normalizedTargetName.contains(normalizedName)) {
                        // 返回名称长度差的绝对值，差值越小表示越相似
                        return Math.abs(normalizedName.length() - normalizedTargetName.length());
                    }
                    // 如果不包含，返回一个较大的值
                    return Integer.MAX_VALUE;
                }))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据CSV格式文本导入数据
     * @param textImportDTO 文本导入DTO
     * @return 导入结果
     */
    @PostMapping("/importFromText")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "根据CSV格式文本导入数据", description = "支持CSV格式，包含名称和条盒条码")
    public R<Boolean> importFromText(@RequestBody TextImportDTO textImportDTO) {
        if (textImportDTO.getImportText() == null || textImportDTO.getImportText().trim().isEmpty()) {
            return R.fail("导入文本不能为空");
        }

        try {
            // 解析文本
            String text = textImportDTO.getImportText().trim();
            List<MsEvidenceYhyt> evidenceList = new ArrayList<>();

            // 按行分割文本
            String[] lines = text.split("\\r?\\n");

            // 跳过标题行
            boolean isFirstLine = true;

            LocalDateTime now = LocalDateTime.now();

            // 使用Map来跟踪已处理的条盒条码，避免重复添加
            // 键为条盒条码，值为对应的涉案物品记录
            Map<String, MsEvidenceYhyt> barcodeMap = new LinkedHashMap<>();

            for (String line : lines) {
                // 跳过标题行
                if (isFirstLine) {
                    isFirstLine = false;
                    continue;
                }

                line = line.trim();
                if (line.isEmpty()) {
                    continue;
                }

                // 解析CSV行
                // 格式：序号,JYID,名称,条盒条码,小盒条码,数量,单位,零售价,批发价,货值,规格,型号,支数,性质,外烟性质,打码信息,物品类型,选择,备注
                String[] fields = line.split(",");

                // 确保有足够的字段
                if (fields.length < 7) {
                    continue;
                }

                try {
                    // 提取关键字段
                    String productName = fields[2].trim(); // 名称
                    String barcode = fields[3].trim();     // 条盒条码
                    String quantityStr = fields[5].trim(); // 数量

                    // 转换数量为BigDecimal
                    BigDecimal quantity = new BigDecimal(quantityStr);

                    // 检查是否已经处理过相同的条盒条码
                    if (barcodeMap.containsKey(barcode)) {
                        // 如果已经处理过，累加数量
                        MsEvidenceYhyt existingEvidence = barcodeMap.get(barcode);
                        BigDecimal newQuantity = existingEvidence.getSelectedQuantity().add(quantity);
                        existingEvidence.setSelectedQuantity(newQuantity);
                        continue;
                    }

                    // 使用条盒条码查询对应的价格标准
                    QueryWrapper<MsPriceStandards> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("barcode", barcode);
                    List<MsPriceStandards> priceStandards = priceStandardsService.list(queryWrapper);

                    // 如果找不到匹配的条码，尝试使用名称模糊匹配
                    if (priceStandards.isEmpty()) {
                        queryWrapper = new QueryWrapper<>();
                        queryWrapper.like("product_name", productName);
                        priceStandards = priceStandardsService.list(queryWrapper);
                    }

                    // 如果在附件中找不到数据，查询阳江表数据
                    if (priceStandards.isEmpty() && StrUtil.isNumeric(barcode)) {
                        // 查询杨江产品信息表
                        QueryWrapper<ProductInfoYangjiangEntity> yangjiangQw = new QueryWrapper<>();
                        yangjiangQw.eq("barcode", barcode).or().eq("barcode2", barcode);
                        List<ProductInfoYangjiangEntity> yangjiangProducts = productInfoYangjiangService.list(yangjiangQw);

                        // 如果找到多个产品，尝试根据名称匹配最佳的产品
                        if (yangjiangProducts.size() > 1) {
                            ProductInfoYangjiangEntity bestMatch = findBestMatchByName(yangjiangProducts, productName);
                            if (bestMatch != null) {
                                // 创建新的列表只包含最佳匹配的产品
                                List<ProductInfoYangjiangEntity> filteredProducts = new ArrayList<>();
                                filteredProducts.add(bestMatch);
                                yangjiangProducts = filteredProducts;
                            }
                        }

                        if (!yangjiangProducts.isEmpty()) {
                            // 查询所有品牌数据，后面会根据提取的中文品牌名匹配
                            QueryWrapper<MsPriceStandards> allBrandQw = new QueryWrapper<>();
                            allBrandQw.eq("std_type", "品牌");
                            List<MsPriceStandards> allBrandStandards = priceStandardsService.list(allBrandQw);

                            // 遍历所有产品，找到包含中文的产品名称来提取品牌名
                            String chineseBrandName = "";
                            for (ProductInfoYangjiangEntity product : yangjiangProducts) {
                                String yangjiangProductName = product.getName();
                                if (yangjiangProductName != null && containsChineseChar(yangjiangProductName)) {
                                    chineseBrandName = extractChineseBrandName(yangjiangProductName);
                                    if (!chineseBrandName.isEmpty()) {
                                        break; // 找到中文品牌名就退出循环
                                    }
                                }
                            }

                            // 如果找到了中文品牌名
                            if (!chineseBrandName.isEmpty()) {
                                // 根据提取的中文品牌名查找对应的品牌数据
                                MsPriceStandards matchedBrand = null;
                                for (MsPriceStandards brand : allBrandStandards) {
                                    if (brand.getProductName().equals(chineseBrandName) ||
                                        brand.getProductName().contains(chineseBrandName) ||
                                        chineseBrandName.contains(brand.getProductName())) {
                                        matchedBrand = brand;
                                        break;
                                    }
                                }

                                // 如果有品牌数据，为每个杨江产品创建新的价格标准
                                if (matchedBrand != null) {
                                    for (ProductInfoYangjiangEntity yangjiangProduct : yangjiangProducts) {
                                        MsPriceStandards newStandard = new MsPriceStandards();
                                        BeanUtil.copyProperties(matchedBrand, newStandard);
                                        newStandard.setStdType("品牌"); // 设置为品牌类型
                                        newStandard.setProductName(yangjiangProduct.getName()); // 使用杨江产品的完整名称
                                        newStandard.setEvidenceType(yangjiangProduct.getEvidenceType()); // 设置涉案物品类型
                                        priceStandards.add(newStandard);
                                    }
                                } else { // 没有使用附件3
                                    QueryWrapper<MsPriceStandards> priceQw = new QueryWrapper<>();
                                    priceQw.eq("product_name", "卷烟型雪茄烟");
                                    List<MsPriceStandards> juanyanList = priceStandardsService.list(priceQw);
                                    if (!juanyanList.isEmpty()) {
                                        for (ProductInfoYangjiangEntity yangjiangProduct : yangjiangProducts) {
                                            MsPriceStandards newStandard = new MsPriceStandards();
                                            BeanUtil.copyProperties(juanyanList.get(0), newStandard);
                                            newStandard.setProductName(yangjiangProduct.getName()); // 使用杨江产品的完整名称
                                            newStandard.setId(Long.valueOf(yangjiangProduct.getId()));
                                            newStandard.setEvidenceType(yangjiangProduct.getEvidenceType()); // 设置涉案物品类型
                                            priceStandards.add(newStandard);
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // 处理价格和设置evidenceType
                    if (!priceStandards.isEmpty()) {
                        List<String> barcodes = new ArrayList<>();
                        // 收集所有记录的barcode
                        for (MsPriceStandards record : priceStandards) {
                            if (record.getBarcode() != null) {
                                barcodes.add(record.getBarcode());
                            }
                        }

                        if (!barcodes.isEmpty()) {
                            QueryWrapper<ProductInfoYangjiangEntity> queryWrapper2 = new QueryWrapper<>();
                            queryWrapper2.in("barcode", barcodes); // 将 barcode 集合传入查询条件
                            List<ProductInfoYangjiangEntity> yangjiangProducts = productInfoYangjiangService.list(queryWrapper2);
                            Map<String, ProductInfoYangjiangEntity> productMap = yangjiangProducts.stream()
                                    .collect(Collectors.toMap(
                                            ProductInfoYangjiangEntity::getBarcode,
                                            Function.identity(),
                                            (existing, replacement) -> existing // 或者选择 replacement
                                    ));

                            for (MsPriceStandards item : priceStandards) {
                                // 先设置涉案物品类型，对所有记录都尝试设置
                                ProductInfoYangjiangEntity productInfoYangjiangEntity = productMap.get(item.getBarcode());
                                if (productInfoYangjiangEntity != null) {
                                    item.setEvidenceType(productInfoYangjiangEntity.getEvidenceType());
                                }

                                // 然后处理价格转换，只对价格单位为"元/支"的记录进行处理
                                if (item.getPriceUnit() != null && item.getPriceUnit().equals("元/支")) {
                                    BigDecimal multiply = null;
                                    if (ObjUtil.isEmpty(productInfoYangjiangEntity)) {
                                        // 如果 productInfoYangjiangEntity 为空，则默认 200 支
                                        multiply = item.getPrice().multiply(new BigDecimal(200));
                                    } else if (productInfoYangjiangEntity.getPackageQty2() == null) {
                                        // 如果 packageQty2 为空，但 type2 不为空，则使用 type2
                                        if (productInfoYangjiangEntity.getType2() != null) {
                                            multiply = item.getPrice().multiply(new BigDecimal(productInfoYangjiangEntity.getType2()));
                                        } else {
                                            multiply = item.getPrice().multiply(new BigDecimal(200)); // 默认值兜底
                                        }
                                    } else {
                                        // 使用 packageQty2
                                        multiply = item.getPrice().multiply(new BigDecimal(productInfoYangjiangEntity.getPackageQty2()));
                                    }

                                    item.setPrice(multiply);
                                    item.setPriceUnit("元/条");
                                }
                            }
                        }
                    }

                    // 处理查询结果
                    if (priceStandards.size() == 1) {
                        // 只有一个匹配项，直接使用
                        MsPriceStandards priceStandard = priceStandards.get(0);

                        // 创建涉案物品记录
                        MsEvidenceYhyt evidenceYhyt = new MsEvidenceYhyt();
                        evidenceYhyt.setPriceStandardsId(priceStandard.getId());
                        evidenceYhyt.setProductName(priceStandard.getProductName());
                        evidenceYhyt.setSelectedQuantity(quantity);
                        evidenceYhyt.setStdType(priceStandard.getStdType());
                        evidenceYhyt.setCurrentUnitPrice(priceStandard.getPrice());
                        evidenceYhyt.setPriceUnit(priceStandard.getPriceUnit());
                        evidenceYhyt.setSelectionTime(now);

                        // 设置价格来源
                        if ("品规".equals(priceStandard.getStdType())) {
                            evidenceYhyt.setPriceSource("附件1"); // 品规数据来自附件1
                        } else if ("品牌".equals(priceStandard.getStdType())) {
                            evidenceYhyt.setPriceSource("附件2"); // 品牌数据来自附件2
                        } else if ("类别".equals(priceStandard.getStdType())) {
                            evidenceYhyt.setPriceSource("附件3"); // 类别数据来自附件3
                        } else {
                            evidenceYhyt.setPriceSource("附件2"); // 默认为附件2
                        }

                        // 设置涉案物品类型
                        if (priceStandard.getEvidenceType() != null) {
                            evidenceYhyt.setEvidenceType(priceStandard.getEvidenceType());
                        } else {
                            evidenceYhyt.setEvidenceType("假烟"); // 默认为卷烟
                        }

                        // 将涉案物品添加到barcodeMap中，以便后续查找重复
                        barcodeMap.put(barcode, evidenceYhyt);
                        evidenceList.add(evidenceYhyt);
                    } else if (priceStandards.size() > 1) {
                        // 多个匹配项，返回可能的匹配
                        return R.fail("产品名称不明确：" + productName + "，条码：" + barcode + "，可能的匹配有：" +
                                priceStandards.stream().map(MsPriceStandards::getProductName).limit(5).reduce((a, b) -> a + ", " + b).orElse(""));
                    } else {
                        // 没有匹配项
                        return R.fail("未找到匹配的产品：" + productName + "，条码：" + barcode);
                    }
                } catch (Exception e) {
                    return R.fail("解析行失败：" + line + "，错误：" + e.getMessage());
                }


            }

            // 从barcodeMap中获取所有处理后的涉案物品
            List<MsEvidenceYhyt> finalEvidenceList = new ArrayList<>(barcodeMap.values());

            if (finalEvidenceList.isEmpty()) {
                return R.fail("未解析到有效数据");
            }

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String formattedTime = LocalDateTime.now().format(formatter);
            EvidenceSubmitDTO dto = new EvidenceSubmitDTO();
            dto.setTitle(formattedTime + "导入");

            // 保存数据
            boolean success = evidenceYhytService.saveData(finalEvidenceList, dto);

            return R.status(success);
        } catch (Exception e) {
            return R.fail("导入失败：" + e.getMessage());
        }
    }


}
