package org.springblade.ms.dingapp.dto;

import lombok.Data;

import java.util.List;

/**
 * 批量用户查询请求DTO
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Data
public class BatchUserQueryDTO {

    /**
     * 用户ID列表
     */
    private List<String> userIds;

    /**
     * 是否包含扩展信息
     */
    private Boolean includeExtension = false;

    /**
     * 语言设置
     */
    private String language = "zh_CN";

    /**
     * 构造函数
     */
    public BatchUserQueryDTO() {
    }

    /**
     * 构造函数
     *
     * @param userIds 用户ID列表
     */
    public BatchUserQueryDTO(List<String> userIds) {
        this.userIds = userIds;
    }

    /**
     * 构造函数
     *
     * @param userIds 用户ID列表
     * @param includeExtension 是否包含扩展信息
     */
    public BatchUserQueryDTO(List<String> userIds, Boolean includeExtension) {
        this.userIds = userIds;
        this.includeExtension = includeExtension;
    }
}
