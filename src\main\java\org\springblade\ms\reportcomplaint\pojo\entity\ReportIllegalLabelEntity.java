package org.springblade.ms.reportcomplaint.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 标签关联对象
 * <AUTHOR> [sijun.zeng]
 * @date 2025-01-16 23:33
 */
@Data
@TableName("ms_report_lllegal_label")
@EqualsAndHashCode(callSuper = true)
public class ReportIllegalLabelEntity extends TenantEntity {

    /**
     * 工单或案件的 ID
     */
    private Long objId;

    /**
     * 关联的标签 ID
     */
    private Long labelId;

    /**
     * 标签类型（品规、零售户）
     */
    private String labelType;

    /**
     * 关联类型（工单、案件）
     */
    private String objType;

}
