<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.ms.itemidentify.mapper.ItemIdentifyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="itemIdentifyResultMap" type="org.springblade.ms.itemidentify.pojo.entity.ItemIdentifyEntity">
        <result column="id" property="id"/>
        <result column="identity_date" property="identifyDate"/>
        <result column="exploration_id" property="explorationId"/>
        <result column="file_id" property="fileId"/>
        <result column="type" property="type"/>
        <result column="identify_order" property="identifyOrder"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectItemIdentifyPage" resultMap="itemIdentifyResultMap">
        select * from ms_item_identify where is_deleted = 0
    </select>


    <select id="exportItemIdentify" resultType="org.springblade.ms.itemidentify.excel.ItemIdentifyExcel">
        SELECT * FROM ms_item_identify ${ew.customSqlSegment}
    </select>

    <select id="getListByExplorationId" resultType="org.springblade.ms.itemidentify.pojo.vo.ItemIdentifyVO">
        SELECT mii.id,mii.identify_date,mii.exploration_id,mii.file_id,mii.type,mii.identify_order,
               mii.create_user,mii.result_file_id,mii.is_recognition_bad,
               ms1.filth_path AS file_url,
               ms2.filth_path AS result_file_url,
               ms1.create_time
                FROM ms_item_identify mii
        left join ms_upload_file ms1 on mii.file_id = ms1.id
        left join ms_upload_file ms2 on mii.result_file_id = ms2.id
        <where>
            <if test="true">
                AND mii.is_deleted = 0
            </if>
            <if test="item.explorationId != null">
                AND mii.exploration_id = #{item.explorationId}
            </if>
            <if test="item.identifyDate != null">
                AND mii.identify_date = #{item.identifyDate}
            </if>
            <if test="item.startTime != null">
                AND mii.create_time >= #{item.startTime}
            </if>
            <if test="item.endTime != null">
                AND mii.create_time &lt; #{item.endTime}
            </if>
            <if test="true">
                AND mii.type in ('烟柜','烟架')
            </if>
        </where>
        order by ms1.create_time
    </select>

    <select id="getDateCountByLicenseId" >
        SELECT COUNT(DISTINCT identify_date) AS distinct_days
        FROM "ms_item_identify" mii
                 LEFT JOIN ms_exploration me ON me.id = mii.exploration_id
                 left join ms_yhyt_license myl ON myl.id = me.license_id
        WHERE myl.lic_no = #{licNo} and mii.is_deleted = 0
    </select>
</mapper>
