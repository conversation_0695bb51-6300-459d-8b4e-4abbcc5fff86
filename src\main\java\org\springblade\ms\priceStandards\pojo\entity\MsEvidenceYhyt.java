package org.springblade.ms.priceStandards.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 涉案物品表
 * @TableName ms_evidence_yhyt
 */
@TableName(value ="ms_evidence_yhyt")
@Data
public class MsEvidenceYhyt extends TenantEntity implements Serializable {
    /**
     * 零售户id
     */
    private Long yhytId;

    /**
     * 涉案价格标准id
     */
    private Long priceStandardsId;

    /**
     * 物品名称（冗余）
     */
    private String productName;

    /**
     * 类型（冗余）
     */
    private String stdType;

    /**
     * 用户选择的物品数量
     */
    private BigDecimal selectedQuantity;

    /**
     * 当前单价，允许修改
     */
    private BigDecimal currentUnitPrice;

    /**
     * 存储时间，统一一次操作的时间
     */
    private LocalDateTime selectionTime;

    private String priceUnit;

    private String title;

    private String enforcementAgency;

    /**
     * 案发时间
     */
    private String caseTime;

    /**
     * 地址
     */
    private String address;

    /**
     * 详细地址
     */
    private String detailedAddress;

    /**
     * 联合执法单位
     */
    private String jointEnforcementAgency;

    /**
     * 案由
     */
    private String caseReason;

    /**
     * 当事人
     */
    private String partyInvolved;

    /**
     * 许可证号
     */
    private String licenseNo;

    /**
     * 涉案物品类型
     */
    private String evidenceType;

    /**
     * 价格来源
     */
    private String priceSource;

    /**
     * 数据来源
     */
    private String source;

    /**
     * 是否湛江市局稽查支队统筹部署
     */
    private Integer isZhanjiangDeployment;
    /**
     * 涉案运输工具详细信息
     */
    private String transportDetails;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
