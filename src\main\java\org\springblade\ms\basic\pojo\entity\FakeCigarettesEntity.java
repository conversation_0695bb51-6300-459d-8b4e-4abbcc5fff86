/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.ms.basic.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import java.io.Serial;

/**
 * 查获假烟统计表 实体类
 *
 * <AUTHOR>
 * @since 2025-04-20
 */
@Data
@TableName("ms_fake_cigarettes")
@Schema(description = "FakeCigarettes对象")
@EqualsAndHashCode(callSuper = true)
public class FakeCigarettesEntity extends TenantEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 供应商名称
	 */
	@Schema(description = "供应商名称")
	private String supplyName;
	/**
	 * 商品唯一标识
	 */
	@Schema(description = "商品唯一标识")
	private String goodsUuid;
	/**
	 * 商品全称
	 */
	@Schema(description = "商品全称")
	private String goodsName;
	/**
	 * 品牌名称
	 */
	@Schema(description = "品牌名称")
	private String brandName;
	/**
	 * 走私数量（单位：万条）
	 */
	@Schema(description = "走私数量（单位：万条）")
	private BigDecimal smuggleQty;
	/**
	 * 真实数量（单位：万条）
	 */
	@Schema(description = "真实数量（单位：万条）")
	private BigDecimal truthQty;
	/**
	 * 假冒数量（单位：万条）
	 */
	@Schema(description = "假冒数量（单位：万条）")
	private BigDecimal fakeQty;
	/**
	 * 总数量（单位：万条）
	 */
	@Schema(description = "总数量（单位：万条）")
	private BigDecimal totalQty;

}
