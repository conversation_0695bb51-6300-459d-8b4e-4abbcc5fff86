/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.ms.itemidentify.service.impl;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.ms.itemidentify.excel.ItemIdentifyExcel;
import org.springblade.ms.itemidentify.mapper.ItemIdentifyMapper;
import org.springblade.ms.itemidentify.pojo.dto.ItemIdentifyDTO;
import org.springblade.ms.itemidentify.pojo.entity.ItemIdentifyEntity;
import org.springblade.ms.itemidentify.pojo.vo.ItemIdentifyVO;
import org.springblade.ms.itemidentify.service.IItemIdentifyService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * 品规识别记录 服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Service
@Slf4j
public class ItemIdentifyServiceImpl extends BaseServiceImpl<ItemIdentifyMapper, ItemIdentifyEntity> implements IItemIdentifyService {

	@Override
	public IPage<ItemIdentifyVO> selectItemIdentifyPage(IPage<ItemIdentifyVO> page, ItemIdentifyVO itemIdentify) {
		return page.setRecords(baseMapper.selectItemIdentifyPage(page, itemIdentify));
	}


	@Override
	public List<ItemIdentifyExcel> exportItemIdentify(Wrapper<ItemIdentifyEntity> queryWrapper) {
		List<ItemIdentifyExcel> itemIdentifyList = baseMapper.exportItemIdentify(queryWrapper);
		//itemIdentifyList.forEach(itemIdentify -> {
		//	itemIdentify.setTypeName(DictCache.getValue(DictEnum.YES_NO, ItemIdentify.getType()));
		//});
		return itemIdentifyList;
	}

	@Override
	public List<ItemIdentifyVO> getListByExplorationId(ItemIdentifyDTO dto) {
		if(ObjUtil.isNotEmpty(dto.getStartHour()) && ObjUtil.isNotEmpty(dto.getEndHour())){
			// 构建时间段参数
			LocalDateTime startTime = LocalDateTime.of(dto.getIdentifyDate(), LocalTime.of(dto.getStartHour(), 0, 0));
			LocalDateTime endTime;

			// 如果需要查询 22:00 到 24:00（即下一天的 00:00）， endTime 需要特殊处理
			if (dto.getEndHour() == 24) {
				endTime = LocalDateTime.of(dto.getIdentifyDate().plusDays(1), LocalTime.of(0, 0, 0));
			} else {
				endTime = LocalDateTime.of(dto.getIdentifyDate(), LocalTime.of(dto.getEndHour(), 0, 0));
			}

			// 设置时间参数到DTO对象中
			dto.setStartTime(startTime);
			dto.setEndTime(endTime);
		}

		List<ItemIdentifyVO> itemIdentifyList = baseMapper.getListByExplorationId(dto);

		return itemIdentifyList;
	}

	@Override
	public Integer getDateCountByLicenseId(String licNo) {
		return baseMapper.getDateCountByLicenseId(licNo);
	}

}
