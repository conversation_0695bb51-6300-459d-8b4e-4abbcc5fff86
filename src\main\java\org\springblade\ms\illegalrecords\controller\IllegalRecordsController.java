/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.ms.illegalrecords.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.ms.illegalrecords.pojo.entity.IllegalRecordsEntity;
import org.springblade.ms.illegalrecords.pojo.vo.IllegalRecordsVO;
import org.springblade.ms.illegalrecords.excel.IllegalRecordsExcel;
import org.springblade.ms.illegalrecords.wrapper.IllegalRecordsWrapper;
import org.springblade.ms.illegalrecords.service.IIllegalRecordsService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 案件信息 控制器
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@RestController
@AllArgsConstructor
@RequestMapping("ms-illegalrecords/illegalRecords")
@Tag(name = "案件信息", description = "案件信息接口")
public class IllegalRecordsController extends BladeController {

	private final IIllegalRecordsService illegalRecordsService;

	/**
	 * 案件信息 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入illegalRecords")
	public R<IllegalRecordsVO> detail(IllegalRecordsEntity illegalRecords) {
		IllegalRecordsEntity detail = illegalRecordsService.getOne(Condition.getQueryWrapper(illegalRecords));
		return R.data(IllegalRecordsWrapper.build().entityVO(detail));
	}
	/**
	 * 案件信息 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入illegalRecords")
	public R<IPage<IllegalRecordsVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> illegalRecords, Query query) {
		IPage<IllegalRecordsEntity> pages = illegalRecordsService.page(Condition.getPage(query), Condition.getQueryWrapper(illegalRecords, IllegalRecordsEntity.class));
		return R.data(IllegalRecordsWrapper.build().pageVO(pages));
	}

	/**
	 * 案件信息 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入illegalRecords")
	public R<IPage<IllegalRecordsVO>> page(IllegalRecordsVO illegalRecords, Query query) {
		IPage<IllegalRecordsVO> pages = illegalRecordsService.selectIllegalRecordsPage(Condition.getPage(query), illegalRecords);
		return R.data(pages);
	}

	/**
	 * 案件信息 分页
	 */
	@GetMapping("/getListByCustCode")
	@ApiOperationSupport(order = 2)
	@Operation( description  = "传入illegalRecords")
	public R<List<IllegalRecordsEntity>> getListByCustCode(IllegalRecordsVO illegalRecords) {
		List<IllegalRecordsEntity> pages = illegalRecordsService.getListByCustCode(illegalRecords);
		return R.data(pages);
	}


	/**
	 * 案件信息 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入illegalRecords")
	public R save(@Valid @RequestBody IllegalRecordsEntity illegalRecords) {
		return R.status(illegalRecordsService.save(illegalRecords));
	}

	/**
	 * 案件信息 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入illegalRecords")
	public R update(@Valid @RequestBody IllegalRecordsEntity illegalRecords) {
		return R.status(illegalRecordsService.updateById(illegalRecords));
	}

	/**
	 * 案件信息 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入illegalRecords")
	public R submit(@Valid @RequestBody IllegalRecordsEntity illegalRecords) {
		return R.status(illegalRecordsService.saveOrUpdate(illegalRecords));
	}

	/**
	 * 案件信息 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(illegalRecordsService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@GetMapping("/export-illegalRecords")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入illegalRecords")
	public void exportIllegalRecords(@Parameter(hidden = true) @RequestParam Map<String, Object> illegalRecords, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<IllegalRecordsEntity> queryWrapper = Condition.getQueryWrapper(illegalRecords, IllegalRecordsEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(IllegalRecords::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(IllegalRecordsEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<IllegalRecordsExcel> list = illegalRecordsService.exportIllegalRecords(queryWrapper);
		ExcelUtil.export(response, "案件信息数据" + DateUtil.time(), "案件信息数据表", list, IllegalRecordsExcel.class);
	}

}
