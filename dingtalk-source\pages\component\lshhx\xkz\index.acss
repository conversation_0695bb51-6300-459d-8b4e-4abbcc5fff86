.xkzMain{
  background-color: white;
  height: 100vh;
  overflow-y: auto;
  padding: 15px;
  
}
.xkz-title{
  font-size: 24px;
  text-align: center;
  font-weight: 500;
  padding: 10px 0 20px;
}

.images{
  width: 70%;
  height: 140px;
  border-radius: 5px;
  display: block;     
  margin: 0 auto 15px; 
}
.text{
  font-size: 16px;
}
.btn-text{
  display: flex;
  justify-content: space-between;
  align-items: flex-start; 
}
.flex{
  display: flex;
  align-items: flex-start;
}
.mtb10{
  margin: 10px 0;
}

.btn{
  background-color: #1c88f9;
  color: white;
  text-align: center;
  border-radius: 12px;
  padding: 12rpx 15rpx;
  height: 15px; /* 明确设置按钮高度 */
  line-height: 15px; /* 确保文字垂直居中 */
  max-width: 50px;
  min-width: 50px;
  margin-left: 14px;
  display: inline-block;
}
.text-line{
  /* line-height: 30px; */
  flex: 1;
  word-break: break-all; /* 强制文本在任意字符处换行 */
  white-space: normal; /* 恢复默认的换行行为 */
  width: 67vw; /* 限制最大宽度 */
  
}


.activity-dialog-footer {
  padding-top: 64rpx;
  padding-bottom: 64rpx;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-direction: row;
}

.searchBar {
  padding: 4px;
  border-top-left-radius: 12px;
  border-bottom-left-radius: 12px;
  flex: 1;
}

.searchBar input {
  padding: 4px;
  background-color: #f7f9fa;

}

.searchBtn {
  background-color: #1c88f9;
  color: white;
  text-align: center;
  border-top-right-radius: 12px;
  border-bottom-right-radius: 12px;
  padding: 19rpx 15rpx;
  /* width: 45px; */
}

