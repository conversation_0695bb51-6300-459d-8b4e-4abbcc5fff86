/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.ms.basic.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import java.io.Serial;

/**
 * 订单信息 实体类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@TableName("ms_sal_order")
@Schema(description = "SalOrder对象")
@EqualsAndHashCode(callSuper = true)
public class SalOrderEntity extends TenantEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@Schema(description = "")
	private String orderUuid;
	/**
	 * 销售类型
	 */
	@Schema(description = "销售类型")
	private String saleType;
	/**
	 * 单据号
	 */
	@Schema(description = "单据号")
	private String billCode;
	/**
	 * 业务日期
	 */
	@Schema(description = "业务日期")
	private String bizDate;
	/**
	 * 明细数
	 */
	@Schema(description = "明细数")
	private Integer detailCount;
	/**
	 * 客户标识
	 */
	@Schema(description = "客户标识")
	private String customerUuid;
	/**
	 * 客户编码
	 */
	@Schema(description = "客户编码")
	private String customerCode;
	/**
	 * 客户名称
	 */
	@Schema(description = "客户名称")
	private String customerName;
	/**
	 * 客户电话
	 */
	@Schema(description = "客户电话")
	private String custMasterTel;
	/**
	 * 营销部门标识
	 */
	@Schema(description = "营销部门标识")
	private String saleDepartUuid;
	/**
	 * 营销部门
	 */
	@Schema(description = "营销部门")
	private String saleDepartName;
	/**
	 * 营销分公司
	 */
	@Schema(description = "营销分公司")
	private String saleCountyName;
	/**
	 * 客户经理
	 */
	@Schema(description = "客户经理")
	private String salerName;
	/**
	 * 是否已审核
	 */
	@Schema(description = "是否已审核")
	private String isAudit;
	/**
	 * 是否被退
	 */
	@Schema(description = "是否被退")
	private String isBeReturn;
	/**
	 * 是否退货单
	 */
	@Schema(description = "是否退货单")
	private String isReturn;
	/**
	 * 是否已付款
	 */
	@Schema(description = "是否已付款")
	private String isPayed;
	/**
	 * 是否到货
	 */
	@Schema(description = "是否到货")
	private String isArrived;
	/**
	 * 是否已开税票
	 */
	@Schema(description = "是否已开税票")
	private String isTaxInv;
	/**
	 * 是否可用
	 */
	@Schema(description = "是否可用")
	private String isActive;

}
