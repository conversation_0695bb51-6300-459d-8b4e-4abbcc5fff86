const app = getApp();
import httpApi from "/utils/http/httpApi";

Component({
  mixins: [],
  data: {
    currentTab: '',
    isShowExplorationDialog: false,
    inputLic: '',
    toastShow:false,
    explorationId:'',
  },
  props: {
    onCreateExploration: () => {}
  },
  didMount() {
    this.setData({
        currentTab: app.globalData.currentTab
    })
  },
  didUpdate() {},
  didUnmount() {},
  methods: {
    tabClick(event) {
        let tabName = event.currentTarget.dataset.tabName;
       
        switch (tabName) {
            case 'map':
                app.globalData.currentTab = tabName;
                this.setData({
                    currentTab: tabName
                });
                dd.redirectTo({
                    url: "/pages/map/map"
                });
            break;
            case 'create':
                this.handleCreateExploration();
            break;
            case 'mine':
                app.globalData.currentTab = tabName;
                this.setData({
                    currentTab: tabName
                });
                dd.redirectTo({
                    url: "/pages/wode/wode"
                });
            break;
        }
    },
    handleCreateExploration() {
        this.setData({
            isShowExplorationDialog: true
        })
      },
      handleExplorationDialogClose() {
        this.setData({
            isShowExplorationDialog: false
        })
      },
       handleSearchScanTap() {
        dd.scan({
          type: 'qr',
          success: (res) => {
            const  text  = res.code;
              if (!text) {
                  dd.alert({
                      content: '未识别到二维码',
                      buttonText: '确定'
                  });
              }

              // 处理扫码结果
              const licenseNumberMatch = text.match(/许可证号:(\d+)/);
              if (licenseNumberMatch) {
                  const licenseNumber = licenseNumberMatch[1];
                  httpApi.request({
                    url: '/api/dingapp/exploration/getTodayExploration?licNo='+licenseNumber,
                    method: 'get',
                  }).then(res=>{
                    if(Object.keys(res.data).length !== 0){
                      this.setData({
                        explorationId:res.data.explorationVO.id,
                        inputLic:licenseNumber,
                      })
                      this.uploadLocation();
                      dd.navigateTo({
                        url: '/pages/survey/survey?xkzh='+this.data.inputLic+"&type=survey",
                      });
                    }else{
                      dd.alert({
                        content: '未找到数据',
                        buttonText: '确定'
                      });
                      this.setData({"inputLic":""})
                    }  
                  })
                 
              } else {
                  dd.alert({
                      content: '未识别到许可证号',
                      buttonText: '确定'
                  });
              }
          }
        })
      },
      handleSearchBtnTap() {
        if(!this.data.inputLic){
          this.setData({
            toastShow:true
          })
        }else{
          httpApi.request({
            url: '/api/dingapp/exploration/getTodayExploration?licNo='+this.data.inputLic,
            method: 'get',
            
          }).then((res) => {
              if(Object.keys(res.data).length !== 0){
                this.setData({
                  explorationId:res.data.explorationVO.id
                })
                this.uploadLocation();
                dd.navigateTo({
                  url: '/pages/survey/survey?xkzh='+this.data.inputLic+"&type=survey",
                });
              }else{
                dd.alert({
                  content: '未找到数据',
                  buttonText: '确定'
                });
                this.setData({"inputLic":""})
              }        
          }).catch((err) => {
            dd.alert({
              content: '服务器异常，请稍后重试',
              buttonText: '确定'
            });
          }) 
        }
      },
      handleLicChange(value, e) {
        this.setData({
            inputLic: value
        })
      },
    handleCloseToast(){
      this.setData({
        toastShow: false
      })
    },
    uploadLocation(){
      dd.getLocation({
        type: 1,
        useCache: false,
        coordinate: '1',
        cacheTimeout: 1,
        withReGeocode: true,
        targetAccuracy: '100',
        success: (res) => {
          httpApi.request({
            url: '/api/dingapp/exploration/submitExplorationCoordinate',
            method: 'POST',
            data:{
              "explorationId": this.data.explorationId,
              "licenseId": this.data.inputLic,
              "longitude": res.longitude,
              "latitude": res.latitude
            }
          })
        },
        fail: (err) => {
        }
      })
    }
  },
  
});
