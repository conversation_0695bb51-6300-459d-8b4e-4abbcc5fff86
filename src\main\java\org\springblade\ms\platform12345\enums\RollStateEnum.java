package org.springblade.ms.platform12345.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 工单状态枚举 A5.2
 */
@Getter
@AllArgsConstructor
public enum RollStateEnum {

    /**
     * 无效工单
     */
    INVALID("-1", "无效工单"),

    /**
     * 预受理完成
     */
    PRE_ACCEPT_COMPLETED("-2", "预受理完成"),

    /**
     * 暂存
     */
    TEMPORARY_STORAGE("0", "暂存"),

    /**
     * 诉求登记
     */
    APPEAL_REGISTRATION("1", "诉求登记"),

    /**
     * 热线审核
     */
    HOTLINE_REVIEW("2", "热线审核"),

    /**
     * 协调员审核
     */
    COORDINATOR_REVIEW("3", "协调员审核"),

    /**
     * 市法制办
     */
    CITY_LEGAL_OFFICE("4", "市法制办"),

    /**
     * 市编办
     */
    CITY_ORGANIZATION_OFFICE("5", "市编办"),

    /**
     * 办结
     */
    COMPLETED("6", "办结"),

    /**
     * 职能局处理
     */
    FUNCTIONAL_BUREAU_PROCESSING("7", "职能局处理"),

    /**
     * 热线复核
     */
    HOTLINE_RECHECK("8", "热线复核"),

    /**
     * 职能局处理领导
     */
    FUNCTIONAL_BUREAU_PROCESSING_LEADER("9", "职能局处理领导"),

    /**
     * 职能局审核领导
     */
    FUNCTIONAL_BUREAU_REVIEW_LEADER("10", "职能局审核领导"),

    /**
     * 挂起
     */
    SUSPENDED("11", "挂起"),

    /**
     * 市政数局审核
     */
    CITY_DATA_BUREAU_REVIEW("12", "市政数局审核"),

    /**
     * 职能局复核
     */
    FUNCTIONAL_BUREAU_RECHECK("13", "职能局复核"),

    /**
     * 热线领导审核
     */
    HOTLINE_LEADER_REVIEW("14", "热线领导审核"),

    /**
     * 归档
     */
    ARCHIVED("15", "归档"),

    /**
     * 职能局审核
     */
    FUNCTIONAL_BUREAU_REVIEW("16", "职能局审核"),

    /**
     * 联合审定
     */
    JOINT_REVIEW("17", "联合审定"),

    /**
     * 会办退单审核
     */
    JOINT_RETURN_REVIEW("18", "会办退单审核"),

    /**
     * 未锁定
     */
    UNLOCKED("20", "未锁定"),

    /**
     * 已锁定
     */
    LOCKED("21", "已锁定"),

    /**
     * 待完善
     */
    TO_BE_IMPROVED("22", "待完善"),

    /**
     * 已完善
     */
    IMPROVED("23", "已完善"),

    /**
     * 未回复
     */
    NO_REPLY("24", "未回复"),

    /**
     * 已关闭
     */
    CLOSED("25", "已关闭"),

    /**
     * 已发布
     */
    PUBLISHED("26", "已发布"),

    /**
     * 已失效
     */
    EXPIRED("27", "已失效"),

    /**
     * 预受理
     */
    PRE_ACCEPT("28", "预受理"),

    /**
     * 差评工单领导退单审核
     */
    POOR_EVALUATION_RETURN_REVIEW("29", "差评工单领导退单审核"),

    /**
     * 差评工单领导办结审核
     */
    POOR_EVALUATION_COMPLETION_REVIEW("30", "差评工单领导办结审核");

    /**
     * 代码
     */
    private final String code;

    /**
     * 值
     */
    private final String value;

    /**
     * 根据代码获取枚举
     *
     * @param code 代码
     * @return 枚举
     */
    public static RollStateEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (RollStateEnum item : RollStateEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 根据代码获取值
     *
     * @param code 代码
     * @return 值
     */
    public static String getValueByCode(String code) {
        RollStateEnum item = getByCode(code);
        return item != null ? item.getValue() : null;
    }

    /**
     * 根据值获取枚举
     *
     * @param value 值
     * @return 枚举
     */
    public static RollStateEnum getByValue(String value) {
        if (value == null) {
            return null;
        }
        for (RollStateEnum item : RollStateEnum.values()) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 根据值获取代码
     *
     * @param value 值
     * @return 代码
     */
    public static String getCodeByValue(String value) {
        RollStateEnum item = getByValue(value);
        return item != null ? item.getCode() : null;
    }
}