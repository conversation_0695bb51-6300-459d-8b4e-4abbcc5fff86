/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.ms.basic.mapper;

import org.springblade.ms.basic.pojo.entity.YhytLicenseEntity;
import org.springblade.ms.basic.pojo.vo.YhytLicenseVO;
import org.springblade.ms.basic.excel.YhytLicenseExcel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 零售户信息 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
public interface YhytLicenseMapper extends BaseMapper<YhytLicenseEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page 分页参数
	 * @param yhytLicense 查询参数
	 * @return List<YhytLicenseVO>
	 */
	List<YhytLicenseVO> selectYhytLicensePage(IPage page, @Param("param") YhytLicenseVO yhytLicense);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper 查询条件
	 * @return List<YhytLicenseExcel>
	 */
	List<YhytLicenseExcel> exportYhytLicense(@Param("ew") Wrapper<YhytLicenseEntity> queryWrapper);

	/**
	 * 获取选择器的数据
	 * @param name
	 * @return
	 */
	List<YhytLicenseEntity> getSelectionList(IPage<YhytLicenseVO> page, @Param("name") String name);

	List<YhytLicenseEntity> getDingMapList(@Param("param") YhytLicenseEntity param);

	List<String> getFormatList();

	/**
	 * 根据距离分页排序
	 * @param page
	 * @param yhytLicense
	 * @return
	 */
	List<YhytLicenseVO> selectYhytLicensePageByDistance(IPage<YhytLicenseVO> page, @Param("param") YhytLicenseVO yhytLicense);
}
