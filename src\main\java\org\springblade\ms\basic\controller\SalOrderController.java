/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.ms.basic.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.ms.basic.pojo.entity.SalOrderEntity;
import org.springblade.ms.basic.pojo.vo.SalOrderVO;
import org.springblade.ms.basic.excel.SalOrderExcel;
import org.springblade.ms.basic.wrapper.SalOrderWrapper;
import org.springblade.ms.basic.service.ISalOrderService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 订单信息 控制器
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@RestController
@AllArgsConstructor
@RequestMapping("ms-salorder/salOrder")
@Tag(name = "订单信息", description = "订单信息接口")
public class SalOrderController extends BladeController {

	private final ISalOrderService salOrderService;

	/**
	 * 订单信息 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入salOrder")
	public R<SalOrderVO> detail(SalOrderEntity salOrder) {
		SalOrderEntity detail = salOrderService.getOne(Condition.getQueryWrapper(salOrder));
		return R.data(SalOrderWrapper.build().entityVO(detail));
	}
	/**
	 * 订单信息 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入salOrder")
	public R<IPage<SalOrderVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> salOrder, Query query) {
		IPage<SalOrderEntity> pages = salOrderService.page(Condition.getPage(query), Condition.getQueryWrapper(salOrder, SalOrderEntity.class));
		return R.data(SalOrderWrapper.build().pageVO(pages));
	}

	/**
	 * 订单信息 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入salOrder")
	public R<IPage<SalOrderVO>> page(SalOrderVO salOrder, Query query) {
		IPage<SalOrderVO> pages = salOrderService.selectSalOrderPage(Condition.getPage(query), salOrder);
		return R.data(pages);
	}

	/**
	 * 订单信息 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入salOrder")
	public R save(@Valid @RequestBody SalOrderEntity salOrder) {
		return R.status(salOrderService.save(salOrder));
	}

	/**
	 * 订单信息 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入salOrder")
	public R update(@Valid @RequestBody SalOrderEntity salOrder) {
		return R.status(salOrderService.updateById(salOrder));
	}

	/**
	 * 订单信息 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入salOrder")
	public R submit(@Valid @RequestBody SalOrderEntity salOrder) {
		return R.status(salOrderService.saveOrUpdate(salOrder));
	}

	/**
	 * 订单信息 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(salOrderService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@GetMapping("/export-salOrder")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入salOrder")
	public void exportSalOrder(@Parameter(hidden = true) @RequestParam Map<String, Object> salOrder, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<SalOrderEntity> queryWrapper = Condition.getQueryWrapper(salOrder, SalOrderEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(SalOrder::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(SalOrderEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<SalOrderExcel> list = salOrderService.exportSalOrder(queryWrapper);
		ExcelUtil.export(response, "订单信息数据" + DateUtil.time(), "订单信息数据表", list, SalOrderExcel.class);
	}

}
