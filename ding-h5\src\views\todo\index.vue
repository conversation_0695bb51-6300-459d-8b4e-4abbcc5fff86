<template>
  <div class="todo-container">
    <!-- 页签切换 -->
    <div class="tabs-container">
      <van-tabs v-model:active="activeTab" @change="onTabChange" color="#1989fa" title-active-color="#1989fa">
        <van-tab title="待办" name="pending" :badge="pendingBadge > 0 ? pendingBadge : ''"></van-tab>
        <van-tab title="已办" name="completed" ></van-tab>
      </van-tabs>
    </div>

    <div class="search-panel">
      <div class="search-box-wrapper">
        <div class="search-box">
          <van-search v-model="searchValue" :placeholder="activeTab === 'pending' ? '搜索待办事项' : '搜索已办事项'" class="search-bar">
            <!-- <template #right-icon>
              <van-icon name="scan" size="24" @click="handleSearchBarScan" />
            </template> -->
          </van-search>
          <div class="search-btn" @click="handleSearch">
            搜索
          </div>
        </div>
      </div>
    </div>
    <van-empty v-if="todoList.length === 0" :description="activeTab === 'pending' ? '暂无待办事项' : '暂无已办事项'" />
    <van-list v-else v-model:loading="loading" :finished="finished" :finished-text="scrollToast || '没有更多了'" @load="scrollToLowerLoad">
      <div class="todo-list">
        <div v-for="item in todoList" :key="item.id" class="todo-item">
          <div class="todo-content" @click="toDetail(item)" >
            <div class="todo-title">{{ item.eventTitle }}</div>
            <div class="todo-desc">{{ item.eventType }}</div>
            <div class="todo-info">
              <span class="todo-time">{{ item.rollDate }}</span>
              <span class="todo-source" v-if="item.dataSource">{{ item.dataSource }}</span>
              <span class="todo-deadline" v-if="item.handleRelExpireTime && activeTab === 'pending' " :class="getDeadlineClass(item.handleRelExpireTime)">
                {{ formatDeadline(item.handleRelExpireTime) }}
              </span>
              <!-- <span :class="['todo-status', item.status === '待处理' ? 'pending' : 'done']">{{ item.status }}</span> -->
            </div>
          </div>
        </div>
      </div>
    </van-list>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Search, Icon, List, Empty, Tabs, Tab } from 'vant'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { http } from '@/utils/http'
import { calculateRemainingDays, formatRemainingDays } from '@/utils/dateUtil'

const router = useRouter()
const searchValue = ref('')
const loading = ref(false)
const finished = ref(false)
const scrollToast = ref('')
const todoList = ref([])
const activeTab = ref('pending') // 当前激活的页签

const userStore = useUserStore()
const userId = userStore.userInfo?.user_id 


// Badge 计数
const pendingBadge = ref(0)

const formPage = ref({
  pageNo: 1,
  pageSize: 15,
  total: 0
})

const handleSearchBarScan = () => {
  // 处理扫描功能
}

const handleSearch = () => {
  formPage.value.pageNo = 1
  todoList.value = []
  finished.value = false
  getTodoList()
}

// 获取badge计数
const getBadgeCounts = async () => {
    // 获取待办数量
    const pendingRes = await http.get('/api/dingapp/reportcomplaint/page-dept-todo', {
      params: {
        // deptId:"1929800326110461954",
        deptId:userStore.userInfo?.dept_id ,
        current: 1,
        size: 1, // 只需要获取总数，不需要具体数据
        keyword: ''
      }
    })
  if (pendingRes.data?.data?.total) {
      pendingBadge.value = pendingRes.data.data.total
    }
}

// 页签切换处理
const onTabChange = (name) => {
  activeTab.value = name
  // 重置搜索和分页
  searchValue.value = ''
  formPage.value.pageNo = 1
  todoList.value = []
  finished.value = false
  loading.value = false
  // 重新加载数据
  getTodoList()
}

const toDetail = (item) => {
  router.push('/detail?id='+item.id)
}



const getTodoList = async () => {
  try {
    // 根据当前页签决定API接口
    let apiUrl = '/api/dingapp/reportcomplaint/page-dept-todo'
    const apiParams = {
      // deptId:"1929800326110461954",
      deptId:userStore.userInfo?.dept_id ,
      current: formPage.value.pageNo,
      size: formPage.value.pageSize,
      keyword: searchValue.value
    }

    // 如果是已办页签，可能需要调用不同的接口或添加状态参数
    if (activeTab.value === 'completed') {
      apiUrl = '/api/dingapp/reportcomplaint/page-dept-completed'
      apiParams.userId = userId
    } else {
    }

    const res = await http.get(apiUrl, {
      params: apiParams
    })
    
    if (res.data) {
      let list = []
      if (formPage.value.pageNo > 1) {
        list = [...todoList.value]
      }

      const resData = res.data.records || []
      list.push(...resData)

      formPage.value.total = res.data.valuetotal || 0
      formPage.value.pageSize = res.data.size || 15
      formPage.value.pageNo = res.data.current || 1
      todoList.value = list

      // 更新badge计数，使用res.data.data.total字段
      const badgeTotal = res.data?.total || 0
      if (activeTab.value === 'pending') {
        pendingBadge.value = badgeTotal
      } 
      loading.value = false
      if (todoList.value.length >= formPage.value.total) {
        finished.value = true
        if (formPage.value.pageNo === 1 && formPage.value.total < 15) {
          scrollToast.value = ''
        } else {
          scrollToast.value = '已经到底啦'
        }
      }
    }
  } catch (error) {
    console.error('获取待办列表失败:', error)
    loading.value = false
    finished.value = true
  }
}

const scrollToLowerLoad = () => {
  if (formPage.value.pageNo * formPage.value.pageSize >= formPage.value.total) {
    return
  }

  formPage.value.pageNo += 1
  loading.value = true
  getTodoList()
}

// 格式化剩余期限显示
const formatDeadline = (dateString) => {
  const remainingDays = calculateRemainingDays(dateString)
  return formatRemainingDays(remainingDays)
}

// 获取剩余期限的样式类
const getDeadlineClass = (dateString) => {
  const remainingDays = calculateRemainingDays(dateString)
  if (remainingDays < 0) {
    return 'expired' // 已过期
  } else if (remainingDays <= 3) {
    return 'urgent' // 紧急（3天内）
  } else if (remainingDays <= 7) {
    return 'warning' // 警告（7天内）
  } else {
    return 'normal' // 正常
  }
}

onMounted(async () => {
  await getBadgeCounts() // 先获取badge计数
  await getTodoList()
})
</script>

<style lang="scss" scoped>
.todo-container {
  min-height: 100vh;
  background-color: #f7f8fa;

  .tabs-container {
    background-color: #FFFFFF;

    :deep(.van-tabs__wrap) {
      border-bottom: 1px solid #ebedf0;
    }

    :deep(.van-tabs__nav) {
      background-color: #FFFFFF;
    }

    :deep(.van-tab) {
      font-size: 16px;
      font-weight: 500;
    }

    :deep(.van-tab--active) {
      color: #1989fa;
      font-weight: 600;
    }
  }

  .search-panel {
    background-color: #FFFFFF;
    padding: 20px 7px 7px 7px;

    .search-box-wrapper {
      padding: 0;

      .search-box {
        display: flex;
        height: 40px;
        width: 100%;
        justify-content: space-between;
        align-items: center;
        gap: 0;

        .search-bar {
          flex: 1;
          padding: 0;
          background-color: #FFFFFF;

          :deep(.van-search__content) {
            border-radius: 15px;
            background-color: #f7f8fa;
          }
        }

        .search-btn {
          flex-shrink: 0;
          display: flex;
          justify-content: center;
          align-items: center;
          height: 36px;
          width: 60px;
          margin-left: 5px;
          background-color: #1989fa;
          color: white;
          border-radius: 0 10px 10px 0;
          font-size: 14px;
          padding: 0;
        }
      }
    }
  }

  .todo-list {
    padding: 12px;
  }

  .todo-item {
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    .todo-content {
      padding: 16px;

      .todo-title {
        font-size: 16px;
        font-weight: 500;
        color: #323233;
        margin-bottom: 8px;
      }

      .todo-desc {
        font-size: 14px;
        color: #646566;
        margin-bottom: 12px;
      }

      .todo-info {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 12px;
        flex-wrap: wrap;
        line-height: 1.2;

        .todo-time,
        .todo-source,
        .todo-deadline {
          display: inline-block;
          line-height: 1.2;
          vertical-align: middle;
          height: auto;
          padding: 0;
          margin: 0;
        }

        .todo-time {
          color: #969799;
        }

        .todo-source {
          color: #969799;
        }

        .todo-deadline {
          &.normal {
            color: #07c160; // 绿色 - 正常
          }

          &.warning {
            color: #ff976a; // 橙色 - 警告
          }

          &.urgent {
            color: #ee0a24; // 红色 - 紧急
          }

          &.expired {
            color: #969799; // 灰色 - 已过期
          }
        }

        .todo-status {
          padding: 2px 6px;
          border-radius: 4px;

          &.pending {
            background-color: #1989fa;
            color: #fff;
          }

          &.done {
            background-color: #07c160;
            color: #fff;
          }
        }
      }
    }
  }
}
</style>