package org.springblade.ms.schoolInfo.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 
 * @TableName ms_school_info
 */
@TableName(value ="ms_school_info")
@Data
public class MsSchoolInfo extends TenantEntity implements Serializable {
    /**
     * 学校代码
     */
    private String schoolCode;

    /**
     * 校区码
     */
    private String campusCode;

    /**
     * 学校名称
     */
    private String schoolName;

    /**
     * 是否普惠性幼儿园
     */
    private String isInclusiveKindergarten;

    /**
     * 社会信用代码
     */
    private String socialCreditCode;

    /**
     * 行业分类码
     */
    private String industryCategoryCode;

    /**
     * 行业分类名称
     */
    private String industryCategoryName;

    /**
     * 主体学校办学类型码
     */
    private String mainSchoolTypeCode;

    /**
     * 主体学校办学类型名称
     */
    private String mainSchoolTypeName;

    /**
     * 是否独立设置少数民族学校
     */
    private String isIndependentSetEthnicSchool;

    /**
     * 学校(机构)地址代码
     */
    private String addressCode;

    /**
     * 学校(机构)地址名称
     */
    private String addressName;

    /**
     * 城乡分类码
     */
    private String urbanRuralCode;

    /**
     * 城乡分类名称
     */
    private String urbanRuralCategory;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * 学校(机构)属地管理教育行政部门代码
     */
    private String localEducationAuthorityCode;

    /**
     * 学校(机构)属地管理教育行政部门名称
     */
    private String localEducationAuthorityName;

    /**
     * 上级主管部门
     */
    private String superiorDepartment;

    /**
     * 上级管理部门码
     */
    private String superiorDepartmentCode;

    /**
     * 上级管理部门名称
     */
    private String superiorDepartmentName;

    /**
     * 学校(机构)举办者码
     */
    private String schoolOperatorCode;

    /**
     * 学校(机构)举办者名称
     */
    private String schoolOperatorName;

    /**
     * 举办者详细名称
     */
    private String operatorDetailName;

    /**
     * 学校(机构)英文名称
     */
    private String schoolEnglishName;

    /**
     * 学校(机构)负责人姓名
     */
    private String principalName;

    /**
     * 办公电话
     */
    private String officePhone;

    /**
     * 传真号码
     */
    private String faxNumber;

    /**
     * 邮政编码
     */
    private String postalCode;

    /**
     * 单位电子信箱
     */
    private String institutionEmail;

    /**
     * 填表人
     */
    private String formFilledBy;

    /**
     * 统计负责人姓名
     */
    private String statisticsOfficerName;

    /**
     * 统计负责人联系电话
     */
    private String statisticsContactNumber;

    /**
     * 小学规定年制码
     */
    private String primaryDurationCode;

    /**
     * 小学规定年制名称
     */
    private String primaryDurationName;

    /**
     * 小学入学年龄码
     */
    private String primaryEntryAgeCode;

    /**
     * 小学 入学年龄名称
     */
    private String primaryEntryAgeName;

    /**
     * 初中规定年制码
     */
    private String juniorHighDurationCode;

    /**
     * 初中规定年制名称
     */
    private String juniorHighDurationName;

    /**
     * 初中入学年龄码
     */
    private String juniorHighEntryAgeCode;

    /**
     * 初中入学年龄名称
     */
    private String juniorHighEntryAgeName;

    /**
     * 直属学校等级名称
     */
    private String directSchoolLevel;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}