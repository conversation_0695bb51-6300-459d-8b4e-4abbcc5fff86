package org.springblade.modules.auth.constant;

/**
 * AuthorizationConstant
 *
 * <AUTHOR>
 */
public interface BladeAuthConstant {

	/**
	 * 是否开启注册参数key
	 */
	String REGISTER_USER_VALUE = "account.registerUser";
	/**
	 * 账号锁定错误次数参数key
	 */
	String FAIL_COUNT_VALUE = "account.failCount";
	/**
	 * 账号锁定默认错误次数
	 */
	Integer FAIL_COUNT = 5;

	/**
	 * 密码有效期(天)
	 */
	String PASSWORD_EXPIRY_DAYS_VALUE = "password.expiry.days";

	/**
	 * 默认密码有效期(天)
	 */
	Integer PASSWORD_EXPIRY_DAYS = 90;

	/**
	 * 账户锁定时间(分钟)
	 */
	String ACCOUNT_LOCK_MINUTES_VALUE = "account.lock.minutes";

	/**
	 * 默认账户锁定时间(分钟)
	 */
	Integer ACCOUNT_LOCK_MINUTES = 30;
}
