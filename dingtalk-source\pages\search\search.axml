<view class="mainApp">
    <view class="searchPanel">
        <view class="searchBox">
            <ant-input
                    placeholder="搜索附近零售户"
                    className="search-bar"
                    focusClassName="search-bar-focus"
                    placeholder-style="font-size:16px;"
                    confirm-type="search"
                    focus
                    placeholderClassName="searchPlaceholderCls"
                    onChange="handleSearchInputChange"
            >
                <ant-icon slot="prefix" type="SearchOutline" style="font-size:18px;" />
                <ant-icon slot="suffix" type="ScanningOutline" style="font-size:24px;margin-right: 10px;" catchTap="handleSearchBarScan"/>
            </ant-input>
            <view class="searchInpBtn" catchTap="handleSearchBtnClick">搜索</view>
        </view>
    </view>
    <view class="formatParamBox">
        <view class="formatParamBtn" style="{{formatParamValue.length === 5 ? 'font-size: 12px' : 'font-size: 14px' }}">
            <ant-picker
                    value="{{formatParamValue}}"
                    placeholder=""
                    title="请选择业态"
                    options="{{formatParamList}}"
                    onOk="handleFormatParamPickerOK"
            >
                <view
                        slot="content"
                        slot-scope="prop"
                >业态：{{prop.value}}
                </view>
            </ant-picker>
        </view>
    </view>
    <view class="userLocationContent">
        当前位置：{{ currentAddress }}
    </view>
    <scroll-view scroll-y="{{true}}" style="height: 75vh; flex: 1;" scroll-top='{{gotoScrollTop}}' onScrollToLower="scrollToLowerSearch" lower-threshold='150'>
        <view class="searchFormList" a:if="{{ searchResultList.length > 0 }}">
            <view class="searchForm" a:for="{{ searchResultList }}" a:for-item="item" a:for-index="index" a:key="index">
                <view class="searchFormBox" data-item="{{item}}" onTap="handleFormBtn">
                    <view class="image" a:if="{{ item.photoPathList.length>0 }}">
                        <image class="vistaImage" src="{{item.photoPathList[0].filthPath}}" fit="cover" catchtap="previewVistaPic" />
                    </view>
                    <view class="image" a:if="{{ !item.photoPathList || item.photoPathList.length<=0 }}">
                        <image class="vistaImage" src="/image/picture-icon.svg" mode="contain" ></image>
                    </view>
                    <view class="text">
                        <view class="detailAddress">
                            <view class="detailAddressText">{{ item.businessAddr }}</view>
                        </view>
                        <view class="detailLicNo">
                            <view class="detailLicNoText">许可证：{{ item.licNo }}</view>
                        </view>
                        <view class="detailDistance">
                            <view class="detailDistanceText">距您{{ item.distance }}米</view>
                        </view>
                    </view>
                </view>
                <view class="searchFormBtn">
                    <view class="retailers" onTap="toLshhx" data-licData="{{item}}">
                        <image class="retailersImage" src="/image/retailers-icon.svg" fit="cover"></image>
                        <view class="retailersText">零售户画像</view>
                    </view>
                </view>
            </view>
        </view>
        <view class="scrollToast" a:if="{{ searchResultList.length >= 15 }}">{{scrollToast}}</view>
        <view class="hintText" a:if="{{ searchResultList.length === 0}}">
            <ant-empty title="暂无搜索结果" />
        </view>
    </scroll-view>
</view>