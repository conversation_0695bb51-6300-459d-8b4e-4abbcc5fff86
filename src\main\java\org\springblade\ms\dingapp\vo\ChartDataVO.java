package org.springblade.ms.dingapp.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 图表数据VO
 *
 * <AUTHOR> Name
 * @since 2025-05-01
 */
@Data
@Schema(description = "图表数据VO")
public class ChartDataVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * X轴数据
     */
    @Schema(description = "X轴数据")
    private List<String> xAxis;

    /**
     * 系列数据
     */
    @Schema(description = "系列数据")
    private List<SeriesVO> series;

    /**
     * 图表系列数据VO
     */
    @Data
    @Schema(description = "图表系列数据VO")
    public static class SeriesVO implements Serializable {

        private static final long serialVersionUID = 1L;
        /**
         * 系列名称
         */
        @Schema(description = "系列名称")
        private String name;

        /**
         * 系列数据
         */
        @Schema(description = "系列数据")
        private List<Integer> data;
    }
}
