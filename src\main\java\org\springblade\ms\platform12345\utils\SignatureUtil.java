package org.springblade.ms.platform12345.utils;

import cn.hutool.crypto.digest.DigestUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * 12345平台签名工具类
 */
@Component
@Slf4j
public class SignatureUtil {

    /**
     * 生成签名
     * 
     * @param account   账号
     * @param password  密码
     * @param timestamp 时间戳
     * @return 签名字符串
     */
    public static String generateSignature(String account, String password, String timestamp) {
        // 1.将account、password、timestamp三个参数进行合并
        // 2.将三个参数字符串拼接成一个字符串进行sha1加密
        String[] arr = {account, timestamp, password};
        // 字典序排序
        Arrays.sort(arr);
        
        // 按排序后的前后顺序连成串
        StringBuilder buf = new StringBuilder();
        buf.append(arr[0]).append(arr[1]).append(arr[2]);
        String befStr = buf.toString();
        
        // SHA1加密
        String signature = DigestUtil.sha1Hex(befStr);
        return signature;
    }
    
    /**
     * 验证签名
     * 
     * @param signature 签名
     * @param account   账号
     * @param password  密码
     * @param timestamp 时间戳
     * @return 是否验证通过
     */
    public static boolean verifySignature(String signature, String account, String password, String timestamp) {
        String calculatedSignature = generateSignature(account, password, timestamp);
        return calculatedSignature.equals(signature);
    }
}
