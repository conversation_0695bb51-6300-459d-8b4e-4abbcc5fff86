/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.ms.reportcomplaint.wrapper;

import org.springblade.common.cache.UserCache;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.modules.system.pojo.entity.User;
import org.springblade.ms.reportcomplaint.pojo.entity.ReportComplaintEntity;
import org.springblade.ms.reportcomplaint.pojo.vo.ReportComplaintVO;
import java.util.Objects;

/**
 * 工单信息 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
public class ReportComplaintWrapper extends BaseEntityWrapper<ReportComplaintEntity, ReportComplaintVO>  {

	public static ReportComplaintWrapper build() {
		return new ReportComplaintWrapper();
 	}

	@Override
	public ReportComplaintVO entityVO(ReportComplaintEntity reportComplaint) {
		ReportComplaintVO reportComplaintVO = Objects.requireNonNull(BeanUtil.copyProperties(reportComplaint, ReportComplaintVO.class));

		//User createUser = UserCache.getUser(reportComplaint.getCreateUser());
		//User updateUser = UserCache.getUser(reportComplaint.getUpdateUser());
		//reportComplaintVO.setCreateUserName(createUser.getName());
		//reportComplaintVO.setUpdateUserName(updateUser.getName());
		if(reportComplaint.getUserId()!=null){
			User updateUser = UserCache.getUser(reportComplaint.getUserId());
			reportComplaintVO.setUserName(updateUser.getName());
		}
		return reportComplaintVO;
	}


}
