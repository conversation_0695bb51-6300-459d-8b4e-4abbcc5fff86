<view>
  <ant-container title="烟柜">
    <view class="pgsb-img-box" a:if="{{ isView }}" >
      <image mode="scaleToFill" src="{{item.url}}" a:for="{{ yanguiFileList }}" onTap="onYanguiImgClick" data-imgIndex="{{index}}" />
    </view>
    <ant-uploader a:if="{{ !isView }}"
      onUpload="onYanguiUpload"
      fileList="{{yanguiFileList}}"
      onRemove="handleYanguiRemove"
      onPreview="onYanguiPreview"
      onChooseImageError="onChooseImageError"
    ></ant-uploader>
  </ant-container>

  <ant-container title="烟架">
    <view class="pgsb-img-box" a:if="{{ isView }}" >
      <image mode="scaleToFill" src="{{item.url}}" a:for="{{ yanjiaFileList }}" onTap="onYanjiaImgClick" data-imgIndex="{{index}}" />
    </view>
    <ant-uploader 
      a:if="{{ !isView }}"
      onUpload="onYanjiaUpload"
      fileList="{{yanjiaFileList}}"
      onRemove="handleYanjiaRemove"
      onPreview="onYanjiaPreview"
      onChooseImageError="onChooseImageError"
    ></ant-uploader>
  </ant-container>
  <ant-container >
    <view>
      专家建议：
    </view>
    <view class="proposal-text">
      {{adviceText}}
    </view>
  </ant-container>
  <view>
    <ant-container
      title="对碰结果"
      className="list"
    >
      <ant-table
        dataSource="{{collisionResult}}"
        columns="{{collistionColumn}}"
      >
      <!-- <view
        slot="item"
        slot-scope="props"
      >
        <view a:if="{{props.item.dataIndex === 'item'}}">
          {{props.row.index+1}}
        </view>
        <view a:if="{{props.item.dataIndex === 'name'}}">
          {{props.item.value}}
        </view>
        <view a:if="{{props.item.dataIndex === 'type'}}">
          {{props.item.value}}
        </view>
      </view> -->
    </ant-table>
    </ant-container>
    <ant-container
      title="识别结果"
      className="list"
    >
      <ant-table
        dataSource="{{identificationResult}}"
        columns="{{identificationColumn}}"
      >
        <!-- <view
          slot="item"
          slot-scope="props"
        >
          <view a:if="{{props.item.dataIndex === 'item'}}">
            {{props.row.index+1}}
          </view>
          <view a:if="{{props.item.dataIndex === 'name'}}">
            {{props.item.value}}
          </view>
 
        </view> -->
      </ant-table>
    </ant-container>
  </view>
</view>