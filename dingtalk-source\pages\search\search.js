import httpApi from "/utils/http/httpApi";
import {calculateDistance} from "../../utils/distanceUtil";

const app = getApp();
Page({
    data: {
        currentLon: null,
        currentLat: null,
        currentAddress: '',
        searchParam: '',
        //滚动提示文字
        scrollToast: '',
        //搜索表单分页
        formPage: {
            pageNo: 1,
            pageSize: 15,
            total: 10,
        },
        gotoScrollTop: 0,

        // 搜索结果集合
        searchResultList: [],

        formatParamList: [],
        formatParamValue: '',
        timer: null,
    },
    onLoad(query) {
        console.log('search onLoad', query);
        this.getFormatParamStorage();
        this.loadGroupFormatList();
        this.setData({
            currentAddress: query.currentAddress,
            currentLon: parseFloat(query.currentLon),
            currentLat: parseFloat(query.currentLat),
        })
        if(query.searchParam){
            this.setData({searchParam:query.searchParam})
        }
    },
    onReady() {
        this.searchList();
    },

    /**
     * 搜索列表
     */
    searchList() {
        httpApi.get('/api/dingapp/license/selectYhytPage', {
            params: {
                searchParam: this.data.searchParam,
                formatParam: this.data.formatParamValue,
                current: this.data.formPage.pageNo,
                size: this.data.formPage.pageSize,
            }
        }).then(res => {
            console.log(res);
            if (res && res.data) {
                let data = res.data;
                if (data.current == 1 && data.total < 10) {
                    this.setData({
                        scrollToast: ''
                    })
                } else {
                    this.setData({
                        scrollToast: "向上拉动显示更多内容"
                    });
                }
                // 填充数据
                let list = [];
                if (this.data.formPage.pageNo > 1) {
                    list = this.data.searchResultList;
                }
                data.records.forEach(item => {
                    // 计算距离
                    let distance = calculateDistance(this.data.currentLat, this.data.currentLon, item.latitude, item.longitude)
                    item.distance = Math.round(distance);
                    list.push(item);
                });

                this.setData({
                    'formPage.total': data.total,
                    'formPage.pageSize': data.size,
                    'formPage.pageNo': data.current,
                    searchResultList: list
                });
            }
        })
    },

    /**
     * 处理滚动到底部的事件
     */
    scrollToLowerSearch() {
        if (this.data.formPage.pageNo * this.data.formPage.pageSize >= this.data.formPage.total) {
            if (this.data.formPage.pageNo == 1 && this.data.formPage.total < 15) {
                this.setData({
                    scrollToast: ''
                })
                return
            }
            this.setData({
                scrollToast: '已经到底啦'
            })
            return;
        }
        this.setData({
            'formPage.pageNo': this.data.formPage.pageNo + 1
        });
        this.searchList();
    },

    getFormatParamStorage() {
        const res = dd.getStorageSync({
            key: 'formatParamValue',
        })

        console.log('getFormatParamStorage', res);
        this.setData({
            formatParamValue: res.data
        })
    },

    /**
     * 跳转到零售户画像页面
     */
    handleOpenLicenseDetailPage(e) {

    },

    /**
     * 处理扫码按钮
     */
    handleSearchBarScan() {
        dd.scan({
            type: 'qr',
            success: (res) => {
                const  text  = res.code;
                if (!text) {
                    dd.alert({
                        content: '未识别到二维码',
                        buttonText: '确定'
                    });
                }

                // 处理扫码结果
                const licenseNumberMatch = text.match(/许可证号:(\d+)/);
                if (licenseNumberMatch) {
                    const licenseNumber = licenseNumberMatch[1];
                    // 跳转到搜索页面
                    this.setData({searchParam:licenseNumber})
                } else {
                    dd.alert({
                        content: '未识别许可证号',
                        buttonText: '确定'
                    });
                }
            }
        })
    },

    /**
     * 处理搜索框输入事件
     * @param value
     * @param e
     */
    handleSearchInputChange(value, e) {
        this.setData({
            searchParam: value
        })
        clearTimeout(this.data.timer);
        // 设置新的定时器
        const newTimer = setTimeout(() => {
            this.handleSearchBtnClick();
        }, 300); // 300ms 的防抖时间
    
        this.setData({ timer: newTimer })
    },
    /**
     * 处理搜索按钮的点击事件
     */
    handleSearchBtnClick() {
        this.setData({
            'formPage.pageNo': 1
        })
        this.searchList();
    },

    /**
     * 获取零售户业态列表
     */
    async loadGroupFormatList() {
        let res = await httpApi.get('/api/dingapp/license/getFormatList');

        if (res.data) {
            this.setData({
                formatParamList: res.data
            })

            if (!this.data.formatParamValue || Object.keys(this.data.formatParamValue).length === 0) {
                this.setData({
                    formatParamValue: res.data[0]
                })

                this.setFormatParamStorage(res.data[0]);
            }
        }
    },

    /**
     * 处理业态选择OK按钮
     * @param value
     */
    handleFormatParamPickerOK(value) {
        let oldFormatParamValue = this.data.formatParamValue;
        if (oldFormatParamValue === value) {
            return;
        }

        this.setData({
            formatParamValue: value
        })
        this.setFormatParamStorage(value)

        this.setData({
            'formPage.pageNo': 1
        })
        this.searchList();
    },

    setFormatParamStorage(val) {
        dd.setStorage({
            key: 'formatParamValue',
            data: val
        })
    },

    toLshhx(e){
        const licData = e.target.dataset.licData 
        let jsonStr = JSON.stringify({licNo:licData.licNo,licId:licData.id})
        console.log(jsonStr,"jsonStr");
        dd.setStorage({
            key:"licData",data:jsonStr
        })
        dd.navigateTo({
            url: `/pages/lshhx/index?view=true&licNo=${licData.licNo}`
        });
        
    },
    handleFormBtn(e){
        console.log(e.target.dataset.item);
        const { id,longitude, latitude } = e.target.dataset.item
        // dd.redirectTo({
        //     url: `/pages/map/map?targetLon=${longitude}&targetLat=${latitude}`,
        // });


        const jsonString = JSON.stringify({
            longitude: longitude,
            latitude: latitude,
            markerId:id
        });
        dd.setStorageSync({key:'targetLocation', data:jsonString});
        dd.navigateBack();
    }

});
