<template>
  <div class="debug-api-page">
    <!-- 页面标题 -->
    <van-nav-bar
      title="接口调试"
      left-text="返回"
      left-arrow
      @click-left="$router.back()"
    />

    <!-- 接口信息 -->
    <div class="api-info">
      <van-cell-group>
        <van-cell title="接口名称" value="获取目标部门及下级部门用户信息" />
        <van-cell title="接口路径" value="/users/target-with-sub" />
        <van-cell title="请求方法" value="POST" />
      </van-cell-group>
    </div>

    <!-- 参数输入区域 -->
    <div class="params-section">
      <van-cell-group>
        <van-cell title="请求参数" />
        <van-cell>
          <template #title>
            <span>部门ID列表</span>
            <van-button
              type="primary"
              size="mini"
              @click="showDeptSelector = true"
              style="margin-left: 10px;"
            >
              选择部门
            </van-button>
          </template>
          <template #value>
            <div class="dept-ids-display">
              <van-tag
                v-for="(deptId, index) in deptIds"
                :key="index"
                type="primary"
                closeable
                @close="removeDeptId(index)"
                style="margin: 2px;"
              >
                {{ deptId }}
              </van-tag>
              <div v-if="deptIds.length === 0" class="empty-hint">
                请选择部门ID
              </div>
            </div>
          </template>
        </van-cell>
        <van-cell>
          <van-field
            v-model="manualDeptId"
            label="手动输入部门ID"
            placeholder="输入部门ID后点击添加"
            clearable
          >
            <template #button>
              <van-button
                size="small"
                type="primary"
                @click="addManualDeptId"
                :disabled="!manualDeptId.trim()"
              >
                添加
              </van-button>
            </template>
          </van-field>
        </van-cell>
      </van-cell-group>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <van-button
        type="primary"
        block
        @click="callApi"
        :loading="loading"
        :disabled="deptIds.length === 0"
      >
        {{ loading ? '请求中...' : '调用接口' }}
      </van-button>
      <van-button
        block
        @click="clearAll"
        style="margin-top: 10px;"
      >
        清空参数
      </van-button>
    </div>

    <!-- 响应结果 -->
    <div class="response-section" v-if="response">
      <van-cell-group>
        <van-cell title="响应结果" />
        <van-cell>
          <div class="response-content">
            <pre>{{ formatResponse(response) }}</pre>
          </div>
        </van-cell>
      </van-cell-group>
    </div>

    <!-- 部门选择弹窗 -->
    <van-popup v-model:show="showDeptSelector" position="bottom" style="height: 60%;">
      <div class="dept-selector">
        <van-nav-bar
          title="选择部门"
          left-text="取消"
          right-text="确定"
          @click-left="showDeptSelector = false"
          @click-right="confirmDeptSelection"
        />
        <div class="dept-list">
          <van-loading v-if="loadingDepts" size="24px">加载部门列表...</van-loading>
          <van-checkbox-group v-else v-model="selectedDepts">
            <van-cell-group>
              <van-cell
                v-for="dept in deptList"
                :key="dept.id"
                :title="dept.deptName"
                :label="`ID: ${dept.id}`"
                clickable
                @click="toggleDept(dept.id)"
              >
                <template #right-icon>
                  <van-checkbox :name="dept.id" />
                </template>
              </van-cell>
            </van-cell-group>
          </van-checkbox-group>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { showToast, showFailToast } from 'vant'
import * as dingNotificationApi from '@/api/dingtalk-notification'
import * as deptApi from '@/api/dept'

// 响应式数据
const deptIds = ref([])
const manualDeptId = ref('')
const loading = ref(false)
const response = ref(null)

// 部门选择相关
const showDeptSelector = ref(false)
const deptList = ref([])
const selectedDepts = ref([])
const loadingDepts = ref(false)

// 添加手动输入的部门ID
const addManualDeptId = () => {
  const id = manualDeptId.value.trim()
  if (id && !deptIds.value.includes(id)) {
    deptIds.value.push(id)
    manualDeptId.value = ''
    showToast('部门ID已添加')
  } else if (deptIds.value.includes(id)) {
    showToast('部门ID已存在')
  }
}

// 移除部门ID
const removeDeptId = (index) => {
  deptIds.value.splice(index, 1)
}

// 清空所有参数
const clearAll = () => {
  deptIds.value = []
  manualDeptId.value = ''
  response.value = null
  selectedDepts.value = []
}

// 调用接口
const callApi = async () => {
  if (deptIds.value.length === 0) {
    showFailToast('请至少添加一个部门ID')
    return
  }

  loading.value = true
  response.value = null

  try {
    console.log('调用接口，参数:', deptIds.value)
    const res = await dingNotificationApi.getTargetDepartmentsWithSubUsersDetails(deptIds.value)

    response.value = res
    console.log('接口响应:', res)
    showToast('接口调用成功')
  } catch (error) {
    console.error('接口调用失败:', error)
    response.value = {
      error: true,
      message: error.message || '接口调用失败',
      details: error
    }
    showFailToast('接口调用失败')
  } finally {
    loading.value = false
  }
}

// 格式化响应结果
const formatResponse = (data) => {
  return JSON.stringify(data, null, 2)
}

// 获取部门列表
const fetchDeptList = async () => {
  loadingDepts.value = true
  try {
    const res = await deptApi.getDeptTree()
    if (res.data) {
      deptList.value = flattenDeptTree(res.data)
    }
  } catch (error) {
    console.error('获取部门列表失败:', error)
    showFailToast('获取部门列表失败')
  } finally {
    loadingDepts.value = false
  }
}

// 扁平化部门树
const flattenDeptTree = (tree) => {
  const result = []
  const traverse = (nodes) => {
    nodes.forEach(node => {
      result.push({
        id: node.id,
        deptName: node.deptName
      })
      if (node.children && node.children.length > 0) {
        traverse(node.children)
      }
    })
  }
  traverse(tree)
  return result
}

// 切换部门选择
const toggleDept = (deptId) => {
  const index = selectedDepts.value.indexOf(deptId)
  if (index > -1) {
    selectedDepts.value.splice(index, 1)
  } else {
    selectedDepts.value.push(deptId)
  }
}

// 确认部门选择
const confirmDeptSelection = () => {
  const addedCount = selectedDepts.value.length
  selectedDepts.value.forEach(deptId => {
    if (!deptIds.value.includes(deptId)) {
      deptIds.value.push(deptId)
    }
  })
  showDeptSelector.value = false
  selectedDepts.value = []
  if (addedCount > 0) {
    showToast(`已添加 ${addedCount} 个部门`)
  }
}

// 组件挂载时获取部门列表
onMounted(() => {
  fetchDeptList()
})
</script>

<style lang="scss" scoped>
.debug-api-page {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.api-info {
  margin: 16px;
}

.params-section {
  margin: 16px;
}

.dept-ids-display {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  min-height: 32px;
}

.empty-hint {
  color: #969799;
  font-size: 14px;
}

.action-buttons {
  margin: 16px;
}

.response-section {
  margin: 16px;
}

.response-content {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  max-height: 400px;
  overflow-y: auto;

  pre {
    margin: 0;
    font-size: 12px;
    line-height: 1.4;
    white-space: pre-wrap;
    word-break: break-all;
  }
}

.dept-selector {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.dept-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
}
</style>
