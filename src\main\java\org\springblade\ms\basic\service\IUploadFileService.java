package org.springblade.ms.basic.service;

import cn.hutool.json.JSONObject;
import jakarta.servlet.http.HttpServletRequest;
import org.springblade.core.mp.base.BaseService;
import org.springblade.ms.basic.pojo.entity.UploadFileEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-02-05 15:51
 */
public interface IUploadFileService extends BaseService<UploadFileEntity> {

    /**
     * 根据对象id查询文件列表
     * @param objId
     * @return
     */
    List<UploadFileEntity> selectFileList(Long objId);

    /**
     * 钉钉上传文件
     * @param objId 对象id
     * @param objName 对象名称
     * @param file 文件
     * @param httpServletRequest 请求
     * @param extName 额外的名称
     * @return
     */
    JSONObject dingUploadFile(Long objId, String objName, MultipartFile file, HttpServletRequest httpServletRequest, String extName);

    /**
     * 获取封面图片
     * objId  yhytId
     */
    List<UploadFileEntity> selectCenterPhotoFileList(Long objId);

    /**
     * 批量获取封面图片
     * @param objIdList
     * @return
     */
    List<UploadFileEntity> selectCenterPhotoFileList(List<Long> objIdList);

    /**
     * 获取勘察记录三张封面图片
     */
    List<UploadFileEntity> selectSurveyFileList(Long objId);

}
