/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.ms.exploration.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.ms.exploration.mapper.ExplorationMapper;
import org.springblade.ms.exploration.pojo.entity.ExplorationEntity;
import org.springblade.ms.exploration.pojo.vo.ExplorationVO;
import org.springblade.ms.exploration.service.IExplorationService;
import org.springframework.stereotype.Service;

/**
 * 勘查记录 服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Service
@Slf4j
public class ExplorationServiceImpl extends BaseServiceImpl<ExplorationMapper, ExplorationEntity> implements IExplorationService {

	@Override
	public IPage<ExplorationVO> selectExplorationPage(IPage<ExplorationVO> page, ExplorationVO exploration) {
		return page.setRecords(baseMapper.selectExplorationPage(page, exploration));
	}

	@Override
	public ExplorationVO getExplorationByParam(ExplorationVO explorationVO) {
		return baseMapper.getExplorationByParam(explorationVO);
	}
}
