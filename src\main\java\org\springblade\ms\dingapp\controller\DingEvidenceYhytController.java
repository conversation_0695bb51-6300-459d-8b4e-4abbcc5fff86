package org.springblade.ms.dingapp.controller;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;

import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.ms.dingapp.dto.EvidenceSubmitDTO;
import org.springblade.ms.dingapp.dto.TextImportDTO;
import org.springblade.ms.dingapp.vo.DingExplorationVO;
import org.springblade.ms.dingapp.vo.DingMinioResult;
import org.springblade.ms.itemidentify.pojo.entity.ItemIdentifyEntity;
import org.springblade.ms.itemidentify.pojo.vo.ItemIdentifyVO;
import org.springblade.ms.itemidentify.wrapper.ItemIdentifyWrapper;
import org.springblade.ms.priceStandards.pojo.entity.MsEvidenceYhyt;
import org.springblade.ms.priceStandards.pojo.entity.MsPriceStandards;
import org.springblade.ms.priceStandards.pojo.vo.MsEvidenceYhytExportVO;
import org.springblade.ms.priceStandards.pojo.vo.MsEvidenceYhytVO;
import org.springblade.ms.priceStandards.service.MsEvidenceYhytService;
import org.springblade.ms.priceStandards.service.MsPriceStandardsService;
import org.springframework.web.bind.annotation.*;

import cn.hutool.core.util.ObjUtil;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@RestController
@AllArgsConstructor
    @RequestMapping("/dingapp/ecidenceYhyt")
public class DingEvidenceYhytController extends BladeController {
    private final MsEvidenceYhytService evidenceYhytService;
    private final MsPriceStandardsService priceStandardsService;

    /**
     * 列表
     * @param query
     * @return
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description  = "")
    public R<IPage<MsEvidenceYhytVO>> list(Query query, MsEvidenceYhytVO vo) {

        IPage<MsEvidenceYhytVO> pages = evidenceYhytService.pageList(Condition.getPage(query), vo);
        return R.data(pages);
    }

    /**
     * 详情
     * @param evidenceYhyt
     * @return
     */
    @GetMapping("/detailList")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "详情", description  = "")
    public R<List<MsEvidenceYhytExportVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> evidenceYhyt) {
        QueryWrapper<MsEvidenceYhyt> qw = new QueryWrapper<>();
        BladeUser bladexUser = AuthUtil.getUser();
        if(ObjUtil.isNotNull(bladexUser)){
            Long userId = bladexUser.getUserId();
            qw.eq("e.create_user", userId);
        }
//        if (ObjUtil.isNotNull(evidenceYhyt.get("yhytId"))) {
//            qw.eq("yhyt_id", Long.parseLong((String) evidenceYhyt.get("yhytId"))); // 明确指定字段名和值类型
//        }
        // 处理 selectionTime
        if (ObjUtil.isNotNull(evidenceYhyt.get("selectionTime"))) {
            String selectionTimeStr = (String) evidenceYhyt.get("selectionTime");
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            LocalDateTime selectionTime = LocalDateTime.parse(selectionTimeStr, formatter);
            qw.eq("e.selection_time", selectionTime); // 使用 LocalDateTime 类型参数
        }

        // 查询未删除数据
        qw.eq("e.is_deleted", 0);

        List<MsEvidenceYhytExportVO> msEvidenceYhytExportVOS = evidenceYhytService.listWithBarcode(qw);
        return R.data(msEvidenceYhytExportVOS);
    }


    /**
     * 提交涉案物品
     * @param submitDTO 提交数据DTO
     * @return 操作结果
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "提交涉案物品", description = "提交涉案物品数据")
    public R submit(@Valid @RequestBody EvidenceSubmitDTO submitDTO) {
        return R.status(evidenceYhytService.saveData(
            submitDTO.getPriceStandardsList(),
            submitDTO
        ));
    }

    /**
     * 导出Excel
     * @param evidenceYhyt 查询参数
     * @return 文件路径
     */
    @GetMapping("/exportExcel")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "导出Excel", description = "")
    public R<DingMinioResult> exportExcel(@Parameter(hidden = true) @RequestParam Map<String, Object> evidenceYhyt) {
        // 直接调用Service中的方法实现Excel导出
        return evidenceYhytService.exportExcel(evidenceYhyt);
    }

    /**
     * 根据ID列表删除涉案物品
     * @param ids 要删除的ID列表
     * @return 删除结果
     */
    @PostMapping("/delete")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "删除涉案物品", description = "根据ID列表删除涉案物品")
    public R<Boolean> delete(@RequestBody List<Long> ids) {
        return evidenceYhytService.deleteByIds(ids);
    }

    /**
     * 根据零售户ID和选择时间删除涉案物品
     * @param selectionTime 选择时间（可选）
     * @return 删除结果
     */
    @GetMapping("/deleteByYhytIdAndTime")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "根据零售户和时间删除", description = "根据零售户ID和选择时间删除涉案物品")
    public R<Boolean> deleteByYhytIdAndTime(
            @RequestParam("userId") Long userId,
            @RequestParam(value = "selectionTimeStr", required = false) String selectionTime) {
        return evidenceYhytService.deleteByYhytIdAndTime(selectionTime,userId);
    }



}
