package org.springblade.ms.dingapp.service.impl;

import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.utils.DateUtil;
import org.springframework.data.redis.core.RedisTemplate;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.ms.basic.pojo.entity.UploadFileEntity;
import org.springblade.ms.basic.pojo.entity.YhytLicenseEntity;
import org.springblade.ms.basic.pojo.entity.YhytLicenseUnlicensedEntity;
import org.springblade.ms.basic.pojo.vo.YhytLicenseVO;
import org.springblade.ms.basic.service.IUploadFileService;
import org.springblade.ms.basic.service.IYhytLicenseService;
import org.springblade.ms.basic.service.YhytLicenseUnlicensedService;
import org.springblade.ms.basic.wrapper.YhytLicenseWrapper;
import org.springblade.ms.common.utils.ApiCenterUtil;
import org.springblade.ms.dingapp.service.IDingLicenseService;
import org.springblade.ms.dingapp.vo.DingMapLicenseVO;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-01-24 00:08
 */
@Service
@AllArgsConstructor
@Slf4j
public class DingLicenseServiceImpl implements IDingLicenseService {

    private final IYhytLicenseService yhytLicenseService;

    private final IUploadFileService uploadFileService;

    private final YhytLicenseUnlicensedService yhytLicenseUnlicensedService;

    private final RedisTemplate<String, Object> redisTemplate;

    @Override
    public List<DingMapLicenseVO> getDingMapLicenseList(String searchParam, String formatParam, BigDecimal longitude, BigDecimal latitude) {

        YhytLicenseEntity param = new YhytLicenseEntity();
        if (StrUtil.isNotBlank(searchParam)) {
            // 判断searchParam内容是否为纯数字
            if (searchParam.matches("^[0-9]+$")) {
                param.setLicNo(searchParam);
            } else {
                param.setCompanyName(searchParam);
            }
        }

        if (StrUtil.isNotBlank(formatParam)) {
            if(!(formatParam.equals("全部"))){
                param.setBizFormat(formatParam);
            }
        }

        if (longitude != null && latitude != null) {
            param.setLongitude(longitude);
            param.setLatitude(latitude);
        }

        List<YhytLicenseEntity> list = yhytLicenseService.getDingMapList(param);

        if (IterUtil.isEmpty(list)) {
            return null;
        }

        // 转换对象
        List<DingMapLicenseVO> dingMapLicenseVOList = new ArrayList<>();
        for (YhytLicenseEntity entity : list) {
            DingMapLicenseVO dingMapLicenseVO = new DingMapLicenseVO(entity);
//            dingMapLicenseVO.setYhytLicenseVO(BeanUtil.copyProperties(entity, YhytLicenseVO.class));
            dingMapLicenseVOList.add(dingMapLicenseVO);
        }

//        if (longitude != null && latitude != null) {
//            for (DingMapLicenseVO dingMapLicenseVO : dingMapLicenseVOList) {
//                if (dingMapLicenseVO.getLatitude() == null || dingMapLicenseVO.getLongitude() == null)
//                    continue;
//                dingMapLicenseVO.setDistance(DistanceUtil.calculateDistance(latitude.doubleValue(), longitude.doubleValue(), dingMapLicenseVO.getLatitude().doubleValue(), dingMapLicenseVO.getLongitude().doubleValue()));
//            }
//        }

        return dingMapLicenseVOList;
    }

    private static final String REDIS_KEY_PREFIX = "ms:dingapp:license:";
    private static final int REDIS_EXPIRE_DAYS = 1; // 1天过期

    @Override
    public DingMapLicenseVO getDingMapLicense(String yhytId, String licNo) {
        log.info("开始获取零售户许可证信息: yhytId={}, licNo={}", yhytId, licNo);

        // 参数校验
        if (StrUtil.isBlank(yhytId) && StrUtil.isBlank(licNo)) {
            log.warn("获取零售户许可证信息失败: 参数为空");
            return null;
        }

        // 构建缓存键
        String cacheKey = buildCacheKey(yhytId, licNo);

        // 1. 尝试从Redis缓存获取
        DingMapLicenseVO cachedResult = getCachedLicense(cacheKey);
        if (cachedResult != null) {
            log.info("从缓存获取零售户许可证信息成功");
            // 1.1获取门面照片
            List<UploadFileEntity> fileList = uploadFileService.selectCenterPhotoFileList(cachedResult.getId());
            cachedResult.setPhotoPathList(fileList);
            return cachedResult;
        }

        // 2. 缓存未命中，尝试从API获取
        if (cachedResult == null) {
            log.info("缓存中未找到零售户信息，尝试从API获取: yhytId={}, licNo={}", yhytId, licNo);

            // 只有当提供了许可证编号时才尝试从API获取
            if (StrUtil.isNotBlank(licNo)) {
                YhytLicenseEntity entity = fetchLicenseFromNewApi(licNo);
                if (entity != null) {
                    log.info("成功从新API获取零售户信息: licNo={}", licNo);
                    if(null != yhytId) {
                        entity.setId(Long.valueOf(yhytId));
                    } else {
                        DingMapLicenseVO dbResult = fetchLicenseFromDatabase(yhytId, licNo);
                        entity.setId(dbResult.getId());
                    }
                    // 转换为VO对象并返回
                    YhytLicenseVO yhytLicenseVO = YhytLicenseWrapper.build().entityVO(entity);
                    cachedResult = new DingMapLicenseVO(yhytLicenseVO);
                }
            }
        }

        // 3. API未命中，从数据库获取
        if(cachedResult == null) {
            log.info("API中未找到零售户信息，尝试从数据库获取: yhytId={}, licNo={}", yhytId, licNo);
            cachedResult = fetchLicenseFromDatabase(yhytId, licNo);
        }

        // 4. 如果获取到结果，更新缓存
        if (cachedResult != null) {
            cacheLicense(cacheKey, cachedResult);
        }else{
            return null;
        }

        // 5.获取门面照片
        List<UploadFileEntity> fileList = uploadFileService.selectCenterPhotoFileList(cachedResult.getId());
        cachedResult.setPhotoPathList(fileList);

        return cachedResult;
    }

    /**
     * 从Redis缓存获取许可证信息
     */
    private DingMapLicenseVO getCachedLicense(String cacheKey) {
        try {
            Object cachedValue = redisTemplate.opsForValue().get(cacheKey);
            if (cachedValue != null) {
                log.debug("缓存命中: {}", cacheKey);
                DingMapLicenseVO result = (DingMapLicenseVO) cachedValue;
                return result;
            }
        } catch (Exception e) {
            log.error("从缓存获取许可证信息失败: {}", cacheKey, e);
        }
        return null;
    }

    /**
     * 构建缓存键
     */
    private String buildCacheKey(String yhytId, String licNo) {
        if (StrUtil.isNotBlank(licNo)) {
            return REDIS_KEY_PREFIX + "licNo:" + licNo;
        } else {
            return REDIS_KEY_PREFIX + "id:" + yhytId;
        }
    }

    /**
     * 将许可证信息存入Redis缓存
     */
    private void cacheLicense(String cacheKey, DingMapLicenseVO licenseVO) {
        try {
            // 存入缓存并设置过期时间
            redisTemplate.opsForValue().set(cacheKey, licenseVO, REDIS_EXPIRE_DAYS, TimeUnit.DAYS);
            log.debug("更新缓存: {}", cacheKey);
        } catch (Exception e) {
            log.error("缓存许可证信息失败: {}", cacheKey, e);
        }
    }

    /**
     * 从数据库获取许可证信息
     */
    private DingMapLicenseVO fetchLicenseFromDatabase(String yhytId, String licNo) {
        // 1. 查询有证零售户
        YhytLicenseEntity entity = fetchLicensedEntity(yhytId, licNo);

        // 2. 如果没有找到有证零售户，查询无证零售户
        YhytLicenseUnlicensedEntity unlicensedEntity = null;
        if (entity == null) {
            unlicensedEntity = fetchUnlicensedEntity(yhytId, null);

            // 3. 如果两者都没找到，返回null
            if (unlicensedEntity == null) {
                log.debug("未找到零售户信息: yhytId={}, licNo={}", yhytId, licNo);
                return null;
            }

            // 4. 将无证零售户转换为有证零售户格式
            entity = new YhytLicenseEntity();
            BeanUtil.copyProperties(unlicensedEntity, entity);
        }

        // 5. 转换为VO对象
        YhytLicenseVO yhytLicenseVO = YhytLicenseWrapper.build().entityVO(entity);
        DingMapLicenseVO dingMapLicenseVO = new DingMapLicenseVO(yhytLicenseVO);

        return dingMapLicenseVO;
    }

    /**
     * 获取有证零售户信息
     */
    private YhytLicenseEntity fetchLicensedEntity(String yhytId, String licNo) {
        if (StrUtil.isBlank(yhytId) && StrUtil.isBlank(licNo)) {
            return null;
        }

        YhytLicenseEntity param = new YhytLicenseEntity();
        if(StrUtil.isNotBlank(yhytId)) {
            param.setId(Long.parseLong(yhytId));
        }
        if(StrUtil.isNotBlank(licNo)) {
            param.setLicNo(licNo);
        }

        return yhytLicenseService.getOne(Condition.getQueryWrapper(param));
    }

    /**
     * 获取无证零售户信息
     */
    private YhytLicenseUnlicensedEntity fetchUnlicensedEntity(String yhytId, String licNo) {
        if (StrUtil.isBlank(yhytId) && StrUtil.isBlank(licNo)) {
            return null;
        }

        YhytLicenseUnlicensedEntity param = new YhytLicenseUnlicensedEntity();
        if(StrUtil.isNotBlank(yhytId)) {
            param.setId(Long.parseLong(yhytId));
        }
        if(StrUtil.isNotBlank(licNo)) {
            param.setLicNo(licNo);
        }

        return yhytLicenseUnlicensedService.getOne(Condition.getQueryWrapper(param));
    }


    /**
     * 通过API获取有证零售户信息
     *
     * @param licNo 许可证编号
     * @return 零售户许可证信息，如果获取失败返回null
     */
    private YhytLicenseEntity fetchLicenseFromApi(String licNo) {
        if (StrUtil.isBlank(licNo)) {
            log.warn("许可证编号为空，无法从API获取数据");
            return null;
        }

        log.debug("开始从API获取零售户许可证信息: licNo={}", licNo);

        try {
            // 1. 调用第一个API获取基础许可证信息 (sv251474XV9R)
            YhytLicenseEntity basicInfo = fetchBasicLicenseInfo(licNo);
            if (basicInfo == null) {
                log.debug("未从API获取到基础许可证信息: licNo={}", licNo);
                return null;
            }

            // 2. 调用第二个API获取补充业务信息 (sv25147KQUC8)
            YhytLicenseEntity businessInfo = fetchBusinessLicenseInfo(licNo);

            // 3. 合并两个API的数据
            if (businessInfo != null) {
                basicInfo.setLicType(businessInfo.getLicType());
                basicInfo.setCustCode(businessInfo.getCustCode());
                basicInfo.setBusiType(businessInfo.getBusiType());
                basicInfo.setLongitude(businessInfo.getLongitude());
                basicInfo.setLatitude(businessInfo.getLatitude());
                basicInfo.setBusiSizeCode(businessInfo.getBusiSizeCode());
                basicInfo.setBusiSizeName(businessInfo.getBusiSizeName());
                basicInfo.setAdscriptionName(businessInfo.getAdscriptionName());
                basicInfo.setBizFormat(businessInfo.getBizFormat());
                basicInfo.setOperateStatus(businessInfo.getOperateStatus());
                basicInfo.setTapPosition(businessInfo.getTapPosition());
                basicInfo.setManagerName(businessInfo.getManagerName());
                basicInfo.setOrderWeekday(businessInfo.getOrderWeekday());
            }

            log.debug("成功从API获取零售户许可证信息: licNo={}", licNo);
            return basicInfo;

        } catch (Exception e) {
            log.error("从API获取零售户许可证信息失败: licNo={}", licNo, e);
            return null;
        }
    }

    /**
     * 获取基础许可证信息 (API: sv251474XV9R)
     */
    private YhytLicenseEntity fetchBasicLicenseInfo(String licNo) {
        try {
            // 构建请求参数
            Map<String, Object> requestParams = new HashMap<>();
            String yesterdayStr = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            requestParams.put("FQZD", yesterdayStr);
            requestParams.put("XKZBH", licNo);
            requestParams.put("PAGENUM", 1);
            requestParams.put("PAGESIZE", 1);

            // 调用API
            String response = ApiCenterUtil.send("sv251474XV9R", requestParams);
            if (Func.isEmpty(response)) {
                log.debug("API sv251474XV9R 返回为空: licNo={}", licNo);
                return null;
            }

            // 解析响应
            JSONObject jsonObject = JSONUtil.parseObj(response);
            Integer errcode = jsonObject.getInt("errcode");
            if (errcode == null || errcode != 0) {
                log.warn("API sv251474XV9R 返回错误: licNo={}, errcode={}", licNo, errcode);
                return null;
            }

            JSONObject dataObj = jsonObject.getJSONObject("data");
            if (dataObj == null) {
                log.debug("API sv251474XV9R 数据为空: licNo={}", licNo);
                return null;
            }

            JSONArray dataArray = dataObj.getJSONArray("data");
            if (dataArray == null || dataArray.isEmpty()) {
                log.debug("API sv251474XV9R 数据列表为空: licNo={}", licNo);
                return null;
            }

            // 转换数据
            JSONObject itemData = dataArray.getJSONObject(0);
            return mapBasicLicenseInfo(itemData);

        } catch (Exception e) {
            log.error("获取基础许可证信息失败: licNo={}", licNo, e);
            return null;
        }
    }

    /**
     * 获取业务许可证信息 (API: sv25147KQUC8)
     */
    private YhytLicenseEntity fetchBusinessLicenseInfo(String licNo) {
        try {
            // 构建请求参数
            Map<String, Object> requestParams = new HashMap<>();
            requestParams.put("XKZBH", licNo);
            requestParams.put("PAGENUM", 1);
            requestParams.put("PAGESIZE", 1);

            // 调用API
            String response = ApiCenterUtil.send("sv25147KQUC8", requestParams);
            if (Func.isEmpty(response)) {
                log.debug("API sv25147KQUC8 返回为空: licNo={}", licNo);
                return null;
            }

            // 解析响应
            JSONObject jsonObject = JSONUtil.parseObj(response);
            Integer errcode = jsonObject.getInt("errcode");
            if (errcode == null || errcode != 0) {
                log.warn("API sv25147KQUC8 返回错误: licNo={}, errcode={}", licNo, errcode);
                return null;
            }

            JSONObject dataObj = jsonObject.getJSONObject("data");
            if (dataObj == null) {
                log.debug("API sv25147KQUC8 数据为空: licNo={}", licNo);
                return null;
            }

            JSONArray dataArray = dataObj.getJSONArray("data");
            if (dataArray == null || dataArray.isEmpty()) {
                log.debug("API sv25147KQUC8 数据列表为空: licNo={}", licNo);
                return null;
            }

            // 转换数据
            JSONObject itemData = dataArray.getJSONObject(0);
            return mapBusinessLicenseInfo(itemData);

        } catch (Exception e) {
            log.error("获取业务许可证信息失败: licNo={}", licNo, e);
            return null;
        }
    }

    /**
     * 映射基础许可证信息 (来自API sv251474XV9R)
     */
    private YhytLicenseEntity mapBasicLicenseInfo(JSONObject data) {
        YhytLicenseEntity entity = new YhytLicenseEntity();

        // 基础信息映射
        entity.setRetailerUuid(data.getStr("LSHUUID"));
        entity.setLicNo(data.getStr("XKZBH"));
        entity.setOldLicNo(data.getStr("LXKZH"));
        entity.setCompanyName(data.getStr("QYMC"));
        entity.setEcoType(data.getStr("QYJJLX"));
        entity.setContractPerson(data.getStr("LXR"));
        entity.setRetailTel(data.getStr("LXDH"));
        entity.setRetailTelBack(data.getStr("BYDH"));
        entity.setBusinessAddr(data.getStr("JYDZ"));

        // 时间字段转换
        entity.setValidateStart(timestampToDate(data.getLong("XKZYXQXQ")));
        entity.setValidateEnd(timestampToDate(data.getLong("XKZYXQXZ")));
        entity.setInvalidTime(timestampToDate(data.getLong("SXSJXKZZXSJ")));

        // 营业执照相关
        entity.setBusinessValidType(data.getStr("YYZZYXQLX"));
        entity.setLicStatus(data.getStr("XKZZT"));
        entity.setOrgName(data.getStr("PQMC"));
        entity.setIsHaveBusinessLic(data.getInt("SFYGSYYZZ"));
        entity.setBusinessLicNo(data.getStr("GSYYZZBMTYSHXYDMZ"));
        entity.setBusinessValidStart(timestampToLocalDate(data.getLong("YYZZYXQQ")));
        entity.setBusinessValidEnd(timestampToLocalDate(data.getLong("YYZZYXQZ")));
        entity.setRegisteredStatus(data.getStr("GSYYZZZT"));

        // 其他业务信息
        entity.setSpecialType(data.getStr("QTLX"));
        entity.setSpecialTypeOther(data.getStr("QTTSQTSM"));
        entity.setBusiSubType(data.getStr("CJSQ"));
        entity.setEnvType(data.getStr("DLHJ"));
        entity.setEcoSubType(data.getStr("JJLXZL"));
        entity.setIsValidate(data.getInt("XKZSFYX"));
        entity.setShopSign(data.getStr("DPZP"));
        entity.setConsumerNeed(data.getStr("XFXQ"));
        entity.setStoreBrand(data.getStr("LSQYPP"));
        entity.setManagerScope(data.getStr("XKZJYFWDX"));
        entity.setAdscriptionCode(data.getStr("JYCDQS"));
        entity.setSupplyStatus(data.getStr("GHZT"));
        entity.setSupplyOrgUuid(data.getStr("GHDWBH"));
        entity.setSupplyCompanyCode(data.getStr("GHDWBM"));
        entity.setSupplyCompanyName(data.getStr("GHDWMC"));

        return entity;
    }

    /**
     * 映射业务许可证信息 (来自API sv25147KQUC8)
     */
    private YhytLicenseEntity mapBusinessLicenseInfo(JSONObject data) {
        YhytLicenseEntity entity = new YhytLicenseEntity();

        // 业务信息映射
        entity.setLicNo(data.getStr("XKZBH"));
        entity.setLicType(data.getStr("XKZZL"));
        entity.setCustCode(data.getStr("KHBM"));
        entity.setBusiType(data.getStr("SZSQ"));
        entity.setLongitude(data.getBigDecimal("JD"));
        entity.setLatitude(data.getBigDecimal("WD"));
        entity.setBusiSizeCode(data.getStr("JYGM"));
        entity.setBusiSizeName(data.getStr("JYGMMC"));
        entity.setAdscriptionName(data.getStr("JYCDQSMC"));
        entity.setBizFormat(data.getStr("JYYT"));
        entity.setOperateStatus(data.getStr("JYZT"));
        entity.setTapPosition(data.getStr("DW"));
        entity.setManagerName(data.getStr("FRDB"));
        entity.setOrderWeekday(data.getStr("DHZQ"));

        return entity;
    }

    /**
     * 将毫秒级时间戳转换为Date对象
     *
     * @param timestampMs 毫秒级时间戳
     * @return Date对象，如果时间戳为null则返回null
     */
    private static Date timestampToDate(Long timestampMs) {
        if (timestampMs == null) {
            return null;
        }
        return Date.from(Instant.ofEpochMilli(timestampMs));
    }

    /**
     * 将毫秒级时间戳转换为LocalDate对象 (北京时间 UTC+8)
     *
     * @param timestampMs 毫秒级时间戳
     * @return LocalDate对象，如果时间戳为null则返回null
     */
    private LocalDate timestampToLocalDate(Long timestampMs) {
        if (timestampMs == null) {
            return null;
        }
        return Instant.ofEpochMilli(timestampMs)
                .atZone(ZoneId.of("Asia/Shanghai"))
                .toLocalDate();
    }



    @Override
    public IPage<DingMapLicenseVO> selectYhytPage(String searchParam, String formatParam, IPage<YhytLicenseVO> page, BigDecimal longitude,BigDecimal latitude) {

        YhytLicenseVO param = new YhytLicenseVO();
        if (StrUtil.isNotBlank(searchParam)) {
            // 判断searchParam内容是否为纯数字
            if (searchParam.matches("^[0-9]+$")) {
                param.setLicNo(searchParam);
            } else {
                param.setCompanyName(searchParam);
            }
        }

        if (StrUtil.isNotBlank(formatParam)) {
            if(!(formatParam.equals("全部"))){
                param.setBizFormat(formatParam);
            }
        }
        if (longitude != null && latitude != null) {
            param.setLatitude(latitude);
            param.setLongitude(longitude);
        }

        IPage<YhytLicenseVO> yhytLicenseVOIPage = yhytLicenseService.selectYhytLicensePage(page, param);
        // 处理数据
        IPage<DingMapLicenseVO> dingPage = new Page<>();
        dingPage.setCurrent(yhytLicenseVOIPage.getCurrent());
        dingPage.setSize(yhytLicenseVOIPage.getSize());
        dingPage.setTotal(yhytLicenseVOIPage.getTotal());

        List<Long> ids = yhytLicenseVOIPage.getRecords().stream()
                .map(YhytLicenseVO::getId)
                .toList();

        // 批量查询所有需要的门面照片
        List<UploadFileEntity> allCenterPhotos = uploadFileService.selectCenterPhotoFileList(ids);

        List<DingMapLicenseVO> dingMapLicenseVOList = new ArrayList<>();
        // 创建一个 Map，以 objId 为 key，存储对应的照片列表，方便后续查找
        Map<Long, List<UploadFileEntity>> photoMap = allCenterPhotos.stream()
                .collect(Collectors.groupingBy(UploadFileEntity::getObjId));

        for (YhytLicenseVO entity : yhytLicenseVOIPage.getRecords()) {
            DingMapLicenseVO dingMapLicenseVO = new DingMapLicenseVO(entity);
            // 从 Map 中根据 id 获取对应的照片列表
            List<UploadFileEntity> fileList = photoMap.getOrDefault(dingMapLicenseVO.getId(), Collections.emptyList());
            dingMapLicenseVO.setPhotoPathList(fileList);
            dingMapLicenseVOList.add(dingMapLicenseVO);
        }

        dingPage.setRecords(dingMapLicenseVOList);
        return dingPage;
    }

    /**
     * 通过新API获取有证零售户信息 (API: sv25225CJBJ5)
     *
     * @param licNo 许可证编号
     * @return 零售户许可证信息，如果获取失败返回null
     */
    private YhytLicenseEntity fetchLicenseFromNewApi(String licNo) {
        if (StrUtil.isBlank(licNo)) {
            log.warn("许可证编号为空，无法从新API获取数据");
            return null;
        }

        log.debug("开始从新API获取零售户许可证信息: licNo={}", licNo);

        try {
            // 构建请求参数
            Map<String, Object> requestParams = new HashMap<>();
            requestParams.put("XKZBH", licNo);
            requestParams.put("PAGENUM", 0);
            requestParams.put("PAGESIZE", 1);

            // 调用新API
            String response = ApiCenterUtil.send("sv25225CJBJ5", requestParams);
            if (Func.isEmpty(response)) {
                log.debug("新API sv25225CJBJ5 返回为空: licNo={}", licNo);
                return null;
            }

            // 解析响应
            JSONObject jsonObject = JSONUtil.parseObj(response);
            Integer errcode = jsonObject.getInt("errcode");
            if (errcode == null || errcode != 0) {
                log.warn("新API sv25225CJBJ5 返回错误: licNo={}, errcode={}", licNo, errcode);
                return null;
            }

            JSONObject dataObj = jsonObject.getJSONObject("data");
            if (dataObj == null) {
                log.debug("新API sv25225CJBJ5 数据为空: licNo={}", licNo);
                return null;
            }

            JSONArray dataArray = dataObj.getJSONArray("data");
            if (dataArray == null || dataArray.isEmpty()) {
                log.debug("新API sv25225CJBJ5 数据列表为空: licNo={}", licNo);
                return null;
            }

            // 转换数据
            JSONObject itemData = dataArray.getJSONObject(0);
            return mapNewApiLicenseInfo(itemData);

        } catch (Exception e) {
            log.error("从新API获取零售户许可证信息失败: licNo={}", licNo, e);
            return null;
        }
    }

    /**
     * 映射新API许可证信息 (来自API sv25225CJBJ5)
     */
    private YhytLicenseEntity mapNewApiLicenseInfo(JSONObject data) {
        YhytLicenseEntity entity = new YhytLicenseEntity();

        // 基础信息映射
        entity.setRetailerUuid(data.getStr("LSHUUID"));
        entity.setLicNo(data.getStr("XKZBH"));
        entity.setLicType(data.getStr("XKZZL"));
        entity.setOldLicNo(data.getStr("LXKZH"));
        entity.setCustCode(data.getStr("KHBM"));
        entity.setCompanyName(data.getStr("QYMC"));
        entity.setEcoType(data.getStr("QYJJLX"));
        entity.setContractPerson(data.getStr("LXR"));
        entity.setRetailTel(data.getStr("LXDH"));
        entity.setRetailTelBack(data.getStr("BYDH"));
        entity.setBusinessAddr(data.getStr("JYDZ"));

        // 时间字段转换
        entity.setValidateStart(parseApiDate(data.getStr("XKZYXQXQ")));
        entity.setValidateEnd(parseApiDate(data.getStr("XKZYXQXZ")));
        entity.setInvalidTime(parseApiDate(data.getStr("SXSJXKZZXSJ")));

        // 许可证状态和有效性
        entity.setLicStatus(data.getStr("XKZZT"));
        entity.setIsValidate(data.getInt("XKZSFYX"));

        // 营业执照相关
        entity.setIsHaveBusinessLic(data.getInt("SFYGSYYZZ"));
        entity.setBusinessLicNo(data.getStr("GSYYZZBM"));
        entity.setBusinessValidType(data.getStr("YYZZYXQLX"));
        entity.setBusinessValidStart(parseApiLocalDate(data.getStr("YYZZYXQQ")));
        entity.setBusinessValidEnd(parseApiLocalDate(data.getStr("YYZZYXQZ")));
        entity.setRegisteredStatus(data.getStr("GSYYZZZT"));

        // 地理位置信息
        entity.setLongitude(data.getBigDecimal("JD"));
        entity.setLatitude(data.getBigDecimal("WD"));

        // 经营相关信息
        entity.setManagerScope(data.getStr("JYFW"));
        entity.setBusiType(data.getStr("SZSQ"));
        entity.setBusiSubType(data.getStr("CJSQ"));
        entity.setEnvType(data.getStr("DLHJ"));
        entity.setConsumerNeed(data.getStr("XFXQ"));
        entity.setBizFormat(data.getStr("JYYT"));
        entity.setOperateStatus(data.getStr("JYZT"));
        entity.setBusiSizeCode(data.getStr("JYGMBM"));
        entity.setBusiSizeName(data.getStr("JYGMMC"));
        entity.setAdscriptionCode(data.getStr("JYCDQSBM"));
        entity.setAdscriptionName(data.getStr("JYCDQSMC"));

        // 供货和管理信息
        entity.setSupplyStatus(data.getStr("GHZT"));
        entity.setSupplyOrgUuid(data.getStr("GHDWBH"));
        entity.setSupplyCompanyCode(data.getStr("GHDWBM"));
        entity.setSupplyCompanyName(data.getStr("GHDWMC"));
        entity.setOrderWeekday(data.getStr("DHZQ"));

        // 其他信息
        entity.setSpecialType(data.getStr("QTLX"));
        entity.setSpecialTypeOther(data.getStr("QTTSQTSM"));
        entity.setEcoSubType(data.getStr("JJLXZL"));
        entity.setShopSign(data.getStr("DPZP"));
        entity.setStoreBrand(data.getStr("LSQYPP"));
        entity.setTapPosition(data.getStr("DW"));
        entity.setOrgName(data.getStr("PQMC"));

        // 发证机关信息
        // entity.setIssueOrgCode(data.getStr("FZJGBM"));
        // entity.setIssueOrgName(data.getStr("FZJGMC"));

        // 信用相关信息
        // entity.setCreditScore(data.getInt("XYFZ"));
         entity.setCreditLevel(data.getStr("XYDJ"));

        // 电子烟相关
         entity.setIsElectronicCigarette(data.getBigDecimal("SFJYDZY"));

        return entity;
    }

    /**
     * 解析API返回的日期字符串为Date对象
     *
     * @param dateStr 日期字符串
     * @return Date对象，如果解析失败返回null
     */
    private Date parseApiDate(String dateStr) {
        if (StrUtil.isBlank(dateStr)) {
            return null;
        }
        try {
            // 假设API返回的是 yyyy-MM-dd 格式的日期字符串
            LocalDate localDate = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            return Date.from(localDate.atStartOfDay(ZoneId.of("Asia/Shanghai")).toInstant());
        } catch (Exception e) {
            log.warn("解析日期字符串失败: {}", dateStr, e);
            return null;
        }
    }

    /**
     * 解析API返回的日期字符串为LocalDate对象
     *
     * @param dateStr 日期字符串
     * @return LocalDate对象，如果解析失败返回null
     */
    private LocalDate parseApiLocalDate(String dateStr) {
        if (StrUtil.isBlank(dateStr)) {
            return null;
        }
        try {
            // 假设API返回的是 yyyy-MM-dd 格式的日期字符串
            return LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        } catch (Exception e) {
            log.warn("解析日期字符串失败: {}", dateStr, e);
            return null;
        }
    }

}
