package org.springblade.ms.priceStandards.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.oss.model.BladeFile;
import org.springblade.core.tool.api.R;
import org.springblade.ms.basic.pojo.entity.UploadFileEntity;
import org.springblade.ms.dingapp.dto.EvidenceSubmitDTO;
import org.springblade.ms.dingapp.vo.DingMinioResult;
import org.springblade.ms.itemidentify.pojo.vo.ItemIdentifyVO;
import org.springblade.ms.priceStandards.excel.MsEvidenceYhytExcel;
import org.springblade.ms.priceStandards.pojo.entity.MsEvidenceYhyt;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springblade.ms.priceStandards.pojo.vo.MsEvidenceYhytExportVO;
import org.springblade.ms.priceStandards.pojo.vo.MsEvidenceYhytVO;

import java.io.ByteArrayOutputStream;
import java.util.Map;

import java.util.List;

/**
* <AUTHOR> @description 针对表【ms_evidence_yhyt(涉案物品表)】的数据库操作Service
* @createDate 2025-04-10 10:09:30
*/
public interface MsEvidenceYhytService extends BaseService<MsEvidenceYhyt> {

    /**
     * 保存涉案物品数据（新方法，使用DTO）
     * @param list 涉案物品列表
     * @param submitDTO 提交数据DTO
     * @return 是否保存成功
     */
    Boolean saveData(List<MsEvidenceYhyt> list, EvidenceSubmitDTO submitDTO);


    IPage<MsEvidenceYhytVO> pageList(IPage<MsEvidenceYhytVO> page, MsEvidenceYhytVO msEvidenceYhyt);

    /**
     * 查询涉案物品列表，包含条形码信息
     * @param queryWrapper 查询条件
     * @return 涉案物品列表，包含条形码信息
     */
    List<MsEvidenceYhytExportVO> listWithBarcode(Wrapper<MsEvidenceYhyt> queryWrapper);

    /**
     * 将涉案物品数据转换为Excel实体列表
     * @param dataList 涉案物品数据列表
     * @return Excel实体列表
     */
    List<MsEvidenceYhytExcel> convertToExcelList(List<MsEvidenceYhytExportVO> dataList);

    /**
     * 生成Excel文件并写入到ByteArrayOutputStream
     * @param excelList Excel实体列表
     * @return 包含文件内容的ByteArrayOutputStream
     */
    ByteArrayOutputStream generateExcelFile(List<MsEvidenceYhytExcel> excelList);

    /**
     * 将Excel文件上传到MinIO并保存文件信息
     * @param bos 包含文件内容的ByteArrayOutputStream
     * @param evidenceYhyt 查询参数
     * @return 文件信息
     */
    R<DingMinioResult> uploadExcelToMinio(ByteArrayOutputStream bos, Map<String, Object> evidenceYhyt,Long evidenceYhytId);

    /**
     * 导出Excel文件
     * @param evidenceYhyt 查询参数
     * @return 文件信息
     */
    R<DingMinioResult> exportExcel(Map<String, Object> evidenceYhyt);

    /**
     * 删除涉案物品
     * @param ids 要删除的ID列表
     * @return 是否删除成功
     */
    R<Boolean> deleteByIds(List<Long> ids);

    /**
     * 根据零售户ID和选择时间删除涉案物品
     * @param yhytId 零售户ID
     * @param selectionTime 选择时间
     * @return 是否删除成功
     */
    R<Boolean> deleteByYhytIdAndTime( String selectionTime,Long userId);
}
