package org.springblade.ms.platform12345.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 工单类型枚举
 * 对应字典A5.4
 */
@Getter
@AllArgsConstructor
public enum RollTypeEnum {

    /**
     * 咨询
     */
    CONSULT("0", "咨询"),

    /**
     * 投诉
     */
    COMPLAINT("1", "投诉"),

    /**
     * 举报
     */
    REPORT("2", "举报"),

    /**
     * 建议
     */
    SUGGESTION("3", "建议"),

    /**
     * 求助
     */
    HELP("4", "求助"),

    /**
     * 表扬
     */
    PRAISE("5", "表扬");

    /**
     * 代码
     */
    private final String code;

    /**
     * 值
     */
    private final String value;

    /**
     * 根据代码获取枚举
     *
     * @param code 代码
     * @return 枚举
     */
    public static RollTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (RollTypeEnum item : RollTypeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 根据代码获取值
     *
     * @param code 代码
     * @return 值
     */
    public static String getValueByCode(String code) {
        RollTypeEnum item = getByCode(code);
        return item != null ? item.getValue() : null;
    }

    /**
     * 根据值获取枚举
     *
     * @param value 值
     * @return 枚举
     */
    public static RollTypeEnum getByValue(String value) {
        if (value == null) {
            return null;
        }
        for (RollTypeEnum item : RollTypeEnum.values()) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 根据值获取代码
     *
     * @param value 值
     * @return 代码
     */
    public static String getCodeByValue(String value) {
        RollTypeEnum item = getByValue(value);
        return item != null ? item.getCode() : null;
    }
}
