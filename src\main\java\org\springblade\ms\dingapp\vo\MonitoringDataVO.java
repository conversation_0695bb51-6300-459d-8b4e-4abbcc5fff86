package org.springblade.ms.dingapp.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 监控数据VO
 *
 * <AUTHOR> Name
 * @since 2025-05-01
 */
@Data
@Schema(description = "监控数据VO")
public class MonitoringDataVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 系统登录人数
     */
    @Schema(description = "系统登录人数")
    private Integer loginCount;

    /**
     * 系统登录人数环比趋势（百分比）
     */
    @Schema(description = "系统登录人数环比趋势（百分比）")
    private Double loginTrend;

    /**
     * 检客户次数
     */
    @Schema(description = "检客户次数")
    private Integer inspectionCount;

    /**
     * 检客户次数环比趋势（百分比）
     */
    @Schema(description = "检客户次数环比趋势（百分比）")
    private Double inspectionTrend;

    /**
     * 品规识别次数
     */
    @Schema(description = "品规识别次数")
    private Integer recognitionCount;

    /**
     * 品规识别次数环比趋势（百分比）
     */
    @Schema(description = "品规识别次数环比趋势（百分比）")
    private Double recognitionTrend;

    /**
     * 识别品规数
     */
    @Schema(description = "识别品规数")
    private Integer specCount;

    /**
     * 识别品规数环比趋势（百分比）
     */
    @Schema(description = "识别品规数环比趋势（百分比）")
    private Double specTrend;

    /**
     * 异常烟数
     */
    @Schema(description = "异常烟数")
    private Integer abnormalCigaretteCount;

    /**
     * 异常烟数环比趋势（百分比）
     */
    @Schema(description = "异常烟数环比趋势（百分比）")
    private Double abnormalCigaretteTrend;
}
