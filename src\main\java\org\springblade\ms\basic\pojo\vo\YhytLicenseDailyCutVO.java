/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.ms.basic.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 零售许可证信息日切表 视图实体类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@Schema(description = "零售许可证信息日切表VO")
public class YhytLicenseDailyCutVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 联系人
	 */
	@Schema(description = "联系人")
	private String lxr;

	/**
	 * 客户编码
	 */
	@Schema(description = "客户编码")
	private String khbm;

	/**
	 * 经度
	 */
	@Schema(description = "经度")
	private String jd;

	/**
	 * 纬度
	 */
	@Schema(description = "纬度")
	private String wd;

	/**
	 * 经营地址
	 */
	@Schema(description = "经营地址")
	private String jydz;

	/**
	 * 当事人名称
	 */
	@Schema(description = "当事人名称")
	private String dsrmc;


	/**
	 * 失效时间（许可证注销时间）
	 */
	@Schema(description = "失效时间（许可证注销时间）")
	private String sxsjxkzzxsj;


	/**
	 * 企业名称
	 */
	@Schema(description = "企业名称")
	private String qymc;


	/**
	 * 联系电话
	 */
	@Schema(description = "联系电话")
	private String lxdh;

	/**
	 * 许可证有效期限起
	 */
	@Schema(description = "许可证有效期限起")
	private String xkzyxqxq;

	/**
	 * 许可证有效期限止
	 */
	@Schema(description = "许可证有效期限止")
	private String xkzyxqxz;

	/**
	 * 许可证编号
	 */
	@Schema(description = "许可证编号")
	private String xkzbh;

	/**
	 * 申请停业期限起
	 */
	@Schema(description = "申请停业期限起")
	private String sqtyqxq;

	/**
	 * 申请停业期限止
	 */
	@Schema(description = "申请停业期限止")
	private String sqtyqxz;

	/**
	 * 零售户uuid
	 */
	@Schema(description = "零售户uuid")
	private String lshuuid;

	/**
	 * 许可证状态
	 */
	@Schema(description = "许可证状态")
	private String xkzzt;

	/**
	 * 许可证状态（字典值）
	 */
	@Schema(description = "许可证状态（字典值）")
	private String xkzztText;

}
