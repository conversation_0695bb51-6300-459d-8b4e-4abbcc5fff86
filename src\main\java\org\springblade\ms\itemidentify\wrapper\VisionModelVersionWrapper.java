/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.ms.itemidentify.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.ms.itemidentify.pojo.entity.VisionModelVersionEntity;
import org.springblade.ms.itemidentify.pojo.vo.VisionModelVersionVO;
import java.util.Objects;

/**
 * 品规模型版本 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
public class VisionModelVersionWrapper extends BaseEntityWrapper<VisionModelVersionEntity, VisionModelVersionVO>  {

	public static VisionModelVersionWrapper build() {
		return new VisionModelVersionWrapper();
 	}

	@Override
	public VisionModelVersionVO entityVO(VisionModelVersionEntity visionModelVersion) {
		VisionModelVersionVO visionModelVersionVO = Objects.requireNonNull(BeanUtil.copyProperties(visionModelVersion, VisionModelVersionVO.class));

		//User createUser = UserCache.getUser(visionModelVersion.getCreateUser());
		//User updateUser = UserCache.getUser(visionModelVersion.getUpdateUser());
		//visionModelVersionVO.setCreateUserName(createUser.getName());
		//visionModelVersionVO.setUpdateUserName(updateUser.getName());

		return visionModelVersionVO;
	}


}
