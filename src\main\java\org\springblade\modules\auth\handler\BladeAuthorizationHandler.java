/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.auth.handler;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.cache.CacheNames;
import org.springblade.common.cache.ParamCache;
import org.springblade.common.cache.SysCache;
import org.springblade.common.constant.TenantConstant;
import org.springblade.core.launch.props.BladeProperties;
import org.springblade.core.oauth2.exception.ExceptionCode;
import org.springblade.core.oauth2.handler.AbstractAuthorizationHandler;
import org.springblade.core.oauth2.props.OAuth2Properties;
import org.springblade.core.oauth2.provider.OAuth2Request;
import org.springblade.core.oauth2.provider.OAuth2Validation;
import org.springblade.core.oauth2.service.OAuth2User;
import org.springblade.core.oauth2.service.impl.OAuth2UserDetail;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tenant.BladeTenantProperties;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.*;
import org.springblade.modules.system.pojo.entity.Tenant;
import org.springblade.modules.system.pojo.entity.User;
import org.springblade.modules.system.service.IUserService;
import org.springblade.modules.system.utils.PasswordUtil;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import static org.springblade.modules.auth.constant.BladeAuthConstant.FAIL_COUNT;
import static org.springblade.modules.auth.constant.BladeAuthConstant.FAIL_COUNT_VALUE;
import static org.springblade.modules.auth.constant.BladeAuthConstant.PASSWORD_EXPIRY_DAYS_VALUE;
import static org.springblade.modules.auth.constant.BladeAuthConstant.PASSWORD_EXPIRY_DAYS;
import static org.springblade.modules.auth.constant.BladeAuthConstant.ACCOUNT_LOCK_MINUTES_VALUE;
import static org.springblade.modules.auth.constant.BladeAuthConstant.ACCOUNT_LOCK_MINUTES;



/**
 * BladeAuthorizationHandler
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
public class BladeAuthorizationHandler extends AbstractAuthorizationHandler {

	private final BladeRedis bladeRedis;
	private final BladeProperties bladeProperties;
	private final BladeTenantProperties tenantProperties;
	private final OAuth2Properties oAuth2Properties;
	private final IUserService userService;

	/**
	 * 自定义弱密码列表
	 */
	private static final List<String> WEAK_PASSWORDS = List.of(
		// 常见管理员账户密码
		"admin", "administrator", "hr", "manager", "boss", "root", "system", "superadmin",
		// 常见简单密码
		"123456", "password", "********", "qwerty", "111111", "123123", "**********", "abc123",
		// 常见弱密码组合
		"admin123", "admin@123", "password123", "p@ssw0rd", "welcome", "welcome123", "test123",
		// 常见中文拼音密码
		"woaini", "iloveyou", "5201314", "woaini1314", "qweasd", "zxcvbn",
		// 常见键盘组合
		"qwertyuiop", "asdfghjkl", "zxcvbnm", "1qaz2wsx", "qazwsx", "1q2w3e4r", "1q2w3e",
		// 常见日期组合
		"19800101", "19900101", "20000101", "20100101", "20200101", "20210101", "20220101", "20230101"
	);

	/**
	 * 认证前校验
	 *
	 * @param request 请求信息
	 * @return boolean
	 */
	@Override
	public OAuth2Validation preValidation(OAuth2Request request) {
		if (request.isPassword() || request.isCaptchaCode()) {
			// 生产环境弱密码校验
			if (bladeProperties.isProd() && isWeakPassword(request.getPassword())) {
				return buildValidationFailure(ExceptionCode.INVALID_USER_PASSWORD);
			}
			// 判断登录是否锁定
			OAuth2Validation failCountValidation = validateFailCount(request.getTenantId(), request.getUsername());
			if (!failCountValidation.isSuccess()) {
				return failCountValidation;
			}
		}
		return super.preValidation(request);
	}

	/**
	 * 认证前失败回调
	 *
	 * @param validation 失败信息
	 */
	@Override
	public void preFailure(OAuth2Request request, OAuth2Validation validation){
		// 增加错误锁定次数
		addFailCount(request.getTenantId(), request.getUsername());
		log.error("用户：{}，认证失败，失败原因：{}", request.getUsername(), validation.getMessage());
	}


	/**
	 * 认证校验
	 *
	 * @param user    用户信息
	 * @param request 请求信息
	 * @return boolean
	 */
	@Override
	public OAuth2Validation authValidation(OAuth2User user, OAuth2Request request) {
		// 密码模式、刷新token模式、验证码模式需要校验租户状态
		if (request.isPassword() || request.isRefreshToken() || request.isCaptchaCode()) {
			// 租户校验
			OAuth2Validation tenantValidation = validateTenant(user.getTenantId());
			if (!tenantValidation.isSuccess()) {
				return tenantValidation;
			}

			// 账户锁定校验
			int failCount = getFailCount(user.getTenantId(), user.getAccount());
			int maxFailCount = Func.toInt(ParamCache.getValue(FAIL_COUNT_VALUE), FAIL_COUNT);
			if (failCount >= maxFailCount) {
				// 获取Redis中的过期时间
				Long ttl = getKeyExpireTime(user.getTenantId(), user.getAccount());

				// 计算解锁时间
				LocalDateTime unlockTime = LocalDateTime.now().plusSeconds(ttl);
				String unlockTimeStr = unlockTime.format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

				// 自定义错误消息
				String customMessage = "账号已被锁定，请于 " + unlockTimeStr + " 后再试";
				return buildValidationFailure(ExceptionCode.USER_TOO_MANY_FAILS.getCode(), customMessage);
			}

			// 密码过期校验
			User userEntity = userService.getById(Func.toLong(user.getUserId()));
			if (userEntity != null && userEntity.getPasswordLastChangedTime() != null) {
				// 获取密码有效期天数（默认90天）
				int passwordExpiryDays = Func.toInt(ParamCache.getValue(PASSWORD_EXPIRY_DAYS_VALUE), PASSWORD_EXPIRY_DAYS);

				// 计算密码是否过期
				LocalDateTime expiryDate = userEntity.getPasswordLastChangedTime().plusDays(passwordExpiryDays);
				if (LocalDateTime.now().isAfter(expiryDate)) {
					return buildValidationFailure(ExceptionCode.PASSWORD_EXPIRED);
				}
			}
		}
		return super.authValidation(user, request);
	}

	/**
	 * 认证成功回调
	 *
	 * @param user 用户信息
	 */
	@Override
	public void authSuccessful(OAuth2User user, OAuth2Request request) {
		// 清空错误锁定次数
		delFailCount(user.getTenantId(), user.getAccount());
		log.info("用户：{}，认证成功", user.getAccount());

		// 检查密码是否即将过期
		checkPasswordExpiry(user);
	}

	/**
	 * 检查密码是否即将过期
	 *
	 * @param user 用户信息
	 */
	private void checkPasswordExpiry(OAuth2User user) {
		try {
			// 获取用户实体
			User userEntity = userService.getById(Func.toLong(user.getUserId()));
			if (userEntity != null && userEntity.getPasswordLastChangedTime() != null) {
				// 获取密码有效期天数（默认90天）
				int passwordExpiryDays = Func.toInt(ParamCache.getValue(PASSWORD_EXPIRY_DAYS_VALUE), PASSWORD_EXPIRY_DAYS);

				// 计算密码过期日期
				LocalDateTime expiryDate = userEntity.getPasswordLastChangedTime().plusDays(passwordExpiryDays);

				// 计算当前日期与过期日期之间的天数
				long daysUntilExpiry = java.time.Duration.between(LocalDateTime.now(), expiryDate).toDays();

				// 如果密码将在7天内过期，添加到用户详情中
				if (daysUntilExpiry >= 0 && daysUntilExpiry <= 7) {
					// 将密码过期信息添加到令牌参数中，在BladeTokenHandler中处理
					if (user instanceof OAuth2UserDetail) {
						OAuth2UserDetail userDetail = (OAuth2UserDetail) user;
						if (userDetail.getDetail() == null) {
							userDetail.setDetail(Kv.create());
						}
						userDetail.getDetail().set("passwordExpiryDays", daysUntilExpiry);
						log.info("用户：{}，密码将在{}天后过期", user.getAccount(), daysUntilExpiry);
					}
				}
			}
		} catch (Exception e) {
			log.error("检查密码过期失败: {}", e.getMessage());
		}
	}

	/**
	 * 认证失败回调
	 *
	 * @param user       用户信息
	 * @param validation 失败信息
	 */
	@Override
	public void authFailure(OAuth2User user, OAuth2Request request, OAuth2Validation validation) {
		// 如果是验证码错误，不增加密码错误次数
		if (validation != null && validation.getMessage() != null && validation.getMessage().contains("验证码不正确")) {
			log.error("用户：{}，认证失败，失败原因：{}", request.getUsername(), validation.getMessage());
			return;
		}

		// 如果用户不为空，增加密码错误次数
		if (user != null && StringUtil.isNotBlank(user.getUserId())) {
			int failCount = getFailCount(user.getTenantId(), user.getAccount());
			int maxFailCount = Func.toInt(ParamCache.getValue(FAIL_COUNT_VALUE), FAIL_COUNT);
			if(failCount >= maxFailCount) {
				// 获取Redis中的过期时间
				Long ttl = getKeyExpireTime(user.getTenantId(), user.getAccount());

				// 计算解锁时间
				LocalDateTime unlockTime = LocalDateTime.now().plusSeconds(ttl);
				String unlockTimeStr = unlockTime.format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

				// 自定义错误消息
				String customMessage = "账号已被锁定，请于 " + unlockTimeStr + " 后再试";
				log.error("用户：{}，认证失败，失败原因：{}", user.getAccount(), customMessage);
				return;
			}
		}

		// 增加登录失败次数
		if (request.getUsername() != null) {
			addFailCount(request.getTenantId(), request.getUsername());
		}

		// 记录失败日志
		String username = user != null ? user.getAccount() : request.getUsername();
		log.error("用户：{}，认证失败，失败原因：{}", username, validation != null ? validation.getMessage() : "未知原因");
	}

	/**
	 * 判断是否为弱密码
	 *
	 * @param rawPassword      加密密码
	 * @return boolean
	 */
	private boolean isWeakPassword(String rawPassword) {
		// 获取公钥
		String publicKey = oAuth2Properties.getPublicKey();
		// 获取私钥
		String privateKey = oAuth2Properties.getPrivateKey();
		// 解密密码
		String decryptPassword = SM2Util.decrypt(rawPassword, publicKey, privateKey);

		// 检查是否在弱密码列表中
		boolean isWeak = WEAK_PASSWORDS.stream()
			.anyMatch(weakPass -> weakPass.equalsIgnoreCase(decryptPassword));

		if (isWeak) {
			return true;
		}

		// 使用密码策略进行复杂度校验
		return !PasswordUtil.validatePasswordComplexity(decryptPassword);
	}

	/**
	 * 租户授权校验
	 *
	 * @param tenantId 租户id
	 * @return OAuth2Validation
	 */
	private OAuth2Validation validateTenant(String tenantId) {
		// 租户校验
		Tenant tenant = SysCache.getTenant(tenantId);
		if (tenant == null) {
			return buildValidationFailure(ExceptionCode.USER_TENANT_NOT_FOUND);
		}
		// 租户授权时间校验
		Date expireTime = tenant.getExpireTime();
		if (tenantProperties.getLicense()) {
			String licenseKey = tenant.getLicenseKey();
			String decrypt = DesUtil.decryptFormHex(licenseKey, TenantConstant.DES_KEY);
			Tenant license = JsonUtil.parse(decrypt, Tenant.class);
			if (license == null || !license.getId().equals(tenant.getId())) {
				return buildValidationFailure(ExceptionCode.UNAUTHORIZED_USER_TENANT);
			}
			expireTime = license.getExpireTime();
		}
		if (expireTime != null && expireTime.before(DateUtil.now())) {
			return buildValidationFailure(ExceptionCode.UNAUTHORIZED_USER_TENANT);
		}
		return new OAuth2Validation();
	}

	/**
	 * 判断登录是否锁定
	 *
	 * @param tenantId 租户id
	 * @param account  账号
	 * @return OAuth2Validation
	 */
	private OAuth2Validation validateFailCount(String tenantId, String account) {
		int failCount = getFailCount(tenantId, account);
		int maxFailCount = Func.toInt(ParamCache.getValue(FAIL_COUNT_VALUE), FAIL_COUNT);
		if (failCount >= maxFailCount) {
			// 获取Redis中的过期时间
			Long ttl = getKeyExpireTime(tenantId, account);

			// 计算解锁时间
			LocalDateTime unlockTime = LocalDateTime.now().plusSeconds(ttl);
			String unlockTimeStr = unlockTime.format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

			// 自定义错误消息
			String customMessage = "账号已被锁定，请于 " + unlockTimeStr + " 后再试";
			return buildValidationFailure(ExceptionCode.USER_TOO_MANY_FAILS.getCode(), customMessage);
		}
		return new OAuth2Validation();
	}

	/**
	 * 获取账号错误次数
	 *
	 * @param tenantId 租户id
	 * @param username 账号
	 * @return int
	 */
	private int getFailCount(String tenantId, String username) {
		if (Func.hasEmpty(tenantId, username)) {
			return 0;
		}
		return Func.toInt(bladeRedis.get(CacheNames.tenantKey(tenantId, CacheNames.USER_FAIL_KEY, username)), 0);
	}

	/**
	 * 设置账号错误次数
	 *
	 * @param tenantId 租户id
	 * @param username 账号
	 */
	private void addFailCount(String tenantId, String username) {
		if (Func.hasEmpty(tenantId, username)) {
			return;
		}
		int failCount = getFailCount(tenantId, username);
		int maxFailCount = Func.toInt(ParamCache.getValue(FAIL_COUNT_VALUE), FAIL_COUNT);
		if(failCount < maxFailCount) {
			int lockMinutes = Func.toInt(ParamCache.getValue(ACCOUNT_LOCK_MINUTES_VALUE), ACCOUNT_LOCK_MINUTES);
			bladeRedis.setEx(CacheNames.tenantKey(tenantId, CacheNames.USER_FAIL_KEY, username), failCount + 1, Duration.ofMinutes(lockMinutes));
		}
	}

	/**
	 * 清空账号错误次数
	 *
	 * @param tenantId 租户id
	 * @param username 账号
	 */
	private void delFailCount(String tenantId, String username) {
		bladeRedis.del(CacheNames.tenantKey(tenantId, CacheNames.USER_FAIL_KEY, username));
	}

	/**
	 * 获取Redis键的过期时间（秒）
	 *
	 * @param tenantId 租户id
	 * @param username 账号
	 * @return 过期时间（秒）
	 */
	private Long getKeyExpireTime(String tenantId, String username) {
		String cacheKey = CacheNames.tenantKey(tenantId, CacheNames.USER_FAIL_KEY, username);
		// 默认30分钟
		long ttl = Func.toLong(ParamCache.getValue(ACCOUNT_LOCK_MINUTES_VALUE), ACCOUNT_LOCK_MINUTES) * 60;
		try {
			// 检查键是否存在
			if (bladeRedis.exists(cacheKey)) {
				// 使用Redis的TTL命令获取键的过期时间
				ttl = bladeRedis.ttl(cacheKey);
			}
		} catch (Exception e) {
			log.warn("获取Redis键过期时间失败: {}", e.getMessage());
		}
		return ttl;
	}

	/**
	 * 构建自定义错误消息的验证失败结果
	 *
	 * @param code 错误码
	 * @param message 错误消息
	 * @return OAuth2Validation
	 */
	private OAuth2Validation buildValidationFailure(int code, String message) {
		OAuth2Validation validation = new OAuth2Validation();
		validation.setSuccess(false);
		validation.setCode(code);
		validation.setMessage(message);
		return validation;
	}

	/**
	 * 构建自定义错误消息的验证失败结果
	 *
	 * @param exceptionCode 错误码枚举
	 * @return OAuth2Validation
	 */
	public OAuth2Validation buildValidationFailure(ExceptionCode exceptionCode) {
		return buildValidationFailure(exceptionCode.getCode(), exceptionCode.getMessage());
	}
}
