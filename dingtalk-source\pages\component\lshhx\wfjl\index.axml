<view class="wfjlMain">
  <scroll-view scroll-y="{{true}}" style="flex: 1;" scroll-top='{{gotoScrollTop}}' onScrollToLower="scrollToLowerSearch" lower-threshold='150'>
    <ant-empty a:if="{{ dataList.length==0 }}"
      title="数据为空"
    />
    <view  a:for="{{ dataList }}">
      <view class="title">
        <text class="title-two">{{item.regTime}}</text>
      </view>
      <view class="info_card">
        <view class="flex text-box" onTap="handleOpenPopup" data-item="{{item}}">
          <view class="text">
            案由：
          </view>
          <view class="text text_content">
          {{item.caseOfAction}}
          </view>
        </view >
        <view class="flex" style="margin-top:10px;">
          <view class="text ">
            行政处罚时间：
          </view>
          <view class="text text-time">
            {{item.punishDecideDate ? item.punishDecideDate : '无'}}
          </view>
        </view >
      </view>
    </view>
  </scroll-view>
</view>
<ant-popup
  visible="{{scrollVisible}}"
  position="bottom"
  title="{{popupData.regTime.split(' ')[0]}} 案由"
  showClose="{{true}}"
  onClose="handlePopupClose"
>
  <scroll-view
    scroll-y
    style="padding: 12px 14px 20px; height: 300px"
    disable-lower-scroll="out-of-bounds"
    disable-upper-scroll="out-of-bounds"
    >
     {{popupData.caseOfAction}}
  </scroll-view>
</ant-popup>