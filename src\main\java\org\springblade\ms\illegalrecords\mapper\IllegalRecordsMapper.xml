<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.ms.illegalrecords.mapper.IllegalRecordsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="illegalRecordsResultMap" type="org.springblade.ms.illegalrecords.pojo.entity.IllegalRecordsEntity">
        <result column="id" property="id"/>
        <result column="case_uuid" property="caseUuid"/>
        <result column="case_name" property="caseName"/>
        <result column="case_code" property="caseCode"/>
        <result column="case_year" property="caseYear"/>
        <result column="case_date" property="caseDate"/>
        <result column="case_place" property="casePlace"/>
        <result column="place_gis_x" property="placeGisX"/>
        <result column="place_gis_y" property="placeGisY"/>
        <result column="case_of_action" property="caseOfAction"/>
        <result column="case_property" property="caseProperty"/>
        <result column="reg_doc_no" property="regDocNo"/>
        <result column="reg_time" property="regTime"/>
        <result column="report_date" property="reportDate"/>
        <result column="case_type" property="caseType"/>
        <result column="is_finshed" property="isFinshed"/>
        <result column="finsh_date" property="finshDate"/>
        <result column="is_report_hotline" property="isReportHotline"/>
        <result column="apply_code" property="applyCode"/>
        <result column="punish_decide_date" property="punishDecideDate"/>
        <result column="decide_full_no" property="decideFullNo"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <resultMap id="illegalRecordsVOResultMap" type="org.springblade.ms.illegalrecords.pojo.vo.IllegalRecordsVO" extends="illegalRecordsResultMap">
    </resultMap>

    <select id="selectIllegalRecordsPage" resultMap="illegalRecordsVOResultMap">
        select * from ms_Illegal_records where is_deleted = 0
    </select>


    <select id="exportIllegalRecords" resultType="org.springblade.ms.illegalrecords.excel.IllegalRecordsExcel">
        SELECT * FROM ms_Illegal_records ${ew.customSqlSegment}
    </select>

</mapper>
