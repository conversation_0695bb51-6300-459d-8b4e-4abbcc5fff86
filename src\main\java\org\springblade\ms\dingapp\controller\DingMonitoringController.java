package org.springblade.ms.dingapp.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.ms.dingapp.service.IMonitoringService;
import org.springblade.ms.dingapp.vo.ChartDataVO;
import org.springblade.ms.dingapp.vo.MonitoringDataVO;
import org.springblade.ms.dingapp.vo.PersonnelRankingVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 钉钉小程序-监控控制器
 *
 * <AUTHOR> Name
 * @since 2025-05-01
 */
@RestController
@AllArgsConstructor
@RequestMapping("/dingapp/monitoring")
@Tag(name = "监控", description = "监控接口")
public class DingMonitoringController extends BladeController {

    private final IMonitoringService monitoringService;
    private final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 获取监控数据
     *
     * @param timeRange   时间范围：day, week, month, quarter, year
     * @param deptId      部门ID，为null时查询所有中队
     * @param userId 用户ID，为null时查询所有人员
     * @return 监控数据
     */
    @GetMapping("/data")
    @Operation(summary = "获取监控数据", description = "获取监控概览数据")
    public R<MonitoringDataVO> getMonitoringData(
            @RequestParam(defaultValue = "day") String timeRange,
            @RequestParam(required = true) String startTime,
            @RequestParam(required = true) String endTime,
            @RequestParam(required = false) String deptId,
            @RequestParam(required = false) String userId) {
        if(StringUtil.isBlank(deptId) || "null".equals(deptId)) {
            deptId = null;
        }
        if(StringUtil.isBlank(userId) || "null".equals(userId)) {
            userId = null;
        }

        MonitoringDataVO data = monitoringService.getMonitoringData(timeRange, LocalDateTime.parse(startTime, FORMATTER), LocalDateTime.parse(endTime, FORMATTER), deptId, userId);
        return R.data(data);
    }

    /**
     * 获取检客户统计数据
     *
     * @param timeRange   时间范围：day, week, month, quarter, year
     * @param deptId      部门ID，为null时查询所有中队
     * @param userId 用户ID，为null时查询所有人员
     * @return 检客户统计数据
     */
    @GetMapping("/inspection-data")
    @Operation(summary = "获取检客户统计数据", description = "获取检客户统计数据")
    public R<ChartDataVO> getInspectionData(
            @RequestParam(defaultValue = "day") String timeRange,
            @RequestParam(required = true) String startTime,
            @RequestParam(required = true) String endTime,
            @RequestParam(required = false) String deptId,
            @RequestParam(required = false) String userId) {

        if(StringUtil.isBlank(deptId) || "null".equals(deptId)) {
            deptId = null;
        }
        if(StringUtil.isBlank(userId) || "null".equals(userId)) {
            userId = null;
        }
        ChartDataVO data = monitoringService.getInspectionData(timeRange, LocalDateTime.parse(startTime, FORMATTER), LocalDateTime.parse(endTime, FORMATTER), deptId, userId);
        return R.data(data);
    }

    /**
     * 获取品规识别统计数据
     *
     * @param timeRange   时间范围：day, week, month, quarter, year
     * @param deptId      部门ID，为null时查询所有中队
     * @param userId 用户ID，为null时查询所有人员
     * @return 品规识别统计数据
     */
    @GetMapping("/recognition-data")
    @Operation(summary = "获取品规识别统计数据", description = "获取品规识别统计数据")
    public R<ChartDataVO> getRecognitionData(
            @RequestParam(defaultValue = "day") String timeRange,
            @RequestParam(required = true) String startTime,
            @RequestParam(required = true) String endTime,
            @RequestParam(required = false) String deptId,
            @RequestParam(required = false) String userId) {
        if(StringUtil.isBlank(deptId) || "null".equals(deptId)) {
            deptId = null;
        }
        if(StringUtil.isBlank(userId) || "null".equals(userId)) {
            userId = null;
        }
        ChartDataVO data = monitoringService.getRecognitionData(timeRange, LocalDateTime.parse(startTime, FORMATTER), LocalDateTime.parse(endTime, FORMATTER), deptId, userId);
        return R.data(data);
    }

    /**
     * 获取正常/异常烟统计数据
     *
     * @param timeRange   时间范围：day, week, month, quarter, year
     * @param deptId      部门ID，为null时查询所有中队
     * @param userId 用户ID，为null时查询所有人员
     * @return 正常/异常烟统计数据
     */
    @GetMapping("/cigarette-data")
    @Operation(summary = "获取正常/异常烟统计数据", description = "获取正常/异常烟统计数据")
    public R<ChartDataVO> getCigaretteData(
            @RequestParam(defaultValue = "day") String timeRange,
            @RequestParam(required = true) String startTime,
            @RequestParam(required = true) String endTime,
            @RequestParam(required = false) String deptId,
            @RequestParam(required = false) String userId) {
        if(StringUtil.isBlank(deptId) || "null".equals(deptId)) {
            deptId = null;
        }
        if(StringUtil.isBlank(userId) || "null".equals(userId)) {
            userId = null;
        }
        ChartDataVO data = monitoringService.getCigaretteData(timeRange, LocalDateTime.parse(startTime, FORMATTER), LocalDateTime.parse(endTime, FORMATTER), deptId, userId);
        return R.data(data);
    }

    /**
     * 获取人员排名数据
     *
     * @param timeRange   时间范围：day, week, month, quarter, year
     * @param deptId      部门ID，必须指定中队
     * @return 人员排名数据列表
     */
    @GetMapping("/personnel-ranking")
    @Operation(summary = "获取人员排名数据", description = "获取人员排名数据")
    public R<List<PersonnelRankingVO>> getPersonnelRanking(
            @RequestParam(defaultValue = "day") String timeRange,
            @RequestParam(required = true) String startTime,
            @RequestParam(required = true) String endTime,
            @RequestParam(required = false) String deptId) {

        if(StringUtil.isBlank(deptId) || "null".equals(deptId)) {
            deptId = null;
        }

        List<PersonnelRankingVO> data = monitoringService.getPersonnelRanking(
                timeRange,
                LocalDateTime.parse(startTime, FORMATTER),
                LocalDateTime.parse(endTime, FORMATTER),
                deptId
        );

        return R.data(data);
    }
}
