package org.springblade.ms.platform12345.utils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.ms.platform12345.config.Platform12345Properties;
import org.springframework.stereotype.Component;

import java.time.Instant;

/**
 * 12345平台客户端工具类
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class Platform12345Client {
    
    private final Platform12345Properties properties;
    
    /**
     * 生成签名
     * 
     * @return 签名信息
     */
    public SignatureInfo generateSignature() {
        // 获取当前时间戳
        String timestamp = String.valueOf(Instant.now().toEpochMilli());
        
        // 生成签名
        String signature = SignatureUtil.generateSignature(
            properties.getAccount(),
            properties.getPassword(),
            timestamp
        );
        
        return new SignatureInfo(signature, timestamp, properties.getAppid());
    }
    
    /**
     * 签名信息类
     */
    public static class SignatureInfo {
        private final String signature;
        private final String timestamp;
        private final String appid;
        
        public SignatureInfo(String signature, String timestamp, String appid) {
            this.signature = signature;
            this.timestamp = timestamp;
            this.appid = appid;
        }
        
        public String getSignature() {
            return signature;
        }
        
        public String getTimestamp() {
            return timestamp;
        }
        
        public String getAppid() {
            return appid;
        }
    }
}
