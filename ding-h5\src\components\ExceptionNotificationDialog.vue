<template>
  <van-dialog
    v-model:show="visible"
    title="异常提醒"
    width="90%"
    :show-cancel-button="false"
    :show-confirm-button="false"
    :close-on-click-overlay="false"
    class="exception-dialog"
  >
    <div class="dialog-content">
      <!-- 搜索框 -->
      <van-search
        v-model="searchKeyword"
        placeholder="输入姓名搜索通知人员"
        @input="handleSearch"
        class="search-box"
      />
      
      <!-- 已选用户显示 -->
      <div v-if="selectedUsers.length > 0" class="selected-users">
        <div class="selected-title">已选择 ({{ selectedUsers.length }})</div>
        <div class="selected-list">
          <van-tag
            v-for="user in selectedUsers"
            :key="user.id"
            closeable
            type="primary"
            @close="removeUser(user)"
            class="user-tag"
          >
            {{ user.realName }}
          </van-tag>
        </div>
      </div>
      
      <!-- 用户列表 -->
      <div class="user-list">
        <van-loading v-if="loading" class="loading" />
        <div v-else-if="filteredUsers.length === 0" class="empty-state">
          {{ searchKeyword ? '未找到匹配的用户' : '暂无可选用户' }}
        </div>
        <template v-else>
          <div class="user-list-header">
            <span class="user-count">可选用户 ({{ filteredUsers.length }})</span>
          </div>
          <van-checkbox-group v-model="selectedUserIds">
            <van-cell
              v-for="user in filteredUsers"
              :key="user.id"
              :title="user.realName"
              :label="user.deptName"
              clickable
              @click="handleCellClick(user)"
            >
              <template #right-icon>
                <van-checkbox
                  :name="user.userid"
                  :model-value="selectedUserIds.includes(user.userid)"
                  @click.stop
                />
              </template>
            </van-cell>
          </van-checkbox-group>
        </template>
      </div>
      
      <!-- 操作按钮 -->
      <div class="action-buttons">
        <van-button
          type="default"
          size="large"
          @click="handleCancel"
          class="cancel-btn"
        >
          取消
        </van-button>
        <van-button
          type="primary"
          size="large"
          @click="handleSend"
          :loading="sending"
          :disabled="selectedUsers.length === 0"
          class="send-btn"
        >
          发送通知 ({{ selectedUsers.length }})
        </van-button>
      </div>
    </div>
  </van-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { showSuccessToast, showFailToast } from 'vant'
import { http } from '@/utils/http'
import * as dingNotificationApi from '@/api/dingtalk-notification'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  exceptionData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:show', 'success'])

// 响应式数据
const visible = ref(false)
const searchKeyword = ref('')
const loading = ref(false)
const sending = ref(false)
const allUsers = ref([])
const selectedUsers = ref([])
const selectedUserIds = ref([])

// 计算属性
const filteredUsers = computed(() => {
  if (!searchKeyword.value) {
    return allUsers.value
  }
  
  const keyword = searchKeyword.value.toLowerCase()
  return allUsers.value.filter(user => 
    user.realName?.toLowerCase().includes(keyword) ||
    user.deptName?.toLowerCase().includes(keyword)
  )
})

// 监听props变化
watch(() => props.show, (newVal) => {
  visible.value = newVal
  if (newVal) {
    loadUsers()
  }
})

watch(visible, (newVal) => {
  emit('update:show', newVal)
  if (!newVal) {
    resetDialog()
  }
})

// 监听选中用户ID变化
watch(selectedUserIds, (newIds) => {
  // console.log('selectedUserIds changed:', newIds)
  selectedUsers.value = allUsers.value.filter(user => {
    // 确保类型匹配，将两者都转换为字符串进行比较，使用userid
    const included = newIds.some(id => String(id) === String(user.userid))
    // console.log(`User ${user.realName} (userid: ${user.userid}) included: ${included}`)
    return included
  })
  // console.log('selectedUsers updated:', selectedUsers.value.length)
}, { deep: true })

// 方法
const loadUsers = async () => {
  loading.value = true
  try {
    // 获取所有部门用户
    const res = await http.get('/api/dingapp/user/getLawList?userName=' + searchKeyword.value)
    if (res && res.data) {
      const totalUsers = res.data.length
      
      // 扁平化用户数据，过滤掉没有userid的用户
      const usersWithUserid = res.data.filter(user => user.userid)
      const filteredCount = totalUsers - usersWithUserid.length

      allUsers.value = usersWithUserid.map(user => ({
        id: user.id,
        userid: user.userid,
        realName: user.realName,
        deptName: user.deptName || '未知部门'
      }))

      allUsers.value.sort((a, b) => {
        if (a.deptName < b.deptName) return -1
        if (a.deptName > b.deptName) return 1
        return 0
      })

      // 如果有用户被过滤掉，显示提示信息
      if (filteredCount > 0) {
        console.log(`已过滤掉 ${filteredCount} 个没有userid的用户`)
        // 可选：显示toast提示
        // showToast(`已过滤掉 ${filteredCount} 个无效用户`)
      }
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    showFailToast('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  // 搜索逻辑已在computed中处理
}

const handleCellClick = (user) => {
  // 点击cell时切换checkbox状态
  const index = selectedUserIds.value.findIndex(id => String(id) === String(user.userid))
  if (index > -1) {
    selectedUserIds.value.splice(index, 1)
  } else {
    selectedUserIds.value.push(user.userid)
  }
}

const removeUser = (user) => {
  const index = selectedUserIds.value.findIndex(id => String(id) === String(user.userid))
  if (index > -1) {
    selectedUserIds.value.splice(index, 1)
  }
}

const handleCancel = () => {
  visible.value = false
}

const handleSend = async () => {
  if (selectedUsers.value.length === 0) {
    showFailToast('请选择要通知的用户')
    return
  }
  
  

  sending.value = true
  try {
    const params = {
      userIds: selectedUserIds.value,
      title: '品规识别异常提醒',
      messageUrl:`/lshhx?view=true&xkzh=${props.exceptionData.licData.licNo}&tab=pgsb&identifyDate=${encodeURIComponent(props.exceptionData.identifyDate || '')}`,
      text: `许可证号：${props.exceptionData.licData.licNo}用户品规识别异常，识别日期：${props.exceptionData.identifyDate || '未知'}，异常数量：${props.exceptionData.errorNum || 0}`,
    }
    
    await dingNotificationApi.sendExceptionNotification(params)
    showSuccessToast('通知发送成功')
    emit('success')
    visible.value = false
  } catch (error) {
    console.error('发送通知失败:', error)
    showFailToast('发送通知失败')
  } finally {
    sending.value = false
  }
}

const resetDialog = () => {
  searchKeyword.value = ''
  selectedUsers.value = []
  selectedUserIds.value = []
}
</script>

<style lang="scss" scoped>
.exception-dialog {
  :deep(.van-dialog__content) {
    padding: 0;
    max-height: 70vh;
    overflow: hidden;
  }
}

.dialog-content {
  display: flex;
  flex-direction: column;
  height: 70vh;
  
  .search-box {
    flex-shrink: 0;
    padding: 16px;
    border-bottom: 1px solid #eee;
  }
  
  .selected-users {
    flex-shrink: 0;
    padding: 12px 16px;
    background-color: #f7f8fa;
    border-bottom: 1px solid #eee;
    
    .selected-title {
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
    }
    
    .selected-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      
      .user-tag {
        margin: 0;
      }
    }
  }
  
  .user-list {
    flex: 1;
    overflow-y: auto;

    .loading {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100px;
    }

    .empty-state {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100px;
      color: #999;
      font-size: 14px;
    }

    .user-list-header {
      padding: 12px 16px;
      background-color: #f7f8fa;
      border-bottom: 1px solid #eee;

      .user-count {
        font-size: 14px;
        color: #666;
        font-weight: 500;
      }
    }
  }
  
  .action-buttons {
    flex-shrink: 0;
    display: flex;
    gap: 12px;
    padding: 16px;
    border-top: 1px solid #eee;
    
    .cancel-btn,
    .send-btn {
      flex: 1;
    }
  }
}
</style>
