package org.springblade.ms.dingapp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import jakarta.servlet.http.HttpServletRequest;
import org.springblade.core.mp.support.Query;
import org.springblade.ms.itemidentify.pojo.dto.ItemIdentifyResultsDTO;
import org.springblade.ms.itemidentify.pojo.entity.ItemIdentifyResultsEntity;
import org.springblade.ms.itemidentify.pojo.vo.ItemIdentifyResultsVO;
import org.springblade.ms.itemidentify.pojo.vo.ItemIdentifyVO;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.List;

public interface IDingItemIdentifyService {

    List<ItemIdentifyResultsEntity> getDetect(MultipartFile file, Long explorationId, String type, Long uploadImgId, Long objId, String objName,
                                              Boolean onlyPingui, Boolean ocr, Integer imgSize, String customerCode, HttpServletRequest httpServletRequest);

    IPage<ItemIdentifyResultsVO> ListGroupByDate(ItemIdentifyResultsDTO dto, Query query);


    Boolean removeById(Long itemIdentifyId);
}
