/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.core.oauth2.utils;

import org.springblade.core.oauth2.exception.ExceptionCode;
import org.springblade.core.oauth2.exception.OAuth2Exception;
import org.springblade.core.oauth2.exception.UserInvalidException;

/**
 * OAuth2 异常工具类
 *
 * <AUTHOR>
 */
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Supplier;
import org.springblade.core.oauth2.exception.ClientInvalidException;
import org.springblade.core.oauth2.exception.ClientNotFoundException;
import org.springblade.core.oauth2.exception.ClientUnauthorizedException;
import org.springblade.core.oauth2.exception.ExceptionCode;
import org.springblade.core.oauth2.exception.GranterInvalidException;
import org.springblade.core.oauth2.exception.OAuth2Exception;
import org.springblade.core.oauth2.exception.UserInvalidException;
import org.springblade.core.oauth2.exception.UserUnauthorizedException;
import org.springblade.core.oauth2.exception.UsernameNotFoundException;

public class OAuth2ExceptionUtil {
    private static final Map<ExceptionCode, Supplier<OAuth2Exception>> OAUTH2_EXCEPTION = new ConcurrentHashMap(16);

    public OAuth2ExceptionUtil() {
    }

    public static void throwFromCode(int code) {
        Supplier<OAuth2Exception> exceptionSupplier = (Supplier)OAUTH2_EXCEPTION.get(ExceptionCode.of(code));
        if (exceptionSupplier != null) {
            throw (OAuth2Exception)exceptionSupplier.get();
        } else {
            throw new UserInvalidException(ExceptionCode.INVALID_USER.getMessage(), new Throwable());
        }
    }

    static {
        OAUTH2_EXCEPTION.put(ExceptionCode.INVALID_REQUEST, () -> {
            return new OAuth2Exception(ExceptionCode.INVALID_REQUEST, ExceptionCode.INVALID_REQUEST.getMessage(), new Throwable());
        });
        OAUTH2_EXCEPTION.put(ExceptionCode.USER_NOT_FOUND, () -> {
            return new UsernameNotFoundException(ExceptionCode.USER_NOT_FOUND.getMessage(), new Throwable());
        });
        OAUTH2_EXCEPTION.put(ExceptionCode.USER_TENANT_NOT_FOUND, () -> {
            return new UserInvalidException(ExceptionCode.USER_TENANT_NOT_FOUND.getMessage(), new Throwable());
        });
        OAUTH2_EXCEPTION.put(ExceptionCode.USER_TOO_MANY_FAILS, () -> {
            return new UserInvalidException(ExceptionCode.USER_TOO_MANY_FAILS.getMessage(), new Throwable());
        });
        OAUTH2_EXCEPTION.put(ExceptionCode.INVALID_USER, () -> {
            return new UserInvalidException(ExceptionCode.INVALID_USER.getMessage(), new Throwable());
        });
        OAUTH2_EXCEPTION.put(ExceptionCode.UNAUTHORIZED_USER, () -> {
            return new UserUnauthorizedException(ExceptionCode.UNAUTHORIZED_USER.getMessage(), new Throwable());
        });
        OAUTH2_EXCEPTION.put(ExceptionCode.UNAUTHORIZED_USER_TENANT, () -> {
            return new UserUnauthorizedException(ExceptionCode.UNAUTHORIZED_USER_TENANT.getMessage(), new Throwable());
        });
        OAUTH2_EXCEPTION.put(ExceptionCode.INVALID_REFRESH_TOKEN, () -> {
            return new GranterInvalidException(ExceptionCode.INVALID_REFRESH_TOKEN.getMessage(), new Throwable());
        });
        OAUTH2_EXCEPTION.put(ExceptionCode.CLIENT_NOT_FOUND, () -> {
            return new ClientNotFoundException(ExceptionCode.CLIENT_NOT_FOUND.getMessage(), new Throwable());
        });
        OAUTH2_EXCEPTION.put(ExceptionCode.INVALID_CLIENT, () -> {
            return new ClientInvalidException(ExceptionCode.INVALID_CLIENT.getMessage(), new Throwable());
        });
        OAUTH2_EXCEPTION.put(ExceptionCode.INVALID_CLIENT_REDIRECT_URI, () -> {
            return new ClientInvalidException(ExceptionCode.INVALID_CLIENT_REDIRECT_URI.getMessage(), new Throwable());
        });
        OAUTH2_EXCEPTION.put(ExceptionCode.UNAUTHORIZED_CLIENT, () -> {
            return new ClientUnauthorizedException(ExceptionCode.UNAUTHORIZED_CLIENT.getMessage(), new Throwable());
        });
        OAUTH2_EXCEPTION.put(ExceptionCode.UNSUPPORTED_GRANT_TYPE, () -> {
            return new OAuth2Exception(ExceptionCode.UNSUPPORTED_GRANT_TYPE, ExceptionCode.UNSUPPORTED_GRANT_TYPE.getMessage(), new Throwable());
        });
        OAUTH2_EXCEPTION.put(ExceptionCode.INVALID_GRANTER, () -> {
            return new GranterInvalidException(ExceptionCode.INVALID_GRANTER.getMessage(), new Throwable());
        });
        OAUTH2_EXCEPTION.put(ExceptionCode.INVALID_SCOPE, () -> {
            return new OAuth2Exception(ExceptionCode.INVALID_SCOPE, ExceptionCode.INVALID_SCOPE.getMessage(), new Throwable());
        });
        OAUTH2_EXCEPTION.put(ExceptionCode.SERVER_ERROR, () -> {
            return new OAuth2Exception(ExceptionCode.SERVER_ERROR, ExceptionCode.SERVER_ERROR.getMessage(), new Throwable());
        });
        OAUTH2_EXCEPTION.put(ExceptionCode.ACCESS_DENIED, () -> {
            return new OAuth2Exception(ExceptionCode.ACCESS_DENIED, ExceptionCode.ACCESS_DENIED.getMessage(), new Throwable());
        });
        OAUTH2_EXCEPTION.put(ExceptionCode.TEMPORARILY_UNAVAILABLE, () -> {
            return new OAuth2Exception(ExceptionCode.TEMPORARILY_UNAVAILABLE, ExceptionCode.TEMPORARILY_UNAVAILABLE.getMessage(), new Throwable());
        });
        OAUTH2_EXCEPTION.put(ExceptionCode.PASSWORD_EXPIRED, () -> {
            return new OAuth2Exception(ExceptionCode.PASSWORD_EXPIRED, ExceptionCode.PASSWORD_EXPIRED.getMessage(), new Throwable());
        });
    }
}
