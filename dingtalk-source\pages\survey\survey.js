import httpApi from "/utils/http/httpApi";
import { config } from "process";
import {options, env} from '/utils/http/config';
import {Base64} from '/utils/base64.js';
import {setting} from "/common/setting";
const app = getApp();

Page({
  data: {
    licNo:'',
    licId:'',
    checked:false,
    address:"",
    fileListOne:[],
    fileListTwo:[],
    fileListThree:[],
    toastShow:false,
    isView:false, //仅查看
    explorationId:'',
    companyName:"",
    yanguiColumn: [
      {
          title: '序号',
          dataIndex: 'order',
          key: 'order',
          width: 150,
          fixed: true,
      },
      {
          title: '姓名',
          dataIndex: 'name',
          key: 'name',
          width: 150,
          fixed: true,
          ellipsisRow: 2,
      },
    ],
    yanjiaColumn: [
      {
          title: '序号',
          dataIndex: 'order',
          key: 'order',
          width: 150,
          fixed: true,
      },
      {
          title: '姓名',
          dataIndex: 'name',
          key: 'name',
          width: 150,
          fixed: true,
          ellipsisRow: 2,
      },
    ],
  },
  async onLoad(options) {
    dd.removeStorage({key:'licData'});
    dd.setNavigationBar({
      title: '勘查',
    });
    if(options && options.xkzh){
      this.setData({
        licNo: options.xkzh
      })
    }
    const res = await httpApi.request({
      url: '/api/dingapp/exploration/getTodayExploration?licNo='+ options.xkzh,
      method: 'get',
    })
    if(Object.keys(res.data).length !== 0){
      let dingMapLicenseVO = res.data.dingMapLicenseVO
      this.setData({
        "companyName":dingMapLicenseVO.companyName,
        "address":dingMapLicenseVO.businessAddr,
        "explorationId":res.data.explorationVO.id,
        licId:dingMapLicenseVO.id
      })

      let jsonStr = JSON.stringify({explorationId:res.data.explorationVO.id,licNo:this.data.licNo,licId:this.data.licId})
      // console.log(jsonStr,"jsonStr");
      dd.setStorage({
        key:"licData",data:jsonStr
      })

      let resFileList = res.data.dingMapLicenseVO.photoPathList
      if(resFileList.length>0){
        let newFileList = resFileList.map(file => ({
          uid: file.id, // 生成唯一的 uid
          status: 'done', 
          url: file.filthPath, 
          fileName:file.fileName,
        }));
        newFileList.forEach(item => {
          // console.log(item)
          if(item.fileName.includes("left")){
            this.setData({fileListOne:[item]})
            console.log(this.data.fileListOne)
          }else if(item.fileName.includes("center")){
            this.setData({fileListTwo:[item]})
          }else if(item.fileName.includes("right")){
            this.setData({fileListThree:[item]})
          }
        });

      }
    }else{
      dd.alert({
        content: '服务器异常',
        buttonText: '确定'
      });
    }    
    
  },
  handleCheckedChange(checked){
    let num = this.data.fileListOne.length+this.data.fileListTwo.length+this.data.fileListThree.length
    if(num == 3){
      this.setData({checked})
    }else{
      this.setData({
        toastShow:true
      })
    }
  },
  onUploadChange(fileList){
    // console.log('图片列表：', fileList);
  },
  async handleRemoveOne(file){
    const res1 = await httpApi.request({
      url: `/api/dingapp/file/remove`,
      method: 'get',
      params:{
        ids:file.uid,
      }
    })
    if(res1.data==true){
      this.setData({
        fileListOne: [],
      });
      dd.showToast({
        type: 'success',
        content: '删除成功',
        duration: 2000,
      });
    }else{
      dd.showToast({
        type: 'fail',
        content: '删除失败',
        duration: 2000,
      });
    }
  },
  async handleRemoveTwo(file){
    const res1 = await httpApi.request({
      url: `/api/dingapp/file/remove`,
      method: 'get',
      params:{
        ids:file.uid,
      }
    })
    if(res1.data==true){
      this.setData({
        fileListTwo: [],
      });
      dd.showToast({
        type: 'success',
        content: '删除成功',
        duration: 2000,
      });
    }else{
      dd.showToast({
        type: 'fail',
        content: '删除失败',
        duration: 2000,
      });
    }
  },
  async handleRemoveThree(file){
    const res1 = await httpApi.request({
      url: `/api/dingapp/file/remove`,
      method: 'get',
      params:{
        ids:file.uid,
      }
    })
    if(res1.data==true){
      this.setData({
        fileListThree: [],
      });
      dd.showToast({
        type: 'success',
        content: '删除成功',
        duration: 2000,
      });
    }else{
      dd.showToast({
        type: 'fail',
        content: '删除失败',
        duration: 2000,
      });
    }
  },
  onUploadOne(filePath) {
    return new Promise((resolve, reject) => {
      dd.uploadFile({
        url: `${options.baseURL}/dingapp/file/upload?objId=${this.data.explorationId}&objName=exploration&extName=_left`, 
        fileType: 'image',
        name: 'file', // 根据后台服务需求替换
        header:{
          "Blade-Requested-With":"BladeHttpRequest",
          "Blade-Auth":'bearer ' + app.globalData.accessToken,
          "Authorization":Base64.encode(setting.clientId + ':' + setting.clientSecret)
        },
        filePath: filePath.path, // 传入 localFile.path

        success: res => {
           this.uploadLocation();
          // 根据后台返回，得到上传成功的图片 url
          
          let data = JSON.parse(res.data);
          let url = data.data.path
          resolve(url); // 调用 resolve 传入图片 url


          // 创建新的文件对象
          let newFile = {
            uid: data.data.id,
            status: 'done', 
            url: url, 
          };

          // 将新文件追加到 fileList 中
          this.setData({
            fileListOne: [ newFile],

          });
        },
        fail: err => {
          reject(); // 上传错误调用 reject
        },
      });
    });

  },
  onUploadTwo(filePath) {
    return new Promise((resolve, reject) => {
      dd.uploadFile({
        url: `${options.baseURL}/dingapp/file/upload?objId=${this.data.explorationId}&objName=exploration&extName=_center`, 
        fileType: 'image',
        name: 'file', // 根据后台服务需求替换
        header:{
          "Blade-Requested-With":"BladeHttpRequest",
          "Blade-Auth":'bearer ' + app.globalData.accessToken,
          "Authorization":Base64.encode(setting.clientId + ':' + setting.clientSecret)
        },
        filePath: filePath.path, // 传入 localFile.path
        success: res => {
           this.uploadLocation();
          // 根据后台返回，得到上传成功的图片 url
          
          let data = JSON.parse(res.data);
          let url = data.data.path
          resolve(url); // 调用 resolve 传入图片 url


          // 创建新的文件对象
          let newFile = {
            uid: data.data.id,
            status: 'done', 
            url: url, 
          };

          // 将新文件追加到 fileList 中
          this.setData({
            fileListTwo: [ newFile],

          });
        },
        fail: err => {
          reject(); // 上传错误调用 reject
        },
      });
    });

  },
  onUploadThree(filePath) {
    return new Promise((resolve, reject) => {
      dd.uploadFile({
        url: `${options.baseURL}/dingapp/file/upload?objId=${this.data.explorationId}&objName=exploration&extName=_right`, 
        fileType: 'image',
        name: 'file', // 根据后台服务需求替换
        header:{
          "Blade-Requested-With":"BladeHttpRequest",
          "Blade-Auth":'bearer ' + app.globalData.accessToken,
          "Authorization":Base64.encode(setting.clientId + ':' + setting.clientSecret)
        },
        filePath: filePath.path, // 传入 localFile.path
        // formData: { 
        //   objId: 'test012', // 替换为实际勘查记录ID
        //   objName: 'exploration',
        //   extName: '', // 可选参数
        // }, // 根据后台服务需求替换
        success: res => {
           this.uploadLocation();
          // 根据后台返回，得到上传成功的图片 url
          
          let data = JSON.parse(res.data);
          console.log(data)
          let url = data.data.path
          resolve(url); // 调用 resolve 传入图片 url


          // 创建新的文件对象
          let newFile = {
            uid: data.data.id,
            status: 'done', 
            url: url, 
          };

          // 将新文件追加到 fileList 中
          this.setData({
            fileListThree: [ newFile],
          });
        },
        fail: err => {
          reject(); // 上传错误调用 reject
        },
      });
    });

  },
  async handleNextClick(){
    // console.log(this.data.fileListTwo)
    let res = await httpApi.request({
      url: '/api/dingapp/exploration/submitCenterPhoto',
      method: 'POST',
      data:{
        "explorationId": this.data.explorationId,
        "licenseId": this.data.licNo,
        fileId:this.data.fileListTwo[0].uid
      }
    })
    if(res.success){
      dd.navigateTo({
        url: '/pages/lshhx/index?view=false'
      });
    }
  },
  handleCloseToast(){
    this.setData({
      toastShow:false
    })
  },
  uploadLocation(){
    dd.getLocation({
      type: 1,
      useCache: false,
      coordinate: '1',
      cacheTimeout: 1,
      withReGeocode: true,
      targetAccuracy: '100',
      success: (res) => {
        httpApi.request({
          url: '/api/dingapp/exploration/submitExplorationCoordinate',
          method: 'POST',
          data:{
            "explorationId": this.data.explorationId,
            "licenseId": this.data.licNo,
            "longitude": res.longitude,
            "latitude": res.latitude
          }
        })
      },
      fail: (err) => {
      }
    })
  },
  onChooseImageError(err){
    console.log(err)
  },
});
