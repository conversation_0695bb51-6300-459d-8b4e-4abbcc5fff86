package org.springblade.ms.dingapp.service;

import org.springblade.core.tool.api.R;

import java.util.List;

/**
 * 钉钉工作通知服务接口
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
public interface IDingNotificationService {

    /**
     * 发送工作通知消息
     *
     * @param userIds 接收者的用户ID列表，最大列表长度：100
     * @param title 消息标题
     * @param content 消息内容
     * @return 发送结果
     */
    R<String> sendTextNotification(List<String> userIds, String title, String content);

    /**
     * 发送工作通知消息给部门
     *
     * @param deptIds 接收者的部门ID列表，最大列表长度：20
     * @param title 消息标题
     * @param content 消息内容
     * @return 发送结果
     */
    R<String> sendTextNotificationToDept(List<String> deptIds, String title, String content);

    /**
     * 发送链接类型工作通知
     *
     * @param userIds 接收者的用户ID列表，最大列表长度：100
     * @param title 消息标题
     * @param text 消息描述
     * @param messageUrl 点击消息跳转的URL
     * @param picUrl 图片URL
     * @return 发送结果
     */
    R<String> sendLinkNotification(List<String> userIds, String title, String text, String messageUrl, String picUrl);

    /**
     * 发送Markdown类型工作通知
     *
     * @param userIds 接收者的用户ID列表，最大列表长度：100
     * @param title 消息标题
     * @param markdownText Markdown格式的消息内容
     * @return 发送结果
     */
    R<String> sendMarkdownNotification(List<String> userIds, String title, String markdownText);

    /**
     * 查询工作通知消息的发送进度
     *
     * @param taskId 发送任务ID
     * @return 发送进度
     */
    R<Object> getNotificationProgress(Long taskId);

    /**
     * 查询工作通知消息的发送结果
     *
     * @param taskId 发送任务ID
     * @return 发送结果
     */
    R<Object> getNotificationResult(Long taskId);

    /**
     * 撤回工作通知消息
     *
     * @param taskId 发送任务ID
     * @return 撤回结果
     */
    R<Boolean> recallNotification(Long taskId);

    /**
     * 获取部门列表
     *
     * @return 部门列表
     */
    R<Object> getDepartmentList();

    /**
     * 获取指定部门的用户ID列表
     *
     * @param deptId 部门ID
     * @return 用户ID列表
     */
    R<Object> getDepartmentUserIds(String deptId);

    /**
     * 获取所有部门的用户ID列表
     *
     * @return 所有部门用户ID列表
     */
    R<Object> getAllDepartmentUserIds();

    /**
     * 根据用户ID获取用户详细信息
     *
     * @param userId 用户ID
     * @return 用户详细信息
     */
    R<Object> getUserDetail(String userId);

    /**
     * 批量获取用户详细信息
     *
     * @param userIds 用户ID列表
     * @return 用户详细信息列表
     */
    R<Object> getBatchUserDetails(List<String> userIds);

    /**
     * 获取所有用户ID并批量获取详细信息
     *
     * @return 所有用户详细信息
     */
    R<Object> getAllUsersWithDetails();

    /**
     * 从指定部门ID开始递归查找包含"湛江"的部门
     *
     * @param startDeptId 起始部门ID，默认为641568739
     * @return 包含"湛江"的部门列表
     */
    R<Object> getZhanjiangDepartments(String startDeptId);

    /**
     * 获取指定部门列表的所有用户详细信息（硬编码部门ID）
     *
     * @return 指定部门的所有用户详细信息
     */
    R<Object> getSpecificDepartmentsUsers();

    /**
     * 获取指定部门列表的所有用户详细信息（前端传参）
     *
     * @param deptIds 部门ID列表
     * @return 指定部门的所有用户详细信息
     */
    R<Object> getSpecificDepartmentsUsers(List<String> deptIds);

    /**
     * 获取目标部门及其所有下级部门的用户详细信息（前端传参）
     *
     * @param deptIds 部门ID列表
     * @return 目标部门及下级部门的所有用户详细信息
     */
    R<Object> getTargetDepartmentsWithSubUsersDetails(List<String> deptIds);
}
