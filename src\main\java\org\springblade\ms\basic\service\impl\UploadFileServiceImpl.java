package org.springblade.ms.basic.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.log.StaticLog;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.oss.MinioTemplate;
import org.springblade.core.oss.model.BladeFile;
import org.springblade.ms.basic.mapper.UploadFileMapper;
import org.springblade.ms.basic.pojo.entity.RetailerStorePhotoEntity;
import org.springblade.ms.basic.pojo.entity.UploadFileEntity;
import org.springblade.ms.basic.pojo.entity.YhytLicenseEntity;
import org.springblade.ms.basic.service.IRetailerStorePhotoService;
import org.springblade.ms.basic.service.IUploadFileService;
import org.springblade.ms.basic.service.IYhytLicenseService;
import org.springblade.ms.dingapp.vo.DingMinioResult;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-02-05 15:51
 */
@Service
@AllArgsConstructor
public class UploadFileServiceImpl extends BaseServiceImpl<UploadFileMapper, UploadFileEntity> implements IUploadFileService {

    private final MinioTemplate minioTemplate;

    private final IYhytLicenseService yhytLicenseService;
    private final IRetailerStorePhotoService retailerStorePhotoService;

    @Override
    public List<UploadFileEntity> selectFileList(Long objId) {
        LambdaQueryWrapper<UploadFileEntity> queryWrapper = Wrappers.<UploadFileEntity>lambdaQuery()
                .eq(UploadFileEntity::getObjId, objId)
                .eq(UploadFileEntity::getIsDeleted, 0)
                .orderByDesc(UploadFileEntity::getCreateTime);

        return super.list(queryWrapper);
    }

    @Override
    public JSONObject dingUploadFile(Long objId, String objName, MultipartFile file, HttpServletRequest httpServletRequest, String extName) {
        if (StrUtil.isBlank(objName)) {
            return null;
        }

        JSONObject res = JSONUtil.createObj();
        res.put("code", 0);
        // 判断上传文件是否为空
        if (null == file || 0 == file.getSize()) {
            res.put("msg", "上传文件不能为空");
            return res;
        }

        try {
            if (StrUtil.isBlank(extName)) extName = "";

            String fileName = extName +  "_" + getFileName(file.getOriginalFilename());;

            UploadFileEntity uploadFileEntity = new UploadFileEntity();
            uploadFileEntity.setObjId(objId);
            uploadFileEntity.setObjName(objName);
            uploadFileEntity.setFileName(fileName);
            uploadFileEntity.setFileSize(BigDecimal.valueOf(file.getSize() / 1024));

            BladeFile bladeFile = minioTemplate.putFile(objName + "/" + fileName, file.getInputStream());
            if (bladeFile != null) {
                String serverURL = getServerUrl(httpServletRequest);
                String path = serverURL + "/" + minioTemplate.getBucket().name() + "/" + bladeFile.getName();
                uploadFileEntity.setFilthPath(path);
            }

            super.save(uploadFileEntity);

            res.put("code", 200);
            DingMinioResult minioResult = new DingMinioResult();
            minioResult.setFilename(fileName);
            minioResult.setPath(uploadFileEntity.getFilthPath());
            minioResult.setId(String.valueOf(uploadFileEntity.getId()));
            res.put("data", minioResult);
            return res;
        } catch (Exception e) {
            StaticLog.error("上传文件失败：{}", e.getMessage());
        }
        res.put("msg", "上传失败");
        return res;
    }

    @Override
    public List<UploadFileEntity> selectCenterPhotoFileList(Long objId) {
        //先从ms_retailer_store_photo从获取门面照片
        if(ObjUtil.isNotNull(objId)){
            QueryWrapper<RetailerStorePhotoEntity> qw1 = new QueryWrapper<>();
            qw1.eq("license_id",objId)
                    .eq("is_deleted",0)
                    .orderByDesc("create_time");
            List<RetailerStorePhotoEntity> list = retailerStorePhotoService.list(qw1);
            if(!list.isEmpty()){
                return Collections.singletonList(super.getById(list.get(0).getFileId()));
            }
        }

        LambdaQueryWrapper<UploadFileEntity> queryWrapper = Wrappers.<UploadFileEntity>lambdaQuery()
                .eq(UploadFileEntity::getObjId, objId)
                .eq(UploadFileEntity::getIsDeleted, 0)
                .eq(UploadFileEntity::getObjName,"license")
                .apply("file_name LIKE '_center%'")
                .or()
                .apply("file_name NOT LIKE '\\_%'")
                .eq(UploadFileEntity::getObjId, objId)
                .eq(UploadFileEntity::getIsDeleted, 0)
                .eq(UploadFileEntity::getObjName,"license")
                .orderByDesc(UploadFileEntity::getCreateTime);

        return super.list(queryWrapper);
    }

    public List<UploadFileEntity> selectCenterPhotoFileList(List<Long> originalObjIdList) {
        if (CollectionUtils.isEmpty(originalObjIdList)) {
            return Collections.emptyList();
        }
        List<Long> objIdList = new ArrayList<>(originalObjIdList);
        List<UploadFileEntity> resultList = new ArrayList<>();
        List<Long> processedObjIds = new ArrayList<>(); // 记录已经处理过的 objId

        // 先从 ms_retailer_store_photo 获取门面照片 (每个 objId 最多取一张)
        QueryWrapper<RetailerStorePhotoEntity> qw1 = new QueryWrapper<>();
        qw1.in("license_id", objIdList)
                .eq("is_deleted", 0)
                .orderByDesc("create_time");
        List<RetailerStorePhotoEntity> retailerStorePhotos = retailerStorePhotoService.list(qw1);

        if (!CollectionUtils.isEmpty(retailerStorePhotos)) {
            // Group by license_id and take the first (most recent) for each
            Map<Long, RetailerStorePhotoEntity> latestPhotos = retailerStorePhotos.stream()
                    .collect(Collectors.groupingBy(RetailerStorePhotoEntity::getLicenseId,
                            Collectors.collectingAndThen(Collectors.toList(),
                                    list -> list.get(0))));

            List<Long> fileIds = latestPhotos.values().stream()
                    .map(RetailerStorePhotoEntity::getFileId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            //ms_retailer_store_photo指向的文件的objid并非零售户id，额外处理供外部根据objid查询
            if (!CollectionUtils.isEmpty(fileIds)) {
                List<UploadFileEntity> retailerPhotoFiles = super.listByIds(fileIds);
                // 使用 license_id 覆盖 UploadFileEntity 的 objId
                for (UploadFileEntity fileEntity : retailerPhotoFiles) {
                    for (Map.Entry<Long, RetailerStorePhotoEntity> entry : latestPhotos.entrySet()) {
                        if (entry.getValue().getFileId().equals(fileEntity.getId())) {
                            fileEntity.setObjId(entry.getKey());
                            break;
                        }
                    }
                }
                resultList.addAll(retailerPhotoFiles); // 只添加修改后的列表
                objIdList.removeAll(latestPhotos.keySet());
                processedObjIds.addAll(latestPhotos.keySet());
            }
        }

        // 筛选出尚未处理的 objId
        List<Long> remainingObjIds = objIdList.stream()
                .filter(objId -> !processedObjIds.contains(objId))
                .collect(Collectors.toList());

        // 查询 ms_upload_file 中符合条件的中心照片 (针对剩余的 objId)
        if (!CollectionUtils.isEmpty(remainingObjIds)) {
            LambdaQueryWrapper<UploadFileEntity> queryWrapper = Wrappers.<UploadFileEntity>lambdaQuery()
                    .in(UploadFileEntity::getObjId, remainingObjIds)
                    .eq(UploadFileEntity::getIsDeleted, 0)
                    .eq(UploadFileEntity::getObjName,"license")
                    .and(qw -> qw.apply("file_name LIKE '_center%'").or().apply("file_name NOT LIKE '\\_%'"))
                    .orderByDesc(UploadFileEntity::getCreateTime);

            resultList.addAll(super.list(queryWrapper));
        }

        return resultList;
    }


    @Override
    public List<UploadFileEntity> selectSurveyFileList(Long objId) {
        List<UploadFileEntity> list = new ArrayList<>();
        LambdaQueryWrapper<UploadFileEntity> queryWrapper2 = Wrappers.<UploadFileEntity>lambdaQuery()
                .eq(UploadFileEntity::getObjId, objId)
                .eq(UploadFileEntity::getIsDeleted, 0)
                .apply("file_name LIKE '_left%'")
                .orderByDesc(UploadFileEntity::getCreateTime);
        List<UploadFileEntity> two = super.list(queryWrapper2);
        if(!two.isEmpty()){
            list.add(two.get(0));
        }
        LambdaQueryWrapper<UploadFileEntity> queryWrapper = Wrappers.<UploadFileEntity>lambdaQuery()
                .eq(UploadFileEntity::getObjId, objId)
                .eq(UploadFileEntity::getIsDeleted, 0)
                .apply("file_name LIKE '_center%'")
                .or()
                .apply("file_name NOT LIKE '_%'")
                .orderByDesc(UploadFileEntity::getCreateTime);
        List<UploadFileEntity> one = super.list(queryWrapper);
        if(!one.isEmpty()){
            list.add(one.get(0));
        }

        LambdaQueryWrapper<UploadFileEntity> queryWrapper3 = Wrappers.<UploadFileEntity>lambdaQuery()
                .eq(UploadFileEntity::getObjId, objId)
                .eq(UploadFileEntity::getIsDeleted, 0)
                .apply("file_name LIKE '_right%'")
                .orderByDesc(UploadFileEntity::getCreateTime);
        List<UploadFileEntity> three = super.list(queryWrapper3);
        if(!three.isEmpty()){
            list.add(three.get(0));
        }
        return list;
    }

    public UploadFileEntity uploadBase64ImgFileEntity(String img, Long objId, String objName, HttpServletRequest httpServletRequest){
        // 解码 Base64 字符串
        byte[] imageBytes = Base64.getDecoder().decode(img);
        BigDecimal fileSize = BigDecimal.valueOf(imageBytes.length / 1024.0);

        try {
            // 处理文件名
            String finalFileName = "_" + getFileName(".png");
            UploadFileEntity uploadFileEntity = new UploadFileEntity();
            uploadFileEntity.setFileName(finalFileName);
            uploadFileEntity.setFileSize(fileSize);
            uploadFileEntity.setObjId(objId);
            uploadFileEntity.setObjName(objName);
            // 上传文件到 MinIO
            BladeFile bladeFile = minioTemplate.putFile(objName + "/" + finalFileName, new ByteArrayInputStream(imageBytes));
            if (bladeFile != null) {
                String serverURL = getServerUrl(httpServletRequest);
                String path = serverURL + "/" + minioTemplate.getBucket().name() + "/" + bladeFile.getName();
                uploadFileEntity.setFilthPath(path);
            }
            super.save(uploadFileEntity);
            return uploadFileEntity;
        } catch (Exception e) {
            StaticLog.error("上传文件失败：{}", e.getMessage());
        }
        return null;
    }


    private String getFileName(String fileName) {
        //去除特殊字符
        String regEx = "[`~!@#$%^&*()+=|{}':;',\\[\\]<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？\\s]";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(fileName);
        fileName = getUniqueFileName(m.replaceAll(""));
        return fileName;
    }

    /**
     * 给文件名添加 UUID, 保留原后缀并保证文件名不大于255个单字节字符.
     */
    private String getUniqueFileName(String fileName) {
        fileName = fileName.replaceAll("[+#\"]", "_");
        int extensionIndex = fileName.lastIndexOf(".");
        String name = extensionIndex < 0 ? fileName : fileName.substring(0, extensionIndex);
        String extension = extensionIndex < 0 ? "" : fileName.substring(extensionIndex);
        int extensionLen = extension.length();
        if (extensionLen > ((255 - (32 + 1))) / 2) {
            extension = "";
            extensionLen = 0;
        }
        int maxLength = (255 - (32 + 1 + extensionLen)) / 2;
        if (name.length() > maxLength) {
            name = name.substring(0, maxLength);
        }
        return /*name + "_" +*/ IdUtil.fastSimpleUUID() + extension;
    }

    public String getServerUrl(HttpServletRequest httpServletRequest) {
        // TODO 写死的代码，需要修改
        //服务器地址
//        String serverURL = "https://tcinspect.foshantc.com/webfile";
//        String serverURL = "/minio";
        // 转换访问的服务器地址
//        String referer = httpServletRequest.getHeader("referer");
//        if (!referer.contains("10.0.10") && !referer.contains("172.19.13") && !referer.contains("127.0.0.1")) {
//            return serverURL;
//        }

//        String serverName = referer;
//        String contextPath = "minio";
//        if (referer.contains("127.0.0.1") || referer.contains("localhost")) {
//            serverName = "http://127.0.0.1:9000";
//            contextPath = "";
//        }
//
//        StringBuilder domain = new StringBuilder();
//        domain.append(serverName);
//        domain.append(contextPath);
//        String val = domain.toString().replace("form-generator/", "");
        return "/minio";
    }

}
