package org.springblade.ms.dingapp.vo;

import lombok.Data;

import java.util.List;

/**
 * 钉钉部门信息VO
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Data
public class DingDepartmentVO {

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 部门名称
     */
    private String name;

    /**
     * 父部门ID
     */
    private Long parentId;

    /**
     * 是否同步创建一个关联此部门的企业群
     */
    private Boolean createDeptGroup;

    /**
     * 部门群已经创建后，有新人加入部门是否会自动加入该群
     */
    private Boolean autoAddUser;

    /**
     * 部门的主管列表
     */
    private List<String> deptManagerUseridList;

    /**
     * 部门的主管类型
     */
    private String deptManagerType;

    /**
     * 部门标识字段
     */
    private String sourceIdentifier;

    /**
     * 部门排序
     */
    private Long order;

    /**
     * 是否限制本部门成员查看通讯录
     */
    private Boolean deptHiding;

    /**
     * 可以查看指定隐藏部门的其他部门列表
     */
    private List<Long> deptPermits;

    /**
     * 可以查看指定隐藏部门的其他人员列表
     */
    private List<String> userPermits;

    /**
     * 是否本部门的员工仅可见员工自己
     */
    private Boolean outerDept;

    /**
     * 本部门的员工仅可见员工自己为true时，可以配置额外可见的其他人员
     */
    private List<String> outerPermitUsers;

    /**
     * 本部门的员工仅可见员工自己为true时，可以配置额外可见的其他部门
     */
    private List<Long> outerPermitDepts;

    /**
     * 企业群群主
     */
    private String orgDeptOwner;

    /**
     * 部门群ID
     */
    private String deptGroupChatId;

    /**
     * 部门是否来自关联组织
     */
    private Boolean fromUnionOrg;

    /**
     * 教育部门标签
     */
    private String tags;

    /**
     * 部门绑定的主管userid
     */
    private List<String> adminList;

    /**
     * 部门绑定的主管类型
     */
    private String groupContainSubDept;

    /**
     * 部门绑定的主管类型
     */
    private Boolean groupContainOuterDept;

    /**
     * 部门绑定的主管类型
     */
    private Boolean groupContainHiddenDept;

    /**
     * 部门绑定的主管类型
     */
    private String brief;

    /**
     * 部门绑定的主管类型
     */
    private String code;

    /**
     * 部门绑定的主管类型
     */
    private Long expand;

    /**
     * 部门绑定的主管类型
     */
    private String telephone;

    /**
     * 部门绑定的主管类型
     */
    private String email;

    /**
     * 部门绑定的主管类型
     */
    private String facsimile;

    /**
     * 部门绑定的主管类型
     */
    private String address;
}
