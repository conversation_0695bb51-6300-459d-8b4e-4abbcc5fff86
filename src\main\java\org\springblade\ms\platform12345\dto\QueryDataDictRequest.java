package org.springblade.ms.platform12345.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 查询数据字典请求参数
 */
@Data
@Schema(description = "查询数据字典请求参数")
public class QueryDataDictRequest {
    
    @Schema(description = "加密校验串")
    private String signature;
    
    @Schema(description = "时间戳")
    private String timestamp;
    
    @Schema(description = "应用ID")
    private String appid;
    
    @Schema(description = "数据字典类型")
    private String paramTypeCode;
    
    @Schema(description = "数据字典编码，可选")
    private String paramCode;
}
