package org.springblade.ms.dingapp.controller;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.ms.basic.pojo.entity.RetailerStorePhotoEntity;
import org.springblade.ms.basic.pojo.entity.YhytLicenseEntity;
import org.springblade.ms.basic.service.IRetailerStorePhotoService;
import org.springblade.ms.basic.service.IYhytLicenseService;
import org.springblade.ms.dingapp.service.IDingExplorationService;
import org.springblade.ms.dingapp.vo.DingExplorationVO;
import org.springblade.ms.dingapp.vo.ExplorationHistoryVO;
import org.springblade.ms.exploration.pojo.entity.ExplorationCoordinateEntity;
import org.springblade.ms.exploration.pojo.entity.ExplorationEntity;
import org.springblade.ms.exploration.pojo.vo.ExplorationVO;
import org.springblade.ms.exploration.service.IExplorationCoordinateService;
import org.springblade.ms.exploration.service.IExplorationService;
import org.springblade.ms.exploration.constant.ExplorationObjectType;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 钉钉小程序-勘查记录控制器
 * <AUTHOR> [sijun.zeng]
 * @date 2025-02-12 09:42
 */
@RestController
@AllArgsConstructor
@RequestMapping("/dingapp/exploration")
public class DingExplorationController extends BladeController {

    private final IDingExplorationService dingExplorationService;

    private final IExplorationCoordinateService explorationCoordinateService;

    private final IRetailerStorePhotoService retailerStorePhotoService;

    private final IYhytLicenseService yhytLicenseService;

    private final IExplorationService explorationService;

    /**
     * 获取今日勘查记录，如无记录，则创建
     * @param licNo
     * @return
     */
    @GetMapping("/getTodayExploration")
    public R<DingExplorationVO> getTodayExploration(@RequestParam(name = "licNo") String licNo) {
        DingExplorationVO dingExplorationVO = dingExplorationService.getTodayExploration(licNo);
        return R.data(dingExplorationVO);
    }

    /**
     * 提交勘查坐标
     * @param entity
     * @return
     */
    @PostMapping("/submitExplorationCoordinate")
    public R submitExplorationCoordinate(@Valid @RequestBody ExplorationCoordinateEntity entity) {
        if (entity.getExplorationId() == null) {
            return R.fail("勘查记录ID不能为空");
        }

        if (entity.getLicenseId() == null) {
            return R.fail("零售户ID不能为空");
        }

        explorationCoordinateService.save(entity);

        return R.status(true);
    }


    /**
     * 提交勘查坐标同时更新零售户坐标
     * @param entity
     * @return
     */
    @PostMapping("/submitAndUpdateLocation")
    public R submitExplorationCoordinateAndUpdateYhyt(@Valid @RequestBody ExplorationCoordinateEntity entity) {
        if (entity.getExplorationId() == null) {
            return R.fail("勘查记录ID不能为空");
        }

        if (entity.getLicenseId() == null) {
            return R.fail("零售户ID不能为空");
        }

        explorationCoordinateService.save(entity);
        Long yhytId = entity.getLicenseId();
        YhytLicenseEntity byId = yhytLicenseService.getById(yhytId);
        byId.setLongitude(entity.getLongitude());
        byId.setLatitude(entity.getLatitude());
        yhytLicenseService.updateLocation(byId);
        return R.status(true);
    }

    /**
     * 上传门面照片信息,如果找不到今天数据则生成
     * 支持零售户、无证户、学校照片上传
     * @param entity
     * @return
     */
    @PostMapping("/submitCenterPhoto")
    public R submitCenterPhoto(@Valid @RequestBody RetailerStorePhotoEntity entity) {
        QueryWrapper<RetailerStorePhotoEntity> qw = new QueryWrapper<>();
        qw.eq("exploration_id",entity.getExplorationId());
        RetailerStorePhotoEntity one = retailerStorePhotoService.getOne(qw);
        if(ObjUtil.isNotNull(one)){
            entity.setId(one.getId());
        }else{
            entity.setCreateDate(LocalDate.now());

            // 根据勘查记录设置照片对象类型
            if (entity.getExplorationId() != null) {
                ExplorationEntity exploration = explorationService.getById(entity.getExplorationId());
                if (exploration != null && exploration.getObjectType() != null) {
                    entity.setObjectType(exploration.getObjectType());
                } else {
                    // 默认设置为零售户类型
                    entity.setObjectType(ExplorationObjectType.RETAILER);
                }
            }
        }
        boolean b = retailerStorePhotoService.saveOrUpdate(entity);
        return R.status(b);
    }

    /**
     * 查询当前用户勘查历史记录
     * @param query 分页参数
     * @return 分页结果
     */
    @GetMapping("/history")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "查询当前用户勘查历史记录", description = "分页查询当前用户的勘查历史记录")
    public R<IPage<ExplorationHistoryVO>> history(Query query) {
        // 获取当前用户ID
        Long userId = AuthUtil.getUserId();

        // 创建查询条件
        QueryWrapper<ExplorationEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("create_user", userId);
        queryWrapper.orderByDesc("create_time");

        // 查询分页数据
        IPage<ExplorationEntity> pages = explorationService.page(Condition.getPage(query), queryWrapper);

        // 收集所有需要查询的零售户ID
        List<Long> licenseIds = pages.getRecords().stream()
            .map(ExplorationEntity::getLicenseId)
            .filter(id -> id != null)
            .distinct()
            .collect(Collectors.toList());

        // 一次性查询所有零售户信息
        Map<Long, YhytLicenseEntity> retailerMap = new HashMap<>();
        if (!licenseIds.isEmpty()) {
            List<YhytLicenseEntity> retailers = yhytLicenseService.listByIds(licenseIds);
            retailers.forEach(retailer -> retailerMap.put(retailer.getId(), retailer));
        }

        // 转换为VO，并设置零售户信息
        IPage<ExplorationHistoryVO> result = pages.convert(exploration -> {
            ExplorationHistoryVO vo = new ExplorationHistoryVO();
            vo.setId(exploration.getId());
            vo.setLicenseId(exploration.getLicenseId());
            vo.setExplorationDate(exploration.getExplorationDate());
            vo.setCreateType(exploration.getCreateType());
            vo.setLongitude(exploration.getLongitude());
            vo.setLatitude(exploration.getLatitude());
            vo.setCreateTime(exploration.getCreateTime());
            vo.setCreateUser(exploration.getCreateUser());

            // 从Map中获取零售户信息
            if (exploration.getLicenseId() != null) {
                YhytLicenseEntity retailer = retailerMap.get(exploration.getLicenseId());
                if (retailer != null) {
                    vo.setRetailerName(retailer.getCompanyName());
                    vo.setRetailerAddress(retailer.getBusinessAddr());
                    vo.setRetailerLicNo(retailer.getLicNo());

                }
            }

            return vo;
        });

        return R.data(result);
    }

    /**
     * 查询勘查记录详情
     * @param id 勘查记录ID
     * @return 勘查记录详情
     */
    @GetMapping("/detail/{id}")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "查询勘查记录详情", description = "根据ID查询勘查记录详情")
    public R<ExplorationHistoryVO> detail(@PathVariable("id") Long id) {
        // 查询勘查记录
        ExplorationEntity exploration = explorationService.getById(id);
        if (exploration == null) {
            return R.fail("勘查记录不存在");
        }

        // 创建详情VO
        ExplorationHistoryVO detailVO = new ExplorationHistoryVO();
        // 复制基本信息
        detailVO.setId(exploration.getId());
        detailVO.setLicenseId(exploration.getLicenseId());
        detailVO.setExplorationDate(exploration.getExplorationDate());
        detailVO.setCreateType(exploration.getCreateType());
        detailVO.setLongitude(exploration.getLongitude());
        detailVO.setLatitude(exploration.getLatitude());
        detailVO.setCreateTime(exploration.getCreateTime());
        detailVO.setCreateUser(exploration.getCreateUser());

        // 查询零售户信息
        if (exploration.getLicenseId() != null) {
            YhytLicenseEntity retailer = yhytLicenseService.getById(exploration.getLicenseId());
            if (retailer != null) {
                detailVO.setRetailerName(retailer.getCompanyName());
                detailVO.setRetailerAddress(retailer.getBusinessAddr());
                detailVO.setRetailerLicNo(retailer.getLicNo());
            }
        }

        return R.data(detailVO);
    }
}
