<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.ms.exploration.mapper.ExplorationCoordinateMapper">

    <resultMap id="explorationCoordinateResultMap" type="org.springblade.ms.exploration.pojo.entity.ExplorationCoordinateEntity">
        <result column="id" property="id"/>
        <result column="exploration_id" property="explorationId"/>
        <result column="license_id" property="licenseId"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <resultMap id="explorationCoordinateVOResultMap" type="org.springblade.ms.exploration.pojo.vo.ExplorationCoordinateVO" extends="explorationCoordinateResultMap"/>

</mapper>
