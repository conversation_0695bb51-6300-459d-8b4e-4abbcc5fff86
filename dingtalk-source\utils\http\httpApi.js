import {setting} from "/common/setting";
import {options, env} from '/utils/http/config';
import {Base64} from '/utils/base64.js';
import Request from '/utils/luch-request/index.js';

const http = new Request(options);
const app = getApp();

http.interceptors.request.use((config) => { // 可使用async await 做异步操作
    config.headers = config.headers ? config.headers : {};
    config.headers['Blade-Requested-With'] = 'BladeHttpRequest';
    // 假设有token值需要在头部需要携带
    let accessToken = app.globalData.accessToken;
    if (accessToken) {
        config.headers['Blade-Auth'] = 'bearer ' + accessToken;
    }
    // 客户端认证参数
    config.headers['Authorization'] = 'Basic ' + Base64.encode(setting.clientId + ':' + setting.clientSecret);

    if (config.url) {
        let url = config.url;
        if (env === "dev" && !url.startsWith("http")) {
            url = url.substring(url.indexOf('/api') + 4)
            config.url = url
        }
    }

    if (config.method === 'POST') {
        config.data = JSON.stringify(config.data)
    }

    return config
}, config => { // 可使用async await 做异步操作
    return Promise.reject(config)
})
http.interceptors.response.use((response) => {
    // 若有数据返回则通过
    if (response.config.method === "DOWNLOAD") {
        return response;
    }
    if (response.data.access_token || response.data.key) {
        return response.data
    }
    // 服务端返回的状态码不等于200，则reject()
    if (response.data.code !== 200) {
        if (response.data.msg) {
            dd.showToast({
                type: 'fail',
                content: response.data.msg,
                duration: 2000
            })
        }
        return Promise.reject(response);
    }
    return response.data;
}, (response) => {
    /*  对响应错误做点什么 （statusCode !== 200）*/
    if (response.data) {
        dd.showToast({
            type: 'fail',
            content: response.data,
            duration: 2000
        })
    }
    if (response.statusCode == 401) {
        // TODO
    }
    return Promise.reject(response)
})
export default http;
