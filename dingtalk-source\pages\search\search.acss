.mainApp {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 97vh;
    background-color: #FFFFFF;
    overflow: hidden;
}

.searchPanel {
    display: flex;
    flex-direction: column;
    padding: 20px 7px 0 7px;
}

.searchBox {
    display: flex;
    height: 40px;
    width: 100%;
    justify-content: center;
}

.searchBox .search-bar {
    border-top-left-radius: 15px;
    border-bottom-left-radius: 15px;
    width: 100%;
    padding-left: 4px;
    margin-left: 9px;
}

.searchBox .search-bar input {
    font-size: 16px;
}

.searchPlaceholderCls {
    font-size: 13px;
    letter-spacing: 1px;
}

.searchBox .searchInpBtn {
    width: 60px;
    height: 40px;
    line-height: 40px;
    border-radius: 0px 10px 10px 0px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    color: #FFFFFF;
    background-color: #4285F4;
    font-size: 16px;
    margin-right: 9px;
}

.formatParamBox {
    width: 100%;
    height: 46px;
    display: flex;
    justify-content: flex-start;
    padding-left: 16px;
}

.formatParamBtn {
    position: fixed;
    right: 0;
    width: 100px;
    height: 36px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    border-radius: 8px;
    box-shadow: 0px 1px 2px rgba(0, 0, 0, .1);
    background-color: #fff;
    overflow: hidden;
    margin-right: 16px;
    padding: 0 10px;
    margin-top: 5px;
}

.userLocationContent {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
    color: #9295A0;
    font-size: 14px;
    margin: 5px 0;
    padding-left: 16px;
}

.searchFormList {
    width: 100%;
    padding: 0 40rpx;
    box-sizing: border-box;
}

.searchForm {
    width: 100%;
    padding: 5rpx 0 5rpx;
    border-bottom: 1rpx solid #F2F3F7;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5rpx;
}

.searchForm:last-child {
    border: none;
}

.searchFormBox {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding-top: 12px;
}

.searchFormBox .text {
    flex-grow: 1;
    color: #9295A0;
}

.searchFormBox .image {
    flex-shrink: 0;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 55px;
    padding-right: 8px;
}

.vistaImage {
    width: 54px;
    height: 51px;
    border-radius: 5px;
}

.detailAddress {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    line-height: 18px;
    font-size: 14px;
}

.detailAddressText {
    flex-grow: 1;
}

.detailLicNo {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    line-height: 14px;
    font-size: 14px;
    margin-top: 8px;
}

.detailLicNoText {
    flex-grow: 1;
}

.detailDistance {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    line-height: 14px;
    font-size: 14px;
    margin-top: 8px;
}

.detailDistanceText {
    flex-grow: 1;
}

.searchFormBtn {
    flex-shrink: 0;
}

.retailers {
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 65px;
    padding-left: 8px;
}

.retailersImage {
    width: 32px;
    height: 32px;
}

.retailersText {
    color: #9295A0;
    font-size: 11px;
    font-weight: 400;
    line-height: 15px;
    margin-top: 5px;
    width: 35px;
    text-align: center;
}

.scrollToast {
    text-align: center;
    padding: 70rpx 0 50rpx;
    color: #9295A0;
    font-size: 25rpx;
}

.hintText {
    text-align: center;
    font-weight: 500;
    color: #f55c21;
    font-size: 28rpx;
    padding: 200rpx 32rpx 0;
}