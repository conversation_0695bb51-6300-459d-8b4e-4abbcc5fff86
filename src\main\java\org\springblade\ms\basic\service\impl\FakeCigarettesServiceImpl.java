/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.ms.basic.service.impl;

import org.springblade.ms.basic.pojo.entity.FakeCigarettesEntity;
import org.springblade.ms.basic.pojo.vo.FakeCigarettesVO;
import org.springblade.ms.basic.excel.FakeCigarettesExcel;
import org.springblade.ms.basic.mapper.FakeCigarettesMapper;
import org.springblade.ms.basic.service.IFakeCigarettesService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 查获假烟统计表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-20
 */
@Service
public class FakeCigarettesServiceImpl extends BaseServiceImpl<FakeCigarettesMapper, FakeCigarettesEntity> implements IFakeCigarettesService {

	@Override
	public IPage<FakeCigarettesVO> selectFakeCigarettesPage(IPage<FakeCigarettesVO> page, FakeCigarettesVO fakeCigarettes) {
		return page.setRecords(baseMapper.selectFakeCigarettesPage(page, fakeCigarettes));
	}


	@Override
	public List<FakeCigarettesExcel> exportFakeCigarettes(Wrapper<FakeCigarettesEntity> queryWrapper) {
		List<FakeCigarettesExcel> fakeCigarettesList = baseMapper.exportFakeCigarettes(queryWrapper);
		//fakeCigarettesList.forEach(fakeCigarettes -> {
		//	fakeCigarettes.setTypeName(DictCache.getValue(DictEnum.YES_NO, FakeCigarettes.getType()));
		//});
		return fakeCigarettesList;
	}

}
