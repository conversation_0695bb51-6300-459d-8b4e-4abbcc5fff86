import httpApi from "/utils/http/httpApi";
import {start$ as ddStartLocation} from "dingtalk-jsapi/api/device/geolocation/start"
import {stop$ as ddStopLocation} from "dingtalk-jsapi/api/device/geolocation/stop"
import {status$ as ddStatusLocation} from "dingtalk-jsapi/api/device/geolocation/status"
import { env } from "/utils/http/config"
import {calculateDistance} from "/utils/distanceUtil";
Page({
    data: {
        sceneId: '0001',
        currentLon: null,
        currentLat: null,
        originalLon: null, // 原始经度
        originalLat: null, // 原始纬度
        currentAddress: '',
        mapMarkers: [],
        mapCircles: [],
        mapControls: [],
        mapIncludePointList: [],
        mapGroundOverlays: [],
        // 这个列表是地图的零售户列表，根据当前位置获取
        mapLicenseList: [],
        mapCtx: null,

        // 是否显示合理化布局
        isShowRationalize: false,

        isShowDetailBox: false,
        currentLicense: null,
        currentMarkersIdx: -1,

        formatParamList: [],
        formatParamValue: '',
        isHaveTargetLocation:false,
        oldTargetLocation:{},  //用来比对
    },
    onLoad(options) {
        // dd.hideTabBar()
        // console.log(dd);

        this.startRefreshLocation();
        this.getFormatParamStorage();
        this.loadGroupFormatList();
    },
    onShow() {
        //通过判断oldTargetLocation是否相同来改变打点范围
        let targetLocation = JSON.parse(dd.getStorageSync({
            key: 'targetLocation',
        }).data)
        
        if(!this.shallowEqual(targetLocation,this.data.oldTargetLocation) && targetLocation){
            // console.log('变了')
            this.setData({
                isHaveTargetLocation:true,
                oldTargetLocation:targetLocation
            })
            this.setData({
                currentLon: targetLocation.longitude,
                currentLat: targetLocation.latitude,
            })
            this.loadMapLicenseList();
            if(targetLocation.markerId){
                this.handleMapMarkerTap(targetLocation);
            }
        }else{
            this.setData({
                isHaveTargetLocation:false,
            })
        }
        
    },
    onReady() {
        this.setData({
            mapCtx: dd.createMapContext('map')
        })
        this.loadMapLicenseList();
    },
    onUnload() {
        this.stopRefreshLocation();
    },
    async startRefreshLocation() {

        /**
         * 注意，开发者工具环境下，无法调用以下方法，需要调用普通的一次性获取位置
         */

        if (env === 'dev') {
            dd.getLocation({
                type: 1,
                useCache: false,
                coordinate: '1',
                cacheTimeout: 1,
                withReGeocode: true,
                targetAccuracy: '100',
                success: (res) => {
                    this.handleReadLocationSuccess(res);
                },
                fail: (err) => {
                    this.handleReadLocationFail(err);
                }
            })

            return
        }

        if (await this.checkRefreshLocationStatus()) {
            return;
        }

        ddStartLocation({
            type: 1,
            useCache: false,
            coordinate: '1',
            cacheTimeout: 1,
            withReGeocode: true,
            targetAccuracy: '100',
            iOSDistanceFilter: 5,
            sceneId: '0001',
            success: (res) => {
                this.handleReadLocationSuccess(res);
            },
            fail: (err) => {
                this.handleReadLocationFail(err);
            }
        })

    },
    stopRefreshLocation() {
        if (!this.checkRefreshLocationStatus() || env === 'dev') {
            return;
        }

        ddStopLocation({
            sceneId: this.data.sceneId
        })

    },

    /**
     * true 表示正在定位
     */
    async checkRefreshLocationStatus() {
        let res = await ddStatusLocation({
            sceneId: [this.data.sceneId]
        });

        return res[0] === 1
    },
    handleReadLocationSuccess(res) {
        this.setData({
            currentLon: res.longitude,
            currentLat: res.latitude,
            originalLon: res.longitude,
            originalLat: res.latitude,
            currentAddress: res.address
        })
        // console.log(res);
    },
    handleReadLocationFail(err) {
        console.log(err);
    },

    async loadMapLicenseList(longitude,latitude) {
        // 添加参数等待，必须等到currentLon和currentLat都有数据才开始加载数据
        await new Promise((resolve) => {
            let interval = setInterval(() => {
                // console.log(this.data.currentLon && this.data.currentLat)
                if (this.data.currentLon && this.data.currentLat) {
                    clearInterval(interval);
                    resolve();
                }
            }, 1000)
        })

        let res = await httpApi.get('/api/dingapp/license/getDingMapLicenseList', {
            params: {
                searchParam: '',
                formatParam: this.data.formatParamValue,
                longitude: longitude ? longitude : this.data.currentLon,
                latitude: latitude ? latitude : this.data.currentLat
            }
        })

        // console.log(res.data)

        if (!res.data) {
            return;
        }

        this.setData({
            mapLicenseList: res.data
        })

        this.generatedMapMarkers();
    },

    //在地图上打点
    generatedMapMarkers() {
        let mapLicenseList = this.data.mapLicenseList;
        if (!mapLicenseList || mapLicenseList.length === 0) {
            return;
        }
        let mapMarkers = [];
        for (let i = 0; i < mapLicenseList.length && i < 500; i++) {
            let mapLicense = mapLicenseList[i];
            mapMarkers.push({
                id: mapLicense.id,
                latitude: mapLicense.latitude,
                longitude: mapLicense.longitude,
                iconPath: '/image/green.png',
                width: 45,
                height: 50,
            })
        }

        this.setData({
            mapMarkers: mapMarkers
        })
    },

    /**
     * 点击详情框的关闭按钮
     */
    handleCloseDetailBox() {
      this.setData({
        isShowDetailBox: false,
          isShowRationalize: false,
          currentLicense: null
      })

        this.refreshRationalizeCircles();
    },

    /**
     * 显示详情可
     * @param licenseData
     */
    handleShowDetailBox(licenseData) {
        this.setData({
            isShowDetailBox: true,
            currentLicense: licenseData
        })

        // console.log(licenseData)
        if (this.data.isShowRationalize) {
            this.refreshRationalizeCircles();
        }
    },

    /**
     * 点击地图上的零售户点
     * @param e
     */
    async handleMapMarkerTap(e) {
        console.log('handleMapMarkerTap', e)
        // 获取描述内容
        let id = e.markerId;

        let res = await httpApi.get('/api/dingapp/license/getDingMapLicense', {
            params: {
                yhytId: id,
            }
        })

        if (!res.data) {
            return;
        }

        // 计算距离，并保留整数
        let distance = calculateDistance(this.data.originalLat, this.data.originalLon, res.data.latitude, res.data.longitude)
        res.data.distance = Math.round(distance);

        this.handleShowDetailBox(res.data);

        // 在集合中搜索对应的点，定位过去，并放大图片
        this.updateFocusCurrMarker(id);
    },

    updateFocusCurrMarker(id) {
        let idx = this.data.mapMarkers.findIndex((item) => item.id === id)
        let oldIdx = this.data.currentMarkersIdx;

        // 恢复旧数据
        if (oldIdx !== -1) {
            this.setData({
                [`mapMarkers[${oldIdx}].iconPath`]: '/image/green.png',
                [`mapMarkers[${oldIdx}].width`]: 45,
                [`mapMarkers[${oldIdx}].height`]: 50,
            })
            this.data.mapCtx.changeMarkers({
                update: [
                    {
                        id: Number(this.data.mapMarkers[oldIdx].id),
                        iconPath: '/image/green.png',
                        width: 45,
                        height: 50,
                    }
                ]
            })
        }

        // 更新新数据
        if (idx !== -1) {
            this.setData({
                [`mapMarkers[${idx}].iconPath`]: '/image/green_selected.png',
                [`mapMarkers[${idx}].width`]: 65,
                [`mapMarkers[${idx}].height`]: 70,
            })
            this.data.mapCtx.changeMarkers({
                update: [
                    {
                        id: Number(id),
                        iconPath: '/image/green_selected.png',
                        width: 65,
                        height: 70,
                    }
                ]
            })
        }
    },

    handleMoveToLocation() {
        console.log(this.data.originalLon)
        this.setData({
            currentLon: this.data.originalLon,
            currentLat: this.data.originalLat,
            oldTargetLocation:{
                longitude:this.data.originalLon,
                latitude:this.data.originalLat,
            }
        });
        if (dd.canIUse('createMapContext')) {
            setTimeout(() => {

                this.data.mapCtx.moveToLocation();

                this.loadMapLicenseList(this.data.originalLon,this.data.originalLat)
            },100);
        }
    },

    /**
     * 合理化布局按钮
     */
    handleShowRationalize() {
        let isShow = !this.data.isShowRationalize;

        if (isShow && !this.data.currentLicense) {
            return;
        }

        this.setData({
            isShowRationalize: isShow
        })

        this.refreshRationalizeCircles();
    },

    /**
     * 刷新合理化布局的圆圈
     */
    refreshRationalizeCircles() {

        if (!this.data.isShowRationalize) {
            this.setData({
                mapCircles: []
            })
            return;
        }

        // 创建圆形
        let circlesArray = []
        circlesArray.push( {
            latitude: this.data.currentLicense.latitude,
            longitude: this.data.currentLicense.longitude,
            color: '#FFE800',
            fillColor: '#FFE80066',
            radius: 50,
            strokeWidth: 1,
        }, {
            latitude: this.data.currentLicense.latitude,
            longitude: this.data.currentLicense.longitude,
            color: '#FF0000',
            fillColor: '#FF000099',
            radius: 30,
            strokeWidth: 1,
        })

        this.setData({
            mapCircles: circlesArray
        })
    },


    /**
     * 处理扫码按钮
     */
    handleSearchBarScan() {
        dd.scan({
            type: 'qr',
            success: (res) => {
                console.log(res,'扫码')
                const  text  = res.code;
                if (!text) {
                    dd.alert({
                        content: '未识别到二维码',
                        buttonText: '确定'
                    });
                }

                // 处理扫码结果
                const licenseNumberMatch = text.match(/许可证号:(\d+)/);
                if (licenseNumberMatch) {
                    const licenseNumber = licenseNumberMatch[1];
                    // 跳转到搜索页面
                    this.gotoSearchResultPage(licenseNumber)
                } else {
                    dd.alert({
                        content: '未识别许可证号',
                        buttonText: '确定'
                    });
                }
            }
        })
    },

    handleSearchBtnClick() {
        this.gotoSearchResultPage('')
    },

    handleSearchInputClick() {
        this.gotoSearchResultPage('')
    },

    gotoSearchResultPage(licenseNumber) {
        dd.navigateTo({
            url: `/pages/search/search?searchParam=${licenseNumber}&currentAddress=${this.data.currentAddress}&currentLat=${this.data.originalLat}&currentLon=${this.data.originalLon}`
        })
    },

    handleFormatParamPickerOK(value) {
        let oldFormatParamValue = this.data.formatParamValue;
        if (oldFormatParamValue === value) {
            return;
        }

        this.setData({
            formatParamValue: value
        })
        this.setFormatParamStorage(value)
        this.loadMapLicenseList();
    },

    /**
     * 获取零售户业态列表
     */
    async loadGroupFormatList() {
        let res = await httpApi.get('/api/dingapp/license/getFormatList');

        if (res.data) {
            this.setData({
                formatParamList: res.data
            })

            if (!this.data.formatParamValue || Object.keys(this.data.formatParamValue).length === 0) {
                this.setData({
                    formatParamValue: res.data[0]
                })

                this.setFormatParamStorage(res.data[0]);
            }
        }
    },

    setFormatParamStorage(val) {
        dd.setStorage({
            key: 'formatParamValue',
            data: val
        })
    },

    getFormatParamStorage() {
        const res = dd.getStorageSync({
            key: 'formatParamValue',
        })

        console.log('getFormatParamStorage',res);
        this.setData({
            formatParamValue: res.data
        })
    },

    handleOpenLicenseDetailPage(e) {

    },

    toLshhx(e){
        const licData = e.target.dataset.licData 
        let jsonStr = JSON.stringify({licNo:licData.licNo,licId:licData.id})
        // console.log(jsonStr,"jsonStr");
        dd.setStorage({
            key:"licData",data:jsonStr
        })
        dd.navigateTo({
            url: `/pages/lshhx/index?view=true&licNo=${licData.licNo}`
        });
        
    },
    shallowEqual(obj1, obj2) {
        // 检查引用
        if (obj1 === obj2) {
          return true;
        }
      
        // 检查类型
        if (typeof obj1 !== 'object' || obj1 === null || typeof obj2 !== 'object' || obj2 === null) {
          return false;
        }
      
        // 检查属性数量
        const keys1 = Object.keys(obj1);
        const keys2 = Object.keys(obj2);
        if (keys1.length !== keys2.length) {
          return false;
        }
      
        // 逐个比较属性值
        for (let key of keys1) {
          if (obj1[key] !== obj2[key]) {
            return false;
          }
        }
      
        return true;
      }
});
