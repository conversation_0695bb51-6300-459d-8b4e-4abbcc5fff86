/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.core.oauth2.exception;

import lombok.Generated;

/**
 * 异常代码
 *
 * <AUTHOR>
 */
public enum ExceptionCode implements Oauth2ExceptionCode{

	INVALID_REQUEST(2000, "无效请求"),
	USER_NOT_FOUND(2001, "用户不存在"),
	USER_TENANT_NOT_FOUND(2002, "用户租户不存在"),
	USER_TOO_MANY_FAILS(2003, "用户登录失败次数过多"),
	INVALID_USER(2004, "认证信息错误或无效"),
	INVALID_USER_PASSWORD(2004, "用户密码强度过低"),
	UNAUTHORIZED_USER(2005, "认证信息错误或无效"),
	UNAUTHORIZED_USER_TENANT(2006, "用户租户未授权"),
	INVALID_REFRESH_TOKEN(2010, "令牌刷新错误或无效"),
	CLIENT_NOT_FOUND(3000, "客户端不存在"),
	INVALID_CLIENT(3001, "客户端认证失败"),
	INVALID_CLIENT_REDIRECT_URI(3002, "客户端未授权"),
	UNAUTHORIZED_CLIENT(3003, "客户端未授权"),
	UNSUPPORTED_GRANT_TYPE(4000, "不支持的授权类型"),
	INVALID_GRANTER(4001, "无效的授权类型"),
	INVALID_SCOPE(4002, "授权范围"),
	SERVER_ERROR(5000, "服务器错误"),
	ACCESS_DENIED(5001, "访问被拒绝"),
	TEMPORARILY_UNAVAILABLE(5002, "服务暂不可用"),
	PASSWORD_EXPIRED(2025, "密码已过期，请联系管理员重置密码");

	final int code;
	final String message;

	public static ExceptionCode of(int code) {
		ExceptionCode[] var1 = values();
		int var2 = var1.length;

		for(int var3 = 0; var3 < var2; ++var3) {
			ExceptionCode value = var1[var3];
			if (value.code == code) {
				return value;
			}
		}

		throw new IllegalArgumentException(String.format("无效的错误代码: %s", code));
	}

	@Generated
	public int getCode() {
		return this.code;
	}

	@Generated
	public String getMessage() {
		return this.message;
	}

	@Generated
	private ExceptionCode(final int code, final String message) {
		this.code = code;
		this.message = message;
	}
}
