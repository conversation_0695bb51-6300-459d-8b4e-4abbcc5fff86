package org.springblade.ms.reportcomplaint.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springblade.ms.reportcomplaint.pojo.entity.ReportIllegalLabelEntity;
import org.springblade.ms.reportcomplaint.pojo.vo.ReportIllegalLabelVO;

import java.util.List;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-01-16 23:36
 */
public interface ReportIllegalLabelMapper extends BaseMapper<ReportIllegalLabelEntity> {

    List<ReportIllegalLabelVO> selectList(@Param("param") ReportIllegalLabelEntity param);

    List<ReportIllegalLabelVO> listLegalPageBycustCode(IPage page,@Param("yhytId") String yhytId);

    Long getIllegalRecordsLastYearCount(Long yhytId);

    List<ReportIllegalLabelVO> listByLabelId(@Param("labelId") Long labelId);


}
