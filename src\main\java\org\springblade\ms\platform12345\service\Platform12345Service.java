package org.springblade.ms.platform12345.service;

import org.springblade.ms.platform12345.dto.AcceptOrderRequest;
import org.springblade.ms.platform12345.dto.ApplyDelayRequest;
import org.springblade.ms.platform12345.dto.CompleteOrderRequest;
import org.springblade.ms.platform12345.dto.DealOrderRequest;
import org.springblade.ms.platform12345.dto.QueryAssistOrderRequest;
import org.springblade.ms.platform12345.dto.QueryDataDictRequest;
import org.springblade.ms.platform12345.dto.QueryMajorOrderRequest;
import org.springblade.ms.platform12345.dto.QueryReCheckResultRequest;
import org.springblade.ms.platform12345.dto.QueryShareOrderRequest;
import org.springblade.ms.platform12345.dto.QuerySuperviseOrderRequest;
import org.springblade.ms.platform12345.dto.QueryCurStateRequest;

/**
 * 12345平台服务接口
 */
public interface Platform12345Service {

    /**
     * 查询主办工单
     *
     * @param request 请求参数
     * @return 工单数据
     */
    String queryMajorOrder(QueryMajorOrderRequest request);

    /**
     * 查询数据字典
     *
     * @param request 请求参数
     * @return 数据字典数据
     */
    String queryDataDict(QueryDataDictRequest request);

    /**
     * 查询共享工单
     *
     * @param request 请求参数
     * @return 共享工单数据
     */
    String queryShareOrder(QueryShareOrderRequest request);

    /**
     * 处理反馈工单
     *
     * @param request 请求参数
     * @return 处理结果
     */
    String dealOrder(DealOrderRequest request);

    /**
     * 申请延期
     *
     * @param request 请求参数
     * @return 处理结果
     */
    String applyDelay(ApplyDelayRequest request);

    /**
     * 提交受理
     *
     * @param request 请求参数
     * @return 处理结果
     */
    String acceptOrder(AcceptOrderRequest request);

    /**
     * 工单处理完成
     *
     * @param request 请求参数
     * @return 处理结果
     */
    String completeOrder(CompleteOrderRequest request);

    /**
     * 查询工单复核结果
     *
     * @param request 请求参数
     * @return 复核结果数据
     */
    String queryReCheckResult(QueryReCheckResultRequest request);

    /**
     * 查询协办工单
     *
     * @param request 请求参数
     * @return 协办工单数据
     */
    String queryAssistOrder(QueryAssistOrderRequest request);

    /**
     * 查询督办工单
     *
     * @param request 请求参数
     * @return 督办工单数据
     */
    String querySuperviseOrder(QuerySuperviseOrderRequest request);

    /**
     * 查询工单满意度
     *
     * @param request 查询工单满意度请求
     * @return 查询结果
     */
    String queryCurState(QueryCurStateRequest request);
}
