package org.springblade.ms.dingapp.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springblade.ms.dingapp.entity.MsDingUserEntity;
import org.springblade.ms.dingapp.mapper.MsDingUserMapper;
import org.springblade.ms.dingapp.service.IMsDingUserService;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 钉钉用户信息 服务实现类
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Slf4j
@Service
public class MsDingUserServiceImpl extends ServiceImpl<MsDingUserMapper, MsDingUserEntity> implements IMsDingUserService {

    @Override
    public boolean saveOrUpdateBatch(List<Map<String, Object>> userInfoList) {
        if (CollUtil.isEmpty(userInfoList)) {
            return true;
        }

        // 收集所有的userid
        List<String> userIds = userInfoList.stream()
                .map(userInfo -> (String) userInfo.get("userid"))
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(userIds)) {
            return true;
        }

        // 批量查询已存在的用户
        Map<String, MsDingUserEntity> existingUserMap = getByUserids(userIds);

        List<MsDingUserEntity> entityList = new ArrayList<>();

        for (Map<String, Object> userInfo : userInfoList) {
            try {
                String userid = (String) userInfo.get("userid");
                if (StrUtil.isBlank(userid)) {
                    continue;
                }

                // 从Map中获取已存在的用户
                MsDingUserEntity existingUser = existingUserMap.get(userid);
                MsDingUserEntity entity;

                if (existingUser != null) {
                    // 更新现有记录
                    entity = existingUser;
                    entity.setUpdateTime(LocalDateTime.now());
                } else {
                    // 创建新记录
                    entity = new MsDingUserEntity();
                    entity.setUserid(userid);
                    entity.setCreateTime(LocalDateTime.now());
                    entity.setStatus(1);
                }

                // 设置用户信息
                entity.setName((String) userInfo.get("name"));
                entity.setMobile((String) userInfo.get("mobile"));
                entity.setTitle((String) userInfo.get("title"));

                // 处理部门ID列表
                Object deptIdListObj = userInfo.get("deptIdList");
                if (deptIdListObj != null) {
                    entity.setDeptIdList(JSONUtil.toJsonStr(deptIdListObj));
                }

                // 处理部门名称列表
                Object deptNamesObj = userInfo.get("deptNames");
                if (deptNamesObj != null) {
                    entity.setDeptNames(JSONUtil.toJsonStr(deptNamesObj));
                }

                // 处理父级部门ID列表
                Object parentDeptIdsObj = userInfo.get("parentDeptIds");
                if (parentDeptIdsObj != null) {
                    entity.setParentDeptIds(JSONUtil.toJsonStr(parentDeptIdsObj));
                }

                entityList.add(entity);

            } catch (Exception e) {
                log.error("处理用户信息异常: {}", userInfo, e);
            }
        }

        if (CollUtil.isNotEmpty(entityList)) {
            return this.saveOrUpdateBatch(entityList);
        }

        return true;
    }

    @Override
    public MsDingUserEntity getByUserid(String userid) {
        if (StrUtil.isBlank(userid)) {
            return null;
        }

        LambdaQueryWrapper<MsDingUserEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MsDingUserEntity::getUserid, userid)
                   .eq(MsDingUserEntity::getStatus, 1);

        return this.getOne(queryWrapper);
    }

    /**
     * 批量根据钉钉用户ID查询用户信息
     *
     * @param userIds 钉钉用户ID列表
     * @return 用户信息Map，key为userid，value为用户实体
     */
    public Map<String, MsDingUserEntity> getByUserids(List<String> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return new HashMap<>();
        }

        LambdaQueryWrapper<MsDingUserEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MsDingUserEntity::getUserid, userIds)
                   .eq(MsDingUserEntity::getStatus, 1);

        List<MsDingUserEntity> userList = this.list(queryWrapper);

        return userList.stream()
                .collect(Collectors.toMap(MsDingUserEntity::getUserid, user -> user));
    }

}
