package org.springblade.ms.dingapp.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.cache.utils.CacheUtil;
import org.springblade.core.launch.constant.AppConstant;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.StringPool;
import org.springblade.modules.system.pojo.entity.DictBiz;
import org.springblade.modules.system.service.IDictBizService;
import org.springblade.modules.system.pojo.vo.DictBizVO;
import org.springblade.modules.system.wrapper.DictBizWrapper;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

import static org.springblade.core.cache.constant.CacheConstant.DICT_CACHE;

/**
 * 控制器
 *
 * <AUTHOR>
 */
@NonDS
@RestController
@AllArgsConstructor
@RequestMapping("/dingapp/dict-biz")
@Tag(name = "业务字典", description = "业务字典")
public class DingDictBizController extends BladeController {

    private final IDictBizService dictService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description = "传入dict")
    public R<DictBizVO> detail(DictBiz dict) {
        DictBiz detail = dictService.getOne(Condition.getQueryWrapper(dict));
        return R.data(DictBizWrapper.build().entityVO(detail));
    }

    /**
     * 列表
     */
    @GetMapping("/list")
    @Parameters({
            @Parameter(name = "code", description = "字典编号", in = ParameterIn.QUERY, schema = @Schema(type = "string")),
            @Parameter(name = "dictValue", description = "字典名称", in = ParameterIn.QUERY, schema = @Schema(type = "string"))
    })
    @ApiOperationSupport(order = 2)
    @Operation(summary = "列表", description = "传入dict")
    public R<List<DictBizVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> dict) {
        List<DictBiz> list = dictService.list(Condition.getQueryWrapper(dict, DictBiz.class).lambda().orderByAsc(DictBiz::getSort));
        return R.data(DictBizWrapper.build().listNodeVO(list));
    }

    /**
     * 顶级列表
     */
    @GetMapping("/parent-list")
    @Parameters({
            @Parameter(name = "code", description = "字典编号", in = ParameterIn.QUERY, schema = @Schema(type = "string")),
            @Parameter(name = "dictValue", description = "字典名称", in = ParameterIn.QUERY, schema = @Schema(type = "string"))
    })
    @ApiOperationSupport(order = 3)
    @Operation(summary = "列表", description = "传入dict")
    public R<IPage<DictBizVO>> parentList(@Parameter(hidden = true) @RequestParam Map<String, Object> dict, Query query) {
        return R.data(dictService.parentList(dict, query));
    }

    /**
     * 子列表
     */
    @GetMapping("/child-list")
    @Parameters({
            @Parameter(name = "code", description = "字典编号", in = ParameterIn.QUERY, schema = @Schema(type = "string")),
            @Parameter(name = "dictValue", description = "字典名称", in = ParameterIn.QUERY, schema = @Schema(type = "string")),
            @Parameter(name = "parentId", description = "字典名称", in = ParameterIn.QUERY, schema = @Schema(type = "string"))
    })
    @ApiOperationSupport(order = 4)
    @Operation(summary = "列表", description = "传入dict")
    public R<List<DictBizVO>> childList(@Parameter(hidden = true) @RequestParam Map<String, Object> dict, @RequestParam(required = false, defaultValue = "-1") Long parentId) {
        return R.data(dictService.childList(dict, parentId));
    }

    /**
     * 获取字典树形结构
     */
    @GetMapping("/tree")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "树形结构", description = "树形结构")
    public R<List<DictBizVO>> tree() {
        List<DictBizVO> tree = dictService.tree();
        return R.data(tree);
    }

    /**
     * 获取字典树形结构
     */
    @GetMapping("/parent-tree")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "树形结构", description = "树形结构")
    public R<List<DictBizVO>> parentTree() {
        List<DictBizVO> tree = dictService.parentTree();
        return R.data(tree);
    }

    /**
     * 新增或修改
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "新增或修改", description = "传入dict")
    public R submit(@Valid @RequestBody DictBiz dict) {
        CacheUtil.clear(DICT_CACHE);
        return R.status(dictService.submit(dict));
    }


    /**
     * 删除
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "删除", description = "传入ids")
    public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
        CacheUtil.clear(DICT_CACHE);
        return R.status(dictService.removeDict(ids));
    }

    /**
     * 获取字典
     */
    @GetMapping("/dictionary")
    @ApiOperationSupport(order = 8)
    @Operation(summary = "获取字典", description = "获取字典")
    public R<List<DictBiz>> dictionary(String code) {
        List<DictBiz> tree = dictService.getList(code);
        return R.data(tree);
    }

    /**
     * 获取字典树
     */
    @GetMapping("/dictionary-tree")
    @ApiOperationSupport(order = 9)
    @Operation(summary = "获取字典树", description = "获取字典树")
    public R<List<DictBizVO>> dictionaryTree(String code) {
        List<DictBiz> tree = dictService.getList(code);
        return R.data(DictBizWrapper.build().listNodeVO(tree));
    }

    /**
     * 字典键值列表
     */
    @GetMapping("/select")
    @ApiOperationSupport(order = 10)
    @Operation(summary = "字典键值列表", description = "字典键值列表")
    public R<List<DictBiz>> select() {
        List<DictBiz> list = dictService.list(Wrappers.<DictBiz>query().lambda().eq(DictBiz::getParentId, CommonConstant.TOP_PARENT_ID));
        list.forEach(dict -> dict.setDictValue(dict.getCode() + StringPool.COLON + StringPool.SPACE + dict.getDictValue()));
        return R.data(list);
    }

    /**
     * 字典全列表
     */
    @GetMapping("/select-all")
    @ApiOperationSupport(order = 11)
    @Operation(summary = "字典全列表", description = "字典全列表")
    public R<List<DictBiz>> selectAll() {
        List<DictBiz> list = dictService.list(Wrappers.<DictBiz>query().lambda().eq(DictBiz::getIsDeleted, BladeConstant.DB_NOT_DELETED));
        return R.data(list);
    }


}
