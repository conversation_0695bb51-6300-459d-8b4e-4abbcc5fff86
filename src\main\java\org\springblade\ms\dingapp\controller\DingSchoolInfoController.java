package org.springblade.ms.dingapp.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.ms.dingapp.vo.DingSchoolInfoVO;
import org.springblade.ms.schoolInfo.pojo.entity.MsSchoolInfo;
import org.springblade.ms.schoolInfo.service.MsSchoolInfoService;
import org.springblade.ms.basic.service.IRetailerStorePhotoService;
import org.springblade.ms.basic.service.IUploadFileService;
import org.springblade.ms.basic.pojo.entity.RetailerStorePhotoEntity;
import org.springblade.ms.basic.pojo.entity.UploadFileEntity;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;
import java.util.Collections;

/**
 * 钉钉小程序-学校信息控制器
 *
 * <AUTHOR> @date 2025-03-20
 */
@RestController
@AllArgsConstructor
@RequestMapping("/dingapp/schoolInfo")
@Tag(name = "钉钉-学校信息", description = "钉钉-学校信息接口")
public class DingSchoolInfoController extends BladeController {

    private final MsSchoolInfoService schoolInfoService;
    private final IRetailerStorePhotoService retailerStorePhotoService;
    private final IUploadFileService uploadFileService;

    /**
     * 获取附近的学校信息
     *
     * @param longitude   经度
     * @param latitude    纬度
     * @param radius      半径（米），默认1000米
     * @param searchParam 搜索参数（学校名称）
     * @return 学校信息列表，按距离排序
     */
    @GetMapping("/nearby")
    @Operation(summary = "查询附近学校", description = "传入经纬度和半径")
    public R<List<DingSchoolInfoVO>> getNearbySchools(
            @RequestParam(required = false) BigDecimal longitude,
            @RequestParam(required = false) BigDecimal latitude,
            @RequestParam(required = false) Double radius,
            @RequestParam(required = false) String searchParam) {

        // 获取原始学校信息
        List<MsSchoolInfo> schoolList = schoolInfoService.getNearbySchools(longitude, latitude, radius, searchParam);

        // 转换为DingSchoolInfoVO，处理坐标转换和图片获取
        List<DingSchoolInfoVO> result = schoolList.stream()
                .map(school -> {
                    DingSchoolInfoVO vo = new DingSchoolInfoVO(school);
                    // 获取学校图片
                    List<UploadFileEntity> photoPathList = getSchoolPhotos(school.getId());
                    vo.setPhotoPathList(photoPathList);
                    return vo;
                })
                .collect(Collectors.toList());

        return R.data(result);
    }

    /**
     * 获取学校图片
     *
     * @param schoolId 学校ID
     * @return 图片列表
     */
    private List<UploadFileEntity> getSchoolPhotos(Long schoolId) {
        // 在ms_retailer_store_photo表中查找license_id=学校id且object_type='SCHOOL'的最新未删除数据
        QueryWrapper<RetailerStorePhotoEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("license_id", schoolId)
                .eq("object_type", "学校")
                .eq("is_deleted", 0)
                .orderByDesc("create_time")
                .last("LIMIT 1"); // 只取最新的一条记录

        List<RetailerStorePhotoEntity> photoEntities = retailerStorePhotoService.list(queryWrapper);

        if (photoEntities.isEmpty()) {
            return Collections.emptyList();
        }

        // 根据file_id去ms_upload_file表获取图片信息
        Long fileId = photoEntities.get(0).getFileId();
        UploadFileEntity uploadFile = uploadFileService.getById(fileId);

        if (uploadFile != null && uploadFile.getIsDeleted().equals(0)) {
            return Collections.singletonList(uploadFile);
        }

        return Collections.emptyList();
    }
}
