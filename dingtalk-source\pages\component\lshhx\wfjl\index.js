import httpApi from "/utils/http/httpApi";
import {getWeekByDate} from '/utils/luch-request/utils';

Component({
  mixins: [],
  data: {
    withinOneYear:[{
      "reason":"未在当地烟草专卖批发企业进货",
      "penaltyTime":"2025-02-12"
    },{
      "reason":"未在当地烟草专卖批发企业进货",
      "penaltyTime":"2025-01-12"
    }],
    overOneYear:[{
      "reason":"XXXXXXX案由",
      "penaltyTime":"2025-02-12"
    }],
    formPage: {
      pageNo: 1,
      pageSize: 15,
      total: 10,
    },
    gotoScrollTop: 0,
    scrollToast:'',
    dataList:[],
    scrollVisible:false,
    popupData:{},
  },
  props: {
    yhytId:'',
  },
  didMount() {
    console.log(this.props.yhytId)
    this.getData();
  },
  didUpdate() {},
  didUnmount() {},
  methods: {
    getData(){
      httpApi.get('/api/dingapp/illegalrecords/getListByCustCode', {
        params: {
            yhytId: this.props.yhytId,
            current: this.data.formPage.pageNo,
            size: this.data.formPage.pageSize,
        }
    }).then(res => {
        console.log(res);
        if (res && res.data) {
            let data = res.data;
            if (data.current == 1 && data.total < 10) {
                this.setData({
                    scrollToast: ''
                })
            } else {
                this.setData({
                    scrollToast: "向上拉动显示更多内容"
                });
            }
            // 填充数据
            let list = [];
            if (this.data.formPage.pageNo > 1) {
                list = this.data.dataList;
            }
              const resData = res.data.records.map(item=>({
                regTime:item.regTime.split(" ")[0]+' '+ getWeekByDate(item.regTime),
                caseOfAction:item.caseOfAction,
                punishDecideDate:item.punishDecideDate
              })
              );
              list.push(...resData);
            this.setData({
                'formPage.total': data.total,
                'formPage.pageSize': data.size,
                'formPage.pageNo': data.current,
                dataList: list
            });
        }
    })
    },
    handlePopupClose() {
      this.setData({
          scrollVisible: false,
      });
    },
    handleOpenPopup(e){
      this.setData({
        scrollVisible: true,
        popupData:e.target.dataset.item,
      });
    },
    scrollToLowerSearch() {
      if (this.data.formPage.pageNo * this.data.formPage.pageSize >= this.data.formPage.total) {
          if (this.data.formPage.pageNo == 1 && this.data.formPage.total < 15) {
              this.setData({
                  scrollToast: ''
              })
              return
          }
          this.setData({
              scrollToast: '已经到底啦'
          })
          return;
      }
      this.setData({
          'formPage.pageNo': this.data.formPage.pageNo + 1
      });
      this.getData();
    }
  },
  
});
