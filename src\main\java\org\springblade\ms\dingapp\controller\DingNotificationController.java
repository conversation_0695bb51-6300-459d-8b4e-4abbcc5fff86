package org.springblade.ms.dingapp.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.ms.dingapp.config.DingAppConfig;
import org.springblade.ms.dingapp.dto.DingNotificationDTO;
import org.springblade.ms.dingapp.dto.BatchUserQueryDTO;
import org.springblade.ms.dingapp.service.IDingNotificationService;
import org.springframework.web.bind.annotation.*;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 钉钉工作通知控制器
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@RestController
@AllArgsConstructor
    @RequestMapping("/dingapp/notification")
@Tag(name = "钉钉工作通知接口")
public class DingNotificationController extends BladeController {

    private final IDingNotificationService dingNotificationService;
    private final DingAppConfig dingAppConfig;

    /**
     * 发送文本工作通知
     */
    @PostMapping("/sendText")
    @Operation(summary = "发送文本工作通知", description = "发送文本工作通知")
    public R<String> sendTextNotification(@RequestBody DingNotificationDTO dto) {
        return dingNotificationService.sendTextNotification(dto.getUserIds(), dto.getTitle(), dto.getContent());
    }

    /**
     * 发送文本工作通知给部门
     */
    @PostMapping("/sendTextToDept")
    @Operation(summary = "发送文本工作通知给部门", description = "发送文本工作通知给部门")
    public R<String> sendTextNotificationToDept(@RequestBody DingNotificationDTO dto) {
        return dingNotificationService.sendTextNotificationToDept(dto.getDeptIds(), dto.getTitle(), dto.getContent());
    }

    /**
     * 发送链接工作通知
     */
    @PostMapping("/sendLink")
    @Operation(summary = "发送链接工作通知", description = "发送链接工作通知")
    public R<String> sendLinkNotification(@RequestBody DingNotificationDTO dto) throws UnsupportedEncodingException {
        dto.setPicUrl("@2132133");
        return dingNotificationService.sendLinkNotification(
                dto.getUserIds(),
                dto.getTitle(),
                dto.getText(),
                dto.getMessageUrl(),
                dto.getPicUrl()
        );
    }

    /**
     * 发送Markdown工作通知
     */
    @PostMapping("/sendMarkdown")
    @Operation(summary = "发送Markdown工作通知", description = "发送Markdown工作通知")
    public R<String> sendMarkdownNotification(@RequestBody DingNotificationDTO dto) {
        return dingNotificationService.sendMarkdownNotification(
                dto.getUserIds(),
                dto.getTitle(),
                dto.getMarkdownText()
        );
    }

    /**
     * 查询工作通知发送进度
     */
    @GetMapping("/progress/{taskId}")
    @Operation(summary = "查询工作通知发送进度", description = "查询工作通知发送进度")
    @Parameters({
            @Parameter(name = "taskId", description = "任务ID", required = true)
    })
    public R<Object> getNotificationProgress(@PathVariable Long taskId) {
        return dingNotificationService.getNotificationProgress(taskId);
    }

    /**
     * 查询工作通知发送结果
     */
    @GetMapping("/result/{taskId}")
    @Operation(summary = "查询工作通知发送结果", description = "查询工作通知发送结果")
    @Parameters({
            @Parameter(name = "taskId", description = "任务ID", required = true)
    })
    public R<Object> getNotificationResult(@PathVariable Long taskId) {
        return dingNotificationService.getNotificationResult(taskId);
    }

    /**
     * 撤回工作通知
     */
    @PostMapping("/recall/{taskId}")
    @Operation(summary = "撤回工作通知", description = "撤回工作通知")
    @Parameters({
            @Parameter(name = "taskId", description = "任务ID", required = true)
    })
    public R<Boolean> recallNotification(@PathVariable Long taskId) {
        return dingNotificationService.recallNotification(taskId);
    }

    /**
     * 获取部门列表
     */
    @GetMapping("/departments")
    @Operation(summary = "获取部门列表", description = "获取钉钉部门列表")
    public R<Object> getDepartmentList() {
        return dingNotificationService.getDepartmentList();
    }





    /**
     * 获取指定部门的用户ID列表
     */
    @GetMapping("/departments/{deptId}/userids")
    @Operation(summary = "获取部门用户ID列表", description = "获取指定部门的用户ID列表")
    @Parameters({
            @Parameter(name = "deptId", description = "部门ID", required = true)
    })
    public R<Object> getDepartmentUserIds(@PathVariable String deptId) {
        return dingNotificationService.getDepartmentUserIds(deptId);
    }

    /**
     * 获取所有部门的用户ID列表
     */
    @GetMapping("/departments/userids")
    @Operation(summary = "获取所有部门用户ID列表", description = "获取所有部门的用户ID列表")
    public R<Object> getAllDepartmentUserIds() {
        return dingNotificationService.getAllDepartmentUserIds();
    }

    /**
     * 根据用户ID获取用户详细信息
     */
    @GetMapping("/users/{userId}")
    @Operation(summary = "获取用户详细信息", description = "根据用户ID获取用户详细信息")
    @Parameters({
            @Parameter(name = "userId", description = "用户ID", required = true)
    })
    public R<Object> getUserDetail(@PathVariable String userId) {
        return dingNotificationService.getUserDetail(userId);
    }

    /**
     * 批量获取用户详细信息
     */
    @PostMapping("/users/batch")
    @Operation(summary = "批量获取用户详细信息", description = "批量获取用户详细信息")
    public R<Object> getBatchUserDetails(@RequestBody List<String> userIds) {
        return dingNotificationService.getBatchUserDetails(userIds);
    }

    /**
     * 获取所有用户ID并批量获取详细信息 -- 数据量巨大  不可用
     */
    @Operation(summary = "获取所有用户详细信息", description = "获取所有用户ID并批量获取详细信息")
    public R<Object> getAllUsersWithDetails() {
        return dingNotificationService.getAllUsersWithDetails();
    }

    /**
     * 获取指定部门的所有用户信息（硬编码部门ID）
     */
    @GetMapping("/users/specific")
    @Operation(summary = "获取指定部门用户信息", description = "获取硬编码指定部门的所有用户详细信息")
    public R<Object> getSpecificDepartmentsUsers() {
        return dingNotificationService.getSpecificDepartmentsUsers();
    }

    /**
     * 获取指定部门的所有用户信息（前端传参）
     */
    @PostMapping("/users/specific")
    @Operation(summary = "获取指定部门用户信息", description = "获取前端传递的指定部门的所有用户详细信息")
    public R<Object> getSpecificDepartmentsUsersWithParams(@RequestBody List<String> deptIds) {
        return dingNotificationService.getSpecificDepartmentsUsers(deptIds);
    }

    /**
     * 获取目标部门及其所有下级部门的用户信息（前端传参）
     */
    @PostMapping("/users/target-with-sub")
    @Operation(summary = "获取目标部门及下级部门用户信息", description = "获取指定目标部门及其所有下级部门的用户详细信息")
    public R<Object> getTargetDepartmentsWithSubUsersDetails(@RequestBody List<String> deptIds) {
        return dingNotificationService.getTargetDepartmentsWithSubUsersDetails(deptIds);
    }

    /**
     * 获取湛江相关部门列表
     */
    @GetMapping("/users/all")
    @Operation(summary = "获取湛江相关部门", description = "从指定部门ID开始递归查找包含'湛江'的部门")
    @Parameters({
            @Parameter(name = "startDeptId", description = "起始部门ID，默认为641568739", required = false)
    })
    public R<Object> getZhanjiangDepartments(@RequestParam(value = "startDeptId", required = false) String startDeptId) {
        return dingNotificationService.getZhanjiangDepartments("1");
    }
}
