package org.springblade.ms.dingapp.util;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.WeekFields;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;

/**
 * 时间范围工具类
 *
 * <AUTHOR> Name
 * @since 2025-05-01
 */
public class TimeRangeUtil {

    /**
     * 获取上一个时间段的时间范围
     *
     * @param timeRange 时间范围类型
     * @param startTime 当前时间段的开始时间
     * @return 上一个时间段的开始和结束时间数组
     */
    public static LocalDateTime[] getPreviousTimeRange(String timeRange, LocalDateTime startTime) {
        LocalDateTime prevStartTime;
        LocalDateTime prevEndTime;

        switch (timeRange) {
            case "week":
                // 前一周
                prevStartTime = startTime.minusWeeks(1);
                prevEndTime = startTime.minusNanos(1);
                break;
            case "month":
                // 前一月
                prevStartTime = startTime.minusMonths(1);
                prevEndTime = startTime.minusNanos(1);
                break;
            case "quarter":
                // 前一季度
                prevStartTime = startTime.minusMonths(3);
                prevEndTime = startTime.minusNanos(1);
                break;
            case "year":
                // 前一年
                prevStartTime = startTime.minusYears(1);
                prevEndTime = startTime.minusNanos(1);
                break;
            case "day":
                // 前一天
            default:
                // 默认前一天
                prevStartTime = startTime.minusDays(1);
                prevEndTime = startTime.minusNanos(1);
                break;
        }

        return new LocalDateTime[]{prevStartTime, prevEndTime};
    }

    /**
     * 根据时间范围获取X轴数据
     *
     * @param timeRange 时间范围类型
     * @return X轴数据列表
     */
    public static List<String> getXAxisByTimeRange(String timeRange) {
        switch (timeRange) {
            case "week":
                // 本周按天划分
                return Arrays.asList("周一", "周二", "周三", "周四", "周五", "周六", "周日");
            case "month":
                // 本月按周划分
                return Arrays.asList("01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31");
            case "quarter":
                // 本季度按月划分
//                int quarter = (LocalDate.now().getMonthValue() - 1) / 3 + 1;
//                int startMonth = (quarter - 1) * 3 + 1;
                return Arrays.asList("1季", "2季", "3季", "4季");
            case "year":
                // 本年按月划分
                return Arrays.asList("1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月");
            case "day":
                // 当天按小时划分
            default:
                // 默认当天按小时划分
                return Arrays.asList("00", "01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23");
        }
    }
}
