package org.springblade.ms.externalDispatch.controller;

import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.ms.common.utils.ApiCenterUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("external-dispatch")
public class ExternalDispatchController {

	@PostMapping("/sapi")
	public R<String> sapi(@RequestBody Map<String, Object> requestBody) {
		Object serviceId = requestBody.get("serviceId");
		if (Func.isEmpty(serviceId)) {
			return R.fail("serviceId不能为空");
		}
		String result = ApiCenterUtil.send(serviceId.toString(), requestBody);
		return R.data(result);
	}
}
