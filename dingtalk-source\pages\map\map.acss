.mainApp {
    flex: 1;
}

.mapCls {
    height: 90vh;
}

.suspendedBox {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
    width: 100%;
    padding: 12px 0;
    position: fixed;
    bottom: 13vh;
    z-index: 97;
    overflow: hidden;
    pointer-events: none;
}

.positionPanel {
    width: 100%;
    display: flex;
    justify-content: space-between;
}

.rationalizeTipsPanel {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    flex-direction: column;
}

.yellowBox {
    width: 70px;
    height: 35px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    border-radius: 8px;
    box-shadow: 0px 1px 2px rgba(0, 0, 0, .1);
    background-color: #fff;
    overflow: hidden;
    margin-left: 16px;
    margin-bottom: 8px;
    padding: 0 5px;
}

.yellowBox .yellowColor {
    background: rgba(255, 232, 0, 0.40);
    width: 20px;
    height: 20px;
}

.redBox {
    width: 70px;
    height: 35px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    border-radius: 8px;
    box-shadow: 0px 1px 2px rgba(0, 0, 0, .1);
    background-color: #fff;
    overflow: hidden;
    margin-left: 16px;
    margin-bottom: 12px;
    padding: 0 5px;
}

.redBox .redColor {
    background: rgba(255, 0, 0, 0.60);
    width: 20px;
    height: 20px;
}

.positionBox {
    width: 36px;
    height: 36px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    border-radius: 8px;
    box-shadow: 0px 1px 2px rgba(0, 0, 0, .1);
    background-color: #fff;
    overflow: hidden;
    margin-left: 16px;
    pointer-events: all;
}

.positionBox .image {
    width: 20px;
    height: 20px;
}

.rationalizeBtnBox {
    width: 100px;
    height: 36px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    border-radius: 8px;
    box-shadow: 0px 1px 2px rgba(0, 0, 0, .1);
    background-color: #fff;
    overflow: hidden;
    margin-right: 16px;
    pointer-events: all;
}

.rationalizeBtnBox .rationalizeBtn {
    font-size: 14px;
}

.activeBtn{
    background-color: #4285F4;
    color: #fff;
}

.detailContent {
    width: 100%;
}

.detail {
    margin: 12px 16px 0;
    padding: 16px;
    border-radius: 15px;
    box-shadow: 0px 1px 2px rgba(0, 0, 0, .1);
    background-color: #fff;
    pointer-events: all;
}

.detailTitle {
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.detailTitle .text {
    flex-grow: 1;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    color: #272637;
    font-size: 18px;
    font-weight: 500;
    line-height: 20px;
    white-space: initial;
    word-wrap: break-word;
    word-break: break-all;
}

.detailTitle .icon {
    flex-shrink: 0;
    width: 22px;
    height: 22px;
    display: flex;
    justify-content: flex-end;
    align-items: flex-start;
}

.closeIcon {
    width: 12px;
    height: 12px;
}

.detailTextBox {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding-top: 12px;
}

.detailTextBox .text {
    flex-grow: 1;
    color: #9295A0;
}

.detailTextBox .image {
    flex-shrink: 0;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 55px;
    padding-right: 8px;
}

.detailTextBox .retailers {
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 65px;
    padding-left: 8px;
}

.vistaImage {
    width: 54px;
    height: 51px;
    border-radius: 5px;
}

.detailAddress {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    line-height: 18px;
    font-size: 14px;
}

.detailAddressText {
    flex-grow: 1;
}

.detailLicNo {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    line-height: 14px;
    font-size: 14px;
    margin-top: 8px;
}

.detailLicNoText {
    flex-grow: 1;
}

.detailDistance {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    line-height: 14px;
    font-size: 14px;
    margin-top: 8px;
}

.detailDistanceText {
    flex-grow: 1;
}

.retailersImage {
    width: 32px;
    height: 32px;
}

.retailersText {
    color: #9295A0;
    font-size: 11px;
    font-weight: 400;
    line-height: 15px;
    margin-top: 5px;
    width: 35px;
    text-align: center;
}

.userLocationContent {
    color: #9295A0;
    font-size: 14px;
    margin-top: 5px;
}

.headerPanel {
    position: fixed;
    z-index: 97;
    left: 0;
    right: 0;
    margin: auto;
    padding: 20px 7px 0 7px;
    top: 0;
    display: flex;
    flex-direction: column;
}

.searchBox {
    display: flex;
    height: 40px;
    width: 100%;
    justify-content: center;
}

.searchBox .search-bar {
    border-top-left-radius: 15px;
    border-bottom-left-radius: 15px;
    width: 100%;
    padding-left: 4px;
    margin-left: 9px;
}

.searchBox .search-bar input {
    font-size: 16px;
}

.searchPlaceholderCls {
    font-size: 13px;
    letter-spacing: 1px;
}

.searchBox .searchInpBtn {
    width: 60px;
    height: 40px;
    line-height: 40px;
    border-radius: 0px 10px 10px 0px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    color: #FFFFFF;
    background-color: #4285F4;
    font-size: 16px;
    margin-right: 9px;
}

.formatParamBox {
    position: fixed;
    z-index: 210;
    right: 0;
    top: 9vh;
    width: 100px;
    height: 36px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    border-radius: 8px;
    box-shadow: 0px 1px 2px rgba(0, 0, 0, .1);
    background-color: #fff;
    overflow: hidden;
    margin-right: 16px;
    padding: 0 10px;
}
