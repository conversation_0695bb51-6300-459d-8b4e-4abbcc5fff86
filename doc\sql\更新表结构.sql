-- 2025-01-16 修改工单表的长度
ALTER TABLE "public"."ms_report_complaint"
    ALTER COLUMN "event_addr" TYPE text COLLATE "pg_catalog"."default" USING "event_addr"::text,
    ALTER COLUMN "roll_handle_result" TYPE text COLLATE "pg_catalog"."default" USING "roll_handle_result"::text;

-- 2025-01-20 补充租户ID
ALTER TABLE "public"."ms_product_info"
    ADD COLUMN "tenant_id" int8;

COMMENT ON COLUMN "public"."ms_product_info"."tenant_id" IS '租户ID';

UPDATE ms_product_info SET tenant_id = '000000' WHERE tenant_id IS NULL


-- 2025-01-23 增加用户的最后登录时间
ALTER TABLE "public"."blade_user"
    ADD COLUMN "last_login_time" timestamp(6);

COMMENT ON COLUMN "public"."blade_user"."last_login_time" IS '最后登录时间';



-- 2025-01-24 添加零售户经营状态字段
ALTER TABLE "public"."ms_yhyt_license"
    ADD COLUMN "operate_status" varchar(10);

COMMENT ON COLUMN "public"."ms_yhyt_license"."operate_status" IS '经营状态';

-- 2025-02-17 修改经纬度精度
ALTER TABLE ms_exploration_coordinate
    ALTER COLUMN longitude TYPE numeric(10,6),
    ALTER COLUMN latitude TYPE numeric(10,6);


-- 2025-02-24 近半年零售户订货品规
DROP TABLE IF EXISTS "public"."ms_recent_retailer_order_product";
CREATE TABLE "public"."ms_recent_retailer_order_product" (
    "id" int8 NOT NULL,
    "customer_uuid" varchar(255) COLLATE "pg_catalog"."default",
    "customer_code" varchar(255) COLLATE "pg_catalog"."default",
    "customer_name" varchar(255) COLLATE "pg_catalog"."default",
    "product_uuid" varchar(255) COLLATE "pg_catalog"."default",
    "product_code" varchar(255) COLLATE "pg_catalog"."default",
    "product_name" varchar(255) COLLATE "pg_catalog"."default",
    "qty" int8,
    "create_user" int8,
    "create_dept" int8,
    "create_time" timestamp(6),
    "update_user" int8,
    "update_time" timestamp(6),
    "status" int4 DEFAULT 1,
    "is_deleted" int4 DEFAULT 0,
    "tenant_id" varchar(12) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."ms_recent_retailer_order_product"."id" IS '主键';
COMMENT ON COLUMN "public"."ms_recent_retailer_order_product"."customer_uuid" IS '客户标识';
COMMENT ON COLUMN "public"."ms_recent_retailer_order_product"."customer_code" IS '客户编码';
COMMENT ON COLUMN "public"."ms_recent_retailer_order_product"."customer_name" IS '客户名称';
COMMENT ON COLUMN "public"."ms_recent_retailer_order_product"."product_uuid" IS '商品标识';
COMMENT ON COLUMN "public"."ms_recent_retailer_order_product"."product_code" IS '商品编码';
COMMENT ON COLUMN "public"."ms_recent_retailer_order_product"."product_name" IS '商品名称';
COMMENT ON COLUMN "public"."ms_recent_retailer_order_product"."qty" IS '订单数量';
COMMENT ON COLUMN "public"."ms_recent_retailer_order_product"."create_user" IS '创建人';
COMMENT ON COLUMN "public"."ms_recent_retailer_order_product"."create_dept" IS '创建部门';
COMMENT ON COLUMN "public"."ms_recent_retailer_order_product"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."ms_recent_retailer_order_product"."update_user" IS '修改人';
COMMENT ON COLUMN "public"."ms_recent_retailer_order_product"."update_time" IS '修改时间';
COMMENT ON COLUMN "public"."ms_recent_retailer_order_product"."status" IS '状态';
COMMENT ON COLUMN "public"."ms_recent_retailer_order_product"."is_deleted" IS '是否已删除';
COMMENT ON COLUMN "public"."ms_recent_retailer_order_product"."tenant_id" IS '租户ID';
COMMENT ON TABLE "public"."ms_recent_retailer_order_product" IS '近半年零售户订货品规';

-- ----------------------------
-- Primary Key structure for table ms_recent_retailer_order_product
-- ----------------------------
ALTER TABLE "public"."ms_recent_retailer_order_product" ADD CONSTRAINT "ms_recent_retailer_order_product_pkey" PRIMARY KEY ("id");

-- 2025-02-26 添加识别返回内容和图片字段
ALTER TABLE "public"."ms_item_identify"
    ADD COLUMN "result_text" text,
    ADD COLUMN "result_file_id" int8;

COMMENT ON COLUMN "public"."ms_item_identify"."result_text" IS '返回内容';
COMMENT ON COLUMN "public"."ms_item_identify"."result_file_id" IS '返回图片';


-- 2025-02-27 添加零售户uuid字段
ALTER TABLE "public"."ms_illegal_records"
    ADD COLUMN "cust_uuid" varchar(255);
COMMENT ON COLUMN "public"."ms_illegal_records"."cust_uuid" IS '零售户uuid';

-- 2025-02-27 添加证件类型字段
ALTER TABLE "public"."ms_illegal_records"
    ADD COLUMN "id_card_type" varchar(5);
COMMENT ON COLUMN "public"."ms_illegal_records"."id_card_type" IS '证件类型';

-- 2025-02-27 添加证件id字段
ALTER TABLE "public"."ms_illegal_records"
    ADD COLUMN "id_card" varchar(255);
COMMENT ON COLUMN "public"."ms_illegal_records"."id_card" IS '证件id';

-- 2025-02-28 修正字段名
ALTER TABLE "public"."ms_item_identify"
    RENAME COLUMN "identity_date" TO "identify_date";

--2025-03-06 新增学校信息表
DROP TABLE IF EXISTS "public"."ms_school_info";
CREATE TABLE "public"."ms_school_info" (
                                           "id" int8 NOT NULL,
                                           "school_code" varchar(55) COLLATE "pg_catalog"."default",
                                           "campus_code" varchar(55) COLLATE "pg_catalog"."default",
                                           "school_name" varchar(255) COLLATE "pg_catalog"."default",
                                           "is_inclusive_kindergarten" varchar(5) COLLATE "pg_catalog"."default",
                                           "social_credit_code" varchar(55) COLLATE "pg_catalog"."default",
                                           "industry_category_code" varchar(55) COLLATE "pg_catalog"."default",
                                           "industry_category_name" varchar(100) COLLATE "pg_catalog"."default",
                                           "main_school_type_code" varchar(55) COLLATE "pg_catalog"."default",
                                           "main_school_type_name" varchar(100) COLLATE "pg_catalog"."default",
                                           "is_independent_set_ethnic_school" varchar(5) COLLATE "pg_catalog"."default",
                                           "address_code" varchar(55) COLLATE "pg_catalog"."default",
                                           "address_name" varchar(255) COLLATE "pg_catalog"."default",
                                           "urban_rural_code" varchar(55) COLLATE "pg_catalog"."default",
                                           "urban_rural_category" varchar(155) COLLATE "pg_catalog"."default",
                                           "longitude" numeric(12,8),
                                           "latitude" numeric(12,8),
                                           "local_education_authority_code" varchar(55) COLLATE "pg_catalog"."default",
                                           "local_education_authority_name" varchar(255) COLLATE "pg_catalog"."default",
                                           "superior_department" varchar(155) COLLATE "pg_catalog"."default",
                                           "superior_department_code" varchar(55) COLLATE "pg_catalog"."default",
                                           "superior_department_name" varchar(255) COLLATE "pg_catalog"."default",
                                           "school_operator_code" varchar(55) COLLATE "pg_catalog"."default",
                                           "school_operator_name" varchar(255) COLLATE "pg_catalog"."default",
                                           "operator_detail_name" varchar(255) COLLATE "pg_catalog"."default",
                                           "school_english_name" varchar(255) COLLATE "pg_catalog"."default",
                                           "principal_name" varchar(100) COLLATE "pg_catalog"."default",
                                           "office_phone" varchar(40) COLLATE "pg_catalog"."default",
                                           "fax_number" varchar(40) COLLATE "pg_catalog"."default",
                                           "postal_code" varchar(10) COLLATE "pg_catalog"."default",
                                           "institution_email" varchar(100) COLLATE "pg_catalog"."default",
                                           "form_filled_by" varchar(50) COLLATE "pg_catalog"."default",
                                           "statistics_officer_name" varchar(20) COLLATE "pg_catalog"."default",
                                           "statistics_contact_number" varchar(20) COLLATE "pg_catalog"."default",
                                           "primary_duration_code" varchar(10) COLLATE "pg_catalog"."default",
                                           "primary_duration_name" varchar(50) COLLATE "pg_catalog"."default",
                                           "primary_entry_age_code" varchar(10) COLLATE "pg_catalog"."default",
                                           "primary_entry_age_name" varchar(50) COLLATE "pg_catalog"."default",
                                           "junior_high_duration_code" varchar(10) COLLATE "pg_catalog"."default",
                                           "junior_high_duration_name" varchar(50) COLLATE "pg_catalog"."default",
                                           "junior_high_entry_age_code" varchar(10) COLLATE "pg_catalog"."default",
                                           "junior_high_entry_age_name" varchar(50) COLLATE "pg_catalog"."default",
                                           "direct_school_level" varchar(255) COLLATE "pg_catalog"."default",
                                           "create_user" int8,
                                           "create_dept" int8,
                                           "create_time" timestamp(6),
                                           "update_user" int8,
                                           "update_time" timestamp(6),
                                           "status" int4 DEFAULT 1,
                                           "is_deleted" int4 DEFAULT 0,
                                           "tenant_id" varchar(12) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."ms_school_info"."school_code" IS '学校代码';
COMMENT ON COLUMN "public"."ms_school_info"."campus_code" IS '校区码';
COMMENT ON COLUMN "public"."ms_school_info"."school_name" IS '学校名称';
COMMENT ON COLUMN "public"."ms_school_info"."is_inclusive_kindergarten" IS '是否普惠性幼儿园';
COMMENT ON COLUMN "public"."ms_school_info"."social_credit_code" IS '社会信用代码';
COMMENT ON COLUMN "public"."ms_school_info"."industry_category_code" IS '行业分类码';
COMMENT ON COLUMN "public"."ms_school_info"."industry_category_name" IS '行业分类名称';
COMMENT ON COLUMN "public"."ms_school_info"."main_school_type_code" IS '主体学校办学类型码';
COMMENT ON COLUMN "public"."ms_school_info"."main_school_type_name" IS '主体学校办学类型名称';
COMMENT ON COLUMN "public"."ms_school_info"."is_independent_set_ethnic_school" IS '是否独立设置少数民族学校';
COMMENT ON COLUMN "public"."ms_school_info"."address_code" IS '学校(机构)地址代码';
COMMENT ON COLUMN "public"."ms_school_info"."address_name" IS '学校(机构)地址名称';
COMMENT ON COLUMN "public"."ms_school_info"."urban_rural_code" IS '城乡分类码';
COMMENT ON COLUMN "public"."ms_school_info"."urban_rural_category" IS '城乡分类名称';
COMMENT ON COLUMN "public"."ms_school_info"."longitude" IS '经度';
COMMENT ON COLUMN "public"."ms_school_info"."latitude" IS '纬度';
COMMENT ON COLUMN "public"."ms_school_info"."local_education_authority_code" IS '学校(机构)属地管理教育行政部门代码';
COMMENT ON COLUMN "public"."ms_school_info"."local_education_authority_name" IS '学校(机构)属地管理教育行政部门名称';
COMMENT ON COLUMN "public"."ms_school_info"."superior_department" IS '上级主管部门';
COMMENT ON COLUMN "public"."ms_school_info"."superior_department_code" IS '上级管理部门码';
COMMENT ON COLUMN "public"."ms_school_info"."superior_department_name" IS '上级管理部门名称';
COMMENT ON COLUMN "public"."ms_school_info"."school_operator_code" IS '学校(机构)举办者码';
COMMENT ON COLUMN "public"."ms_school_info"."school_operator_name" IS '学校(机构)举办者名称';
COMMENT ON COLUMN "public"."ms_school_info"."operator_detail_name" IS '举办者详细名称';
COMMENT ON COLUMN "public"."ms_school_info"."school_english_name" IS '学校(机构)英文名称';
COMMENT ON COLUMN "public"."ms_school_info"."principal_name" IS '学校(机构)负责人姓名';
COMMENT ON COLUMN "public"."ms_school_info"."office_phone" IS '办公电话';
COMMENT ON COLUMN "public"."ms_school_info"."fax_number" IS '传真号码';
COMMENT ON COLUMN "public"."ms_school_info"."postal_code" IS '邮政编码';
COMMENT ON COLUMN "public"."ms_school_info"."institution_email" IS '单位电子信箱';
COMMENT ON COLUMN "public"."ms_school_info"."form_filled_by" IS '填表人';
COMMENT ON COLUMN "public"."ms_school_info"."statistics_officer_name" IS '统计负责人姓名';
COMMENT ON COLUMN "public"."ms_school_info"."statistics_contact_number" IS '统计负责人联系电话';
COMMENT ON COLUMN "public"."ms_school_info"."primary_duration_code" IS '小学规定年制码';
COMMENT ON COLUMN "public"."ms_school_info"."primary_duration_name" IS '小学规定年制名称';
COMMENT ON COLUMN "public"."ms_school_info"."primary_entry_age_code" IS '小学入学年龄码';
COMMENT ON COLUMN "public"."ms_school_info"."primary_entry_age_name" IS '小学 入学年龄名称';
COMMENT ON COLUMN "public"."ms_school_info"."junior_high_duration_code" IS '初中规定年制码';
COMMENT ON COLUMN "public"."ms_school_info"."junior_high_duration_name" IS '初中规定年制名称';
COMMENT ON COLUMN "public"."ms_school_info"."junior_high_entry_age_code" IS '初中入学年龄码';
COMMENT ON COLUMN "public"."ms_school_info"."junior_high_entry_age_name" IS '初中入学年龄名称';
COMMENT ON COLUMN "public"."ms_school_info"."direct_school_level" IS '直属学校等级名称';
COMMENT ON COLUMN "public"."ms_school_info"."create_user" IS '创建人';
COMMENT ON COLUMN "public"."ms_school_info"."create_dept" IS '创建部门';
COMMENT ON COLUMN "public"."ms_school_info"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."ms_school_info"."update_user" IS '修改人';
COMMENT ON COLUMN "public"."ms_school_info"."update_time" IS '修改时间';
COMMENT ON COLUMN "public"."ms_school_info"."status" IS '状态';
COMMENT ON COLUMN "public"."ms_school_info"."is_deleted" IS '是否已删除';
COMMENT ON COLUMN "public"."ms_school_info"."tenant_id" IS '租户ID';
-- ----------------------------
-- Primary Key structure for table ms_school_info
-- ----------------------------
ALTER TABLE "public"."ms_school_info" ADD CONSTRAINT "ms_school_info_pkey" PRIMARY KEY ("id");




-- 2025-03-10 添加档位字段
ALTER TABLE "public"."ms_yhyt_license"
    ADD COLUMN "tap_position" varchar(50);
COMMENT ON COLUMN "public"."ms_yhyt_license"."tap_position" IS '档位';


-- 2025-03-11 添加是识别结果是否有误字段
ALTER TABLE "public"."ms_item_identify"
    ADD COLUMN "is_recognition_bad" bool DEFAULT false;
COMMENT ON COLUMN "public"."ms_item_identify"."is_recognition_bad" IS '识别结果是否有误';

-- 2025-03-11 添加周边是否有学校字段
ALTER TABLE "public"."ms_yhyt_license"
    ADD COLUMN "has_school_nearby" bool DEFAULT false;
COMMENT ON COLUMN "public"."ms_yhyt_license"."has_school_nearby" IS '周边是否有学校';


--2025-03-18 新增举报投诉-零售户关联表
DROP TABLE IF EXISTS "public"."ms_complaint_yhyt";
CREATE TABLE "public"."ms_complaint_yhyt" (
                                              "id" int8 NOT NULL,
                                              "yhyt_id" int8,
                                              "complaint_id" int8,
                                              "create_user" int8,
                                              "create_dept" int8,
                                              "create_time" timestamp(6),
                                              "update_user" int8,
                                              "update_time" timestamp(6),
                                              "status" int4 DEFAULT 1,
                                              "is_deleted" int4 DEFAULT 0,
                                              "tenant_id" varchar(12) COLLATE "pg_catalog"."default");
COMMENT ON COLUMN "public"."ms_complaint_yhyt"."yhyt_id" IS '一户一图id';
COMMENT ON COLUMN "public"."ms_complaint_yhyt"."complaint_id" IS '零售户id';
COMMENT ON COLUMN "public"."ms_complaint_yhyt"."create_user" IS '创建人';
COMMENT ON COLUMN "public"."ms_complaint_yhyt"."create_dept" IS '创建部门';
COMMENT ON COLUMN "public"."ms_complaint_yhyt"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."ms_complaint_yhyt"."update_user" IS '修改人';
COMMENT ON COLUMN "public"."ms_complaint_yhyt"."update_time" IS '修改时间';
COMMENT ON COLUMN "public"."ms_complaint_yhyt"."status" IS '状态';
COMMENT ON COLUMN "public"."ms_complaint_yhyt"."is_deleted" IS '是否已删除';
COMMENT ON COLUMN "public"."ms_complaint_yhyt"."tenant_id" IS '租户ID';

 -- 2025-03-24 修正字段名
ALTER TABLE "public"."ms_item_identify"
    RENAME COLUMN "order" TO "identify_order";

-- 2025-03-24 添加用户id字段
ALTER TABLE "public"."ms_report_complaint"
    ADD COLUMN "user_id" int8;
COMMENT ON COLUMN "public"."ms_report_complaint"."user_id" IS '用户id';

-- 2025-03-24 添加事件标题和事件目标字段
ALTER TABLE "public"."ms_report_complaint"
    ADD COLUMN "event_title" varchar(255),
    ADD COLUMN "event_target" varchar(255);
COMMENT ON COLUMN "public"."ms_report_complaint"."event_title" IS '诉求标题';
COMMENT ON COLUMN "public"."ms_report_complaint"."event_target" IS '涉事主体';

-- 2025-03-25租户字段
ALTER TABLE "public"."ms_product_info"
    ADD COLUMN "tenant_id" varchar(12) COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "public"."ms_product_info"."tenant_id" IS '租户ID';


-- 2025-04-08 添加法人代表字段
ALTER TABLE "public"."ms_yhyt_license"
    ADD COLUMN "manager_name" varchar(10);

COMMENT ON COLUMN "public"."ms_yhyt_license"."manager_name" IS '法人代表';

-- 2025-04-09 添加涉案价格标准表
CREATE TABLE ms_price_standards (
    "id" int8 NOT NULL,
    "std_type" VARCHAR(50) NOT NULL,        -- 类型：品名/品规（卷烟规格名称）/品牌/类别
    "product_name" VARCHAR(255),            -- 通用字段：品名/品规（卷烟规格名称）/品牌/类别
    "product_category" VARCHAR(50),         -- 附件1专用: 类别
    "barcode" VARCHAR(50),                  -- 附件1专用：条包条形码
    "price" NUMERIC(12,6),                  -- 通用字段：价格（建议零售价/平均零售价/参考价格）
    "price_unit" VARCHAR(20),               -- 通用字段：价格单位
    "remarks" VARCHAR(255),                 -- 附件4专用：备注
    "price_year" int4,                      -- 通用字段：年份
    "create_user" int8,
    "create_dept" int8,
    "create_time" timestamp(6),
    "update_user" int8,
    "update_time" timestamp(6),
    "status" int4 DEFAULT 1,
    "is_deleted" int4 DEFAULT 0,
    "tenant_id" varchar(12) COLLATE "pg_catalog"."default"
);

ALTER TABLE "public"."ms_price_standards" ADD PRIMARY KEY (id);
COMMENT ON TABLE "public"."ms_price_standards" IS '涉案价格标准表';

COMMENT ON COLUMN "public"."ms_price_standards"."id" IS '主键ID';
COMMENT ON COLUMN "public"."ms_price_standards"."std_type" IS '类型：品名/品规（卷烟规格名称）/品牌/类别';
COMMENT ON COLUMN "public"."ms_price_standards"."product_name" IS '通用字段：品名/品规（卷烟规格名称）/品牌/类别';
COMMENT ON COLUMN "public"."ms_price_standards"."product_category" IS '附件1专用: 类别';
COMMENT ON COLUMN "public"."ms_price_standards"."barcode" IS '附件1专用：条包条形码';
COMMENT ON COLUMN "public"."ms_price_standards"."price" IS '通用字段：价格（建议零售价/平均零售价/参考价格）';
COMMENT ON COLUMN "public"."ms_price_standards"."price_unit" IS '通用字段：价格单位';
COMMENT ON COLUMN "public"."ms_price_standards"."remarks" IS '附件4专用：备注';
COMMENT ON COLUMN "public"."ms_price_standards"."price_year" IS '通用字段：年份';
COMMENT ON COLUMN "public"."ms_price_standards"."create_user" IS '创建人ID';
COMMENT ON COLUMN "public"."ms_price_standards"."create_dept" IS '创建部门ID';
COMMENT ON COLUMN "public"."ms_price_standards"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."ms_price_standards"."update_user" IS '更新人ID';
COMMENT ON COLUMN "public"."ms_price_standards"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."ms_price_standards"."status" IS '状态：1表示有效，0表示无效';
COMMENT ON COLUMN "public"."ms_price_standards"."is_deleted" IS '是否删除：0表示未删除，1表示已删除';
COMMENT ON COLUMN "public"."ms_price_standards"."tenant_id" IS '租户ID';


-- 2025-04-09 添加决定处罚文书号字段
ALTER TABLE "public"."ms_illegal_records"
    ADD COLUMN "decide_full_no" varchar(255);

COMMENT ON COLUMN "public"."ms_illegal_records"."decide_full_no" IS '决定处罚文书号';

-- 2025-04-11 新增涉案物品管理表
DROP TABLE IF EXISTS "public"."ms_evidence_yhyt";
CREATE TABLE "public"."ms_evidence_yhyt" (
                                             "id" int8 NOT NULL,
                                             "yhyt_id" int8,
                                             "price_standards_id" int8,
                                             "product_name" varchar(255) COLLATE "pg_catalog"."default",
                                             "std_type" varchar(50) COLLATE "pg_catalog"."default",
                                             "selected_quantity" numeric(10,2),
                                             "current_unit_price" numeric(12,6),
                                             "selection_time" timestamp(6),
                                             "create_user" int8,
                                             "create_dept" int8,
                                             "create_time" timestamp(6),
                                             "update_user" int8,
                                             "update_time" timestamp(6),
                                             "status" int4 DEFAULT 1,
                                             "is_deleted" int4 DEFAULT 0,
                                             "tenant_id" varchar(12) COLLATE "pg_catalog"."default",
                                             "price_unit" varchar(20) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."ms_evidence_yhyt"."id" IS '主键';
COMMENT ON COLUMN "public"."ms_evidence_yhyt"."yhyt_id" IS '零售户id';
COMMENT ON COLUMN "public"."ms_evidence_yhyt"."price_standards_id" IS '涉案价格标准id';
COMMENT ON COLUMN "public"."ms_evidence_yhyt"."product_name" IS '物品名称（冗余）';
COMMENT ON COLUMN "public"."ms_evidence_yhyt"."std_type" IS '类型（冗余）';
COMMENT ON COLUMN "public"."ms_evidence_yhyt"."selected_quantity" IS '用户选择的物品数量';
COMMENT ON COLUMN "public"."ms_evidence_yhyt"."current_unit_price" IS '当前单价，允许修改';
COMMENT ON COLUMN "public"."ms_evidence_yhyt"."selection_time" IS '存储时间，统一一次操作的时间';
COMMENT ON COLUMN "public"."ms_evidence_yhyt"."create_user" IS '创建人ID';
COMMENT ON COLUMN "public"."ms_evidence_yhyt"."create_dept" IS '创建部门ID';
COMMENT ON COLUMN "public"."ms_evidence_yhyt"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."ms_evidence_yhyt"."update_user" IS '更新人ID';
COMMENT ON COLUMN "public"."ms_evidence_yhyt"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."ms_evidence_yhyt"."status" IS '状态：1表示有效，0表示无效';
COMMENT ON COLUMN "public"."ms_evidence_yhyt"."is_deleted" IS '是否删除：0表示未删除，1表示已删除';
COMMENT ON COLUMN "public"."ms_evidence_yhyt"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "public"."ms_evidence_yhyt"."price_unit" IS '冗余，价格单位';
COMMENT ON TABLE "public"."ms_evidence_yhyt" IS '涉案物品表';

ALTER TABLE "public"."ms_evidence_yhyt" ADD CONSTRAINT "ms_evidence_yhyt_pkey" PRIMARY KEY ("id");


-- 2025-04-20 查获假烟统计表
DROP TABLE IF EXISTS "public"."ms_fake_cigarettes";
CREATE TABLE "public"."ms_fake_cigarettes" (
  "id" INT8 NOT NULL,
  "supply_name" VARCHAR(255),
  "goods_uuid" VARCHAR(50),
  "goods_name" VARCHAR(255),
  "brand_name" VARCHAR(255),
  "smuggle_qty" NUMERIC (10, 2),
  "truth_qty" NUMERIC (10, 2),
  "fake_qty" NUMERIC (10, 2),
  "total_qty" NUMERIC (10, 2),
  "create_user" INT8,
  "create_dept" INT8,
  "create_time" TIMESTAMP (6),
  "update_user" INT8,
  "update_time" TIMESTAMP (6),
  "status" INT4 DEFAULT 1,
  "is_deleted" INT4 DEFAULT 0,
  "tenant_id" VARCHAR(12) COLLATE "pg_catalog"."default"
);

COMMENT ON COLUMN "public"."ms_fake_cigarettes"."id" IS '主键';
COMMENT ON COLUMN "public"."ms_fake_cigarettes"."supply_name" IS '供应商名称';
COMMENT ON COLUMN "public"."ms_fake_cigarettes"."goods_uuid" IS '商品唯一标识';
COMMENT ON COLUMN "public"."ms_fake_cigarettes"."goods_name" IS '商品全称';
COMMENT ON COLUMN "public"."ms_fake_cigarettes"."brand_name" IS '品牌名称';
COMMENT ON COLUMN "public"."ms_fake_cigarettes"."smuggle_qty" IS '走私数量（单位：万条）';
COMMENT ON COLUMN "public"."ms_fake_cigarettes"."truth_qty" IS '真实数量（单位：万条）';
COMMENT ON COLUMN "public"."ms_fake_cigarettes"."fake_qty" IS '假冒数量（单位：万条）';
COMMENT ON COLUMN "public"."ms_fake_cigarettes"."total_qty" IS '总数量（单位：万条）';
COMMENT ON COLUMN "public"."ms_fake_cigarettes"."create_user" IS '创建人ID';
COMMENT ON COLUMN "public"."ms_fake_cigarettes"."create_dept" IS '创建部门ID';
COMMENT ON COLUMN "public"."ms_fake_cigarettes"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."ms_fake_cigarettes"."update_user" IS '更新人ID';
COMMENT ON COLUMN "public"."ms_fake_cigarettes"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."ms_fake_cigarettes"."status" IS '状态：1表示有效，0表示无效';
COMMENT ON COLUMN "public"."ms_fake_cigarettes"."is_deleted" IS '是否删除：0表示未删除，1表示已删除';
COMMENT ON COLUMN "public"."ms_fake_cigarettes"."tenant_id" IS '租户ID';
COMMENT ON TABLE "public"."ms_fake_cigarettes" IS '查获假烟统计表';
ALTER TABLE "public"."ms_fake_cigarettes" ADD CONSTRAINT "ms_fake_cigarettes_pkey" PRIMARY KEY ( "id" );


-- 2025-04-20 无证户信息表
DROP TABLE IF EXISTS "public"."ms_yhyt_license_unlicensed";
CREATE TABLE "public"."ms_yhyt_license_unlicensed" (
  "id" int8 NOT NULL,
  "retailer_uuid" varchar(255) COLLATE "pg_catalog"."default",
  "lic_no" varchar(255) COLLATE "pg_catalog"."default",
  "lic_type" varchar(255) COLLATE "pg_catalog"."default",
  "old_lic_no" varchar(255) COLLATE "pg_catalog"."default",
  "cust_code" varchar(255) COLLATE "pg_catalog"."default",
  "company_name" varchar(255) COLLATE "pg_catalog"."default",
  "eco_type" varchar(255) COLLATE "pg_catalog"."default",
  "contract_person" varchar(255) COLLATE "pg_catalog"."default",
  "retail_tel" varchar(255) COLLATE "pg_catalog"."default",
  "retail_tel_back" varchar(255) COLLATE "pg_catalog"."default",
  "business_addr" varchar(255) COLLATE "pg_catalog"."default",
  "validate_start" date,
  "validate_end" date,
  "lic_status" varchar(255) COLLATE "pg_catalog"."default",
  "invalid_time" timestamp(6),
  "org_name" varchar(255) COLLATE "pg_catalog"."default",
  "is_have_business_lic" int4,
  "business_lic_no" varchar(255) COLLATE "pg_catalog"."default",
  "business_valid_type" varchar(255) COLLATE "pg_catalog"."default",
  "business_valid_start" date,
  "business_valid_end" date,
  "registered_status" varchar(255) COLLATE "pg_catalog"."default",
  "special_type" varchar(255) COLLATE "pg_catalog"."default",
  "special_type_other" varchar(255) COLLATE "pg_catalog"."default",
  "busi_type" varchar(255) COLLATE "pg_catalog"."default",
  "busi_sub_type" varchar(255) COLLATE "pg_catalog"."default",
  "env_type" varchar(255) COLLATE "pg_catalog"."default",
  "longitude" numeric,
  "latitude" numeric,
  "eco_sub_type" varchar(255) COLLATE "pg_catalog"."default",
  "is_validate" int4,
  "shop_sign" varchar(255) COLLATE "pg_catalog"."default",
  "consumer_need" varchar(255) COLLATE "pg_catalog"."default",
  "store_brand" varchar(255) COLLATE "pg_catalog"."default",
  "manager_scope" varchar(255) COLLATE "pg_catalog"."default",
  "busi_size_code" varchar(255) COLLATE "pg_catalog"."default",
  "busi_size_name" varchar(255) COLLATE "pg_catalog"."default",
  "adscription_code" varchar(255) COLLATE "pg_catalog"."default",
  "adscription_name" varchar(255) COLLATE "pg_catalog"."default",
  "biz_format" varchar(255) COLLATE "pg_catalog"."default",
  "supply_status" varchar(255) COLLATE "pg_catalog"."default",
  "supply_org_uuid" varchar(255) COLLATE "pg_catalog"."default",
  "supply_company_code" varchar(255) COLLATE "pg_catalog"."default",
  "supply_company_name" varchar(255) COLLATE "pg_catalog"."default",
  "create_user" int8,
  "create_dept" int8,
  "create_time" timestamp(6),
  "update_user" int8,
  "update_time" timestamp(6),
  "status" int4 DEFAULT 1,
  "is_deleted" int4 DEFAULT 0,
  "tenant_id" varchar(12) COLLATE "pg_catalog"."default",
  "operate_status" varchar(10) COLLATE "pg_catalog"."default",
  "tap_position" varchar(50) COLLATE "pg_catalog"."default",
  "has_school_nearby" bool DEFAULT false,
  "manager_name" varchar(10) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."ms_yhyt_license"."id" IS '主键';
COMMENT ON COLUMN "public"."ms_yhyt_license"."retailer_uuid" IS '零售户uuid';
COMMENT ON COLUMN "public"."ms_yhyt_license"."lic_no" IS '许可证编号';
COMMENT ON COLUMN "public"."ms_yhyt_license"."lic_type" IS '许可证种类';
COMMENT ON COLUMN "public"."ms_yhyt_license"."old_lic_no" IS '老许可证号';
COMMENT ON COLUMN "public"."ms_yhyt_license"."cust_code" IS '客户编码';
COMMENT ON COLUMN "public"."ms_yhyt_license"."company_name" IS '企业名称';
COMMENT ON COLUMN "public"."ms_yhyt_license"."eco_type" IS '企业经济类型，字典：l_eco_type';
COMMENT ON COLUMN "public"."ms_yhyt_license"."contract_person" IS '联系人';
COMMENT ON COLUMN "public"."ms_yhyt_license"."retail_tel" IS '联系电话';
COMMENT ON COLUMN "public"."ms_yhyt_license"."retail_tel_back" IS '备用电话';
COMMENT ON COLUMN "public"."ms_yhyt_license"."business_addr" IS '经营地址';
COMMENT ON COLUMN "public"."ms_yhyt_license"."validate_start" IS '许可证有效期限起';
COMMENT ON COLUMN "public"."ms_yhyt_license"."validate_end" IS '许可证有效期限止';
COMMENT ON COLUMN "public"."ms_yhyt_license"."lic_status" IS '许可证状态，字典：l_lic_status';
COMMENT ON COLUMN "public"."ms_yhyt_license"."invalid_time" IS '失效时间（许可证注销时间）';
COMMENT ON COLUMN "public"."ms_yhyt_license"."org_name" IS '片区名称';
COMMENT ON COLUMN "public"."ms_yhyt_license"."is_have_business_lic" IS '是否有工商营业执照';
COMMENT ON COLUMN "public"."ms_yhyt_license"."business_lic_no" IS '工商营业执照编码（统一社会信用代码证）';
COMMENT ON COLUMN "public"."ms_yhyt_license"."business_valid_type" IS '营业执照有效期类型，字典：l_business_valid_type';
COMMENT ON COLUMN "public"."ms_yhyt_license"."business_valid_start" IS '营业执照有效期起';
COMMENT ON COLUMN "public"."ms_yhyt_license"."business_valid_end" IS '营业执照有效期止';
COMMENT ON COLUMN "public"."ms_yhyt_license"."registered_status" IS '工商营业执照状态 ，字典：l_business_registered_status';
COMMENT ON COLUMN "public"."ms_yhyt_license"."special_type" IS '群体类型';
COMMENT ON COLUMN "public"."ms_yhyt_license"."special_type_other" IS '其他特殊群体说明';
COMMENT ON COLUMN "public"."ms_yhyt_license"."busi_type" IS '商圈，字典：l_busi_type';
COMMENT ON COLUMN "public"."ms_yhyt_license"."busi_sub_type" IS '次级商圈：入口：retailder_busi_sub_type';
COMMENT ON COLUMN "public"."ms_yhyt_license"."env_type" IS '地理环境，字典：l_env_type';
COMMENT ON COLUMN "public"."ms_yhyt_license"."longitude" IS '经度';
COMMENT ON COLUMN "public"."ms_yhyt_license"."latitude" IS '纬度';
COMMENT ON COLUMN "public"."ms_yhyt_license"."eco_sub_type" IS '经济类型-子类（个人经营、家庭经营）';
COMMENT ON COLUMN "public"."ms_yhyt_license"."is_validate" IS '许可证是否有效';
COMMENT ON COLUMN "public"."ms_yhyt_license"."shop_sign" IS '店铺招牌';
COMMENT ON COLUMN "public"."ms_yhyt_license"."consumer_need" IS '消费需求';
COMMENT ON COLUMN "public"."ms_yhyt_license"."store_brand" IS '连锁企业品牌';
COMMENT ON COLUMN "public"."ms_yhyt_license"."manager_scope" IS '许可证经营范围多选，字典：l_manager_scope';
COMMENT ON COLUMN "public"."ms_yhyt_license"."busi_size_code" IS '经营规模';
COMMENT ON COLUMN "public"."ms_yhyt_license"."busi_size_name" IS '经营规模名称';
COMMENT ON COLUMN "public"."ms_yhyt_license"."adscription_code" IS '经营场地权属';
COMMENT ON COLUMN "public"."ms_yhyt_license"."adscription_name" IS '经营场地权属名称';
COMMENT ON COLUMN "public"."ms_yhyt_license"."biz_format" IS '经营业态';
COMMENT ON COLUMN "public"."ms_yhyt_license"."supply_status" IS '供货状态，字典：l_supply_status';
COMMENT ON COLUMN "public"."ms_yhyt_license"."supply_org_uuid" IS '供货单位编号，表s_org_base_info';
COMMENT ON COLUMN "public"."ms_yhyt_license"."supply_company_code" IS '供货单位编号';
COMMENT ON COLUMN "public"."ms_yhyt_license"."supply_company_name" IS '供货单位名称';
COMMENT ON COLUMN "public"."ms_yhyt_license"."create_user" IS '创建人';
COMMENT ON COLUMN "public"."ms_yhyt_license"."create_dept" IS '创建部门';
COMMENT ON COLUMN "public"."ms_yhyt_license"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."ms_yhyt_license"."update_user" IS '修改人';
COMMENT ON COLUMN "public"."ms_yhyt_license"."update_time" IS '修改时间';
COMMENT ON COLUMN "public"."ms_yhyt_license"."status" IS '状态';
COMMENT ON COLUMN "public"."ms_yhyt_license"."is_deleted" IS '是否已删除';
COMMENT ON COLUMN "public"."ms_yhyt_license"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "public"."ms_yhyt_license"."operate_status" IS '经营状态';
COMMENT ON COLUMN "public"."ms_yhyt_license"."tap_position" IS '档位';
COMMENT ON COLUMN "public"."ms_yhyt_license"."has_school_nearby" IS '周边是否有学校';
COMMENT ON COLUMN "public"."ms_yhyt_license"."manager_name" IS '法人代表';
COMMENT ON TABLE "public"."ms_yhyt_license" IS '无证户信息表';


ALTER TABLE "public"."ms_yhyt_license_unlicensed" ADD CONSTRAINT "ms_yhyt_license_unlicensed_pkey" PRIMARY KEY ("id");

-- 2025-04-20 添涉案物品管理标题
ALTER TABLE "public"."ms_evidence_yhyt"
    ADD COLUMN "title" varchar(255);
COMMENT ON COLUMN "public"."ms_evidence_yhyt"."title" IS '标题';

-- 2025-04-27 添涉案物品管理执法单位
ALTER TABLE "public"."ms_evidence_yhyt"
    ADD COLUMN "enforcement_agency" varchar(155);
COMMENT ON COLUMN "public"."ms_evidence_yhyt"."enforcement_agency" IS '执法单位';

--2025-04-27 添加涉案物品管理相关字段
ALTER TABLE "public"."ms_evidence_yhyt"
    ADD COLUMN "case_time" varchar(50),
    ADD COLUMN "address" varchar(255),
    ADD COLUMN "detailed_address" varchar(500),
    ADD COLUMN "joint_enforcement_agency" varchar(255),
    ADD COLUMN "case_reason" varchar(500),
    ADD COLUMN "party_involved" varchar(255),
    ADD COLUMN "license_no" varchar(100);
COMMENT ON COLUMN "public"."ms_evidence_yhyt"."case_time" IS '案发时间';
COMMENT ON COLUMN "public"."ms_evidence_yhyt"."address" IS '地址';
COMMENT ON COLUMN "public"."ms_evidence_yhyt"."detailed_address" IS '详细地址';
COMMENT ON COLUMN "public"."ms_evidence_yhyt"."joint_enforcement_agency" IS '联合执法单位';
COMMENT ON COLUMN "public"."ms_evidence_yhyt"."case_reason" IS '案由';
COMMENT ON COLUMN "public"."ms_evidence_yhyt"."party_involved" IS '当事人';
COMMENT ON COLUMN "public"."ms_evidence_yhyt"."license_no" IS '许可证号';

-- 2025-04-28 扩大字典值字段长度
ALTER TABLE blade_dict_biz
ALTER COLUMN dict_key TYPE varchar(512);

-- 2025-04-30 添加导入价格来源、品规类型字段
ALTER TABLE "public"."ms_evidence_yhyt"
    ADD COLUMN "price_source" varchar(50),
    ADD COLUMN "evidence_type" varchar(50);
COMMENT ON COLUMN "public"."ms_evidence_yhyt"."price_source" IS '价格来源';
COMMENT ON COLUMN "public"."ms_evidence_yhyt"."evidence_type" IS '涉案物品类型（真烟、假烟、走私烟）';


-- 2025-05-08 添加订货周期
ALTER TABLE "public"."ms_yhyt_license"
    ADD COLUMN "order_weekday" varchar(50);

COMMENT ON COLUMN "public"."ms_yhyt_license"."order_weekday" IS '订货周期';


-- 2025-05-08 添加密码策略相关字段到用户表
ALTER TABLE blade_user ADD COLUMN password_last_changed_time TIMESTAMP;
-- 更新所有用户的密码最后修改时间为当前时间
UPDATE blade_user SET password_last_changed_time = NOW() WHERE password_last_changed_time IS NULL;


-- 2025-05-14 添加包条码字段
ALTER TABLE "public"."ms_product_info"
    ADD COLUMN "pack_code" varchar(50);
COMMENT ON COLUMN "public"."ms_product_info"."pack_code" IS '包条形码';

ALTER TABLE "public"."ms_price_standards"
    ADD COLUMN "pack_code" varchar(50);
COMMENT ON COLUMN "public"."ms_price_standards"."pack_code" IS '包条形码';


-- 2025-05-14 添加件条码字段
ALTER TABLE "public"."ms_product_info"
    ADD COLUMN "item_code" varchar(50);
COMMENT ON COLUMN "public"."ms_product_info"."item_code" IS '件条形码';

-- 2025-05-14 添加支数字段
ALTER TABLE "public"."ms_product_info"
    ADD COLUMN "package_qty" int8;
COMMENT ON COLUMN "public"."ms_product_info"."package_qty" IS '包包装支数';

ALTER TABLE "public"."ms_product_info"
    ADD COLUMN "package_qty2" int8;
COMMENT ON COLUMN "public"."ms_product_info"."package_qty2" IS '条包装支数';

ALTER TABLE "public"."ms_product_info"
    ADD COLUMN "package_qty3" int8;
COMMENT ON COLUMN "public"."ms_product_info"."package_qty3" IS '件包装支数';

ALTER TABLE "public"."ms_product_info"
    ADD COLUMN "default_branch_qty" int8;
COMMENT ON COLUMN "public"."ms_product_info"."default_branch_qty" IS '默认支数量';


-- 2025-05-15 添加执法证号码字段
ALTER TABLE "public"."blade_user"
    ADD COLUMN "law_enforcement_certificate_no" varchar(50);

COMMENT ON COLUMN "public"."blade_user"."law_enforcement_certificate_no" IS '执法证号码';

-- 2025-05-19 工单添加处理结果字段
ALTER TABLE "public"."ms_report_complaint"
    ADD COLUMN "handle_result" text;
COMMENT ON COLUMN "public"."ms_report_complaint"."handle_result" IS '处理结果';

-- 2025-05-19 工单添加状态字段
ALTER TABLE "public"."ms_report_complaint"
    ADD COLUMN "complaint_status" varchar(50);
COMMENT ON COLUMN "public"."ms_report_complaint"."complaint_status" IS '工单状态';


-- 2025-05-27 添加组织架构用户表
CREATE TABLE ms_ding_user (
                              id BIGINT NOT NULL PRIMARY KEY,
                              userid VARCHAR(100) NOT NULL,
                              name VARCHAR(100),
                              mobile VARCHAR(20),
                              title VARCHAR(100),
                              dept_id_list TEXT,
                              dept_names TEXT,
                              parent_dept_ids TEXT,
                              create_time TIMESTAMP,
                              update_time TIMESTAMP,
                              status INT DEFAULT 1
);

-- 添加注释
COMMENT ON COLUMN ms_ding_user.userid IS '钉钉用户ID';
COMMENT ON COLUMN ms_ding_user.name IS '用户姓名';
COMMENT ON COLUMN ms_ding_user.mobile IS '手机号';
COMMENT ON COLUMN ms_ding_user.title IS '职位';
COMMENT ON COLUMN ms_ding_user.dept_id_list IS '部门ID列表（JSON格式）';
COMMENT ON COLUMN ms_ding_user.dept_names IS '部门名称列表（JSON格式）';
COMMENT ON COLUMN ms_ding_user.parent_dept_ids IS '父级部门ID列表（JSON格式）';
COMMENT ON COLUMN ms_ding_user.create_time IS '创建时间';
COMMENT ON COLUMN ms_ding_user.update_time IS '更新时间';
COMMENT ON COLUMN ms_ding_user.status IS '状态（1-正常，0-删除）';

-- 创建索引
CREATE UNIQUE INDEX uk_userid ON ms_ding_user(userid);


-- 2025-05-28
ALTER TABLE "public"."ms_evidence_yhyt"
    ADD COLUMN "source" varchar(50);
COMMENT ON COLUMN "public"."ms_evidence_yhyt"."source" IS '来源';

ALTER TABLE "public"."ms_evidence_yhyt"
    ADD COLUMN "is_zhanjiang_deployment" INT DEFAULT 1;
COMMENT ON COLUMN "public"."ms_evidence_yhyt"."is_zhanjiang_deployment" IS '是否湛江市局稽查支队统筹部署';

ALTER TABLE "public"."ms_evidence_yhyt"
    ADD COLUMN "transport_details" varchar(255);;
COMMENT ON COLUMN "public"."ms_evidence_yhyt"."transport_details" IS '涉案运输工具详细信息';



-- 2025-06-03
-- 近一期是否到货
ALTER TABLE "public"."ms_recent_retailer_order_product"
    ADD COLUMN "is_recent_arrived" INTEGER;
COMMENT ON COLUMN "public"."ms_recent_retailer_order_product"."is_recent_arrived" IS '近一期是否到货';

-- 近一期订单数量
ALTER TABLE "public"."ms_recent_retailer_order_product"
    ADD COLUMN "recent_qty" INTEGER;
COMMENT ON COLUMN "public"."ms_recent_retailer_order_product"."recent_qty" IS '近一期订单数量';

-- 近一期业务日期
ALTER TABLE "public"."ms_recent_retailer_order_product"
    ADD COLUMN "recent_biz_date" VARCHAR(20);
COMMENT ON COLUMN "public"."ms_recent_retailer_order_product"."recent_biz_date" IS '近一期业务日期';

-- 近一期含税金额
ALTER TABLE "public"."ms_recent_retailer_order_product"
    ADD COLUMN "recent_with_tax_amount" NUMERIC(18, 2);
COMMENT ON COLUMN "public"."ms_recent_retailer_order_product"."recent_with_tax_amount" IS '近一期含税金额';

-- 近一期要货数量
ALTER TABLE "public"."ms_recent_retailer_order_product"
    ADD COLUMN "recent_req_qty" INTEGER;
COMMENT ON COLUMN "public"."ms_recent_retailer_order_product"."recent_req_qty" IS '近一期要货数量';

-- 累计订货次数
ALTER TABLE "public"."ms_recent_retailer_order_product"
    ADD COLUMN "total_ord_tims" INTEGER;
COMMENT ON COLUMN "public"."ms_recent_retailer_order_product"."total_ord_tims" IS '累计订货次数';

-- 累计订单数量
ALTER TABLE "public"."ms_recent_retailer_order_product"
    ADD COLUMN "total_qty" INTEGER;
COMMENT ON COLUMN "public"."ms_recent_retailer_order_product"."total_qty" IS '累计订单数量';

-- 累计含税金额
ALTER TABLE "public"."ms_recent_retailer_order_product"
    ADD COLUMN "total_with_tax_amount" NUMERIC(18, 2);
COMMENT ON COLUMN "public"."ms_recent_retailer_order_product"."total_with_tax_amount" IS '累计含税金额';

-- 累计要货数量
ALTER TABLE "public"."ms_recent_retailer_order_product"
    ADD COLUMN "total_req_qty" INTEGER;
COMMENT ON COLUMN "public"."ms_recent_retailer_order_product"."total_req_qty" IS '累计要货数量';


-- 2025-05-30 添加工单表字段
ALTER TABLE "public"."ms_report_complaint"
    ADD COLUMN "data_source" varchar(50);
COMMENT ON COLUMN "public"."ms_report_complaint"."data_source" IS '来源';
ALTER TABLE "public"."ms_report_complaint"
    ADD COLUMN "audit_user" BIGINT;
COMMENT ON COLUMN "public"."ms_report_complaint"."audit_user" IS '办结人id';

-- 2025-06-03 添加用户id字段
ALTER TABLE "public"."ms_report_complaint"
    ADD COLUMN "dept_id" int8;
COMMENT ON COLUMN "public"."ms_report_complaint"."dept_id" IS '部门id';
ALTER TABLE "public"."ms_report_complaint"
    ADD COLUMN "has_license" int4;
COMMENT ON COLUMN "public"."ms_report_complaint"."has_license" IS '是否涉及许可证';
ALTER TABLE "public"."ms_report_complaint"
    ADD COLUMN "contact_phone" varchar(50);
COMMENT ON COLUMN "public"."ms_report_complaint"."contact_phone" IS '联系电话';

-- 2025-06-09 添加勘查对象类型字段
ALTER TABLE "public"."ms_exploration"
    ADD COLUMN "object_type" varchar(20);

COMMENT ON COLUMN "public"."ms_exploration"."object_type" IS '勘查对象类型（零售户、无证户、学校）';


--  2025-06-09  添加零售户门店照片对象类型字段
ALTER TABLE "public"."ms_retailer_store_photo"
    ADD COLUMN "object_type" varchar(20);

COMMENT ON COLUMN "public"."ms_retailer_store_photo"."object_type" IS '照片对象类型（零售户、无证户、学校）';


--  2025-06-22  是否经营电子烟
ALTER TABLE "public"."ms_yhyt_license"
    ADD COLUMN "is_electronic_cigarette" int8 default 0;
COMMENT ON COLUMN "public"."ms_yhyt_license"."is_electronic_cigarette" IS '是否经营电子烟';

-- 2025-08-12 信用等级
ALTER TABLE "public"."ms_yhyt_license"
    ADD COLUMN "credit_level" varchar(20) ;
COMMENT ON COLUMN "public"."ms_yhyt_license"."credit_level" IS '信用等级';