package org.springblade.ms.dingapp.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-01-22 13:01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ms_dingapp_user")
public class DingAppUser extends TenantEntity {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 小程序UNIONID
     */
    private String unionId;

}
