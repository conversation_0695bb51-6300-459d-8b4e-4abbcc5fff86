import {ddLoginAsync, refreshToken} from "/utils/userHelp"

const app = getApp();

Page({
  data: {
    refreshTokenTask: null,
    loginFailDialog: false,
    footer: {
      buttons: [{ text: '我知道了' }],
    },
  },

  onLoad(query) {

    // 页面加载
    console.info(`Page onLoad with query: ${JSON.stringify(query)}`);
  },
  async onReady() {
    // 页面加载完成
    if (!app.globalData.isLogin) {
      try {
        await ddLoginAsync()
      } catch (err) {
        this.handleWxLoginFail(err);
      }
    }
  },
  onShow() {
    // 页面显示
    if (app.globalData.isLogin) {
      this.startRefreshTokenTask();
      dd.reLaunch({
        url: "/pages/map/map"
    })
    }
  },
  onHide() {
    // 页面隐藏
  },
  onUnload() {
    // 页面被关闭
  },
  onTitleClick() {
    // 标题被点击
  },
  onPullDownRefresh() {
    // 页面被下拉
  },
  onReachBottom() {
    // 页面被拉到底部
  },
  onShareAppMessage() {
    // 返回自定义分享信息
    return {
      title: '',
      desc: '',
      path: 'pages/index/index',
    };
  },

  startRefreshTokenTask() {
    
    if (this.data.refreshTokenTask) {
        clearInterval(this.data.refreshTokenTask)
    }

    let task = setInterval(() => {
      if (!app.globalData.isLogin) {
        return;
      }

      refreshToken(app.globalData.refreshToken).then((res) => {
        console.log(res);
        if (res.data) {
          app.globalData.accessToken = res.data.access_token;
          app.globalData.refreshToken = res.data.refresh_token;
        }
      })

    }, 30000)

    this.setData({
        refreshTokenTask: task
    })

  },

  handleWxLoginFail(err) {
    console.error(err);
    this.setData({
      loginFailDialog: true
    })
  },
  onFailDialogButtonTap(buttonItem) {
    this.handleFailDialogClose();
  },
  handleFailDialogClose() {
    this.setData({
        loginFailDialog: false,
    });
  },
});
