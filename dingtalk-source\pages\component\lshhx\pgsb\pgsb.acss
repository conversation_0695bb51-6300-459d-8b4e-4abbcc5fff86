.pgsbMain{
  flex: 1;
  padding: 10px 16px 16px;
}
.info_card{
  width: 100%;
  background-color: white;
  border-radius: 15rpx;
  box-sizing: border-box;
  margin: 10px auto 16px;
  padding: 16px 16px 10px;
  min-height: 130px;
  box-shadow: 0px 1px 2px rgba(0, 0, 0, .1);
}
.title{
  font-size: 14px;
}
.text_content {
  flex: 1;        /* 占据剩余空间 */
  min-width: 0;   /* 允许缩小，防止溢出 */
  white-space: normal; /* 允许自动换行 */
  word-break: break-all;
  overflow: hidden; /* 隐藏溢出内容 */
  text-overflow: ellipsis; /* 溢出内容用省略号代替 */
  display: -webkit-box; /* 使用弹性盒子布局 */
  -webkit-line-clamp: 3; /* 限制最大显示行数为3 */
  -webkit-box-orient: vertical; /* 使子元素竖直排列 */
}
.text-time{
  flex: 1;        /* 占据剩余空间 */
  min-width: 0;   /* 允许缩小，防止溢出 */
  white-space: normal; /* 允许自动换行 */
  word-break: break-all;
  overflow: hidden; /* 隐藏溢出内容 */
  text-overflow: ellipsis; /* 溢出内容用省略号代替 */
  display: -webkit-box; /* 使用弹性盒子布局 */
  -webkit-line-clamp: 2; /* 限制最大显示行数为3 */
  -webkit-box-orient: vertical; /* 使子元素竖直排列 */
}
.flex{
  display: flex;
  align-items: flex-start;
}
.text-box{
  /* height: 60px; */
}
.title-one{
  width: 115px;
  display: inline-block;
}
.title-two{
  width: 140px;
  padding-left: 10px;
  display: inline-block;

}

.btn-container {
  display: flex;
  justify-content: flex-end; /* 使按钮对齐到右侧 */
  width: 100%; /* 父容器宽度为 100% */
}
.pgsb-btn{
  background-color: #1c88f9;
  color: white;
  text-align: center;
  border-radius: 7px;
  padding: 12rpx 15rpx;
  /* border: solid 1px grey;
  padding: 2px 8px;
  border-radius: 5px; */
}