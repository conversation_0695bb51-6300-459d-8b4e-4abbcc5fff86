<view class="pgsbMain">
  <scroll-view scroll-y="{{true}}" style="flex: 1;" scroll-top='{{gotoScrollTop}}' onScrollToLower="scrollToLowerLoad" lower-threshold='150'>
    <view a:if="{{ !lshhxIsView  }}" onTap="click" >
      <view class="title">
        <text class="title-two">{{todayData.identifyDate}}</text>
      </view>
      <view class="info_card" >
        <view class="flex ">
          <view class="text">
            识别品规数：
          </view>
          <view class="text">
            {{todayData.identifyNum}}
          </view>
        </view >
        <view class="flex"  style="margin-top:10px;margin-bottom:30px">
          <view class="text ">
            识别异常数：
          </view>
          <view class="text text-time">
            {{todayData.errorNum}}
          </view>
        </view >
        <ant-divider />
        <view class="btn-container">
          <view class="pgsb-btn" onTap="handleStartRecognition">开始识别</view>
        </view>
      </view>
    </view>

    <ant-empty a:if="{{ dataList.length==0 && lshhxIsView }}"
      title="数据为空"
    />

    <view a:for="{{ dataList }}">

      <view class="title">
        <text class="title-two">{{item.identifyDate}} </text>
      </view>
      <view class="info_card" >
        <view class="flex ">
          <view class="text">
            识别品规数：
          </view>
          <view class="text">
            {{item.identifyNum}}
          </view>
        </view >
        <view class="flex"  style="margin-top:10px;margin-bottom:30px">
          <view class="text ">
            识别异常数：
          </view>
          <view class="text text-time">
            {{item.errorNum}}
          </view>
        </view >
        <ant-divider />
        <view class="btn-container">
          <view class="pgsb-btn" onTap="handleViewClick" data-item={{item}}>查看详情</view>
        </view>
      </view>
    </view>
  </scroll-view>
</view>