<view class="mainApp">
    <map 
        class="mapCls"
        id="map"
        longitude="{{currentLon}}"
        latitude="{{currentLat}}" 
        show-location="{{true}}"
        markers="{{mapMarkers}}"
        circles="{{mapCircles}}"
        controls="{{mapControls}}"
        optimize="{{true}}"
        onTap="handleMapPointTap"
        onMarkerTap="handleMapMarkerTap"
        include-points="{{mapIncludePointList}}"
        ground-overlays="{{mapGroundOverlays}}"
    >
    </map>

    <!-- 头部搜索框 -->
    <view class="headerPanel">
        <view class="searchBox"
              onTap="handleSearchInputClick"
        >
            <ant-input
                    placeholder="搜索附近零售户"
                    className="search-bar"
                    placeholder-style="font-size:16px;"
                    focusClassName="search-bar-focus"
                    confirm-type="search"
                    focus
                    placeholderClassName="searchPlaceholderCls"
            >
                <ant-icon style="font-size:18px" slot="prefix" type="SearchOutline" />
                <ant-icon  slot="suffix" type="ScanningOutline" style="font-size:24px;margin-right: 10px;" catchTap="handleSearchBarScan" />
            </ant-input>
            <view class="searchInpBtn" catchTap="handleSearchBtnClick">搜索</view>
        </view>
    </view>

    <!-- 零售户明细小弹窗 -->
    <view class="suspendedBox">
        <view class="rationalizeTipsPanel" a:if="{{ isShowRationalize }}">
            <view class="yellowBox">
                <view class="yellowColor"></view>
                <view style="font-size: 14px;">{{'<'}}50米</view>
            </view>
            <view class="redBox">
                <view class="redColor"></view>
                <view style="font-size: 14px;">{{'<'}}30米</view>
            </view>
        </view>
        <view class="positionPanel">
            <view class="positionBox" onTap="handleMoveToLocation">
                <image class="image" src="/image/position.svg"></image>
            </view>
            <view class="rationalizeBtnBox {{isShowRationalize?'activeBtn':''}}" a:if="{{ isShowDetailBox }}" onTap="handleShowRationalize">
                <view class="rationalizeBtn">合理化布局</view>
                   
            </view>
        </view>
        <view class="detailContent" a:if="{{ isShowDetailBox }}">
            <view class="detail">
                <view class="detailTitle">
                    <view class="text">
                        <view>{{ currentLicense.companyName }}</view>
                    </view>
                    <view class="icon" onTap="handleCloseDetailBox">
                        <image class="closeIcon" src="/image/close-icon.svg"></image>
                    </view>
                </view>
                <view class="userLocationContent">
                    当前位置：{{ currentAddress }}
                </view>
                <view class="detailTextBox">
                    <view class="image" a:if="{{ currentLicense.photoPathList.length>0 }}">
                        <image class="vistaImage" src="{{currentLicense.photoPathList[0].filthPath}}" fit="cover" catchtap="previewVistaPic" />
                    </view>
                    <view class="image" a:if="{{ !currentLicense.photoPathList || currentLicense.photoPathList.length<=0 }}">
                        <image class="vistaImage" src="/image/picture-icon.svg" mode="contain" ></image>
                    </view>
                    <view class="text">
                        <view class="detailAddress">
                            <view class="detailAddressText">{{ currentLicense.businessAddr }}</view>
                        </view>
                        <view class="detailLicNo">
                            <view class="detailLicNoText">许可证：{{ currentLicense.licNo }}</view>
                        </view>
                        <view class="detailDistance">
                            <view class="detailDistanceText">距您{{ currentLicense.distance }}米</view>
                        </view>
                    </view>
                    <view class="retailers" onTap="toLshhx" data-licData="{{currentLicense}}">
                        <image class="retailersImage" src="/image/retailers-icon.svg" fit="cover"></image>
                        <view class="retailersText">零售户画像</view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</view>
<custom-tab-bar/>

<view class="formatParamBox" >
    <view class="formatParamBtn" style="{{formatParamValue.length === 5 ? 'font-size: 12px' : 'font-size: 14px' }}">
        <ant-picker
                value="{{formatParamValue}}"
                placeholder=""
                title="请选择业态"
                options="{{formatParamList}}"
                style="z-index: 1000;"
                onOk="handleFormatParamPickerOK"
        >
            <view
                    slot="content"
                    slot-scope="prop"
            >业态：{{prop.value}}</view>
        </ant-picker>
    </view>
</view>