/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.ms.itemidentify.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDate;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;

/**
 * 品规识别记录 实体类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@TableName("ms_item_identify")
@Schema(description = "ItemIdentify对象")
@EqualsAndHashCode(callSuper = true)
public class ItemIdentifyEntity extends TenantEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 识别日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@Schema(description = "识别日期")
	private LocalDate identifyDate;
	/**
	 * 勘查 ID
	 */
	@Schema(description = "勘查 ID")
	private Long explorationId;
	/**
	 * 文件 ID
	 */
	@Schema(description = "文件 ID")
	private Long fileId;
	/**
	 * 类型（烟柜、烟架）
	 */
	@Schema(description = "类型（烟柜、烟架）")
	private String type;
	/**
	 * 排序
	 */
	@Schema(description = "排序")
	private Integer identifyOrder;

	@Schema(description = "返回内容")
	private String resultText;

	@Schema(description = "返回图片")
	private Long resultFileId;

	@Schema(description = "识别结果较差")
	private Boolean isRecognitionBad;

}
