<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.ms.basic.mapper.YhytLicenseUnlicensedMapper">

    <resultMap id="BaseResultMap" type="org.springblade.ms.basic.pojo.entity.YhytLicenseUnlicensedEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="retailerUuid" column="retailer_uuid" jdbcType="VARCHAR"/>
            <result property="licNo" column="lic_no" jdbcType="VARCHAR"/>
            <result property="licType" column="lic_type" jdbcType="VARCHAR"/>
            <result property="oldLicNo" column="old_lic_no" jdbcType="VARCHAR"/>
            <result property="custCode" column="cust_code" jdbcType="VARCHAR"/>
            <result property="companyName" column="company_name" jdbcType="VARCHAR"/>
            <result property="ecoType" column="eco_type" jdbcType="VARCHAR"/>
            <result property="contractPerson" column="contract_person" jdbcType="VARCHAR"/>
            <result property="retailTel" column="retail_tel" jdbcType="VARCHAR"/>
            <result property="retailTelBack" column="retail_tel_back" jdbcType="VARCHAR"/>
            <result property="businessAddr" column="business_addr" jdbcType="VARCHAR"/>
            <result property="validateStart" column="validate_start" jdbcType="DATE"/>
            <result property="validateEnd" column="validate_end" jdbcType="DATE"/>
            <result property="licStatus" column="lic_status" jdbcType="VARCHAR"/>
            <result property="invalidTime" column="invalid_time" jdbcType="TIMESTAMP"/>
            <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
            <result property="isHaveBusinessLic" column="is_have_business_lic" jdbcType="INTEGER"/>
            <result property="businessLicNo" column="business_lic_no" jdbcType="VARCHAR"/>
            <result property="businessValidType" column="business_valid_type" jdbcType="VARCHAR"/>
            <result property="businessValidStart" column="business_valid_start" jdbcType="DATE"/>
            <result property="businessValidEnd" column="business_valid_end" jdbcType="DATE"/>
            <result property="registeredStatus" column="registered_status" jdbcType="VARCHAR"/>
            <result property="specialType" column="special_type" jdbcType="VARCHAR"/>
            <result property="specialTypeOther" column="special_type_other" jdbcType="VARCHAR"/>
            <result property="busiType" column="busi_type" jdbcType="VARCHAR"/>
            <result property="busiSubType" column="busi_sub_type" jdbcType="VARCHAR"/>
            <result property="envType" column="env_type" jdbcType="VARCHAR"/>
            <result property="longitude" column="longitude" jdbcType="NUMERIC"/>
            <result property="latitude" column="latitude" jdbcType="NUMERIC"/>
            <result property="ecoSubType" column="eco_sub_type" jdbcType="VARCHAR"/>
            <result property="isValidate" column="is_validate" jdbcType="INTEGER"/>
            <result property="shopSign" column="shop_sign" jdbcType="VARCHAR"/>
            <result property="consumerNeed" column="consumer_need" jdbcType="VARCHAR"/>
            <result property="storeBrand" column="store_brand" jdbcType="VARCHAR"/>
            <result property="managerScope" column="manager_scope" jdbcType="VARCHAR"/>
            <result property="busiSizeCode" column="busi_size_code" jdbcType="VARCHAR"/>
            <result property="busiSizeName" column="busi_size_name" jdbcType="VARCHAR"/>
            <result property="adscriptionCode" column="adscription_code" jdbcType="VARCHAR"/>
            <result property="adscriptionName" column="adscription_name" jdbcType="VARCHAR"/>
            <result property="bizFormat" column="biz_format" jdbcType="VARCHAR"/>
            <result property="supplyStatus" column="supply_status" jdbcType="VARCHAR"/>
            <result property="supplyOrgUuid" column="supply_org_uuid" jdbcType="VARCHAR"/>
            <result property="supplyCompanyCode" column="supply_company_code" jdbcType="VARCHAR"/>
            <result property="supplyCompanyName" column="supply_company_name" jdbcType="VARCHAR"/>
            <result property="createUser" column="create_user" jdbcType="BIGINT"/>
            <result property="createDept" column="create_dept" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateUser" column="update_user" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="operateStatus" column="operate_status" jdbcType="VARCHAR"/>
            <result property="tapPosition" column="tap_position" jdbcType="VARCHAR"/>
            <result property="hasSchoolNearby" column="has_school_nearby" jdbcType="BOOLEAN"/>
            <result property="managerName" column="manager_name" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,retailer_uuid,lic_no,
        lic_type,old_lic_no,cust_code,
        company_name,eco_type,contract_person,
        retail_tel,retail_tel_back,business_addr,
        validate_start,validate_end,lic_status,
        invalid_time,org_name,is_have_business_lic,
        business_lic_no,business_valid_type,business_valid_start,
        business_valid_end,registered_status,special_type,
        special_type_other,busi_type,busi_sub_type,
        env_type,longitude,latitude,
        eco_sub_type,is_validate,shop_sign,
        consumer_need,store_brand,manager_scope,
        busi_size_code,busi_size_name,adscription_code,
        adscription_name,biz_format,supply_status,
        supply_org_uuid,supply_company_code,supply_company_name,
        create_user,create_dept,create_time,
        update_user,update_time,status,
        is_deleted,tenant_id,operate_status,
        tap_position,has_school_nearby,manager_name
    </sql>
</mapper>
