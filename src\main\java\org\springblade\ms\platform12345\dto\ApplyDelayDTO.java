package org.springblade.ms.platform12345.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 延期申请DTO
 */
@Data
@Schema(description = "延期申请DTO")
public class ApplyDelayDTO {

    @Schema(description = "热线系统工单ID")
    private String proWoId;

    @Schema(description = "热线系统工单编号")
    private String proWoCode;

    @Schema(description = "部门申请延期理由")
    private String content;

    @Schema(description = "申请时间：YYYY-MM-DD HH24:MI:SS")
    private String applyTime;

    @Schema(description = "第三方系统申请人帐号")
    private String applyUserNo;

    @Schema(description = "延期申请部门名称")
    private String applyOrgName;

    @Schema(description = "申请的处理期限")
    private String maxFldelay;

    @Schema(description = "申请的工作日")
    private String dateType;

}
