package org.springblade.ms.basic.enums;

public enum CstScope {
    CONVETIONAL_CIGARETTE_RETAIL("01", "卷烟零售"),
    SNUS_RETAIL("02", "雪茄烟零售"),
    CONFISCATED_TOBACCO_PRODUCTS_RETAIL("03", "罚没烟草制品零售"),
    PACKED_TOBACCO_THREADS("04", "有包装的烟丝");

    private final String code;
    private final String description;

    CstScope(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    // 根据code获取对应的枚举值
    public static CstScope fromCode(String code) {
        for (CstScope scope : CstScope.values()) {
            if (scope.getCode().equals(code)) {
                return scope;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }

    // 根据多个code获取对应的描述字符串
    public static String getDescriptionsFromCodes(String codes) {
        StringBuilder descriptions = new StringBuilder();
        String[] codeArray = codes.split(",");

        for (String code : codeArray) {
            code = code.trim();  // 去除可能存在的空格
            try {
                CstScope scope = fromCode(code);
                descriptions.append(scope.getDescription()).append(", ");
            } catch (IllegalArgumentException e) {
                System.out.println(e.getMessage());
            }
        }

        // 移除最后一个多余的逗号和空格
        if (!descriptions.isEmpty()) {
            descriptions.setLength(descriptions.length() - 2);
        }

        return descriptions.toString();
    }
}
