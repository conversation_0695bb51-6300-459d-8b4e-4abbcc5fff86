package org.springblade.ms.dingapp.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 坐标系转换工具类
 * 提供高德地图坐标系（GCJ-02）到天地图坐标系（CGCS2000/WGS84）的转换
 */
public class CoordinateTransformUtil {
    private static final double PI = 3.14159265358979324;
    private static final double X_PI = PI * 3000.0 / 180.0;
    private static final double A = 6378245.0;
    private static final double EE = 0.00669342162296594323;

    /**
     * 高德坐标系（GCJ-02）转天地图坐标系（CGCS2000/WGS84）
     * @param gcjLat GCJ-02纬度
     * @param gcjLng GCJ-02经度
     * @return BigDecimal[]{纬度, 经度}
     */
    public static BigDecimal[] gcj02ToCgcs2000(BigDecimal gcjLat, BigDecimal gcjLng) {
        if (gcjLat == null || gcjLng == null) {
            return new BigDecimal[]{gcjLat, gcjLng};
        }
        
        double lat = gcjLat.doubleValue();
        double lng = gcjLng.doubleValue();
        
        double dlat = transformLat(lng - 105.0, lat - 35.0);
        double dlng = transformLng(lng - 105.0, lat - 35.0);
        double radlat = lat / 180.0 * PI;
        double magic = Math.sin(radlat);
        magic = 1 - EE * magic * magic;
        double sqrtmagic = Math.sqrt(magic);
        dlat = (dlat * 180.0) / ((A * (1 - EE)) / (magic * sqrtmagic) * PI);
        dlng = (dlng * 180.0) / (A / sqrtmagic * Math.cos(radlat) * PI);
        
        double wgsLat = lat - dlat;
        double wgsLng = lng - dlng;
        
        return new BigDecimal[]{BigDecimal.valueOf(wgsLat).setScale(6, RoundingMode.HALF_UP),
                               BigDecimal.valueOf(wgsLng).setScale(6, RoundingMode.HALF_UP)};
    }

    private static double transformLat(double x, double y) {
        double ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * Math.sqrt(Math.abs(x));
        ret += (20.0 * Math.sin(6.0 * x * PI) + 20.0 * Math.sin(2.0 * x * PI)) * 2.0 / 3.0;
        ret += (20.0 * Math.sin(y * PI) + 40.0 * Math.sin(y / 3.0 * PI)) * 2.0 / 3.0;
        ret += (160.0 * Math.sin(y / 12.0 * PI) + 320 * Math.sin(y * PI / 30.0)) * 2.0 / 3.0;
        return ret;
    }

    private static double transformLng(double x, double y) {
        double ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * Math.sqrt(Math.abs(x));
        ret += (20.0 * Math.sin(6.0 * x * PI) + 20.0 * Math.sin(2.0 * x * PI)) * 2.0 / 3.0;
        ret += (20.0 * Math.sin(x * PI) + 40.0 * Math.sin(x / 3.0 * PI)) * 2.0 / 3.0;
        ret += (150.0 * Math.sin(x / 12.0 * PI) + 300.0 * Math.sin(x / 30.0 * PI)) * 2.0 / 3.0;
        return ret;
    }
}