package org.springblade.ms.dingapp.controller;

import lombok.AllArgsConstructor;
import org.springblade.core.tool.api.R;
import org.springblade.ms.basic.pojo.entity.ProductInfoEntity;
import org.springblade.ms.basic.service.IProductInfoService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@AllArgsConstructor
@RequestMapping("/dingapp/productInfo")
public class DingProductInfoController {
    private final IProductInfoService productInfoService;
    @GetMapping("/getSelectionList")
    public R<List<ProductInfoEntity>> getSelectionList(@RequestParam("name") String name) {
        return R.data(productInfoService.getSelectionList(name));
    }

}
