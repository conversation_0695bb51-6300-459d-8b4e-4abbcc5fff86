import 'dingtalk-jsapi/entry/mobile';

App({
    globalData: {
        userInfo: null,
        accessToken: null,
        refreshToken: null,
        isLogin: false,
        currentTab: 'map',
    },
    onLaunch(options) {
        // 第一次打开
        // options.query == {number:1}
        console.info('App onLaunch');
        checkUpdateVersion();
    },
    onShow(options) {
        // 从后台被 scheme 重新打开
        // options.query == {number:1}
    },
});


// 检查小程序是否有新版本更新
function checkUpdateVersion() {
    const updateManager = dd.getUpdateManager();
    updateManager.onUpdateReady(function () {
        dd.showModal({
			title: '更新提示',
			content: '发现新版本，是否重启应用？',
			success: function (res) {
				if (res.confirm) {
					// 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
					updateManager.applyUpdate();
				}
			}
		});
    });
}