/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.ms.basic.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springblade.ms.basic.pojo.entity.ProductInfoEntity;
import org.springblade.ms.basic.pojo.vo.ProductInfoVO;
import org.springblade.ms.basic.excel.ProductInfoExcel;
import org.springblade.ms.basic.mapper.ProductInfoMapper;
import org.springblade.ms.basic.service.IProductInfoService;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * 品规信息 服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Service
public class ProductInfoServiceImpl extends BaseServiceImpl<ProductInfoMapper, ProductInfoEntity> implements IProductInfoService {

	@Override
	public IPage<ProductInfoVO> selectProductInfoPage(IPage<ProductInfoVO> page, ProductInfoVO productInfo) {
		return page.setRecords(baseMapper.selectProductInfoPage(page, productInfo));
	}


	@Override
	public List<ProductInfoExcel> exportProductInfo(Wrapper<ProductInfoEntity> queryWrapper) {
		List<ProductInfoExcel> productInfoList = baseMapper.exportProductInfo(queryWrapper);
		//productInfoList.forEach(productInfo -> {
		//	productInfo.setTypeName(DictCache.getValue(DictEnum.YES_NO, ProductInfo.getType()));
		//});
		return productInfoList;
	}

	@Override
	@Cacheable(cacheNames = "ms:productInfo", key = "'getById:' + #id")
	public ProductInfoEntity getById(Serializable id) {
		return super.getById(id);
	}

	@Override
	@Cacheable(cacheNames = "ms:productInfo", key = "'getSelectionList:' + #name", unless = "#result.isEmpty()")
	public List<ProductInfoEntity> getSelectionList(String name) {
		Page<ProductInfoVO> page = new Page<>();
		page.setSize(50);
		page.setCurrent(1);
		List<ProductInfoEntity> result = baseMapper.getSelectionList(page, name);
		return CollectionUtils.isEmpty(result) ? Collections.emptyList() : result;
	}

}
