import httpApi from "/utils/http/httpApi";

Page({
  data: {
    title:"前柜分析",
    imgurl:'',
  
    throttleFlag: false, // 节流标志
    collisionResult:[
      // {
      //   name:"芙蓉王",
      //   type:"正常"
      // },

    ],
    collistionColumn:[
      {
        title: '品规码',
        dataIndex: 'itemCode',
        key: 'name',
        width: 180,
      },
      {
        title: '品规',
        dataIndex: 'itemName',
        key: 'name',
        width: 300,
      },
      {
        title: '类别',
        dataIndex: 'collisionType',
        key: 'name',
        width: 180,
      },
    ],
    identificationResult:[],
    identificationColumn:[
      {
        title: '品规码',
        dataIndex: 'itemCode',
        key: 'name',
        width: 200,
      },
      {
        title: '品规',
        dataIndex: 'itemName',
        key: 'name',
        width: 500,
      }
    ],
    identifyId:'',
    options:[{ value: false, label: '否' },{ value: true, label: '是' }],
    checked:false,
  },
  onLoad(options) {
    dd.setNavigationBar({
      title: '前柜分析',
    });
    console.log(options)
    if(options&&options.url){
      this.setData({
        imgurl:options.url,
        identifyId:options.id
      });
      this.getDataList();
      this.getIdentifyDetail();
    }
  },

  async getDataList(){
    const res = await httpApi.request({
      url: `/api/dingapp/itemIdentifyResults/list`,
      method: 'get',
      params:{
        identifyId:this.data.identifyId,
      }
    })
    this.setData({
      identificationResult:res.data
    })
    let errorData = res.data;
      errorData.sort((a, b) => {
        if (a.collisionType > b.collisionType) {
          return -1;
        }
        if (a.collisionType < b.collisionType) {
          return 1; // a 排在 b 之后
        }
        // collision_type 相同，按 item_code 排序
        if (a.itemCode < b.itemCode) {
          return -1;
        }
        if (a.itemCode > b.itemCode) {
          return 1;
        }
        return 0; // a 和 b 相等
      });
      console.log(errorData)
      this.setData({collisionResult:errorData})
  },
  
  async getIdentifyDetail(){
    const res = await httpApi.request({
      url: `/api/dingapp/itemIdentify/detail`,
      method: 'get',
      params:{
        id:this.data.identifyId,
      }
    })
    if(res.data){
      this.setData({
        checked:res.data.isRecognitionBad
      })
    }
  },

  async checkOnChange(e){
    if (this.data.throttleFlag) {
      return; 
    }

    this.setData({
      checked: e,
      throttleFlag: true, 
    });
    console.log('开关状态改变：', e);
    let res = await httpApi.request({
      url: `/api/dingapp/itemIdentify/updateIsBad`,
      method: 'get',
      params:{
        id:this.data.identifyId,
        isRecognitionBad:e,
      }
    })
    setTimeout(() => {
      this.setData({
        throttleFlag: false, 
      });
    }, 500); // 节流时间间隔，例如 500 毫秒
  }

});
