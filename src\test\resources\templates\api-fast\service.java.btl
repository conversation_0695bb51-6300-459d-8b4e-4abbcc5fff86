/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package ${package.Service!};

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import ${packageName!}.pojo.entity.${entityKey!}Entity;
import ${packageName!}.pojo.vo.${entityKey!}VO;
import ${packageName!}.excel.${entityKey!}Excel;
import com.baomidou.mybatisplus.core.metadata.IPage;
#if(hasSuperEntity){
import ${superServiceClassPackage!};
#}else{
import com.baomidou.mybatisplus.extension.service.IService;
#}
import java.util.List;

/**
 * ${table.comment!} 服务类
 *
 * <AUTHOR>
 * @since ${date!}
 */
#if(hasSuperEntity){
public interface ${table.serviceName!} extends ${superServiceClass!}<${entity!}> {
#}else{
public interface ${table.serviceName!} extends IService<${entity!}> {
#}
	/**
	 * 自定义分页
	 *
	 * @param page 分页参数
	 * @param ${entityKeyPath!} 查询参数
	 * @return IPage<${entityKey!}VO>
	 */
	IPage<${entityKey!}VO> select${entityKey!}Page(IPage<${entityKey!}VO> page, ${entityKey!}VO ${entityKeyPath!});

	/**
	 * 导出数据
	 *
	 * @param queryWrapper 查询条件
	 * @return List<${entityKey!}Excel>
	 */
	List<${entityKey!}Excel> export${entityKey!}(Wrapper<${entityKey!}Entity> queryWrapper);

}
