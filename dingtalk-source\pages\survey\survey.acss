.mainApp {
  flex: 1 ;
  background-color: #F5F5F5;
}
.lsh_info_card{
  width: 94%;
  min-height: 22vh;
  background-color: white;
  border-radius: 15rpx;
  box-sizing: border-box;
  margin: 10px auto 0;
  padding: 16px;
  box-shadow: 0px 1px 2px rgba(0, 0, 0, .1);
}
.photo_card{
  width: 94%;
  min-height: 450rpx;
  background-color: white;
  border-radius: 15rpx;
  box-sizing: border-box;
  margin: 10px auto 0;
  padding: 16px;
  box-shadow: 0px 1px 2px rgba(0, 0, 0, .1);
}

.text_content{
  flex: 1;        /* 占据剩余空间 */
  min-width: 0;   /* 允许缩小，防止溢出 */
  white-space: normal; /* 允许自动换行 */
  word-break: break-all;
}

.survey-btn{
  margin-bottom: 5vh;
}

.mt10{
  margin-top: 10px;
}
.pt10{
  padding-top: 10px;
}
.pb10{
  padding-bottom: 10px;
}
.title{
  font-size: 18px;
  margin-bottom: 12px;
}
.blue{
  color: #4285F4;
}
.blod{
  font-weight: bold;
}

.text{
  margin:16px 0 0;
}

.flex{
  display: flex;
  align-items: flex-start;
}

.container-box > .ant-container-content{
  display: flex
}