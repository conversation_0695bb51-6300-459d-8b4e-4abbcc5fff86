package org.springblade.ms.priceStandards.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.ms.priceStandards.pojo.entity.MsPriceStandards;
import org.springblade.ms.priceStandards.service.MsPriceStandardsService;
import org.springblade.ms.priceStandards.mapper.MsPriceStandardsMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR> @description 针对表【ms_price_standards(涉案价格标准表)】的数据库操作Service实现
* @createDate 2025-04-10 09:46:03
*/
@Service
public class MsPriceStandardsServiceImpl extends BaseServiceImpl<MsPriceStandardsMapper, MsPriceStandards>
    implements MsPriceStandardsService{


}




