package org.springblade.ms.reportcomplaint.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 
 * @TableName ms_complaint_yhyt
 */
@TableName(value ="ms_complaint_yhyt")
@Data
public class ComplaintYhyt extends TenantEntity implements Serializable {
    /**
     * 一户一图id
     */
    private Long yhytId;

    /**
     * 零售户id
     */
    private Long complaintId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}