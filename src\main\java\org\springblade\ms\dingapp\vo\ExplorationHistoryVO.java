package org.springblade.ms.dingapp.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.ms.exploration.pojo.vo.ExplorationVO;

import java.io.Serial;

/**
 * 勘查历史记录视图对象，包含零售户信息
 *
 * <AUTHOR>
 * @since 2025-03-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ExplorationHistoryVO extends ExplorationVO {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 零售户名称
     */
    private String retailerName;

    /**
     * 零售户地址
     */
    private String retailerAddress;
    private String retailerLicNo;
}
