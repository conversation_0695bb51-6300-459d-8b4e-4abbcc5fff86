package org.springblade.ms.platform12345.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 诉求来源枚举 A5.1
 */
@Getter
@AllArgsConstructor
public enum AppealSourceEnum {

    /**
     * 门户网站
     */
    PORTAL_WEBSITE("1", "门户网站"),

    /**
     * 手机APP
     */
    MOBILE_APP("2", "手机APP"),

    /**
     * 微信
     */
    WECHAT("3", "微信"),

    /**
     * 现场上访
     */
    ON_SITE_VISIT("4", "现场上访"),

    /**
     * 信件
     */
    LETTER("5", "信件"),

    /**
     * 网络问政
     */
    ONLINE_POLITICS("6", "网络问政"),

    /**
     * 多媒体
     */
    MULTIMEDIA("7", "多媒体"),

    /**
     * 语音坐席
     */
    VOICE_SEAT("8", "语音坐席"),

    /**
     * 市长信箱
     */
    MAYOR_MAILBOX("9", "市长信箱"),

    /**
     * 网上办事大厅
     */
    ONLINE_SERVICE_HALL("10", "网上办事大厅"),

    /**
     * 市政府门户
     */
    GOVERNMENT_PORTAL("11", "市政府门户"),

    /**
     * 领导信箱
     */
    LEADER_MAILBOX("12", "领导信箱"),

    /**
     * 建言献策
     */
    SUGGESTIONS("13", "建言献策"),

    /**
     * 监控平台
     */
    MONITORING_PLATFORM("14", "监控平台"),

    /**
     * 粤省心
     */
    YUE_SHENG_XIN("15", "粤省心"),

    /**
     * 好差评
     */
    GOOD_BAD_REVIEW("16", "好差评");

    /**
     * 代码
     */
    private final String code;

    /**
     * 值
     */
    private final String value;

    /**
     * 根据代码获取枚举
     *
     * @param code 代码
     * @return 枚举
     */
    public static AppealSourceEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (AppealSourceEnum item : AppealSourceEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 根据代码获取值
     *
     * @param code 代码
     * @return 值
     */
    public static String getValueByCode(String code) {
        AppealSourceEnum item = getByCode(code);
        return item != null ? item.getValue() : null;
    }

    /**
     * 根据值获取枚举
     *
     * @param value 值
     * @return 枚举
     */
    public static AppealSourceEnum getByValue(String value) {
        if (value == null) {
            return null;
        }
        for (AppealSourceEnum item : AppealSourceEnum.values()) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 根据值获取代码
     *
     * @param value 值
     * @return 代码
     */
    public static String getCodeByValue(String value) {
        AppealSourceEnum item = getByValue(value);
        return item != null ? item.getCode() : null;
    }
}
