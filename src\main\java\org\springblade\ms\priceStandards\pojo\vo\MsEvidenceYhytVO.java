package org.springblade.ms.priceStandards.pojo.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.ms.priceStandards.pojo.entity.MsEvidenceYhyt;

import java.io.Serial;

@Data
@EqualsAndHashCode(callSuper = true)
public class MsEvidenceYhytVO extends MsEvidenceYhyt {
    @Serial
    private static final long serialVersionUID = 1L;
    private String searchText;
    private String companyName;
    private String licNO;
    private Long packageQty2;

}
