/*
 Navicat Premium Data Transfer

 Source Server         : oracle_localhost
 Source Server Type    : Oracle
 Source Server Version : 110200
 Source Host           : 127.0.0.1:1521
 Source Schema         : BLADEX

 Target Server Type    : Oracle
 Target Server Version : 110200
 File Encoding         : 65001

 Date: 15/03/2024 01:16:45
*/


-- ----------------------------
-- Table structure for ACT_APP_APPDEF
-- ----------------------------
-- DROP TABLE "ACT_APP_APPDEF";
CREATE TABLE "ACT_APP_APPDEF" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL,
  "REV_" NUMBER NOT NULL,
  "NAME_" VARCHAR2(255 BYTE),
  "KEY_" VARCHAR2(255 BYTE) NOT NULL,
  "VERSION_" NUMBER NOT NULL,
  "CATEGORY_" VARCHAR2(255 BYTE),
  "DEPLOYMENT_ID_" VARCHAR2(255 BYTE),
  "RESOURCE_NAME_" VARCHAR2(4000 BYTE),
  "DESCRIPTION_" VARCHAR2(4000 BYTE),
  "TENANT_ID_" VARCHAR2(255 BYTE) DEFAULT ''
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_APP_APPDEF
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_APP_DATABASECHANGELOG
-- ----------------------------
-- DROP TABLE "ACT_APP_DATABASECHANGELOG";
CREATE TABLE "ACT_APP_DATABASECHANGELOG" (
  "ID" VARCHAR2(255 BYTE) NOT NULL,
  "AUTHOR" VARCHAR2(255 BYTE) NOT NULL,
  "FILENAME" VARCHAR2(255 BYTE) NOT NULL,
  "DATEEXECUTED" TIMESTAMP(6) NOT NULL,
  "ORDEREXECUTED" NUMBER NOT NULL,
  "EXECTYPE" VARCHAR2(10 BYTE) NOT NULL,
  "MD5SUM" VARCHAR2(35 BYTE),
  "DESCRIPTION" VARCHAR2(255 BYTE),
  "COMMENTS" VARCHAR2(255 BYTE),
  "TAG" VARCHAR2(255 BYTE),
  "LIQUIBASE" VARCHAR2(20 BYTE),
  "CONTEXTS" VARCHAR2(255 BYTE),
  "LABELS" VARCHAR2(255 BYTE),
  "DEPLOYMENT_ID" VARCHAR2(10 BYTE)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_APP_DATABASECHANGELOG
-- ----------------------------
INSERT INTO "ACT_APP_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") VALUES ('1', 'flowable', 'org/flowable/app/db/liquibase/flowable-app-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:02.893666', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '1', 'EXECUTED', '9:959783069c0c7ce80320a0617aa48969', 'createTable tableName=ACT_APP_DEPLOYMENT; createTable tableName=ACT_APP_DEPLOYMENT_RESOURCE; addForeignKeyConstraint baseTableName=ACT_APP_DEPLOYMENT_RESOURCE, constraintName=ACT_FK_APP_RSRC_DPL, referencedTableName=ACT_APP_DEPLOYMENT; createIndex...', NULL, NULL, '3.5.3', NULL, NULL, '9500486764');
INSERT INTO "ACT_APP_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") VALUES ('3', 'flowable', 'org/flowable/app/db/liquibase/flowable-app-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:03.090284', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '2', 'EXECUTED', '9:c05b79a3b00e95136533085718361208', 'createIndex indexName=ACT_IDX_APP_DEF_UNIQ, tableName=ACT_APP_APPDEF', NULL, NULL, '3.5.3', NULL, NULL, '9500486764');
COMMIT;

-- ----------------------------
-- Table structure for ACT_APP_DATABASECHANGELOGLOCK
-- ----------------------------
-- DROP TABLE "ACT_APP_DATABASECHANGELOGLOCK";
CREATE TABLE "ACT_APP_DATABASECHANGELOGLOCK" (
  "ID" NUMBER NOT NULL,
  "LOCKED" NUMBER(1,0) NOT NULL,
  "LOCKGRANTED" TIMESTAMP(6),
  "LOCKEDBY" VARCHAR2(255 BYTE)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_APP_DATABASECHANGELOGLOCK
-- ----------------------------
INSERT INTO "ACT_APP_DATABASECHANGELOGLOCK" ("ID", "LOCKED", "LOCKGRANTED", "LOCKEDBY") VALUES ('1', '0', NULL, NULL);
COMMIT;

-- ----------------------------
-- Table structure for ACT_APP_DEPLOYMENT
-- ----------------------------
-- DROP TABLE "ACT_APP_DEPLOYMENT";
CREATE TABLE "ACT_APP_DEPLOYMENT" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL,
  "NAME_" VARCHAR2(255 BYTE),
  "CATEGORY_" VARCHAR2(255 BYTE),
  "KEY_" VARCHAR2(255 BYTE),
  "DEPLOY_TIME_" TIMESTAMP(6),
  "TENANT_ID_" VARCHAR2(255 BYTE) DEFAULT ''
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_APP_DEPLOYMENT
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_APP_DEPLOYMENT_RESOURCE
-- ----------------------------
-- DROP TABLE "ACT_APP_DEPLOYMENT_RESOURCE";
CREATE TABLE "ACT_APP_DEPLOYMENT_RESOURCE" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL,
  "NAME_" VARCHAR2(255 BYTE),
  "DEPLOYMENT_ID_" VARCHAR2(255 BYTE),
  "RESOURCE_BYTES_" BLOB
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_APP_DEPLOYMENT_RESOURCE
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_CMMN_CASEDEF
-- ----------------------------
-- DROP TABLE "ACT_CMMN_CASEDEF";
CREATE TABLE "ACT_CMMN_CASEDEF" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL,
  "REV_" NUMBER NOT NULL,
  "NAME_" VARCHAR2(255 BYTE),
  "KEY_" VARCHAR2(255 BYTE) NOT NULL,
  "VERSION_" NUMBER NOT NULL,
  "CATEGORY_" VARCHAR2(255 BYTE),
  "DEPLOYMENT_ID_" VARCHAR2(255 BYTE),
  "RESOURCE_NAME_" VARCHAR2(4000 BYTE),
  "DESCRIPTION_" VARCHAR2(4000 BYTE),
  "HAS_GRAPHICAL_NOTATION_" NUMBER(1,0),
  "TENANT_ID_" VARCHAR2(255 BYTE) DEFAULT '',
  "DGRM_RESOURCE_NAME_" VARCHAR2(4000 BYTE),
  "HAS_START_FORM_KEY_" NUMBER(1,0)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_CMMN_CASEDEF
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_CMMN_DATABASECHANGELOG
-- ----------------------------
-- DROP TABLE "ACT_CMMN_DATABASECHANGELOG";
CREATE TABLE "ACT_CMMN_DATABASECHANGELOG" (
  "ID" VARCHAR2(255 BYTE) NOT NULL,
  "AUTHOR" VARCHAR2(255 BYTE) NOT NULL,
  "FILENAME" VARCHAR2(255 BYTE) NOT NULL,
  "DATEEXECUTED" TIMESTAMP(6) NOT NULL,
  "ORDEREXECUTED" NUMBER NOT NULL,
  "EXECTYPE" VARCHAR2(10 BYTE) NOT NULL,
  "MD5SUM" VARCHAR2(35 BYTE),
  "DESCRIPTION" VARCHAR2(255 BYTE),
  "COMMENTS" VARCHAR2(255 BYTE),
  "TAG" VARCHAR2(255 BYTE),
  "LIQUIBASE" VARCHAR2(20 BYTE),
  "CONTEXTS" VARCHAR2(255 BYTE),
  "LABELS" VARCHAR2(255 BYTE),
  "DEPLOYMENT_ID" VARCHAR2(10 BYTE)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_CMMN_DATABASECHANGELOG
-- ----------------------------
INSERT INTO "ACT_CMMN_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") VALUES ('8', 'flowable', 'org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml', TO_TIMESTAMP('2024-03-15 01:15:34.302026', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '7', 'EXECUTED', '9:eda5e43816221f2d8554bfcc90f1c37e', 'addColumn tableName=ACT_CMMN_HI_PLAN_ITEM_INST', NULL, NULL, '4.24.0', NULL, NULL, '0436534181');
INSERT INTO "ACT_CMMN_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") VALUES ('9', 'flowable', 'org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml', TO_TIMESTAMP('2024-03-15 01:15:34.333359', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '8', 'EXECUTED', '9:c34685611779075a73caf8c380f078ea', 'addColumn tableName=ACT_CMMN_RU_PLAN_ITEM_INST; addColumn tableName=ACT_CMMN_HI_PLAN_ITEM_INST', NULL, NULL, '4.24.0', NULL, NULL, '0436534181');
INSERT INTO "ACT_CMMN_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") VALUES ('10', 'flowable', 'org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml', TO_TIMESTAMP('2024-03-15 01:15:34.407269', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '9', 'EXECUTED', '9:368e9472ad2348206205170d6c52d58e', 'addColumn tableName=ACT_CMMN_RU_CASE_INST; addColumn tableName=ACT_CMMN_RU_CASE_INST; createIndex indexName=ACT_IDX_CASE_INST_REF_ID_, tableName=ACT_CMMN_RU_CASE_INST; addColumn tableName=ACT_CMMN_HI_CASE_INST; addColumn tableName=ACT_CMMN_HI_CASE...', NULL, NULL, '4.24.0', NULL, NULL, '0436534181');
INSERT INTO "ACT_CMMN_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") VALUES ('11', 'flowable', 'org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml', TO_TIMESTAMP('2024-03-15 01:15:34.459647', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '10', 'EXECUTED', '9:e54b50ceb2bcd5355ae4dfb56d9ff3ad', 'addColumn tableName=ACT_CMMN_RU_PLAN_ITEM_INST; addColumn tableName=ACT_CMMN_HI_PLAN_ITEM_INST', NULL, NULL, '4.24.0', NULL, NULL, '0436534181');
INSERT INTO "ACT_CMMN_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") VALUES ('12', 'flowable', 'org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml', TO_TIMESTAMP('2024-03-15 01:15:34.477268', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '11', 'EXECUTED', '9:f53f262768d04e74529f43fcd93429b0', 'addColumn tableName=ACT_CMMN_RU_CASE_INST', NULL, NULL, '4.24.0', NULL, NULL, '0436534181');
INSERT INTO "ACT_CMMN_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") VALUES ('13', 'flowable', 'org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml', TO_TIMESTAMP('2024-03-15 01:15:34.505746', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '12', 'EXECUTED', '9:64e7eafbe97997094654e83caea99895', 'addColumn tableName=ACT_CMMN_RU_PLAN_ITEM_INST; addColumn tableName=ACT_CMMN_HI_PLAN_ITEM_INST', NULL, NULL, '4.24.0', NULL, NULL, '0436534181');
INSERT INTO "ACT_CMMN_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") VALUES ('14', 'flowable', 'org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml', TO_TIMESTAMP('2024-03-15 01:15:34.556427', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '13', 'EXECUTED', '9:ab7d934abde497eac034701542e0a281', 'addColumn tableName=ACT_CMMN_RU_CASE_INST; addColumn tableName=ACT_CMMN_HI_CASE_INST', NULL, NULL, '4.24.0', NULL, NULL, '0436534181');
INSERT INTO "ACT_CMMN_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") VALUES ('16', 'flowable', 'org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml', TO_TIMESTAMP('2024-03-15 01:15:34.594593', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '14', 'EXECUTED', '9:03928d422e510959770e7a9daa5a993f', 'addColumn tableName=ACT_CMMN_RU_CASE_INST; addColumn tableName=ACT_CMMN_HI_CASE_INST', NULL, NULL, '4.24.0', NULL, NULL, '0436534181');
INSERT INTO "ACT_CMMN_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") VALUES ('17', 'flowable', 'org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml', TO_TIMESTAMP('2024-03-15 01:15:34.612429', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '15', 'EXECUTED', '9:f30304cf001d6eac78c793ea88cd5781', 'createIndex indexName=ACT_IDX_HI_CASE_INST_END, tableName=ACT_CMMN_HI_CASE_INST', NULL, NULL, '4.24.0', NULL, NULL, '0436534181');
INSERT INTO "ACT_CMMN_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") VALUES ('18', 'flowable', 'org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml', TO_TIMESTAMP('2024-03-15 01:15:34.649692', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '16', 'EXECUTED', '9:d782865087d6c0c3dc033ac20e783008', 'createIndex indexName=ACT_IDX_HI_PLAN_ITEM_INST_CASE, tableName=ACT_CMMN_HI_PLAN_ITEM_INST', NULL, NULL, '4.24.0', NULL, NULL, '0436534181');
INSERT INTO "ACT_CMMN_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") VALUES ('1', 'flowable', 'org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:05.572529', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '1', 'EXECUTED', '9:d0cc0aaadf0e4ef70c5b412cd05fadc4', 'createTable tableName=ACT_CMMN_DEPLOYMENT; createTable tableName=ACT_CMMN_DEPLOYMENT_RESOURCE; addForeignKeyConstraint baseTableName=ACT_CMMN_DEPLOYMENT_RESOURCE, constraintName=ACT_FK_CMMN_RSRC_DPL, referencedTableName=ACT_CMMN_DEPLOYMENT; create...', NULL, NULL, '3.5.3', NULL, NULL, '9500487786');
INSERT INTO "ACT_CMMN_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") VALUES ('2', 'flowable', 'org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:06.062159', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '2', 'EXECUTED', '9:8095a5a8a222a100c2d0310cacbda5e7', 'addColumn tableName=ACT_CMMN_CASEDEF; addColumn tableName=ACT_CMMN_DEPLOYMENT_RESOURCE; addColumn tableName=ACT_CMMN_RU_CASE_INST; addColumn tableName=ACT_CMMN_RU_PLAN_ITEM_INST', NULL, NULL, '3.5.3', NULL, NULL, '9500487786');
INSERT INTO "ACT_CMMN_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") VALUES ('3', 'flowable', 'org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:06.474729', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '3', 'EXECUTED', '9:f031b4f0ae67bc5a640736b379049b12', 'addColumn tableName=ACT_CMMN_RU_PLAN_ITEM_INST; addColumn tableName=ACT_CMMN_RU_CASE_INST; createIndex indexName=ACT_IDX_PLAN_ITEM_STAGE_INST, tableName=ACT_CMMN_RU_PLAN_ITEM_INST; addColumn tableName=ACT_CMMN_RU_PLAN_ITEM_INST; addColumn tableNam...', NULL, NULL, '3.5.3', NULL, NULL, '9500487786');
INSERT INTO "ACT_CMMN_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") VALUES ('4', 'flowable', 'org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:06.633601', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '4', 'EXECUTED', '9:c484ecfb08719feccac2f80fc962dda9', 'createTable tableName=ACT_CMMN_HI_PLAN_ITEM_INST; addColumn tableName=ACT_CMMN_RU_MIL_INST; addColumn tableName=ACT_CMMN_HI_MIL_INST', NULL, NULL, '3.5.3', NULL, NULL, '9500487786');
INSERT INTO "ACT_CMMN_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") VALUES ('6', 'flowable', 'org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:06.752638', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '5', 'EXECUTED', '9:7343ab247d959e5add9278b5386de833', 'createIndex indexName=ACT_IDX_CASE_DEF_UNIQ, tableName=ACT_CMMN_CASEDEF', NULL, NULL, '3.5.3', NULL, NULL, '9500487786');
INSERT INTO "ACT_CMMN_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") VALUES ('7', 'flowable', 'org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:07.919259', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '6', 'EXECUTED', '9:d73200db684b6cdb748cc03570d5d2e9', 'renameColumn newColumnName=CREATE_TIME_, oldColumnName=START_TIME_, tableName=ACT_CMMN_RU_PLAN_ITEM_INST; renameColumn newColumnName=CREATE_TIME_, oldColumnName=CREATED_TIME_, tableName=ACT_CMMN_HI_PLAN_ITEM_INST; addColumn tableName=ACT_CMMN_RU_P...', NULL, NULL, '3.5.3', NULL, NULL, '9500487786');
COMMIT;

-- ----------------------------
-- Table structure for ACT_CMMN_DATABASECHANGELOGLOCK
-- ----------------------------
-- DROP TABLE "ACT_CMMN_DATABASECHANGELOGLOCK";
CREATE TABLE "ACT_CMMN_DATABASECHANGELOGLOCK" (
  "ID" NUMBER NOT NULL,
  "LOCKED" NUMBER(1,0) NOT NULL,
  "LOCKGRANTED" TIMESTAMP(6),
  "LOCKEDBY" VARCHAR2(255 BYTE)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_CMMN_DATABASECHANGELOGLOCK
-- ----------------------------
INSERT INTO "ACT_CMMN_DATABASECHANGELOGLOCK" ("ID", "LOCKED", "LOCKGRANTED", "LOCKEDBY") VALUES ('1', '0', NULL, NULL);
COMMIT;

-- ----------------------------
-- Table structure for ACT_CMMN_DEPLOYMENT
-- ----------------------------
-- DROP TABLE "ACT_CMMN_DEPLOYMENT";
CREATE TABLE "ACT_CMMN_DEPLOYMENT" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL,
  "NAME_" VARCHAR2(255 BYTE),
  "CATEGORY_" VARCHAR2(255 BYTE),
  "KEY_" VARCHAR2(255 BYTE),
  "DEPLOY_TIME_" TIMESTAMP(6),
  "PARENT_DEPLOYMENT_ID_" VARCHAR2(255 BYTE),
  "TENANT_ID_" VARCHAR2(255 BYTE) DEFAULT ''
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_CMMN_DEPLOYMENT
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_CMMN_DEPLOYMENT_RESOURCE
-- ----------------------------
-- DROP TABLE "ACT_CMMN_DEPLOYMENT_RESOURCE";
CREATE TABLE "ACT_CMMN_DEPLOYMENT_RESOURCE" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL,
  "NAME_" VARCHAR2(255 BYTE),
  "DEPLOYMENT_ID_" VARCHAR2(255 BYTE),
  "RESOURCE_BYTES_" BLOB,
  "GENERATED_" NUMBER(1,0)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_CMMN_DEPLOYMENT_RESOURCE
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_CMMN_HI_CASE_INST
-- ----------------------------
-- DROP TABLE "ACT_CMMN_HI_CASE_INST";
CREATE TABLE "ACT_CMMN_HI_CASE_INST" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL,
  "REV_" NUMBER NOT NULL,
  "BUSINESS_KEY_" VARCHAR2(255 BYTE),
  "NAME_" VARCHAR2(255 BYTE),
  "PARENT_ID_" VARCHAR2(255 BYTE),
  "CASE_DEF_ID_" VARCHAR2(255 BYTE),
  "STATE_" VARCHAR2(255 BYTE),
  "START_TIME_" TIMESTAMP(6),
  "END_TIME_" TIMESTAMP(6),
  "START_USER_ID_" VARCHAR2(255 BYTE),
  "CALLBACK_ID_" VARCHAR2(255 BYTE),
  "CALLBACK_TYPE_" VARCHAR2(255 BYTE),
  "TENANT_ID_" VARCHAR2(255 BYTE) DEFAULT '',
  "REFERENCE_ID_" VARCHAR2(255 BYTE),
  "REFERENCE_TYPE_" VARCHAR2(255 BYTE),
  "LAST_REACTIVATION_TIME_" TIMESTAMP(3),
  "LAST_REACTIVATION_USER_ID_" VARCHAR2(255 BYTE),
  "BUSINESS_STATUS_" VARCHAR2(255 BYTE)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_CMMN_HI_CASE_INST
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_CMMN_HI_MIL_INST
-- ----------------------------
-- DROP TABLE "ACT_CMMN_HI_MIL_INST";
CREATE TABLE "ACT_CMMN_HI_MIL_INST" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL,
  "REV_" NUMBER NOT NULL,
  "NAME_" VARCHAR2(255 BYTE) NOT NULL,
  "TIME_STAMP_" TIMESTAMP(6) NOT NULL,
  "CASE_INST_ID_" VARCHAR2(255 BYTE) NOT NULL,
  "CASE_DEF_ID_" VARCHAR2(255 BYTE) NOT NULL,
  "ELEMENT_ID_" VARCHAR2(255 BYTE) NOT NULL,
  "TENANT_ID_" VARCHAR2(255 BYTE) DEFAULT ''
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_CMMN_HI_MIL_INST
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_CMMN_HI_PLAN_ITEM_INST
-- ----------------------------
-- DROP TABLE "ACT_CMMN_HI_PLAN_ITEM_INST";
CREATE TABLE "ACT_CMMN_HI_PLAN_ITEM_INST" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL,
  "REV_" NUMBER NOT NULL,
  "NAME_" VARCHAR2(255 BYTE),
  "STATE_" VARCHAR2(255 BYTE),
  "CASE_DEF_ID_" VARCHAR2(255 BYTE),
  "CASE_INST_ID_" VARCHAR2(255 BYTE),
  "STAGE_INST_ID_" VARCHAR2(255 BYTE),
  "IS_STAGE_" NUMBER(1,0),
  "ELEMENT_ID_" VARCHAR2(255 BYTE),
  "ITEM_DEFINITION_ID_" VARCHAR2(255 BYTE),
  "ITEM_DEFINITION_TYPE_" VARCHAR2(255 BYTE),
  "CREATE_TIME_" TIMESTAMP(6),
  "LAST_AVAILABLE_TIME_" TIMESTAMP(6),
  "LAST_ENABLED_TIME_" TIMESTAMP(6),
  "LAST_DISABLED_TIME_" TIMESTAMP(6),
  "LAST_STARTED_TIME_" TIMESTAMP(6),
  "LAST_SUSPENDED_TIME_" TIMESTAMP(6),
  "COMPLETED_TIME_" TIMESTAMP(6),
  "OCCURRED_TIME_" TIMESTAMP(6),
  "TERMINATED_TIME_" TIMESTAMP(6),
  "EXIT_TIME_" TIMESTAMP(6),
  "ENDED_TIME_" TIMESTAMP(6),
  "LAST_UPDATED_TIME_" TIMESTAMP(6),
  "START_USER_ID_" VARCHAR2(255 BYTE),
  "REFERENCE_ID_" VARCHAR2(255 BYTE),
  "REFERENCE_TYPE_" VARCHAR2(255 BYTE),
  "TENANT_ID_" VARCHAR2(255 BYTE) DEFAULT '',
  "ENTRY_CRITERION_ID_" VARCHAR2(255 BYTE),
  "EXIT_CRITERION_ID_" VARCHAR2(255 BYTE),
  "SHOW_IN_OVERVIEW_" NUMBER(1,0),
  "EXTRA_VALUE_" VARCHAR2(255 BYTE),
  "DERIVED_CASE_DEF_ID_" VARCHAR2(255 BYTE),
  "LAST_UNAVAILABLE_TIME_" TIMESTAMP(3)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_CMMN_HI_PLAN_ITEM_INST
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_CMMN_RU_CASE_INST
-- ----------------------------
-- DROP TABLE "ACT_CMMN_RU_CASE_INST";
CREATE TABLE "ACT_CMMN_RU_CASE_INST" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL,
  "REV_" NUMBER NOT NULL,
  "BUSINESS_KEY_" VARCHAR2(255 BYTE),
  "NAME_" VARCHAR2(255 BYTE),
  "PARENT_ID_" VARCHAR2(255 BYTE),
  "CASE_DEF_ID_" VARCHAR2(255 BYTE),
  "STATE_" VARCHAR2(255 BYTE),
  "START_TIME_" TIMESTAMP(6),
  "START_USER_ID_" VARCHAR2(255 BYTE),
  "CALLBACK_ID_" VARCHAR2(255 BYTE),
  "CALLBACK_TYPE_" VARCHAR2(255 BYTE),
  "TENANT_ID_" VARCHAR2(255 BYTE) DEFAULT '',
  "LOCK_TIME_" TIMESTAMP(6),
  "IS_COMPLETEABLE_" NUMBER(1,0),
  "REFERENCE_ID_" VARCHAR2(255 BYTE),
  "REFERENCE_TYPE_" VARCHAR2(255 BYTE),
  "LOCK_OWNER_" VARCHAR2(255 BYTE),
  "LAST_REACTIVATION_TIME_" TIMESTAMP(3),
  "LAST_REACTIVATION_USER_ID_" VARCHAR2(255 BYTE),
  "BUSINESS_STATUS_" VARCHAR2(255 BYTE)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_CMMN_RU_CASE_INST
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_CMMN_RU_MIL_INST
-- ----------------------------
-- DROP TABLE "ACT_CMMN_RU_MIL_INST";
CREATE TABLE "ACT_CMMN_RU_MIL_INST" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL,
  "NAME_" VARCHAR2(255 BYTE) NOT NULL,
  "TIME_STAMP_" TIMESTAMP(6) NOT NULL,
  "CASE_INST_ID_" VARCHAR2(255 BYTE) NOT NULL,
  "CASE_DEF_ID_" VARCHAR2(255 BYTE) NOT NULL,
  "ELEMENT_ID_" VARCHAR2(255 BYTE) NOT NULL,
  "TENANT_ID_" VARCHAR2(255 BYTE) DEFAULT ''
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_CMMN_RU_MIL_INST
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_CMMN_RU_PLAN_ITEM_INST
-- ----------------------------
-- DROP TABLE "ACT_CMMN_RU_PLAN_ITEM_INST";
CREATE TABLE "ACT_CMMN_RU_PLAN_ITEM_INST" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL,
  "REV_" NUMBER NOT NULL,
  "CASE_DEF_ID_" VARCHAR2(255 BYTE),
  "CASE_INST_ID_" VARCHAR2(255 BYTE),
  "STAGE_INST_ID_" VARCHAR2(255 BYTE),
  "IS_STAGE_" NUMBER(1,0),
  "ELEMENT_ID_" VARCHAR2(255 BYTE),
  "NAME_" VARCHAR2(255 BYTE),
  "STATE_" VARCHAR2(255 BYTE),
  "CREATE_TIME_" TIMESTAMP(6),
  "START_USER_ID_" VARCHAR2(255 BYTE),
  "REFERENCE_ID_" VARCHAR2(255 BYTE),
  "REFERENCE_TYPE_" VARCHAR2(255 BYTE),
  "TENANT_ID_" VARCHAR2(255 BYTE) DEFAULT '',
  "ITEM_DEFINITION_ID_" VARCHAR2(255 BYTE),
  "ITEM_DEFINITION_TYPE_" VARCHAR2(255 BYTE),
  "IS_COMPLETEABLE_" NUMBER(1,0),
  "IS_COUNT_ENABLED_" NUMBER(1,0),
  "VAR_COUNT_" NUMBER,
  "SENTRY_PART_INST_COUNT_" NUMBER,
  "LAST_AVAILABLE_TIME_" TIMESTAMP(3),
  "LAST_ENABLED_TIME_" TIMESTAMP(3),
  "LAST_DISABLED_TIME_" TIMESTAMP(3),
  "LAST_STARTED_TIME_" TIMESTAMP(3),
  "LAST_SUSPENDED_TIME_" TIMESTAMP(3),
  "COMPLETED_TIME_" TIMESTAMP(3),
  "OCCURRED_TIME_" TIMESTAMP(3),
  "TERMINATED_TIME_" TIMESTAMP(3),
  "EXIT_TIME_" TIMESTAMP(3),
  "ENDED_TIME_" TIMESTAMP(3),
  "ENTRY_CRITERION_ID_" VARCHAR2(255 BYTE),
  "EXIT_CRITERION_ID_" VARCHAR2(255 BYTE),
  "EXTRA_VALUE_" VARCHAR2(255 BYTE),
  "DERIVED_CASE_DEF_ID_" VARCHAR2(255 BYTE),
  "LAST_UNAVAILABLE_TIME_" TIMESTAMP(3)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_CMMN_RU_PLAN_ITEM_INST
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_CMMN_RU_SENTRY_PART_INST
-- ----------------------------
-- DROP TABLE "ACT_CMMN_RU_SENTRY_PART_INST";
CREATE TABLE "ACT_CMMN_RU_SENTRY_PART_INST" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL,
  "REV_" NUMBER NOT NULL,
  "CASE_DEF_ID_" VARCHAR2(255 BYTE),
  "CASE_INST_ID_" VARCHAR2(255 BYTE),
  "PLAN_ITEM_INST_ID_" VARCHAR2(255 BYTE),
  "ON_PART_ID_" VARCHAR2(255 BYTE),
  "IF_PART_ID_" VARCHAR2(255 BYTE),
  "TIME_STAMP_" TIMESTAMP(6)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_CMMN_RU_SENTRY_PART_INST
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_CO_CONTENT_ITEM
-- ----------------------------
-- DROP TABLE "ACT_CO_CONTENT_ITEM";
CREATE TABLE "ACT_CO_CONTENT_ITEM" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL,
  "NAME_" VARCHAR2(255 BYTE) NOT NULL,
  "MIME_TYPE_" VARCHAR2(255 BYTE),
  "TASK_ID_" VARCHAR2(255 BYTE),
  "PROC_INST_ID_" VARCHAR2(255 BYTE),
  "CONTENT_STORE_ID_" VARCHAR2(255 BYTE),
  "CONTENT_STORE_NAME_" VARCHAR2(255 BYTE),
  "FIELD_" VARCHAR2(400 BYTE),
  "CONTENT_AVAILABLE_" NUMBER(1,0) DEFAULT 0,
  "CREATED_" TIMESTAMP(6),
  "CREATED_BY_" VARCHAR2(255 BYTE),
  "LAST_MODIFIED_" TIMESTAMP(6),
  "LAST_MODIFIED_BY_" VARCHAR2(255 BYTE),
  "CONTENT_SIZE_" NUMBER(38,0) DEFAULT 0,
  "TENANT_ID_" VARCHAR2(255 BYTE),
  "SCOPE_ID_" VARCHAR2(255 BYTE),
  "SCOPE_TYPE_" VARCHAR2(255 BYTE)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_CO_CONTENT_ITEM
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_CO_DATABASECHANGELOG
-- ----------------------------
-- DROP TABLE "ACT_CO_DATABASECHANGELOG";
CREATE TABLE "ACT_CO_DATABASECHANGELOG" (
  "ID" VARCHAR2(255 BYTE) NOT NULL,
  "AUTHOR" VARCHAR2(255 BYTE) NOT NULL,
  "FILENAME" VARCHAR2(255 BYTE) NOT NULL,
  "DATEEXECUTED" TIMESTAMP(6) NOT NULL,
  "ORDEREXECUTED" NUMBER NOT NULL,
  "EXECTYPE" VARCHAR2(10 BYTE) NOT NULL,
  "MD5SUM" VARCHAR2(35 BYTE),
  "DESCRIPTION" VARCHAR2(255 BYTE),
  "COMMENTS" VARCHAR2(255 BYTE),
  "TAG" VARCHAR2(255 BYTE),
  "LIQUIBASE" VARCHAR2(20 BYTE),
  "CONTEXTS" VARCHAR2(255 BYTE),
  "LABELS" VARCHAR2(255 BYTE),
  "DEPLOYMENT_ID" VARCHAR2(10 BYTE)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_CO_DATABASECHANGELOG
-- ----------------------------
INSERT INTO "ACT_CO_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") VALUES ('1', 'activiti', 'org/flowable/content/db/liquibase/flowable-content-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:15.412866', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '1', 'EXECUTED', '8:7644d7165cfe799200a2abdd3419e8b6', 'createTable tableName=ACT_CO_CONTENT_ITEM; createIndex indexName=idx_contitem_taskid, tableName=ACT_CO_CONTENT_ITEM; createIndex indexName=idx_contitem_procid, tableName=ACT_CO_CONTENT_ITEM', NULL, NULL, '3.5.3', NULL, NULL, '9500492548');
INSERT INTO "ACT_CO_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") VALUES ('2', 'flowable', 'org/flowable/content/db/liquibase/flowable-content-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:59.614707', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '2', 'EXECUTED', '8:fe7b11ac7dbbf9c43006b23bbab60bab', 'addColumn tableName=ACT_CO_CONTENT_ITEM; createIndex indexName=idx_contitem_scope, tableName=ACT_CO_CONTENT_ITEM', NULL, NULL, '3.5.3', NULL, NULL, '9500492548');
COMMIT;

-- ----------------------------
-- Table structure for ACT_CO_DATABASECHANGELOGLOCK
-- ----------------------------
-- DROP TABLE "ACT_CO_DATABASECHANGELOGLOCK";
CREATE TABLE "ACT_CO_DATABASECHANGELOGLOCK" (
  "ID" NUMBER NOT NULL,
  "LOCKED" NUMBER(1,0) NOT NULL,
  "LOCKGRANTED" TIMESTAMP(6),
  "LOCKEDBY" VARCHAR2(255 BYTE)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_CO_DATABASECHANGELOGLOCK
-- ----------------------------
INSERT INTO "ACT_CO_DATABASECHANGELOGLOCK" ("ID", "LOCKED", "LOCKGRANTED", "LOCKEDBY") VALUES ('1', '0', NULL, NULL);
COMMIT;

-- ----------------------------
-- Table structure for ACT_DE_DATABASECHANGELOG
-- ----------------------------
-- DROP TABLE "ACT_DE_DATABASECHANGELOG";
CREATE TABLE "ACT_DE_DATABASECHANGELOG" (
  "ID" VARCHAR2(255 BYTE) NOT NULL,
  "AUTHOR" VARCHAR2(255 BYTE) NOT NULL,
  "FILENAME" VARCHAR2(255 BYTE) NOT NULL,
  "DATEEXECUTED" TIMESTAMP(6) NOT NULL,
  "ORDEREXECUTED" NUMBER NOT NULL,
  "EXECTYPE" VARCHAR2(10 BYTE) NOT NULL,
  "MD5SUM" VARCHAR2(35 BYTE),
  "DESCRIPTION" VARCHAR2(255 BYTE),
  "COMMENTS" VARCHAR2(255 BYTE),
  "TAG" VARCHAR2(255 BYTE),
  "LIQUIBASE" VARCHAR2(20 BYTE),
  "CONTEXTS" VARCHAR2(255 BYTE),
  "LABELS" VARCHAR2(255 BYTE),
  "DEPLOYMENT_ID" VARCHAR2(10 BYTE)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_DE_DATABASECHANGELOG
-- ----------------------------
INSERT INTO "ACT_DE_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") VALUES ('1', 'flowable', 'META-INF/liquibase/flowable-modeler-app-db-changelog.xml', TO_TIMESTAMP('2019-08-01 02:21:28.919681', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '1', 'EXECUTED', '8:e70d1d9d3899a734296b2514ccc71501', 'createTable tableName=ACT_DE_MODEL; createIndex indexName=idx_proc_mod_created, tableName=ACT_DE_MODEL; createTable tableName=ACT_DE_MODEL_HISTORY; createIndex indexName=idx_proc_mod_history_proc, tableName=ACT_DE_MODEL_HISTORY; createTable tableN...', NULL, NULL, '3.6.3', NULL, NULL, '4626087832');
INSERT INTO "ACT_DE_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") VALUES ('3', 'flowable', 'META-INF/liquibase/flowable-modeler-app-db-changelog.xml', TO_TIMESTAMP('2019-08-01 02:21:29.414387', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '2', 'EXECUTED', '8:3a9143bef2e45f2316231cc1369138b6', 'addColumn tableName=ACT_DE_MODEL; addColumn tableName=ACT_DE_MODEL_HISTORY', NULL, NULL, '3.6.3', NULL, NULL, '4626087832');
COMMIT;

-- ----------------------------
-- Table structure for ACT_DE_DATABASECHANGELOGLOCK
-- ----------------------------
-- DROP TABLE "ACT_DE_DATABASECHANGELOGLOCK";
CREATE TABLE "ACT_DE_DATABASECHANGELOGLOCK" (
  "ID" NUMBER NOT NULL,
  "LOCKED" NUMBER(1,0) NOT NULL,
  "LOCKGRANTED" TIMESTAMP(6),
  "LOCKEDBY" VARCHAR2(255 BYTE)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_DE_DATABASECHANGELOGLOCK
-- ----------------------------
INSERT INTO "ACT_DE_DATABASECHANGELOGLOCK" ("ID", "LOCKED", "LOCKGRANTED", "LOCKEDBY") VALUES ('1', '0', NULL, NULL);
COMMIT;

-- ----------------------------
-- Table structure for ACT_DE_MODEL
-- ----------------------------
-- DROP TABLE "ACT_DE_MODEL";
CREATE TABLE "ACT_DE_MODEL" (
  "ID" VARCHAR2(255 BYTE) NOT NULL,
  "NAME" VARCHAR2(400 BYTE) NOT NULL,
  "MODEL_KEY" VARCHAR2(400 BYTE) NOT NULL,
  "DESCRIPTION" VARCHAR2(4000 BYTE),
  "MODEL_COMMENT" VARCHAR2(4000 BYTE),
  "CREATED" TIMESTAMP(6),
  "CREATED_BY" VARCHAR2(255 BYTE),
  "LAST_UPDATED" TIMESTAMP(6),
  "LAST_UPDATED_BY" VARCHAR2(255 BYTE),
  "VERSION" NUMBER,
  "MODEL_EDITOR_JSON" CLOB,
  "MODEL_EDITOR_XML" CLOB,
  "THUMBNAIL" BLOB,
  "MODEL_TYPE" NUMBER,
  "TENANT_ID" VARCHAR2(255 BYTE)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_DE_MODEL
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_DE_MODEL_HISTORY
-- ----------------------------
-- DROP TABLE "ACT_DE_MODEL_HISTORY";
CREATE TABLE "ACT_DE_MODEL_HISTORY" (
  "ID" VARCHAR2(255 BYTE) NOT NULL,
  "NAME" VARCHAR2(400 BYTE) NOT NULL,
  "MODEL_KEY" VARCHAR2(400 BYTE) NOT NULL,
  "DESCRIPTION" VARCHAR2(4000 BYTE),
  "MODEL_COMMENT" VARCHAR2(4000 BYTE),
  "CREATED" TIMESTAMP(6),
  "CREATED_BY" VARCHAR2(255 BYTE),
  "LAST_UPDATED" TIMESTAMP(6),
  "LAST_UPDATED_BY" VARCHAR2(255 BYTE),
  "REMOVAL_DATE" TIMESTAMP(6),
  "VERSION" NUMBER,
  "MODEL_EDITOR_JSON" CLOB,
  "MODEL_ID" VARCHAR2(255 BYTE) NOT NULL,
  "MODEL_TYPE" NUMBER,
  "TENANT_ID" VARCHAR2(255 BYTE)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_DE_MODEL_HISTORY
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_DE_MODEL_RELATION
-- ----------------------------
-- DROP TABLE "ACT_DE_MODEL_RELATION";
CREATE TABLE "ACT_DE_MODEL_RELATION" (
  "ID" VARCHAR2(255 BYTE) NOT NULL,
  "PARENT_MODEL_ID" VARCHAR2(255 BYTE),
  "MODEL_ID" VARCHAR2(255 BYTE),
  "RELATION_TYPE" VARCHAR2(255 BYTE)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_DE_MODEL_RELATION
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_DMN_DATABASECHANGELOG
-- ----------------------------
-- DROP TABLE "ACT_DMN_DATABASECHANGELOG";
CREATE TABLE "ACT_DMN_DATABASECHANGELOG" (
  "ID" VARCHAR2(255 BYTE) NOT NULL,
  "AUTHOR" VARCHAR2(255 BYTE) NOT NULL,
  "FILENAME" VARCHAR2(255 BYTE) NOT NULL,
  "DATEEXECUTED" TIMESTAMP(6) NOT NULL,
  "ORDEREXECUTED" NUMBER NOT NULL,
  "EXECTYPE" VARCHAR2(10 BYTE) NOT NULL,
  "MD5SUM" VARCHAR2(35 BYTE),
  "DESCRIPTION" VARCHAR2(255 BYTE),
  "COMMENTS" VARCHAR2(255 BYTE),
  "TAG" VARCHAR2(255 BYTE),
  "LIQUIBASE" VARCHAR2(20 BYTE),
  "CONTEXTS" VARCHAR2(255 BYTE),
  "LABELS" VARCHAR2(255 BYTE),
  "DEPLOYMENT_ID" VARCHAR2(10 BYTE)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_DMN_DATABASECHANGELOG
-- ----------------------------
INSERT INTO "ACT_DMN_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") VALUES ('7', 'flowable', 'org/flowable/dmn/db/liquibase/flowable-dmn-db-changelog.xml', TO_TIMESTAMP('2024-03-15 01:15:32.336251', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '6', 'EXECUTED', '9:d24d4c5f44083b4edf1231a7a682a2cd', 'dropIndex indexName=ACT_IDX_DEC_TBL_UNIQ, tableName=ACT_DMN_DECISION_TABLE; renameTable newTableName=ACT_DMN_DECISION, oldTableName=ACT_DMN_DECISION_TABLE; createIndex indexName=ACT_IDX_DMN_DEC_UNIQ, tableName=ACT_DMN_DECISION', NULL, NULL, '4.24.0', NULL, NULL, '0436532249');
INSERT INTO "ACT_DMN_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") VALUES ('8', 'flowable', 'org/flowable/dmn/db/liquibase/flowable-dmn-db-changelog.xml', TO_TIMESTAMP('2024-03-15 01:15:32.357741', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '7', 'EXECUTED', '9:3998ef0958b46fe9c19458183952d2a0', 'addColumn tableName=ACT_DMN_DECISION', NULL, NULL, '4.24.0', NULL, NULL, '0436532249');
INSERT INTO "ACT_DMN_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") VALUES ('9', 'flowable', 'org/flowable/dmn/db/liquibase/flowable-dmn-db-changelog.xml', TO_TIMESTAMP('2024-03-15 01:15:32.370931', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '8', 'EXECUTED', '9:5c9dc65601456faa1aa12f8d3afe0e9e', 'createIndex indexName=ACT_IDX_DMN_INSTANCE_ID, tableName=ACT_DMN_HI_DECISION_EXECUTION', NULL, NULL, '4.24.0', NULL, NULL, '0436532249');
INSERT INTO "ACT_DMN_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") VALUES ('1', 'activiti', 'org/flowable/dmn/db/liquibase/flowable-dmn-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:09.445285', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '1', 'EXECUTED', '9:5b36e70aee5a2e42f6e7a62ea5fa681b', 'createTable tableName=ACT_DMN_DEPLOYMENT; createTable tableName=ACT_DMN_DEPLOYMENT_RESOURCE; createTable tableName=ACT_DMN_DECISION_TABLE', NULL, NULL, '3.5.3', NULL, NULL, '9500490105');
INSERT INTO "ACT_DMN_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") VALUES ('2', 'flowable', 'org/flowable/dmn/db/liquibase/flowable-dmn-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:09.548377', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '2', 'EXECUTED', '9:fd13fa3f7af55d2b72f763fc261da30d', 'createTable tableName=ACT_DMN_HI_DECISION_EXECUTION', NULL, NULL, '3.5.3', NULL, NULL, '9500490105');
INSERT INTO "ACT_DMN_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") VALUES ('3', 'flowable', 'org/flowable/dmn/db/liquibase/flowable-dmn-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:09.617030', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '3', 'EXECUTED', '9:9f30e6a3557d4b4c713dbb2dcc141782', 'addColumn tableName=ACT_DMN_HI_DECISION_EXECUTION', NULL, NULL, '3.5.3', NULL, NULL, '9500490105');
INSERT INTO "ACT_DMN_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") VALUES ('4', 'flowable', 'org/flowable/dmn/db/liquibase/flowable-dmn-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:10.086703', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '4', 'EXECUTED', '9:41085fbde807dba96104ee75a2fcc4cc', 'dropColumn columnName=PARENT_DEPLOYMENT_ID_, tableName=ACT_DMN_DECISION_TABLE', NULL, NULL, '3.5.3', NULL, NULL, '9500490105');
INSERT INTO "ACT_DMN_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") VALUES ('6', 'flowable', 'org/flowable/dmn/db/liquibase/flowable-dmn-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:10.165065', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '5', 'EXECUTED', '9:f00f92f3ef1af3fc1604f0323630f9b1', 'createIndex indexName=ACT_IDX_DEC_TBL_UNIQ, tableName=ACT_DMN_DECISION_TABLE', NULL, NULL, '3.5.3', NULL, NULL, '9500490105');
COMMIT;

-- ----------------------------
-- Table structure for ACT_DMN_DATABASECHANGELOGLOCK
-- ----------------------------
-- DROP TABLE "ACT_DMN_DATABASECHANGELOGLOCK";
CREATE TABLE "ACT_DMN_DATABASECHANGELOGLOCK" (
  "ID" NUMBER NOT NULL,
  "LOCKED" NUMBER(1,0) NOT NULL,
  "LOCKGRANTED" TIMESTAMP(6),
  "LOCKEDBY" VARCHAR2(255 BYTE)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_DMN_DATABASECHANGELOGLOCK
-- ----------------------------
INSERT INTO "ACT_DMN_DATABASECHANGELOGLOCK" ("ID", "LOCKED", "LOCKGRANTED", "LOCKEDBY") VALUES ('1', '0', NULL, NULL);
COMMIT;

-- ----------------------------
-- Table structure for ACT_DMN_DECISION
-- ----------------------------
-- DROP TABLE "ACT_DMN_DECISION";
CREATE TABLE "ACT_DMN_DECISION" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL,
  "NAME_" VARCHAR2(255 BYTE),
  "VERSION_" NUMBER,
  "KEY_" VARCHAR2(255 BYTE),
  "CATEGORY_" VARCHAR2(255 BYTE),
  "DEPLOYMENT_ID_" VARCHAR2(255 BYTE),
  "TENANT_ID_" VARCHAR2(255 BYTE),
  "RESOURCE_NAME_" VARCHAR2(255 BYTE),
  "DESCRIPTION_" VARCHAR2(255 BYTE),
  "DECISION_TYPE_" VARCHAR2(255 BYTE)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_DMN_DECISION
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_DMN_DEPLOYMENT
-- ----------------------------
-- DROP TABLE "ACT_DMN_DEPLOYMENT";
CREATE TABLE "ACT_DMN_DEPLOYMENT" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL,
  "NAME_" VARCHAR2(255 BYTE),
  "CATEGORY_" VARCHAR2(255 BYTE),
  "DEPLOY_TIME_" TIMESTAMP(6),
  "TENANT_ID_" VARCHAR2(255 BYTE),
  "PARENT_DEPLOYMENT_ID_" VARCHAR2(255 BYTE)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_DMN_DEPLOYMENT
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_DMN_DEPLOYMENT_RESOURCE
-- ----------------------------
-- DROP TABLE "ACT_DMN_DEPLOYMENT_RESOURCE";
CREATE TABLE "ACT_DMN_DEPLOYMENT_RESOURCE" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL,
  "NAME_" VARCHAR2(255 BYTE),
  "DEPLOYMENT_ID_" VARCHAR2(255 BYTE),
  "RESOURCE_BYTES_" BLOB
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_DMN_DEPLOYMENT_RESOURCE
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_DMN_HI_DECISION_EXECUTION
-- ----------------------------
-- DROP TABLE "ACT_DMN_HI_DECISION_EXECUTION";
CREATE TABLE "ACT_DMN_HI_DECISION_EXECUTION" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL,
  "DECISION_DEFINITION_ID_" VARCHAR2(255 BYTE),
  "DEPLOYMENT_ID_" VARCHAR2(255 BYTE),
  "START_TIME_" TIMESTAMP(6),
  "END_TIME_" TIMESTAMP(6),
  "INSTANCE_ID_" VARCHAR2(255 BYTE),
  "EXECUTION_ID_" VARCHAR2(255 BYTE),
  "ACTIVITY_ID_" VARCHAR2(255 BYTE),
  "FAILED_" NUMBER(1,0) DEFAULT 0,
  "TENANT_ID_" VARCHAR2(255 BYTE),
  "EXECUTION_JSON_" CLOB,
  "SCOPE_TYPE_" VARCHAR2(255 BYTE)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_DMN_HI_DECISION_EXECUTION
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_EVT_LOG
-- ----------------------------
-- DROP TABLE "ACT_EVT_LOG";
CREATE TABLE "ACT_EVT_LOG" (
  "LOG_NR_" NUMBER(19,0) NOT NULL,
  "TYPE_" NVARCHAR2(64),
  "PROC_DEF_ID_" NVARCHAR2(64),
  "PROC_INST_ID_" NVARCHAR2(64),
  "EXECUTION_ID_" NVARCHAR2(64),
  "TASK_ID_" NVARCHAR2(64),
  "TIME_STAMP_" TIMESTAMP(6) NOT NULL,
  "USER_ID_" NVARCHAR2(255),
  "DATA_" BLOB,
  "LOCK_OWNER_" NVARCHAR2(255),
  "LOCK_TIME_" TIMESTAMP(6),
  "IS_PROCESSED_" NUMBER(3,0) DEFAULT 0
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_EVT_LOG
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_FO_DATABASECHANGELOG
-- ----------------------------
-- DROP TABLE "ACT_FO_DATABASECHANGELOG";
CREATE TABLE "ACT_FO_DATABASECHANGELOG" (
  "ID" VARCHAR2(255 BYTE) NOT NULL,
  "AUTHOR" VARCHAR2(255 BYTE) NOT NULL,
  "FILENAME" VARCHAR2(255 BYTE) NOT NULL,
  "DATEEXECUTED" TIMESTAMP(6) NOT NULL,
  "ORDEREXECUTED" NUMBER NOT NULL,
  "EXECTYPE" VARCHAR2(10 BYTE) NOT NULL,
  "MD5SUM" VARCHAR2(35 BYTE),
  "DESCRIPTION" VARCHAR2(255 BYTE),
  "COMMENTS" VARCHAR2(255 BYTE),
  "TAG" VARCHAR2(255 BYTE),
  "LIQUIBASE" VARCHAR2(20 BYTE),
  "CONTEXTS" VARCHAR2(255 BYTE),
  "LABELS" VARCHAR2(255 BYTE),
  "DEPLOYMENT_ID" VARCHAR2(10 BYTE)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_FO_DATABASECHANGELOG
-- ----------------------------
INSERT INTO "ACT_FO_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") VALUES ('1', 'activiti', 'org/flowable/form/db/liquibase/flowable-form-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:10.875144', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '1', 'EXECUTED', '8:033ebf9380889aed7c453927ecc3250d', 'createTable tableName=ACT_FO_FORM_DEPLOYMENT; createTable tableName=ACT_FO_FORM_RESOURCE; createTable tableName=ACT_FO_FORM_DEFINITION; createTable tableName=ACT_FO_FORM_INSTANCE', NULL, NULL, '3.5.3', NULL, NULL, '9500491442');
INSERT INTO "ACT_FO_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") VALUES ('2', 'flowable', 'org/flowable/form/db/liquibase/flowable-form-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:11.176570', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '2', 'EXECUTED', '8:986365ceb40445ce3b27a8e6b40f159b', 'addColumn tableName=ACT_FO_FORM_INSTANCE', NULL, NULL, '3.5.3', NULL, NULL, '9500491442');
INSERT INTO "ACT_FO_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") VALUES ('3', 'flowable', 'org/flowable/form/db/liquibase/flowable-form-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:13.571387', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '3', 'EXECUTED', '8:abf482518ceb09830ef674e52c06bf15', 'dropColumn columnName=PARENT_DEPLOYMENT_ID_, tableName=ACT_FO_FORM_DEFINITION', NULL, NULL, '3.5.3', NULL, NULL, '9500491442');
INSERT INTO "ACT_FO_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") VALUES ('5', 'flowable', 'org/flowable/form/db/liquibase/flowable-form-db-changelog.xml', TO_TIMESTAMP('2019-08-01 01:36:13.675232', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '4', 'EXECUTED', '8:b4be732b89e5ca028bdd520c6ad4d446', 'createIndex indexName=ACT_IDX_FORM_DEF_UNIQ, tableName=ACT_FO_FORM_DEFINITION', NULL, NULL, '3.5.3', NULL, NULL, '9500491442');
COMMIT;

-- ----------------------------
-- Table structure for ACT_FO_DATABASECHANGELOGLOCK
-- ----------------------------
-- DROP TABLE "ACT_FO_DATABASECHANGELOGLOCK";
CREATE TABLE "ACT_FO_DATABASECHANGELOGLOCK" (
  "ID" NUMBER NOT NULL,
  "LOCKED" NUMBER(1,0) NOT NULL,
  "LOCKGRANTED" TIMESTAMP(6),
  "LOCKEDBY" VARCHAR2(255 BYTE)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_FO_DATABASECHANGELOGLOCK
-- ----------------------------
INSERT INTO "ACT_FO_DATABASECHANGELOGLOCK" ("ID", "LOCKED", "LOCKGRANTED", "LOCKEDBY") VALUES ('1', '0', NULL, NULL);
COMMIT;

-- ----------------------------
-- Table structure for ACT_FO_FORM_DEFINITION
-- ----------------------------
-- DROP TABLE "ACT_FO_FORM_DEFINITION";
CREATE TABLE "ACT_FO_FORM_DEFINITION" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL,
  "NAME_" VARCHAR2(255 BYTE),
  "VERSION_" NUMBER,
  "KEY_" VARCHAR2(255 BYTE),
  "CATEGORY_" VARCHAR2(255 BYTE),
  "DEPLOYMENT_ID_" VARCHAR2(255 BYTE),
  "TENANT_ID_" VARCHAR2(255 BYTE),
  "RESOURCE_NAME_" VARCHAR2(255 BYTE),
  "DESCRIPTION_" VARCHAR2(255 BYTE)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_FO_FORM_DEFINITION
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_FO_FORM_DEPLOYMENT
-- ----------------------------
-- DROP TABLE "ACT_FO_FORM_DEPLOYMENT";
CREATE TABLE "ACT_FO_FORM_DEPLOYMENT" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL,
  "NAME_" VARCHAR2(255 BYTE),
  "CATEGORY_" VARCHAR2(255 BYTE),
  "DEPLOY_TIME_" TIMESTAMP(6),
  "TENANT_ID_" VARCHAR2(255 BYTE),
  "PARENT_DEPLOYMENT_ID_" VARCHAR2(255 BYTE)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_FO_FORM_DEPLOYMENT
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_FO_FORM_INSTANCE
-- ----------------------------
-- DROP TABLE "ACT_FO_FORM_INSTANCE";
CREATE TABLE "ACT_FO_FORM_INSTANCE" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL,
  "FORM_DEFINITION_ID_" VARCHAR2(255 BYTE) NOT NULL,
  "TASK_ID_" VARCHAR2(255 BYTE),
  "PROC_INST_ID_" VARCHAR2(255 BYTE),
  "PROC_DEF_ID_" VARCHAR2(255 BYTE),
  "SUBMITTED_DATE_" TIMESTAMP(6),
  "SUBMITTED_BY_" VARCHAR2(255 BYTE),
  "FORM_VALUES_ID_" VARCHAR2(255 BYTE),
  "TENANT_ID_" VARCHAR2(255 BYTE),
  "SCOPE_ID_" VARCHAR2(255 BYTE),
  "SCOPE_TYPE_" VARCHAR2(255 BYTE),
  "SCOPE_DEFINITION_ID_" VARCHAR2(255 BYTE)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_FO_FORM_INSTANCE
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_FO_FORM_RESOURCE
-- ----------------------------
-- DROP TABLE "ACT_FO_FORM_RESOURCE";
CREATE TABLE "ACT_FO_FORM_RESOURCE" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL,
  "NAME_" VARCHAR2(255 BYTE),
  "DEPLOYMENT_ID_" VARCHAR2(255 BYTE),
  "RESOURCE_BYTES_" BLOB
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_FO_FORM_RESOURCE
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_GE_BYTEARRAY
-- ----------------------------
-- DROP TABLE "ACT_GE_BYTEARRAY";
CREATE TABLE "ACT_GE_BYTEARRAY" (
  "ID_" NVARCHAR2(64) NOT NULL,
  "REV_" NUMBER,
  "NAME_" NVARCHAR2(255),
  "DEPLOYMENT_ID_" NVARCHAR2(64),
  "BYTES_" BLOB,
  "GENERATED_" NUMBER(1,0)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_GE_BYTEARRAY
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_GE_PROPERTY
-- ----------------------------
-- DROP TABLE "ACT_GE_PROPERTY";
CREATE TABLE "ACT_GE_PROPERTY" (
  "NAME_" NVARCHAR2(64) NOT NULL,
  "VALUE_" NVARCHAR2(300),
  "REV_" NUMBER
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_GE_PROPERTY
-- ----------------------------
INSERT INTO "ACT_GE_PROPERTY" ("NAME_", "VALUE_", "REV_") VALUES ('batch.schema.version', '*******', '1');
INSERT INTO "ACT_GE_PROPERTY" ("NAME_", "VALUE_", "REV_") VALUES ('common.schema.version', '*******', '1');
INSERT INTO "ACT_GE_PROPERTY" ("NAME_", "VALUE_", "REV_") VALUES ('next.dbid', '1', '1');
INSERT INTO "ACT_GE_PROPERTY" ("NAME_", "VALUE_", "REV_") VALUES ('entitylink.schema.version', '*******', '1');
INSERT INTO "ACT_GE_PROPERTY" ("NAME_", "VALUE_", "REV_") VALUES ('identitylink.schema.version', '*******', '1');
INSERT INTO "ACT_GE_PROPERTY" ("NAME_", "VALUE_", "REV_") VALUES ('job.schema.version', '*******', '1');
INSERT INTO "ACT_GE_PROPERTY" ("NAME_", "VALUE_", "REV_") VALUES ('task.schema.version', '*******', '1');
INSERT INTO "ACT_GE_PROPERTY" ("NAME_", "VALUE_", "REV_") VALUES ('variable.schema.version', '*******', '1');
INSERT INTO "ACT_GE_PROPERTY" ("NAME_", "VALUE_", "REV_") VALUES ('eventsubscription.schema.version', '*******', '1');
INSERT INTO "ACT_GE_PROPERTY" ("NAME_", "VALUE_", "REV_") VALUES ('schema.version', '*******', '2');
INSERT INTO "ACT_GE_PROPERTY" ("NAME_", "VALUE_", "REV_") VALUES ('schema.history', 'upgrade(*******->*******)', '2');
INSERT INTO "ACT_GE_PROPERTY" ("NAME_", "VALUE_", "REV_") VALUES ('cfg.execution-related-entities-count', 'true', '1');
INSERT INTO "ACT_GE_PROPERTY" ("NAME_", "VALUE_", "REV_") VALUES ('cfg.task-related-entities-count', 'true', '1');
COMMIT;

-- ----------------------------
-- Table structure for ACT_HI_ACTINST
-- ----------------------------
-- DROP TABLE "ACT_HI_ACTINST";
CREATE TABLE "ACT_HI_ACTINST" (
  "ID_" NVARCHAR2(64) NOT NULL,
  "REV_" NUMBER DEFAULT 1,
  "PROC_DEF_ID_" NVARCHAR2(64) NOT NULL,
  "PROC_INST_ID_" NVARCHAR2(64) NOT NULL,
  "EXECUTION_ID_" NVARCHAR2(64) NOT NULL,
  "ACT_ID_" NVARCHAR2(255) NOT NULL,
  "TASK_ID_" NVARCHAR2(64),
  "CALL_PROC_INST_ID_" NVARCHAR2(64),
  "ACT_NAME_" NVARCHAR2(255),
  "ACT_TYPE_" NVARCHAR2(255) NOT NULL,
  "ASSIGNEE_" NVARCHAR2(255),
  "START_TIME_" TIMESTAMP(6) NOT NULL,
  "END_TIME_" TIMESTAMP(6),
  "DURATION_" NUMBER(19,0),
  "DELETE_REASON_" NVARCHAR2(2000),
  "TENANT_ID_" NVARCHAR2(255) DEFAULT '',
  "TRANSACTION_ORDER_" NUMBER
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_HI_ACTINST
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_HI_ATTACHMENT
-- ----------------------------
-- DROP TABLE "ACT_HI_ATTACHMENT";
CREATE TABLE "ACT_HI_ATTACHMENT" (
  "ID_" NVARCHAR2(64) NOT NULL,
  "REV_" NUMBER,
  "USER_ID_" NVARCHAR2(255),
  "NAME_" NVARCHAR2(255),
  "DESCRIPTION_" NVARCHAR2(2000),
  "TYPE_" NVARCHAR2(255),
  "TASK_ID_" NVARCHAR2(64),
  "PROC_INST_ID_" NVARCHAR2(64),
  "URL_" NVARCHAR2(2000),
  "CONTENT_ID_" NVARCHAR2(64),
  "TIME_" TIMESTAMP(6)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_HI_ATTACHMENT
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_HI_COMMENT
-- ----------------------------
-- DROP TABLE "ACT_HI_COMMENT";
CREATE TABLE "ACT_HI_COMMENT" (
  "ID_" NVARCHAR2(64) NOT NULL,
  "TYPE_" NVARCHAR2(255),
  "TIME_" TIMESTAMP(6) NOT NULL,
  "USER_ID_" NVARCHAR2(255),
  "TASK_ID_" NVARCHAR2(64),
  "PROC_INST_ID_" NVARCHAR2(64),
  "ACTION_" NVARCHAR2(255),
  "MESSAGE_" NVARCHAR2(2000),
  "FULL_MSG_" BLOB
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_HI_COMMENT
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_HI_DETAIL
-- ----------------------------
-- DROP TABLE "ACT_HI_DETAIL";
CREATE TABLE "ACT_HI_DETAIL" (
  "ID_" NVARCHAR2(64) NOT NULL,
  "TYPE_" NVARCHAR2(255) NOT NULL,
  "PROC_INST_ID_" NVARCHAR2(64),
  "EXECUTION_ID_" NVARCHAR2(64),
  "TASK_ID_" NVARCHAR2(64),
  "ACT_INST_ID_" NVARCHAR2(64),
  "NAME_" NVARCHAR2(255) NOT NULL,
  "VAR_TYPE_" NVARCHAR2(64),
  "REV_" NUMBER,
  "TIME_" TIMESTAMP(6) NOT NULL,
  "BYTEARRAY_ID_" NVARCHAR2(64),
  "DOUBLE_" NUMBER,
  "LONG_" NUMBER(19,0),
  "TEXT_" NVARCHAR2(2000),
  "TEXT2_" NVARCHAR2(2000)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_HI_DETAIL
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_HI_ENTITYLINK
-- ----------------------------
-- DROP TABLE "ACT_HI_ENTITYLINK";
CREATE TABLE "ACT_HI_ENTITYLINK" (
  "ID_" NVARCHAR2(64) NOT NULL,
  "LINK_TYPE_" NVARCHAR2(255),
  "CREATE_TIME_" TIMESTAMP(6),
  "SCOPE_ID_" NVARCHAR2(255),
  "SCOPE_TYPE_" NVARCHAR2(255),
  "SCOPE_DEFINITION_ID_" NVARCHAR2(255),
  "REF_SCOPE_ID_" NVARCHAR2(255),
  "REF_SCOPE_TYPE_" NVARCHAR2(255),
  "REF_SCOPE_DEFINITION_ID_" NVARCHAR2(255),
  "HIERARCHY_TYPE_" NVARCHAR2(255),
  "ROOT_SCOPE_ID_" NVARCHAR2(255),
  "ROOT_SCOPE_TYPE_" NVARCHAR2(255),
  "SUB_SCOPE_ID_" NVARCHAR2(255),
  "PARENT_ELEMENT_ID_" NVARCHAR2(255)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_HI_ENTITYLINK
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_HI_IDENTITYLINK
-- ----------------------------
-- DROP TABLE "ACT_HI_IDENTITYLINK";
CREATE TABLE "ACT_HI_IDENTITYLINK" (
  "ID_" NVARCHAR2(64) NOT NULL,
  "GROUP_ID_" NVARCHAR2(255),
  "TYPE_" NVARCHAR2(255),
  "USER_ID_" NVARCHAR2(255),
  "TASK_ID_" NVARCHAR2(64),
  "CREATE_TIME_" TIMESTAMP(6),
  "PROC_INST_ID_" NVARCHAR2(64),
  "SCOPE_ID_" NVARCHAR2(255),
  "SCOPE_TYPE_" NVARCHAR2(255),
  "SCOPE_DEFINITION_ID_" NVARCHAR2(255),
  "SUB_SCOPE_ID_" NVARCHAR2(255)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_HI_IDENTITYLINK
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_HI_PROCINST
-- ----------------------------
-- DROP TABLE "ACT_HI_PROCINST";
CREATE TABLE "ACT_HI_PROCINST" (
  "ID_" NVARCHAR2(64) NOT NULL,
  "REV_" NUMBER DEFAULT 1,
  "PROC_INST_ID_" NVARCHAR2(64) NOT NULL,
  "BUSINESS_KEY_" NVARCHAR2(255),
  "PROC_DEF_ID_" NVARCHAR2(64) NOT NULL,
  "START_TIME_" TIMESTAMP(6) NOT NULL,
  "END_TIME_" TIMESTAMP(6),
  "DURATION_" NUMBER(19,0),
  "START_USER_ID_" NVARCHAR2(255),
  "START_ACT_ID_" NVARCHAR2(255),
  "END_ACT_ID_" NVARCHAR2(255),
  "SUPER_PROCESS_INSTANCE_ID_" NVARCHAR2(64),
  "DELETE_REASON_" NVARCHAR2(2000),
  "TENANT_ID_" NVARCHAR2(255) DEFAULT '',
  "NAME_" NVARCHAR2(255),
  "CALLBACK_ID_" NVARCHAR2(255),
  "CALLBACK_TYPE_" NVARCHAR2(255),
  "REFERENCE_ID_" NVARCHAR2(255),
  "REFERENCE_TYPE_" NVARCHAR2(255),
  "PROPAGATED_STAGE_INST_ID_" NVARCHAR2(255),
  "BUSINESS_STATUS_" NVARCHAR2(255)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_HI_PROCINST
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_HI_TASKINST
-- ----------------------------
-- DROP TABLE "ACT_HI_TASKINST";
CREATE TABLE "ACT_HI_TASKINST" (
  "ID_" NVARCHAR2(64) NOT NULL,
  "REV_" NUMBER DEFAULT 1,
  "PROC_DEF_ID_" NVARCHAR2(64),
  "TASK_DEF_ID_" NVARCHAR2(64),
  "TASK_DEF_KEY_" NVARCHAR2(255),
  "PROC_INST_ID_" NVARCHAR2(64),
  "EXECUTION_ID_" NVARCHAR2(64),
  "SCOPE_ID_" NVARCHAR2(255),
  "SUB_SCOPE_ID_" NVARCHAR2(255),
  "SCOPE_TYPE_" NVARCHAR2(255),
  "SCOPE_DEFINITION_ID_" NVARCHAR2(255),
  "PARENT_TASK_ID_" NVARCHAR2(64),
  "NAME_" NVARCHAR2(255),
  "DESCRIPTION_" NVARCHAR2(2000),
  "OWNER_" NVARCHAR2(255),
  "ASSIGNEE_" NVARCHAR2(255),
  "START_TIME_" TIMESTAMP(6) NOT NULL,
  "CLAIM_TIME_" TIMESTAMP(6),
  "END_TIME_" TIMESTAMP(6),
  "DURATION_" NUMBER(19,0),
  "DELETE_REASON_" NVARCHAR2(2000),
  "PRIORITY_" NUMBER,
  "DUE_DATE_" TIMESTAMP(6),
  "FORM_KEY_" NVARCHAR2(255),
  "CATEGORY_" NVARCHAR2(255),
  "TENANT_ID_" NVARCHAR2(255) DEFAULT '',
  "LAST_UPDATED_TIME_" TIMESTAMP(6),
  "PROPAGATED_STAGE_INST_ID_" NVARCHAR2(255),
  "STATE_" NVARCHAR2(255),
  "IN_PROGRESS_TIME_" TIMESTAMP(6),
  "IN_PROGRESS_STARTED_BY_" NVARCHAR2(255),
  "CLAIMED_BY_" NVARCHAR2(255),
  "SUSPENDED_TIME_" TIMESTAMP(6),
  "SUSPENDED_BY_" NVARCHAR2(255),
  "COMPLETED_BY_" NVARCHAR2(255),
  "IN_PROGRESS_DUE_DATE_" TIMESTAMP(6)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_HI_TASKINST
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_HI_TSK_LOG
-- ----------------------------
-- DROP TABLE "ACT_HI_TSK_LOG";
CREATE TABLE "ACT_HI_TSK_LOG" (
  "ID_" NUMBER(19,0) NOT NULL,
  "TYPE_" NVARCHAR2(64),
  "TASK_ID_" NVARCHAR2(64) NOT NULL,
  "TIME_STAMP_" TIMESTAMP(6) NOT NULL,
  "USER_ID_" NVARCHAR2(255),
  "DATA_" NVARCHAR2(2000),
  "EXECUTION_ID_" NVARCHAR2(64),
  "PROC_INST_ID_" NVARCHAR2(64),
  "PROC_DEF_ID_" NVARCHAR2(64),
  "SCOPE_ID_" NVARCHAR2(255),
  "SCOPE_DEFINITION_ID_" NVARCHAR2(255),
  "SUB_SCOPE_ID_" NVARCHAR2(255),
  "SCOPE_TYPE_" NVARCHAR2(255),
  "TENANT_ID_" NVARCHAR2(255) DEFAULT ''
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_HI_TSK_LOG
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_HI_VARINST
-- ----------------------------
-- DROP TABLE "ACT_HI_VARINST";
CREATE TABLE "ACT_HI_VARINST" (
  "ID_" NVARCHAR2(64) NOT NULL,
  "REV_" NUMBER DEFAULT 1,
  "PROC_INST_ID_" NVARCHAR2(64),
  "EXECUTION_ID_" NVARCHAR2(64),
  "TASK_ID_" NVARCHAR2(64),
  "NAME_" NVARCHAR2(255) NOT NULL,
  "VAR_TYPE_" NVARCHAR2(100),
  "SCOPE_ID_" NVARCHAR2(255),
  "SUB_SCOPE_ID_" NVARCHAR2(255),
  "SCOPE_TYPE_" NVARCHAR2(255),
  "BYTEARRAY_ID_" NVARCHAR2(64),
  "DOUBLE_" NUMBER,
  "LONG_" NUMBER(19,0),
  "TEXT_" NVARCHAR2(2000),
  "TEXT2_" NVARCHAR2(2000),
  "CREATE_TIME_" TIMESTAMP(6),
  "LAST_UPDATED_TIME_" TIMESTAMP(6),
  "META_INFO_" NVARCHAR2(2000)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_HI_VARINST
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_ID_BYTEARRAY
-- ----------------------------
-- DROP TABLE "ACT_ID_BYTEARRAY";
CREATE TABLE "ACT_ID_BYTEARRAY" (
  "ID_" NVARCHAR2(64) NOT NULL,
  "REV_" NUMBER,
  "NAME_" NVARCHAR2(255),
  "BYTES_" BLOB
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_ID_BYTEARRAY
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_ID_GROUP
-- ----------------------------
-- DROP TABLE "ACT_ID_GROUP";
CREATE TABLE "ACT_ID_GROUP" (
  "ID_" NVARCHAR2(64) NOT NULL,
  "REV_" NUMBER,
  "NAME_" NVARCHAR2(255),
  "TYPE_" NVARCHAR2(255)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_ID_GROUP
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_ID_INFO
-- ----------------------------
-- DROP TABLE "ACT_ID_INFO";
CREATE TABLE "ACT_ID_INFO" (
  "ID_" NVARCHAR2(64) NOT NULL,
  "REV_" NUMBER,
  "USER_ID_" NVARCHAR2(64),
  "TYPE_" NVARCHAR2(64),
  "KEY_" NVARCHAR2(255),
  "VALUE_" NVARCHAR2(255),
  "PASSWORD_" BLOB,
  "PARENT_ID_" NVARCHAR2(255)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_ID_INFO
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_ID_MEMBERSHIP
-- ----------------------------
-- DROP TABLE "ACT_ID_MEMBERSHIP";
CREATE TABLE "ACT_ID_MEMBERSHIP" (
  "USER_ID_" NVARCHAR2(64) NOT NULL,
  "GROUP_ID_" NVARCHAR2(64) NOT NULL
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_ID_MEMBERSHIP
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_ID_PRIV
-- ----------------------------
-- DROP TABLE "ACT_ID_PRIV";
CREATE TABLE "ACT_ID_PRIV" (
  "ID_" NVARCHAR2(64) NOT NULL,
  "NAME_" NVARCHAR2(255) NOT NULL
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_ID_PRIV
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_ID_PRIV_MAPPING
-- ----------------------------
-- DROP TABLE "ACT_ID_PRIV_MAPPING";
CREATE TABLE "ACT_ID_PRIV_MAPPING" (
  "ID_" NVARCHAR2(64) NOT NULL,
  "PRIV_ID_" NVARCHAR2(64) NOT NULL,
  "USER_ID_" NVARCHAR2(255),
  "GROUP_ID_" NVARCHAR2(255)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_ID_PRIV_MAPPING
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_ID_PROPERTY
-- ----------------------------
-- DROP TABLE "ACT_ID_PROPERTY";
CREATE TABLE "ACT_ID_PROPERTY" (
  "NAME_" NVARCHAR2(64) NOT NULL,
  "VALUE_" NVARCHAR2(300),
  "REV_" NUMBER
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_ID_PROPERTY
-- ----------------------------
INSERT INTO "ACT_ID_PROPERTY" ("NAME_", "VALUE_", "REV_") VALUES ('schema.version', '*******', '1');
COMMIT;

-- ----------------------------
-- Table structure for ACT_ID_TOKEN
-- ----------------------------
-- DROP TABLE "ACT_ID_TOKEN";
CREATE TABLE "ACT_ID_TOKEN" (
  "ID_" NVARCHAR2(64) NOT NULL,
  "REV_" NUMBER,
  "TOKEN_VALUE_" NVARCHAR2(255),
  "TOKEN_DATE_" TIMESTAMP(6),
  "IP_ADDRESS_" NVARCHAR2(255),
  "USER_AGENT_" NVARCHAR2(255),
  "USER_ID_" NVARCHAR2(255),
  "TOKEN_DATA_" NVARCHAR2(2000)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_ID_TOKEN
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_ID_USER
-- ----------------------------
-- DROP TABLE "ACT_ID_USER";
CREATE TABLE "ACT_ID_USER" (
  "ID_" NVARCHAR2(64) NOT NULL,
  "REV_" NUMBER,
  "FIRST_" NVARCHAR2(255),
  "LAST_" NVARCHAR2(255),
  "DISPLAY_NAME_" NVARCHAR2(255),
  "EMAIL_" NVARCHAR2(255),
  "PWD_" NVARCHAR2(255),
  "PICTURE_ID_" NVARCHAR2(64),
  "TENANT_ID_" NVARCHAR2(255) DEFAULT ''
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_ID_USER
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_PROCDEF_INFO
-- ----------------------------
-- DROP TABLE "ACT_PROCDEF_INFO";
CREATE TABLE "ACT_PROCDEF_INFO" (
  "ID_" NVARCHAR2(64) NOT NULL,
  "PROC_DEF_ID_" NVARCHAR2(64) NOT NULL,
  "REV_" NUMBER,
  "INFO_JSON_ID_" NVARCHAR2(64)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_PROCDEF_INFO
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_RE_DEPLOYMENT
-- ----------------------------
-- DROP TABLE "ACT_RE_DEPLOYMENT";
CREATE TABLE "ACT_RE_DEPLOYMENT" (
  "ID_" NVARCHAR2(64) NOT NULL,
  "NAME_" NVARCHAR2(255),
  "CATEGORY_" NVARCHAR2(255),
  "KEY_" NVARCHAR2(255),
  "TENANT_ID_" NVARCHAR2(255) DEFAULT '',
  "DEPLOY_TIME_" TIMESTAMP(6),
  "DERIVED_FROM_" NVARCHAR2(64),
  "DERIVED_FROM_ROOT_" NVARCHAR2(64),
  "PARENT_DEPLOYMENT_ID_" NVARCHAR2(255),
  "ENGINE_VERSION_" NVARCHAR2(255)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_RE_DEPLOYMENT
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_RE_MODEL
-- ----------------------------
-- DROP TABLE "ACT_RE_MODEL";
CREATE TABLE "ACT_RE_MODEL" (
  "ID_" NVARCHAR2(64) NOT NULL,
  "REV_" NUMBER,
  "NAME_" NVARCHAR2(255),
  "KEY_" NVARCHAR2(255),
  "CATEGORY_" NVARCHAR2(255),
  "CREATE_TIME_" TIMESTAMP(6),
  "LAST_UPDATE_TIME_" TIMESTAMP(6),
  "VERSION_" NUMBER,
  "META_INFO_" NVARCHAR2(2000),
  "DEPLOYMENT_ID_" NVARCHAR2(64),
  "EDITOR_SOURCE_VALUE_ID_" NVARCHAR2(64),
  "EDITOR_SOURCE_EXTRA_VALUE_ID_" NVARCHAR2(64),
  "TENANT_ID_" NVARCHAR2(255) DEFAULT ''
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_RE_MODEL
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_RE_PROCDEF
-- ----------------------------
-- DROP TABLE "ACT_RE_PROCDEF";
CREATE TABLE "ACT_RE_PROCDEF" (
  "ID_" NVARCHAR2(64) NOT NULL,
  "REV_" NUMBER,
  "CATEGORY_" NVARCHAR2(255),
  "NAME_" NVARCHAR2(255),
  "KEY_" NVARCHAR2(255) NOT NULL,
  "VERSION_" NUMBER NOT NULL,
  "DEPLOYMENT_ID_" NVARCHAR2(64),
  "RESOURCE_NAME_" NVARCHAR2(2000),
  "DGRM_RESOURCE_NAME_" VARCHAR2(4000 BYTE),
  "DESCRIPTION_" NVARCHAR2(2000),
  "HAS_START_FORM_KEY_" NUMBER(1,0),
  "HAS_GRAPHICAL_NOTATION_" NUMBER(1,0),
  "SUSPENSION_STATE_" NUMBER,
  "TENANT_ID_" NVARCHAR2(255) DEFAULT '',
  "DERIVED_FROM_" NVARCHAR2(64),
  "DERIVED_FROM_ROOT_" NVARCHAR2(64),
  "DERIVED_VERSION_" NUMBER DEFAULT 0 NOT NULL,
  "ENGINE_VERSION_" NVARCHAR2(255)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_RE_PROCDEF
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_RU_ACTINST
-- ----------------------------
-- DROP TABLE "ACT_RU_ACTINST";
CREATE TABLE "ACT_RU_ACTINST" (
  "ID_" NVARCHAR2(64) NOT NULL,
  "REV_" NUMBER DEFAULT 1,
  "PROC_DEF_ID_" NVARCHAR2(64) NOT NULL,
  "PROC_INST_ID_" NVARCHAR2(64) NOT NULL,
  "EXECUTION_ID_" NVARCHAR2(64) NOT NULL,
  "ACT_ID_" NVARCHAR2(255) NOT NULL,
  "TASK_ID_" NVARCHAR2(64),
  "CALL_PROC_INST_ID_" NVARCHAR2(64),
  "ACT_NAME_" NVARCHAR2(255),
  "ACT_TYPE_" NVARCHAR2(255) NOT NULL,
  "ASSIGNEE_" NVARCHAR2(255),
  "START_TIME_" TIMESTAMP(6) NOT NULL,
  "END_TIME_" TIMESTAMP(6),
  "DURATION_" NUMBER(19,0),
  "DELETE_REASON_" NVARCHAR2(2000),
  "TENANT_ID_" NVARCHAR2(255) DEFAULT '',
  "TRANSACTION_ORDER_" NUMBER
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_RU_ACTINST
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_RU_DEADLETTER_JOB
-- ----------------------------
-- DROP TABLE "ACT_RU_DEADLETTER_JOB";
CREATE TABLE "ACT_RU_DEADLETTER_JOB" (
  "ID_" NVARCHAR2(64) NOT NULL,
  "REV_" NUMBER,
  "TYPE_" NVARCHAR2(255) NOT NULL,
  "EXCLUSIVE_" NUMBER(1,0),
  "EXECUTION_ID_" NVARCHAR2(64),
  "PROCESS_INSTANCE_ID_" NVARCHAR2(64),
  "PROC_DEF_ID_" NVARCHAR2(64),
  "ELEMENT_ID_" NVARCHAR2(255),
  "ELEMENT_NAME_" NVARCHAR2(255),
  "SCOPE_ID_" NVARCHAR2(255),
  "SUB_SCOPE_ID_" NVARCHAR2(255),
  "SCOPE_TYPE_" NVARCHAR2(255),
  "SCOPE_DEFINITION_ID_" NVARCHAR2(255),
  "EXCEPTION_STACK_ID_" NVARCHAR2(64),
  "EXCEPTION_MSG_" NVARCHAR2(2000),
  "DUEDATE_" TIMESTAMP(6),
  "REPEAT_" NVARCHAR2(255),
  "HANDLER_TYPE_" NVARCHAR2(255),
  "HANDLER_CFG_" NVARCHAR2(2000),
  "CUSTOM_VALUES_ID_" NVARCHAR2(64),
  "CREATE_TIME_" TIMESTAMP(6),
  "TENANT_ID_" NVARCHAR2(255) DEFAULT '',
  "CATEGORY_" NVARCHAR2(255),
  "CORRELATION_ID_" NVARCHAR2(255)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_RU_DEADLETTER_JOB
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_RU_ENTITYLINK
-- ----------------------------
-- DROP TABLE "ACT_RU_ENTITYLINK";
CREATE TABLE "ACT_RU_ENTITYLINK" (
  "ID_" NVARCHAR2(64) NOT NULL,
  "REV_" NUMBER,
  "CREATE_TIME_" TIMESTAMP(6),
  "LINK_TYPE_" NVARCHAR2(255),
  "SCOPE_ID_" NVARCHAR2(255),
  "SCOPE_TYPE_" NVARCHAR2(255),
  "SCOPE_DEFINITION_ID_" NVARCHAR2(255),
  "REF_SCOPE_ID_" NVARCHAR2(255),
  "REF_SCOPE_TYPE_" NVARCHAR2(255),
  "REF_SCOPE_DEFINITION_ID_" NVARCHAR2(255),
  "HIERARCHY_TYPE_" NVARCHAR2(255),
  "ROOT_SCOPE_ID_" NVARCHAR2(255),
  "ROOT_SCOPE_TYPE_" NVARCHAR2(255),
  "SUB_SCOPE_ID_" NVARCHAR2(255),
  "PARENT_ELEMENT_ID_" NVARCHAR2(255)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_RU_ENTITYLINK
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_RU_EVENT_SUBSCR
-- ----------------------------
-- DROP TABLE "ACT_RU_EVENT_SUBSCR";
CREATE TABLE "ACT_RU_EVENT_SUBSCR" (
  "ID_" NVARCHAR2(64) NOT NULL,
  "REV_" NUMBER,
  "EVENT_TYPE_" NVARCHAR2(255) NOT NULL,
  "EVENT_NAME_" NVARCHAR2(255),
  "EXECUTION_ID_" NVARCHAR2(64),
  "PROC_INST_ID_" NVARCHAR2(64),
  "ACTIVITY_ID_" NVARCHAR2(64),
  "CONFIGURATION_" NVARCHAR2(255),
  "CREATED_" TIMESTAMP(6) NOT NULL,
  "PROC_DEF_ID_" NVARCHAR2(64),
  "SUB_SCOPE_ID_" NVARCHAR2(64),
  "SCOPE_ID_" NVARCHAR2(64),
  "SCOPE_DEFINITION_ID_" NVARCHAR2(64),
  "SCOPE_TYPE_" NVARCHAR2(64),
  "TENANT_ID_" NVARCHAR2(255) DEFAULT '',
  "LOCK_TIME_" TIMESTAMP(6),
  "LOCK_OWNER_" NVARCHAR2(255),
  "SCOPE_DEFINITION_KEY_" NVARCHAR2(255)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_RU_EVENT_SUBSCR
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_RU_EXECUTION
-- ----------------------------
-- DROP TABLE "ACT_RU_EXECUTION";
CREATE TABLE "ACT_RU_EXECUTION" (
  "ID_" NVARCHAR2(64) NOT NULL,
  "REV_" NUMBER,
  "PROC_INST_ID_" NVARCHAR2(64),
  "BUSINESS_KEY_" NVARCHAR2(255),
  "PARENT_ID_" NVARCHAR2(64),
  "PROC_DEF_ID_" NVARCHAR2(64),
  "SUPER_EXEC_" NVARCHAR2(64),
  "ROOT_PROC_INST_ID_" NVARCHAR2(64),
  "ACT_ID_" NVARCHAR2(255),
  "IS_ACTIVE_" NUMBER(1,0),
  "IS_CONCURRENT_" NUMBER(1,0),
  "IS_SCOPE_" NUMBER(1,0),
  "IS_EVENT_SCOPE_" NUMBER(1,0),
  "IS_MI_ROOT_" NUMBER(1,0),
  "SUSPENSION_STATE_" NUMBER,
  "CACHED_ENT_STATE_" NUMBER,
  "TENANT_ID_" NVARCHAR2(255) DEFAULT '',
  "NAME_" NVARCHAR2(255),
  "START_ACT_ID_" NVARCHAR2(255),
  "START_TIME_" TIMESTAMP(6),
  "START_USER_ID_" NVARCHAR2(255),
  "LOCK_TIME_" TIMESTAMP(6),
  "IS_COUNT_ENABLED_" NUMBER(1,0),
  "EVT_SUBSCR_COUNT_" NUMBER,
  "TASK_COUNT_" NUMBER,
  "JOB_COUNT_" NUMBER,
  "TIMER_JOB_COUNT_" NUMBER,
  "SUSP_JOB_COUNT_" NUMBER,
  "DEADLETTER_JOB_COUNT_" NUMBER,
  "VAR_COUNT_" NUMBER,
  "ID_LINK_COUNT_" NUMBER,
  "CALLBACK_ID_" NVARCHAR2(255),
  "CALLBACK_TYPE_" NVARCHAR2(255),
  "REFERENCE_ID_" NVARCHAR2(255),
  "REFERENCE_TYPE_" NVARCHAR2(255),
  "PROPAGATED_STAGE_INST_ID_" NVARCHAR2(255),
  "LOCK_OWNER_" NVARCHAR2(255),
  "EXTERNAL_WORKER_JOB_COUNT_" NUMBER,
  "BUSINESS_STATUS_" NVARCHAR2(255)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_RU_EXECUTION
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_RU_EXTERNAL_JOB
-- ----------------------------
-- DROP TABLE "ACT_RU_EXTERNAL_JOB";
CREATE TABLE "ACT_RU_EXTERNAL_JOB" (
  "ID_" NVARCHAR2(64) NOT NULL,
  "REV_" NUMBER,
  "CATEGORY_" NVARCHAR2(255),
  "TYPE_" NVARCHAR2(255) NOT NULL,
  "LOCK_EXP_TIME_" TIMESTAMP(6),
  "LOCK_OWNER_" NVARCHAR2(255),
  "EXCLUSIVE_" NUMBER(1,0),
  "EXECUTION_ID_" NVARCHAR2(64),
  "PROCESS_INSTANCE_ID_" NVARCHAR2(64),
  "PROC_DEF_ID_" NVARCHAR2(64),
  "ELEMENT_ID_" NVARCHAR2(255),
  "ELEMENT_NAME_" NVARCHAR2(255),
  "SCOPE_ID_" NVARCHAR2(255),
  "SUB_SCOPE_ID_" NVARCHAR2(255),
  "SCOPE_TYPE_" NVARCHAR2(255),
  "SCOPE_DEFINITION_ID_" NVARCHAR2(255),
  "RETRIES_" NUMBER,
  "EXCEPTION_STACK_ID_" NVARCHAR2(64),
  "EXCEPTION_MSG_" NVARCHAR2(2000),
  "DUEDATE_" TIMESTAMP(6),
  "REPEAT_" NVARCHAR2(255),
  "HANDLER_TYPE_" NVARCHAR2(255),
  "HANDLER_CFG_" NVARCHAR2(2000),
  "CUSTOM_VALUES_ID_" NVARCHAR2(64),
  "CREATE_TIME_" TIMESTAMP(6),
  "TENANT_ID_" NVARCHAR2(255) DEFAULT '',
  "CORRELATION_ID_" NVARCHAR2(255)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_RU_EXTERNAL_JOB
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_RU_HISTORY_JOB
-- ----------------------------
-- DROP TABLE "ACT_RU_HISTORY_JOB";
CREATE TABLE "ACT_RU_HISTORY_JOB" (
  "ID_" NVARCHAR2(64) NOT NULL,
  "REV_" NUMBER,
  "LOCK_EXP_TIME_" TIMESTAMP(6),
  "LOCK_OWNER_" NVARCHAR2(255),
  "RETRIES_" NUMBER,
  "EXCEPTION_STACK_ID_" NVARCHAR2(64),
  "EXCEPTION_MSG_" NVARCHAR2(2000),
  "HANDLER_TYPE_" NVARCHAR2(255),
  "HANDLER_CFG_" NVARCHAR2(2000),
  "CUSTOM_VALUES_ID_" NVARCHAR2(64),
  "ADV_HANDLER_CFG_ID_" NVARCHAR2(64),
  "CREATE_TIME_" TIMESTAMP(6),
  "SCOPE_TYPE_" NVARCHAR2(255),
  "TENANT_ID_" NVARCHAR2(255) DEFAULT ''
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_RU_HISTORY_JOB
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_RU_IDENTITYLINK
-- ----------------------------
-- DROP TABLE "ACT_RU_IDENTITYLINK";
CREATE TABLE "ACT_RU_IDENTITYLINK" (
  "ID_" NVARCHAR2(64) NOT NULL,
  "REV_" NUMBER,
  "GROUP_ID_" NVARCHAR2(255),
  "TYPE_" NVARCHAR2(255),
  "USER_ID_" NVARCHAR2(255),
  "TASK_ID_" NVARCHAR2(64),
  "PROC_INST_ID_" NVARCHAR2(64),
  "PROC_DEF_ID_" NVARCHAR2(64),
  "SCOPE_ID_" NVARCHAR2(255),
  "SCOPE_TYPE_" NVARCHAR2(255),
  "SCOPE_DEFINITION_ID_" NVARCHAR2(255),
  "SUB_SCOPE_ID_" NVARCHAR2(255)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_RU_IDENTITYLINK
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_RU_JOB
-- ----------------------------
-- DROP TABLE "ACT_RU_JOB";
CREATE TABLE "ACT_RU_JOB" (
  "ID_" NVARCHAR2(64) NOT NULL,
  "REV_" NUMBER,
  "TYPE_" NVARCHAR2(255) NOT NULL,
  "LOCK_EXP_TIME_" TIMESTAMP(6),
  "LOCK_OWNER_" NVARCHAR2(255),
  "EXCLUSIVE_" NUMBER(1,0),
  "EXECUTION_ID_" NVARCHAR2(64),
  "PROCESS_INSTANCE_ID_" NVARCHAR2(64),
  "PROC_DEF_ID_" NVARCHAR2(64),
  "ELEMENT_ID_" NVARCHAR2(255),
  "ELEMENT_NAME_" NVARCHAR2(255),
  "SCOPE_ID_" NVARCHAR2(255),
  "SUB_SCOPE_ID_" NVARCHAR2(255),
  "SCOPE_TYPE_" NVARCHAR2(255),
  "SCOPE_DEFINITION_ID_" NVARCHAR2(255),
  "RETRIES_" NUMBER,
  "EXCEPTION_STACK_ID_" NVARCHAR2(64),
  "EXCEPTION_MSG_" NVARCHAR2(2000),
  "DUEDATE_" TIMESTAMP(6),
  "REPEAT_" NVARCHAR2(255),
  "HANDLER_TYPE_" NVARCHAR2(255),
  "HANDLER_CFG_" NVARCHAR2(2000),
  "CUSTOM_VALUES_ID_" NVARCHAR2(64),
  "CREATE_TIME_" TIMESTAMP(6),
  "TENANT_ID_" NVARCHAR2(255) DEFAULT '',
  "CATEGORY_" NVARCHAR2(255),
  "CORRELATION_ID_" NVARCHAR2(255)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_RU_JOB
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_RU_SUSPENDED_JOB
-- ----------------------------
-- DROP TABLE "ACT_RU_SUSPENDED_JOB";
CREATE TABLE "ACT_RU_SUSPENDED_JOB" (
  "ID_" NVARCHAR2(64) NOT NULL,
  "REV_" NUMBER,
  "TYPE_" NVARCHAR2(255) NOT NULL,
  "EXCLUSIVE_" NUMBER(1,0),
  "EXECUTION_ID_" NVARCHAR2(64),
  "PROCESS_INSTANCE_ID_" NVARCHAR2(64),
  "PROC_DEF_ID_" NVARCHAR2(64),
  "ELEMENT_ID_" NVARCHAR2(255),
  "ELEMENT_NAME_" NVARCHAR2(255),
  "SCOPE_ID_" NVARCHAR2(255),
  "SUB_SCOPE_ID_" NVARCHAR2(255),
  "SCOPE_TYPE_" NVARCHAR2(255),
  "SCOPE_DEFINITION_ID_" NVARCHAR2(255),
  "RETRIES_" NUMBER,
  "EXCEPTION_STACK_ID_" NVARCHAR2(64),
  "EXCEPTION_MSG_" NVARCHAR2(2000),
  "DUEDATE_" TIMESTAMP(6),
  "REPEAT_" NVARCHAR2(255),
  "HANDLER_TYPE_" NVARCHAR2(255),
  "HANDLER_CFG_" NVARCHAR2(2000),
  "CUSTOM_VALUES_ID_" NVARCHAR2(64),
  "CREATE_TIME_" TIMESTAMP(6),
  "TENANT_ID_" NVARCHAR2(255) DEFAULT '',
  "CATEGORY_" NVARCHAR2(255),
  "CORRELATION_ID_" NVARCHAR2(255)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_RU_SUSPENDED_JOB
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_RU_TASK
-- ----------------------------
-- DROP TABLE "ACT_RU_TASK";
CREATE TABLE "ACT_RU_TASK" (
  "ID_" NVARCHAR2(64) NOT NULL,
  "REV_" NUMBER,
  "EXECUTION_ID_" NVARCHAR2(64),
  "PROC_INST_ID_" NVARCHAR2(64),
  "PROC_DEF_ID_" NVARCHAR2(64),
  "TASK_DEF_ID_" NVARCHAR2(64),
  "SCOPE_ID_" NVARCHAR2(255),
  "SUB_SCOPE_ID_" NVARCHAR2(255),
  "SCOPE_TYPE_" NVARCHAR2(255),
  "SCOPE_DEFINITION_ID_" NVARCHAR2(255),
  "NAME_" NVARCHAR2(255),
  "PARENT_TASK_ID_" NVARCHAR2(64),
  "DESCRIPTION_" NVARCHAR2(2000),
  "TASK_DEF_KEY_" NVARCHAR2(255),
  "OWNER_" NVARCHAR2(255),
  "ASSIGNEE_" NVARCHAR2(255),
  "DELEGATION_" NVARCHAR2(64),
  "PRIORITY_" NUMBER,
  "CREATE_TIME_" TIMESTAMP(6),
  "DUE_DATE_" TIMESTAMP(6),
  "CATEGORY_" NVARCHAR2(255),
  "SUSPENSION_STATE_" NUMBER,
  "TENANT_ID_" NVARCHAR2(255) DEFAULT '',
  "FORM_KEY_" NVARCHAR2(255),
  "CLAIM_TIME_" TIMESTAMP(6),
  "IS_COUNT_ENABLED_" NUMBER(1,0),
  "VAR_COUNT_" NUMBER,
  "ID_LINK_COUNT_" NUMBER,
  "SUB_TASK_COUNT_" NUMBER,
  "PROPAGATED_STAGE_INST_ID_" NVARCHAR2(255),
  "STATE_" NVARCHAR2(255),
  "IN_PROGRESS_TIME_" TIMESTAMP(6),
  "IN_PROGRESS_STARTED_BY_" NVARCHAR2(255),
  "CLAIMED_BY_" NVARCHAR2(255),
  "SUSPENDED_TIME_" TIMESTAMP(6),
  "SUSPENDED_BY_" NVARCHAR2(255),
  "IN_PROGRESS_DUE_DATE_" TIMESTAMP(6)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_RU_TASK
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_RU_TIMER_JOB
-- ----------------------------
-- DROP TABLE "ACT_RU_TIMER_JOB";
CREATE TABLE "ACT_RU_TIMER_JOB" (
  "ID_" NVARCHAR2(64) NOT NULL,
  "REV_" NUMBER,
  "TYPE_" NVARCHAR2(255) NOT NULL,
  "LOCK_EXP_TIME_" TIMESTAMP(6),
  "LOCK_OWNER_" NVARCHAR2(255),
  "EXCLUSIVE_" NUMBER(1,0),
  "EXECUTION_ID_" NVARCHAR2(64),
  "PROCESS_INSTANCE_ID_" NVARCHAR2(64),
  "PROC_DEF_ID_" NVARCHAR2(64),
  "ELEMENT_ID_" NVARCHAR2(255),
  "ELEMENT_NAME_" NVARCHAR2(255),
  "SCOPE_ID_" NVARCHAR2(255),
  "SUB_SCOPE_ID_" NVARCHAR2(255),
  "SCOPE_TYPE_" NVARCHAR2(255),
  "SCOPE_DEFINITION_ID_" NVARCHAR2(255),
  "RETRIES_" NUMBER,
  "EXCEPTION_STACK_ID_" NVARCHAR2(64),
  "EXCEPTION_MSG_" NVARCHAR2(2000),
  "DUEDATE_" TIMESTAMP(6),
  "REPEAT_" NVARCHAR2(255),
  "HANDLER_TYPE_" NVARCHAR2(255),
  "HANDLER_CFG_" NVARCHAR2(2000),
  "CUSTOM_VALUES_ID_" NVARCHAR2(64),
  "CREATE_TIME_" TIMESTAMP(6),
  "TENANT_ID_" NVARCHAR2(255) DEFAULT '',
  "CATEGORY_" NVARCHAR2(255),
  "CORRELATION_ID_" NVARCHAR2(255)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_RU_TIMER_JOB
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for ACT_RU_VARIABLE
-- ----------------------------
-- DROP TABLE "ACT_RU_VARIABLE";
CREATE TABLE "ACT_RU_VARIABLE" (
  "ID_" NVARCHAR2(64) NOT NULL,
  "REV_" NUMBER,
  "TYPE_" NVARCHAR2(255) NOT NULL,
  "NAME_" NVARCHAR2(255) NOT NULL,
  "EXECUTION_ID_" NVARCHAR2(64),
  "PROC_INST_ID_" NVARCHAR2(64),
  "TASK_ID_" NVARCHAR2(64),
  "SCOPE_ID_" NVARCHAR2(255),
  "SUB_SCOPE_ID_" NVARCHAR2(255),
  "SCOPE_TYPE_" NVARCHAR2(255),
  "BYTEARRAY_ID_" NVARCHAR2(64),
  "DOUBLE_" NUMBER,
  "LONG_" NUMBER(19,0),
  "TEXT_" NVARCHAR2(2000),
  "TEXT2_" NVARCHAR2(2000),
  "META_INFO_" NVARCHAR2(2000)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of ACT_RU_VARIABLE
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for FLW_CHANNEL_DEFINITION
-- ----------------------------
-- DROP TABLE "FLW_CHANNEL_DEFINITION";
CREATE TABLE "FLW_CHANNEL_DEFINITION" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL,
  "NAME_" VARCHAR2(255 BYTE),
  "VERSION_" NUMBER,
  "KEY_" VARCHAR2(255 BYTE),
  "CATEGORY_" VARCHAR2(255 BYTE),
  "DEPLOYMENT_ID_" VARCHAR2(255 BYTE),
  "CREATE_TIME_" TIMESTAMP(3),
  "TENANT_ID_" VARCHAR2(255 BYTE),
  "RESOURCE_NAME_" VARCHAR2(255 BYTE),
  "DESCRIPTION_" VARCHAR2(255 BYTE),
  "TYPE_" VARCHAR2(255 BYTE),
  "IMPLEMENTATION_" VARCHAR2(255 BYTE)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of FLW_CHANNEL_DEFINITION
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for FLW_EVENT_DEFINITION
-- ----------------------------
-- DROP TABLE "FLW_EVENT_DEFINITION";
CREATE TABLE "FLW_EVENT_DEFINITION" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL,
  "NAME_" VARCHAR2(255 BYTE),
  "VERSION_" NUMBER,
  "KEY_" VARCHAR2(255 BYTE),
  "CATEGORY_" VARCHAR2(255 BYTE),
  "DEPLOYMENT_ID_" VARCHAR2(255 BYTE),
  "TENANT_ID_" VARCHAR2(255 BYTE),
  "RESOURCE_NAME_" VARCHAR2(255 BYTE),
  "DESCRIPTION_" VARCHAR2(255 BYTE)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of FLW_EVENT_DEFINITION
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for FLW_EVENT_DEPLOYMENT
-- ----------------------------
-- DROP TABLE "FLW_EVENT_DEPLOYMENT";
CREATE TABLE "FLW_EVENT_DEPLOYMENT" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL,
  "NAME_" VARCHAR2(255 BYTE),
  "CATEGORY_" VARCHAR2(255 BYTE),
  "DEPLOY_TIME_" TIMESTAMP(3),
  "TENANT_ID_" VARCHAR2(255 BYTE),
  "PARENT_DEPLOYMENT_ID_" VARCHAR2(255 BYTE)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of FLW_EVENT_DEPLOYMENT
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for FLW_EVENT_RESOURCE
-- ----------------------------
-- DROP TABLE "FLW_EVENT_RESOURCE";
CREATE TABLE "FLW_EVENT_RESOURCE" (
  "ID_" VARCHAR2(255 BYTE) NOT NULL,
  "NAME_" VARCHAR2(255 BYTE),
  "DEPLOYMENT_ID_" VARCHAR2(255 BYTE),
  "RESOURCE_BYTES_" BLOB
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of FLW_EVENT_RESOURCE
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for FLW_EV_DATABASECHANGELOG
-- ----------------------------
-- DROP TABLE "FLW_EV_DATABASECHANGELOG";
CREATE TABLE "FLW_EV_DATABASECHANGELOG" (
  "ID" VARCHAR2(255 BYTE) NOT NULL,
  "AUTHOR" VARCHAR2(255 BYTE) NOT NULL,
  "FILENAME" VARCHAR2(255 BYTE) NOT NULL,
  "DATEEXECUTED" TIMESTAMP(6) NOT NULL,
  "ORDEREXECUTED" NUMBER NOT NULL,
  "EXECTYPE" VARCHAR2(10 BYTE) NOT NULL,
  "MD5SUM" VARCHAR2(35 BYTE),
  "DESCRIPTION" VARCHAR2(255 BYTE),
  "COMMENTS" VARCHAR2(255 BYTE),
  "TAG" VARCHAR2(255 BYTE),
  "LIQUIBASE" VARCHAR2(20 BYTE),
  "CONTEXTS" VARCHAR2(255 BYTE),
  "LABELS" VARCHAR2(255 BYTE),
  "DEPLOYMENT_ID" VARCHAR2(10 BYTE)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of FLW_EV_DATABASECHANGELOG
-- ----------------------------
INSERT INTO "FLW_EV_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") VALUES ('1', 'flowable', 'org/flowable/eventregistry/db/liquibase/flowable-eventregistry-db-changelog.xml', TO_TIMESTAMP('2024-03-15 01:15:30.559500', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '1', 'EXECUTED', '9:63268f536c469325acef35970312551b', 'createTable tableName=FLW_EVENT_DEPLOYMENT; createTable tableName=FLW_EVENT_RESOURCE; createTable tableName=FLW_EVENT_DEFINITION; createIndex indexName=ACT_IDX_EVENT_DEF_UNIQ, tableName=FLW_EVENT_DEFINITION; createTable tableName=FLW_CHANNEL_DEFIN...', NULL, NULL, '4.24.0', NULL, NULL, '0436530407');
INSERT INTO "FLW_EV_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") VALUES ('2', 'flowable', 'org/flowable/eventregistry/db/liquibase/flowable-eventregistry-db-changelog.xml', TO_TIMESTAMP('2024-03-15 01:15:30.597357', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '2', 'EXECUTED', '9:dcb58b7dfd6dbda66939123a96985536', 'addColumn tableName=FLW_CHANNEL_DEFINITION; addColumn tableName=FLW_CHANNEL_DEFINITION', NULL, NULL, '4.24.0', NULL, NULL, '0436530407');
INSERT INTO "FLW_EV_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") VALUES ('3', 'flowable', 'org/flowable/eventregistry/db/liquibase/flowable-eventregistry-db-changelog.xml', TO_TIMESTAMP('2024-03-15 01:15:30.614363', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '3', 'EXECUTED', '9:d0c05678d57af23ad93699991e3bf4f6', 'customChange', NULL, NULL, '4.24.0', NULL, NULL, '0436530407');
COMMIT;

-- ----------------------------
-- Table structure for FLW_EV_DATABASECHANGELOGLOCK
-- ----------------------------
-- DROP TABLE "FLW_EV_DATABASECHANGELOGLOCK";
CREATE TABLE "FLW_EV_DATABASECHANGELOGLOCK" (
  "ID" NUMBER NOT NULL,
  "LOCKED" NUMBER(1,0) NOT NULL,
  "LOCKGRANTED" TIMESTAMP(6),
  "LOCKEDBY" VARCHAR2(255 BYTE)
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of FLW_EV_DATABASECHANGELOGLOCK
-- ----------------------------
INSERT INTO "FLW_EV_DATABASECHANGELOGLOCK" ("ID", "LOCKED", "LOCKGRANTED", "LOCKEDBY") VALUES ('1', '0', NULL, NULL);
COMMIT;

-- ----------------------------
-- Table structure for FLW_RU_BATCH
-- ----------------------------
-- DROP TABLE "FLW_RU_BATCH";
CREATE TABLE "FLW_RU_BATCH" (
  "ID_" NVARCHAR2(64) NOT NULL,
  "REV_" NUMBER,
  "TYPE_" NVARCHAR2(64) NOT NULL,
  "SEARCH_KEY_" NVARCHAR2(255),
  "SEARCH_KEY2_" NVARCHAR2(255),
  "CREATE_TIME_" TIMESTAMP(6) NOT NULL,
  "COMPLETE_TIME_" TIMESTAMP(6),
  "STATUS_" NVARCHAR2(255),
  "BATCH_DOC_ID_" NVARCHAR2(64),
  "TENANT_ID_" NVARCHAR2(255) DEFAULT ''
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of FLW_RU_BATCH
-- ----------------------------
COMMIT;

-- ----------------------------
-- Table structure for FLW_RU_BATCH_PART
-- ----------------------------
-- DROP TABLE "FLW_RU_BATCH_PART";
CREATE TABLE "FLW_RU_BATCH_PART" (
  "ID_" NVARCHAR2(64) NOT NULL,
  "REV_" NUMBER,
  "BATCH_ID_" NVARCHAR2(64),
  "TYPE_" NVARCHAR2(64) NOT NULL,
  "SCOPE_ID_" NVARCHAR2(64),
  "SUB_SCOPE_ID_" NVARCHAR2(64),
  "SCOPE_TYPE_" NVARCHAR2(64),
  "SEARCH_KEY_" NVARCHAR2(255),
  "SEARCH_KEY2_" NVARCHAR2(255),
  "CREATE_TIME_" TIMESTAMP(6) NOT NULL,
  "COMPLETE_TIME_" TIMESTAMP(6),
  "STATUS_" NVARCHAR2(255),
  "RESULT_DOC_ID_" NVARCHAR2(64),
  "TENANT_ID_" NVARCHAR2(255) DEFAULT ''
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Records of FLW_RU_BATCH_PART
-- ----------------------------
COMMIT;

-- ----------------------------
-- Sequence structure for ACT_EVT_LOG_SEQ
-- ----------------------------
-- DROP SEQUENCE "ACT_EVT_LOG_SEQ";
CREATE SEQUENCE "ACT_EVT_LOG_SEQ" MINVALUE 1 MAXVALUE 9999999999999999999999999999 INCREMENT BY 1 CACHE 20;

-- ----------------------------
-- Sequence structure for ACT_HI_TASK_EVT_LOG_SEQ
-- ----------------------------
-- DROP SEQUENCE "ACT_HI_TASK_EVT_LOG_SEQ";
CREATE SEQUENCE "ACT_HI_TASK_EVT_LOG_SEQ" MINVALUE 1 MAXVALUE 9999999999999999999999999999 INCREMENT BY 1 CACHE 20;

-- ----------------------------
-- Primary Key structure for table ACT_APP_APPDEF
-- ----------------------------
ALTER TABLE "ACT_APP_APPDEF" ADD CONSTRAINT "PK_ACT_APP_APPDEF" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_APP_APPDEF
-- ----------------------------
ALTER TABLE "ACT_APP_APPDEF" ADD CONSTRAINT "SYS_C0013782" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_APP_APPDEF" ADD CONSTRAINT "SYS_C0013783" CHECK ("REV_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_APP_APPDEF" ADD CONSTRAINT "SYS_C0013784" CHECK ("KEY_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_APP_APPDEF" ADD CONSTRAINT "SYS_C0013785" CHECK ("VERSION_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_APP_APPDEF" ADD CONSTRAINT "SYS_C0015409" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_APP_APPDEF" ADD CONSTRAINT "SYS_C0015410" CHECK ("REV_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_APP_APPDEF" ADD CONSTRAINT "SYS_C0015411" CHECK ("KEY_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_APP_APPDEF" ADD CONSTRAINT "SYS_C0015412" CHECK ("VERSION_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_APP_APPDEF
-- ----------------------------
CREATE INDEX "ACT_IDX_APP_DEF_DPLY"
  ON "ACT_APP_APPDEF" ("DEPLOYMENT_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE UNIQUE INDEX "ACT_IDX_APP_DEF_UNIQ"
  ON "ACT_APP_APPDEF" ("VERSION_" ASC, "TENANT_ID_" ASC, "KEY_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Checks structure for table ACT_APP_DATABASECHANGELOG
-- ----------------------------
ALTER TABLE "ACT_APP_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0013786" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_APP_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0013787" CHECK ("AUTHOR" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_APP_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0013788" CHECK ("FILENAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_APP_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0013789" CHECK ("DATEEXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_APP_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0013790" CHECK ("ORDEREXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_APP_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0013791" CHECK ("EXECTYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_APP_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015398" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_APP_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015399" CHECK ("AUTHOR" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_APP_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015400" CHECK ("FILENAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_APP_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015401" CHECK ("DATEEXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_APP_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015402" CHECK ("ORDEREXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_APP_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015403" CHECK ("EXECTYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_APP_DATABASECHANGELOGLOCK
-- ----------------------------
ALTER TABLE "ACT_APP_DATABASECHANGELOGLOCK" ADD CONSTRAINT "PK_ACT_APP_DATABASECHANGELOGLO" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table ACT_APP_DATABASECHANGELOGLOCK
-- ----------------------------
ALTER TABLE "ACT_APP_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0013792" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_APP_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0013793" CHECK ("LOCKED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_APP_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0015395" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_APP_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0015396" CHECK ("LOCKED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_APP_DEPLOYMENT
-- ----------------------------
ALTER TABLE "ACT_APP_DEPLOYMENT" ADD CONSTRAINT "PK_ACT_APP_DEPLOYMENT" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_APP_DEPLOYMENT
-- ----------------------------
ALTER TABLE "ACT_APP_DEPLOYMENT" ADD CONSTRAINT "SYS_C0013794" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_APP_DEPLOYMENT" ADD CONSTRAINT "SYS_C0015404" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_APP_DEPLOYMENT_RESOURCE
-- ----------------------------
ALTER TABLE "ACT_APP_DEPLOYMENT_RESOURCE" ADD CONSTRAINT "PK_APP_DEPLOYMENT_RESOURCE" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_APP_DEPLOYMENT_RESOURCE
-- ----------------------------
ALTER TABLE "ACT_APP_DEPLOYMENT_RESOURCE" ADD CONSTRAINT "SYS_C0013795" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_APP_DEPLOYMENT_RESOURCE" ADD CONSTRAINT "SYS_C0015406" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_APP_DEPLOYMENT_RESOURCE
-- ----------------------------
CREATE INDEX "ACT_IDX_APP_RSRC_DPL"
  ON "ACT_APP_DEPLOYMENT_RESOURCE" ("DEPLOYMENT_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_CMMN_CASEDEF
-- ----------------------------
ALTER TABLE "ACT_CMMN_CASEDEF" ADD CONSTRAINT "PK_ACT_CMMN_CASEDEF" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_CMMN_CASEDEF
-- ----------------------------
ALTER TABLE "ACT_CMMN_CASEDEF" ADD CONSTRAINT "SYS_C0013796" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_CASEDEF" ADD CONSTRAINT "SYS_C0013797" CHECK ("REV_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_CASEDEF" ADD CONSTRAINT "SYS_C0013798" CHECK ("KEY_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_CASEDEF" ADD CONSTRAINT "SYS_C0013799" CHECK ("VERSION_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_CASEDEF" ADD CONSTRAINT "SYS_C0015429" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_CASEDEF" ADD CONSTRAINT "SYS_C0015430" CHECK ("REV_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_CASEDEF" ADD CONSTRAINT "SYS_C0015431" CHECK ("KEY_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_CASEDEF" ADD CONSTRAINT "SYS_C0015432" CHECK ("VERSION_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_CMMN_CASEDEF
-- ----------------------------
CREATE INDEX "ACT_IDX_CASE_DEF_DPLY"
  ON "ACT_CMMN_CASEDEF" ("DEPLOYMENT_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE UNIQUE INDEX "ACT_IDX_CASE_DEF_UNIQ"
  ON "ACT_CMMN_CASEDEF" ("TENANT_ID_" ASC, "KEY_" ASC, "VERSION_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Checks structure for table ACT_CMMN_DATABASECHANGELOG
-- ----------------------------
ALTER TABLE "ACT_CMMN_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0013800" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0013801" CHECK ("AUTHOR" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0013802" CHECK ("FILENAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0013803" CHECK ("DATEEXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0013804" CHECK ("ORDEREXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0013805" CHECK ("EXECTYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015418" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015419" CHECK ("AUTHOR" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015420" CHECK ("FILENAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015421" CHECK ("DATEEXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015422" CHECK ("ORDEREXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015423" CHECK ("EXECTYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_CMMN_DATABASECHANGELOGLOCK
-- ----------------------------
ALTER TABLE "ACT_CMMN_DATABASECHANGELOGLOCK" ADD CONSTRAINT "PK_ACT_CMMN_DATABASECHANGELOGL" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table ACT_CMMN_DATABASECHANGELOGLOCK
-- ----------------------------
ALTER TABLE "ACT_CMMN_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0013806" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0013807" CHECK ("LOCKED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0015415" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0015416" CHECK ("LOCKED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_CMMN_DEPLOYMENT
-- ----------------------------
ALTER TABLE "ACT_CMMN_DEPLOYMENT" ADD CONSTRAINT "PK_ACT_CMMN_DEPLOYMENT" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_CMMN_DEPLOYMENT
-- ----------------------------
ALTER TABLE "ACT_CMMN_DEPLOYMENT" ADD CONSTRAINT "SYS_C0013808" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_DEPLOYMENT" ADD CONSTRAINT "SYS_C0015424" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_CMMN_DEPLOYMENT_RESOURCE
-- ----------------------------
ALTER TABLE "ACT_CMMN_DEPLOYMENT_RESOURCE" ADD CONSTRAINT "PK_CMMN_DEPLOYMENT_RESOURCE" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_CMMN_DEPLOYMENT_RESOURCE
-- ----------------------------
ALTER TABLE "ACT_CMMN_DEPLOYMENT_RESOURCE" ADD CONSTRAINT "SYS_C0013809" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_DEPLOYMENT_RESOURCE" ADD CONSTRAINT "SYS_C0015426" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_CMMN_DEPLOYMENT_RESOURCE
-- ----------------------------
CREATE INDEX "ACT_IDX_CMMN_RSRC_DPL"
  ON "ACT_CMMN_DEPLOYMENT_RESOURCE" ("DEPLOYMENT_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_CMMN_HI_CASE_INST
-- ----------------------------
ALTER TABLE "ACT_CMMN_HI_CASE_INST" ADD CONSTRAINT "PK_ACT_CMMN_HI_CASE_INST" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_CMMN_HI_CASE_INST
-- ----------------------------
ALTER TABLE "ACT_CMMN_HI_CASE_INST" ADD CONSTRAINT "SYS_C0013810" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_HI_CASE_INST" ADD CONSTRAINT "SYS_C0013811" CHECK ("REV_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_HI_CASE_INST" ADD CONSTRAINT "SYS_C0015459" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_HI_CASE_INST" ADD CONSTRAINT "SYS_C0015460" CHECK ("REV_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_CMMN_HI_CASE_INST
-- ----------------------------
CREATE INDEX "ACT_IDX_HI_CASE_INST_END"
  ON "ACT_CMMN_HI_CASE_INST" ("END_TIME_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_CMMN_HI_MIL_INST
-- ----------------------------
ALTER TABLE "ACT_CMMN_HI_MIL_INST" ADD CONSTRAINT "PK_ACT_CMMN_HI_MIL_INST" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_CMMN_HI_MIL_INST
-- ----------------------------
ALTER TABLE "ACT_CMMN_HI_MIL_INST" ADD CONSTRAINT "SYS_C0013812" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_HI_MIL_INST" ADD CONSTRAINT "SYS_C0013813" CHECK ("REV_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_HI_MIL_INST" ADD CONSTRAINT "SYS_C0013814" CHECK ("NAME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_HI_MIL_INST" ADD CONSTRAINT "SYS_C0013815" CHECK ("TIME_STAMP_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_HI_MIL_INST" ADD CONSTRAINT "SYS_C0013816" CHECK ("CASE_INST_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_HI_MIL_INST" ADD CONSTRAINT "SYS_C0013817" CHECK ("CASE_DEF_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_HI_MIL_INST" ADD CONSTRAINT "SYS_C0013818" CHECK ("ELEMENT_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_HI_MIL_INST" ADD CONSTRAINT "SYS_C0015462" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_HI_MIL_INST" ADD CONSTRAINT "SYS_C0015463" CHECK ("REV_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_HI_MIL_INST" ADD CONSTRAINT "SYS_C0015464" CHECK ("NAME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_HI_MIL_INST" ADD CONSTRAINT "SYS_C0015465" CHECK ("TIME_STAMP_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_HI_MIL_INST" ADD CONSTRAINT "SYS_C0015466" CHECK ("CASE_INST_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_HI_MIL_INST" ADD CONSTRAINT "SYS_C0015467" CHECK ("CASE_DEF_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_HI_MIL_INST" ADD CONSTRAINT "SYS_C0015468" CHECK ("ELEMENT_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_CMMN_HI_PLAN_ITEM_INST
-- ----------------------------
ALTER TABLE "ACT_CMMN_HI_PLAN_ITEM_INST" ADD CONSTRAINT "PK_ACT_CMMN_HI_PLAN_ITEM_INST" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_CMMN_HI_PLAN_ITEM_INST
-- ----------------------------
ALTER TABLE "ACT_CMMN_HI_PLAN_ITEM_INST" ADD CONSTRAINT "SYS_C0013819" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_HI_PLAN_ITEM_INST" ADD CONSTRAINT "SYS_C0013820" CHECK ("REV_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_HI_PLAN_ITEM_INST" ADD CONSTRAINT "SYS_C0015470" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_HI_PLAN_ITEM_INST" ADD CONSTRAINT "SYS_C0015471" CHECK ("REV_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_CMMN_HI_PLAN_ITEM_INST
-- ----------------------------
CREATE INDEX "ACT_IDX_HI_PLAN_ITEM_INST_CASE"
  ON "ACT_CMMN_HI_PLAN_ITEM_INST" ("CASE_INST_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_CMMN_RU_CASE_INST
-- ----------------------------
ALTER TABLE "ACT_CMMN_RU_CASE_INST" ADD CONSTRAINT "PK_ACT_CMMN_RU_CASE_INST" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_CMMN_RU_CASE_INST
-- ----------------------------
ALTER TABLE "ACT_CMMN_RU_CASE_INST" ADD CONSTRAINT "SYS_C0013821" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_RU_CASE_INST" ADD CONSTRAINT "SYS_C0013822" CHECK ("REV_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_RU_CASE_INST" ADD CONSTRAINT "SYS_C0015435" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_RU_CASE_INST" ADD CONSTRAINT "SYS_C0015436" CHECK ("REV_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_CMMN_RU_CASE_INST
-- ----------------------------
CREATE INDEX "ACT_IDX_CASE_INST_CASE_DEF"
  ON "ACT_CMMN_RU_CASE_INST" ("CASE_DEF_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_CASE_INST_PARENT"
  ON "ACT_CMMN_RU_CASE_INST" ("PARENT_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_CASE_INST_REF_ID_"
  ON "ACT_CMMN_RU_CASE_INST" ("REFERENCE_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_CMMN_RU_MIL_INST
-- ----------------------------
ALTER TABLE "ACT_CMMN_RU_MIL_INST" ADD CONSTRAINT "PK_ACT_CMMN_RU_MIL_INST" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_CMMN_RU_MIL_INST
-- ----------------------------
ALTER TABLE "ACT_CMMN_RU_MIL_INST" ADD CONSTRAINT "SYS_C0013823" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_RU_MIL_INST" ADD CONSTRAINT "SYS_C0013824" CHECK ("NAME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_RU_MIL_INST" ADD CONSTRAINT "SYS_C0013825" CHECK ("TIME_STAMP_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_RU_MIL_INST" ADD CONSTRAINT "SYS_C0013826" CHECK ("CASE_INST_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_RU_MIL_INST" ADD CONSTRAINT "SYS_C0013827" CHECK ("CASE_DEF_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_RU_MIL_INST" ADD CONSTRAINT "SYS_C0013828" CHECK ("ELEMENT_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_RU_MIL_INST" ADD CONSTRAINT "SYS_C0015450" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_RU_MIL_INST" ADD CONSTRAINT "SYS_C0015451" CHECK ("NAME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_RU_MIL_INST" ADD CONSTRAINT "SYS_C0015452" CHECK ("TIME_STAMP_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_RU_MIL_INST" ADD CONSTRAINT "SYS_C0015453" CHECK ("CASE_INST_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_RU_MIL_INST" ADD CONSTRAINT "SYS_C0015454" CHECK ("CASE_DEF_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_RU_MIL_INST" ADD CONSTRAINT "SYS_C0015455" CHECK ("ELEMENT_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_CMMN_RU_MIL_INST
-- ----------------------------
CREATE INDEX "ACT_IDX_MIL_CASE_DEF"
  ON "ACT_CMMN_RU_MIL_INST" ("CASE_DEF_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_MIL_CASE_INST"
  ON "ACT_CMMN_RU_MIL_INST" ("CASE_INST_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_CMMN_RU_PLAN_ITEM_INST
-- ----------------------------
ALTER TABLE "ACT_CMMN_RU_PLAN_ITEM_INST" ADD CONSTRAINT "PK_CMMN_PLAN_ITEM_INST" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_CMMN_RU_PLAN_ITEM_INST
-- ----------------------------
ALTER TABLE "ACT_CMMN_RU_PLAN_ITEM_INST" ADD CONSTRAINT "SYS_C0013829" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_RU_PLAN_ITEM_INST" ADD CONSTRAINT "SYS_C0013830" CHECK ("REV_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_RU_PLAN_ITEM_INST" ADD CONSTRAINT "SYS_C0015439" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_RU_PLAN_ITEM_INST" ADD CONSTRAINT "SYS_C0015440" CHECK ("REV_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_CMMN_RU_PLAN_ITEM_INST
-- ----------------------------
CREATE INDEX "ACT_IDX_PLAN_ITEM_CASE_DEF"
  ON "ACT_CMMN_RU_PLAN_ITEM_INST" ("CASE_DEF_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_PLAN_ITEM_CASE_INST"
  ON "ACT_CMMN_RU_PLAN_ITEM_INST" ("CASE_INST_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_PLAN_ITEM_STAGE_INST"
  ON "ACT_CMMN_RU_PLAN_ITEM_INST" ("STAGE_INST_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_CMMN_RU_SENTRY_PART_INST
-- ----------------------------
ALTER TABLE "ACT_CMMN_RU_SENTRY_PART_INST" ADD CONSTRAINT "PK_CMMN_SENTRY_PART_INST" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_CMMN_RU_SENTRY_PART_INST
-- ----------------------------
ALTER TABLE "ACT_CMMN_RU_SENTRY_PART_INST" ADD CONSTRAINT "SYS_C0013831" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_RU_SENTRY_PART_INST" ADD CONSTRAINT "SYS_C0013832" CHECK ("REV_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_RU_SENTRY_PART_INST" ADD CONSTRAINT "SYS_C0015444" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CMMN_RU_SENTRY_PART_INST" ADD CONSTRAINT "SYS_C0015445" CHECK ("REV_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_CMMN_RU_SENTRY_PART_INST
-- ----------------------------
CREATE INDEX "ACT_IDX_SENTRY_CASE_DEF"
  ON "ACT_CMMN_RU_SENTRY_PART_INST" ("CASE_DEF_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_SENTRY_CASE_INST"
  ON "ACT_CMMN_RU_SENTRY_PART_INST" ("CASE_INST_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_SENTRY_PLAN_ITEM"
  ON "ACT_CMMN_RU_SENTRY_PART_INST" ("PLAN_ITEM_INST_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_CO_CONTENT_ITEM
-- ----------------------------
ALTER TABLE "ACT_CO_CONTENT_ITEM" ADD CONSTRAINT "PK_ACT_CO_CONTENT_ITEM" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_CO_CONTENT_ITEM
-- ----------------------------
ALTER TABLE "ACT_CO_CONTENT_ITEM" ADD CONSTRAINT "SYS_C0013833" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CO_CONTENT_ITEM" ADD CONSTRAINT "SYS_C0013834" CHECK ("NAME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CO_CONTENT_ITEM" ADD CONSTRAINT "SYS_C0015517" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CO_CONTENT_ITEM" ADD CONSTRAINT "SYS_C0015518" CHECK ("NAME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_CO_CONTENT_ITEM
-- ----------------------------
CREATE INDEX "IDX_CONTITEM_PROCID"
  ON "ACT_CO_CONTENT_ITEM" ("PROC_INST_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "IDX_CONTITEM_SCOPE"
  ON "ACT_CO_CONTENT_ITEM" ("SCOPE_TYPE_" ASC, "SCOPE_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "IDX_CONTITEM_TASKID"
  ON "ACT_CO_CONTENT_ITEM" ("TASK_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Checks structure for table ACT_CO_DATABASECHANGELOG
-- ----------------------------
ALTER TABLE "ACT_CO_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0013835" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CO_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0013836" CHECK ("AUTHOR" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CO_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0013837" CHECK ("FILENAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CO_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0013838" CHECK ("DATEEXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CO_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0013839" CHECK ("ORDEREXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CO_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0013840" CHECK ("EXECTYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CO_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015511" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CO_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015512" CHECK ("AUTHOR" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CO_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015513" CHECK ("FILENAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CO_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015514" CHECK ("DATEEXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CO_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015515" CHECK ("ORDEREXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CO_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015516" CHECK ("EXECTYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_CO_DATABASECHANGELOGLOCK
-- ----------------------------
ALTER TABLE "ACT_CO_DATABASECHANGELOGLOCK" ADD CONSTRAINT "PK_ACT_CO_DATABASECHANGELOGLOC" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table ACT_CO_DATABASECHANGELOGLOCK
-- ----------------------------
ALTER TABLE "ACT_CO_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0013841" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CO_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0013842" CHECK ("LOCKED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CO_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0015508" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_CO_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0015509" CHECK ("LOCKED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Checks structure for table ACT_DE_DATABASECHANGELOG
-- ----------------------------
ALTER TABLE "ACT_DE_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0013843" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0013844" CHECK ("AUTHOR" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0013845" CHECK ("FILENAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0013846" CHECK ("DATEEXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0013847" CHECK ("ORDEREXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0013848" CHECK ("EXECTYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015523" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015524" CHECK ("AUTHOR" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015525" CHECK ("FILENAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015526" CHECK ("DATEEXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015527" CHECK ("ORDEREXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015528" CHECK ("EXECTYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_DE_DATABASECHANGELOGLOCK
-- ----------------------------
ALTER TABLE "ACT_DE_DATABASECHANGELOGLOCK" ADD CONSTRAINT "PK_ACT_DE_DATABASECHANGELOGLOC" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table ACT_DE_DATABASECHANGELOGLOCK
-- ----------------------------
ALTER TABLE "ACT_DE_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0013849" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0013850" CHECK ("LOCKED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0015520" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0015521" CHECK ("LOCKED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_DE_MODEL
-- ----------------------------
ALTER TABLE "ACT_DE_MODEL" ADD CONSTRAINT "PK_ACT_DE_MODEL" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table ACT_DE_MODEL
-- ----------------------------
ALTER TABLE "ACT_DE_MODEL" ADD CONSTRAINT "SYS_C0013851" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_MODEL" ADD CONSTRAINT "SYS_C0013852" CHECK ("NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_MODEL" ADD CONSTRAINT "SYS_C0013853" CHECK ("MODEL_KEY" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_MODEL" ADD CONSTRAINT "SYS_C0015529" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_MODEL" ADD CONSTRAINT "SYS_C0015530" CHECK ("NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_MODEL" ADD CONSTRAINT "SYS_C0015531" CHECK ("MODEL_KEY" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_DE_MODEL
-- ----------------------------
CREATE INDEX "IDX_PROC_MOD_CREATED"
  ON "ACT_DE_MODEL" ("CREATED_BY" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_DE_MODEL_HISTORY
-- ----------------------------
ALTER TABLE "ACT_DE_MODEL_HISTORY" ADD CONSTRAINT "PK_ACT_DE_MODEL_HISTORY" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table ACT_DE_MODEL_HISTORY
-- ----------------------------
ALTER TABLE "ACT_DE_MODEL_HISTORY" ADD CONSTRAINT "SYS_C0013854" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_MODEL_HISTORY" ADD CONSTRAINT "SYS_C0013855" CHECK ("NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_MODEL_HISTORY" ADD CONSTRAINT "SYS_C0013856" CHECK ("MODEL_KEY" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_MODEL_HISTORY" ADD CONSTRAINT "SYS_C0013857" CHECK ("MODEL_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_MODEL_HISTORY" ADD CONSTRAINT "SYS_C0015533" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_MODEL_HISTORY" ADD CONSTRAINT "SYS_C0015534" CHECK ("NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_MODEL_HISTORY" ADD CONSTRAINT "SYS_C0015535" CHECK ("MODEL_KEY" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_MODEL_HISTORY" ADD CONSTRAINT "SYS_C0015536" CHECK ("MODEL_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_DE_MODEL_HISTORY
-- ----------------------------
CREATE INDEX "IDX_PROC_MOD_HISTORY_PROC"
  ON "ACT_DE_MODEL_HISTORY" ("MODEL_ID" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_DE_MODEL_RELATION
-- ----------------------------
ALTER TABLE "ACT_DE_MODEL_RELATION" ADD CONSTRAINT "PK_ACT_DE_MODEL_RELATION" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table ACT_DE_MODEL_RELATION
-- ----------------------------
ALTER TABLE "ACT_DE_MODEL_RELATION" ADD CONSTRAINT "SYS_C0013858" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_MODEL_RELATION" ADD CONSTRAINT "SYS_C0015538" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Checks structure for table ACT_DMN_DATABASECHANGELOG
-- ----------------------------
ALTER TABLE "ACT_DMN_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0013859" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DMN_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0013860" CHECK ("AUTHOR" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DMN_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0013861" CHECK ("FILENAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DMN_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0013862" CHECK ("DATEEXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DMN_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0013863" CHECK ("ORDEREXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DMN_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0013864" CHECK ("EXECTYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DMN_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015476" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DMN_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015477" CHECK ("AUTHOR" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DMN_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015478" CHECK ("FILENAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DMN_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015479" CHECK ("DATEEXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DMN_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015480" CHECK ("ORDEREXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DMN_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015481" CHECK ("EXECTYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_DMN_DATABASECHANGELOGLOCK
-- ----------------------------
ALTER TABLE "ACT_DMN_DATABASECHANGELOGLOCK" ADD CONSTRAINT "PK_ACT_DMN_DATABASECHANGELOGLO" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table ACT_DMN_DATABASECHANGELOGLOCK
-- ----------------------------
ALTER TABLE "ACT_DMN_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0013865" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DMN_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0013866" CHECK ("LOCKED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DMN_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0015473" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DMN_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0015474" CHECK ("LOCKED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_DMN_DECISION
-- ----------------------------
ALTER TABLE "ACT_DMN_DECISION" ADD CONSTRAINT "PK_ACT_DMN_DECISION_TABLE" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_DMN_DECISION
-- ----------------------------
ALTER TABLE "ACT_DMN_DECISION" ADD CONSTRAINT "SYS_C0013867" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DMN_DECISION" ADD CONSTRAINT "SYS_C0015486" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_DMN_DECISION
-- ----------------------------
CREATE UNIQUE INDEX "ACT_IDX_DMN_DEC_UNIQ"
  ON "ACT_DMN_DECISION" ("KEY_" ASC, "VERSION_" ASC, "TENANT_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_DMN_DEPLOYMENT
-- ----------------------------
ALTER TABLE "ACT_DMN_DEPLOYMENT" ADD CONSTRAINT "PK_ACT_DMN_DEPLOYMENT" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_DMN_DEPLOYMENT
-- ----------------------------
ALTER TABLE "ACT_DMN_DEPLOYMENT" ADD CONSTRAINT "SYS_C0013868" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DMN_DEPLOYMENT" ADD CONSTRAINT "SYS_C0015482" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_DMN_DEPLOYMENT_RESOURCE
-- ----------------------------
ALTER TABLE "ACT_DMN_DEPLOYMENT_RESOURCE" ADD CONSTRAINT "PK_ACT_DMN_DEPLOYMENT_RESOURCE" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_DMN_DEPLOYMENT_RESOURCE
-- ----------------------------
ALTER TABLE "ACT_DMN_DEPLOYMENT_RESOURCE" ADD CONSTRAINT "SYS_C0013869" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DMN_DEPLOYMENT_RESOURCE" ADD CONSTRAINT "SYS_C0015484" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_DMN_HI_DECISION_EXECUTION
-- ----------------------------
ALTER TABLE "ACT_DMN_HI_DECISION_EXECUTION" ADD CONSTRAINT "PK_ACT_DMN_HI_DECISION_EXECUTI" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_DMN_HI_DECISION_EXECUTION
-- ----------------------------
ALTER TABLE "ACT_DMN_HI_DECISION_EXECUTION" ADD CONSTRAINT "SYS_C0013870" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DMN_HI_DECISION_EXECUTION" ADD CONSTRAINT "SYS_C0015488" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_DMN_HI_DECISION_EXECUTION
-- ----------------------------
CREATE INDEX "ACT_IDX_DMN_INSTANCE_ID"
  ON "ACT_DMN_HI_DECISION_EXECUTION" ("INSTANCE_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_EVT_LOG
-- ----------------------------
ALTER TABLE "ACT_EVT_LOG" ADD CONSTRAINT "SYS_C0015308" PRIMARY KEY ("LOG_NR_");

-- ----------------------------
-- Checks structure for table ACT_EVT_LOG
-- ----------------------------
ALTER TABLE "ACT_EVT_LOG" ADD CONSTRAINT "SYS_C0013871" CHECK ("LOG_NR_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_EVT_LOG" ADD CONSTRAINT "SYS_C0013872" CHECK ("TIME_STAMP_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_EVT_LOG" ADD CONSTRAINT "SYS_C0015307" CHECK ("TIME_STAMP_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Checks structure for table ACT_FO_DATABASECHANGELOG
-- ----------------------------
ALTER TABLE "ACT_FO_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0013873" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_FO_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0013874" CHECK ("AUTHOR" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_FO_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0013875" CHECK ("FILENAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_FO_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0013876" CHECK ("DATEEXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_FO_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0013877" CHECK ("ORDEREXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_FO_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0013878" CHECK ("EXECTYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_FO_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015493" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_FO_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015494" CHECK ("AUTHOR" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_FO_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015495" CHECK ("FILENAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_FO_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015496" CHECK ("DATEEXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_FO_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015497" CHECK ("ORDEREXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_FO_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0015498" CHECK ("EXECTYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_FO_DATABASECHANGELOGLOCK
-- ----------------------------
ALTER TABLE "ACT_FO_DATABASECHANGELOGLOCK" ADD CONSTRAINT "PK_ACT_FO_DATABASECHANGELOGLOC" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table ACT_FO_DATABASECHANGELOGLOCK
-- ----------------------------
ALTER TABLE "ACT_FO_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0013879" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_FO_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0013880" CHECK ("LOCKED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_FO_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0015490" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_FO_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0015491" CHECK ("LOCKED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_FO_FORM_DEFINITION
-- ----------------------------
ALTER TABLE "ACT_FO_FORM_DEFINITION" ADD CONSTRAINT "PK_ACT_FO_FORM_DEFINITION" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_FO_FORM_DEFINITION
-- ----------------------------
ALTER TABLE "ACT_FO_FORM_DEFINITION" ADD CONSTRAINT "SYS_C0013881" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_FO_FORM_DEFINITION" ADD CONSTRAINT "SYS_C0015503" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_FO_FORM_DEFINITION
-- ----------------------------
CREATE UNIQUE INDEX "ACT_IDX_FORM_DEF_UNIQ"
  ON "ACT_FO_FORM_DEFINITION" ("KEY_" ASC, "TENANT_ID_" ASC, "VERSION_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_FO_FORM_DEPLOYMENT
-- ----------------------------
ALTER TABLE "ACT_FO_FORM_DEPLOYMENT" ADD CONSTRAINT "PK_ACT_FO_FORM_DEPLOYMENT" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_FO_FORM_DEPLOYMENT
-- ----------------------------
ALTER TABLE "ACT_FO_FORM_DEPLOYMENT" ADD CONSTRAINT "SYS_C0013882" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_FO_FORM_DEPLOYMENT" ADD CONSTRAINT "SYS_C0015499" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_FO_FORM_INSTANCE
-- ----------------------------
ALTER TABLE "ACT_FO_FORM_INSTANCE" ADD CONSTRAINT "PK_ACT_FO_FORM_INSTANCE" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_FO_FORM_INSTANCE
-- ----------------------------
ALTER TABLE "ACT_FO_FORM_INSTANCE" ADD CONSTRAINT "SYS_C0013883" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_FO_FORM_INSTANCE" ADD CONSTRAINT "SYS_C0013884" CHECK ("FORM_DEFINITION_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_FO_FORM_INSTANCE" ADD CONSTRAINT "SYS_C0015505" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_FO_FORM_INSTANCE" ADD CONSTRAINT "SYS_C0015506" CHECK ("FORM_DEFINITION_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_FO_FORM_RESOURCE
-- ----------------------------
ALTER TABLE "ACT_FO_FORM_RESOURCE" ADD CONSTRAINT "PK_ACT_FO_FORM_RESOURCE" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_FO_FORM_RESOURCE
-- ----------------------------
ALTER TABLE "ACT_FO_FORM_RESOURCE" ADD CONSTRAINT "SYS_C0013885" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_FO_FORM_RESOURCE" ADD CONSTRAINT "SYS_C0015501" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_GE_BYTEARRAY
-- ----------------------------
ALTER TABLE "ACT_GE_BYTEARRAY" ADD CONSTRAINT "SYS_C0015239" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_GE_BYTEARRAY
-- ----------------------------
ALTER TABLE "ACT_GE_BYTEARRAY" ADD CONSTRAINT "SYS_C0013886" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_GE_BYTEARRAY" ADD CONSTRAINT "SYS_C0015238" CHECK (GENERATED_ IN (1,0)) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_GE_BYTEARRAY
-- ----------------------------
CREATE INDEX "ACT_IDX_BYTEAR_DEPL"
  ON "ACT_GE_BYTEARRAY" ("DEPLOYMENT_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_GE_PROPERTY
-- ----------------------------
ALTER TABLE "ACT_GE_PROPERTY" ADD CONSTRAINT "SYS_C0015237" PRIMARY KEY ("NAME_");

-- ----------------------------
-- Checks structure for table ACT_GE_PROPERTY
-- ----------------------------
ALTER TABLE "ACT_GE_PROPERTY" ADD CONSTRAINT "SYS_C0013887" CHECK ("NAME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_HI_ACTINST
-- ----------------------------
ALTER TABLE "ACT_HI_ACTINST" ADD CONSTRAINT "SYS_C0015366" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_HI_ACTINST
-- ----------------------------
ALTER TABLE "ACT_HI_ACTINST" ADD CONSTRAINT "SYS_C0013888" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_ACTINST" ADD CONSTRAINT "SYS_C0013889" CHECK ("PROC_DEF_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_ACTINST" ADD CONSTRAINT "SYS_C0013890" CHECK ("PROC_INST_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_ACTINST" ADD CONSTRAINT "SYS_C0013891" CHECK ("EXECUTION_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_ACTINST" ADD CONSTRAINT "SYS_C0013892" CHECK ("ACT_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_ACTINST" ADD CONSTRAINT "SYS_C0013893" CHECK ("ACT_TYPE_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_ACTINST" ADD CONSTRAINT "SYS_C0013894" CHECK ("START_TIME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_ACTINST" ADD CONSTRAINT "SYS_C0015359" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_ACTINST" ADD CONSTRAINT "SYS_C0015360" CHECK ("PROC_DEF_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_ACTINST" ADD CONSTRAINT "SYS_C0015361" CHECK ("PROC_INST_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_ACTINST" ADD CONSTRAINT "SYS_C0015362" CHECK ("EXECUTION_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_ACTINST" ADD CONSTRAINT "SYS_C0015363" CHECK ("ACT_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_ACTINST" ADD CONSTRAINT "SYS_C0015364" CHECK ("ACT_TYPE_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_ACTINST" ADD CONSTRAINT "SYS_C0015365" CHECK ("START_TIME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_HI_ACTINST
-- ----------------------------
CREATE INDEX "ACT_IDX_HI_ACT_INST_END"
  ON "ACT_HI_ACTINST" ("END_TIME_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_ACT_INST_EXEC"
  ON "ACT_HI_ACTINST" ("EXECUTION_ID_" ASC, "ACT_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_ACT_INST_PROCINST"
  ON "ACT_HI_ACTINST" ("PROC_INST_ID_" ASC, "ACT_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_ACT_INST_START"
  ON "ACT_HI_ACTINST" ("START_TIME_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_HI_ATTACHMENT
-- ----------------------------
ALTER TABLE "ACT_HI_ATTACHMENT" ADD CONSTRAINT "SYS_C0015376" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_HI_ATTACHMENT
-- ----------------------------
ALTER TABLE "ACT_HI_ATTACHMENT" ADD CONSTRAINT "SYS_C0013895" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_ATTACHMENT" ADD CONSTRAINT "SYS_C0015375" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_HI_COMMENT
-- ----------------------------
ALTER TABLE "ACT_HI_COMMENT" ADD CONSTRAINT "SYS_C0015374" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_HI_COMMENT
-- ----------------------------
ALTER TABLE "ACT_HI_COMMENT" ADD CONSTRAINT "SYS_C0013896" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_COMMENT" ADD CONSTRAINT "SYS_C0013897" CHECK ("TIME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_COMMENT" ADD CONSTRAINT "SYS_C0015372" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_COMMENT" ADD CONSTRAINT "SYS_C0015373" CHECK ("TIME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_HI_DETAIL
-- ----------------------------
ALTER TABLE "ACT_HI_DETAIL" ADD CONSTRAINT "SYS_C0015371" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_HI_DETAIL
-- ----------------------------
ALTER TABLE "ACT_HI_DETAIL" ADD CONSTRAINT "SYS_C0013898" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_DETAIL" ADD CONSTRAINT "SYS_C0013899" CHECK ("TYPE_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_DETAIL" ADD CONSTRAINT "SYS_C0013900" CHECK ("NAME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_DETAIL" ADD CONSTRAINT "SYS_C0013901" CHECK ("TIME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_DETAIL" ADD CONSTRAINT "SYS_C0015367" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_DETAIL" ADD CONSTRAINT "SYS_C0015368" CHECK ("TYPE_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_DETAIL" ADD CONSTRAINT "SYS_C0015369" CHECK ("NAME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_DETAIL" ADD CONSTRAINT "SYS_C0015370" CHECK ("TIME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_HI_DETAIL
-- ----------------------------
CREATE INDEX "ACT_IDX_HI_DETAIL_ACT_INST"
  ON "ACT_HI_DETAIL" ("ACT_INST_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_DETAIL_NAME"
  ON "ACT_HI_DETAIL" ("NAME_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_DETAIL_PROC_INST"
  ON "ACT_HI_DETAIL" ("PROC_INST_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_DETAIL_TASK_ID"
  ON "ACT_HI_DETAIL" ("TASK_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_DETAIL_TIME"
  ON "ACT_HI_DETAIL" ("TIME_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_HI_ENTITYLINK
-- ----------------------------
ALTER TABLE "ACT_HI_ENTITYLINK" ADD CONSTRAINT "SYS_C0015241" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_HI_ENTITYLINK
-- ----------------------------
ALTER TABLE "ACT_HI_ENTITYLINK" ADD CONSTRAINT "SYS_C0013902" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_HI_ENTITYLINK
-- ----------------------------
CREATE INDEX "ACT_IDX_HI_ENT_LNK_REF_SCOPE"
  ON "ACT_HI_ENTITYLINK" ("REF_SCOPE_ID_" ASC, "REF_SCOPE_TYPE_" ASC, "LINK_TYPE_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_ENT_LNK_ROOT_SCOPE"
  ON "ACT_HI_ENTITYLINK" ("ROOT_SCOPE_ID_" ASC, "ROOT_SCOPE_TYPE_" ASC, "LINK_TYPE_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_ENT_LNK_SCOPE"
  ON "ACT_HI_ENTITYLINK" ("SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC, "LINK_TYPE_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_ENT_LNK_SCOPE_DEF"
  ON "ACT_HI_ENTITYLINK" ("SCOPE_DEFINITION_ID_" ASC, "SCOPE_TYPE_" ASC, "LINK_TYPE_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_HI_IDENTITYLINK
-- ----------------------------
ALTER TABLE "ACT_HI_IDENTITYLINK" ADD CONSTRAINT "SYS_C0015243" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_HI_IDENTITYLINK
-- ----------------------------
ALTER TABLE "ACT_HI_IDENTITYLINK" ADD CONSTRAINT "SYS_C0013903" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_HI_IDENTITYLINK
-- ----------------------------
CREATE INDEX "ACT_IDX_HI_IDENT_LNK_PROCINST"
  ON "ACT_HI_IDENTITYLINK" ("PROC_INST_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_IDENT_LNK_SCOPE"
  ON "ACT_HI_IDENTITYLINK" ("SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_IDENT_LNK_SCOPE_DEF"
  ON "ACT_HI_IDENTITYLINK" ("SCOPE_TYPE_" ASC, "SCOPE_DEFINITION_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_IDENT_LNK_SUB_SCOPE"
  ON "ACT_HI_IDENTITYLINK" ("SUB_SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_IDENT_LNK_TASK"
  ON "ACT_HI_IDENTITYLINK" ("TASK_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_IDENT_LNK_USER"
  ON "ACT_HI_IDENTITYLINK" ("USER_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_HI_PROCINST
-- ----------------------------
ALTER TABLE "ACT_HI_PROCINST" ADD CONSTRAINT "SYS_C0015357" PRIMARY KEY ("ID_");

-- ----------------------------
-- Uniques structure for table ACT_HI_PROCINST
-- ----------------------------
ALTER TABLE "ACT_HI_PROCINST" ADD CONSTRAINT "SYS_C0015358" UNIQUE ("PROC_INST_ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Checks structure for table ACT_HI_PROCINST
-- ----------------------------
ALTER TABLE "ACT_HI_PROCINST" ADD CONSTRAINT "SYS_C0013904" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_PROCINST" ADD CONSTRAINT "SYS_C0013905" CHECK ("PROC_INST_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_PROCINST" ADD CONSTRAINT "SYS_C0013906" CHECK ("PROC_DEF_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_PROCINST" ADD CONSTRAINT "SYS_C0013907" CHECK ("START_TIME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_PROCINST" ADD CONSTRAINT "SYS_C0015353" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_PROCINST" ADD CONSTRAINT "SYS_C0015354" CHECK ("PROC_INST_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_PROCINST" ADD CONSTRAINT "SYS_C0015355" CHECK ("PROC_DEF_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_PROCINST" ADD CONSTRAINT "SYS_C0015356" CHECK ("START_TIME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_HI_PROCINST
-- ----------------------------
CREATE INDEX "ACT_IDX_HI_PRO_INST_END"
  ON "ACT_HI_PROCINST" ("END_TIME_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_PRO_I_BUSKEY"
  ON "ACT_HI_PROCINST" ("BUSINESS_KEY_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_PRO_SUPER_PROCINST"
  ON "ACT_HI_PROCINST" ("SUPER_PROCESS_INSTANCE_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_HI_TASKINST
-- ----------------------------
ALTER TABLE "ACT_HI_TASKINST" ADD CONSTRAINT "SYS_C0015274" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_HI_TASKINST
-- ----------------------------
ALTER TABLE "ACT_HI_TASKINST" ADD CONSTRAINT "SYS_C0013908" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_TASKINST" ADD CONSTRAINT "SYS_C0013909" CHECK ("START_TIME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_TASKINST" ADD CONSTRAINT "SYS_C0015272" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_TASKINST" ADD CONSTRAINT "SYS_C0015273" CHECK ("START_TIME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_HI_TASKINST
-- ----------------------------
CREATE INDEX "ACT_IDX_HI_TASK_INST_PROCINST"
  ON "ACT_HI_TASKINST" ("PROC_INST_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_TASK_SCOPE"
  ON "ACT_HI_TASKINST" ("SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_TASK_SCOPE_DEF"
  ON "ACT_HI_TASKINST" ("SCOPE_DEFINITION_ID_" ASC, "SCOPE_TYPE_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_TASK_SUB_SCOPE"
  ON "ACT_HI_TASKINST" ("SCOPE_TYPE_" ASC, "SUB_SCOPE_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_HI_TSK_LOG
-- ----------------------------
ALTER TABLE "ACT_HI_TSK_LOG" ADD CONSTRAINT "SYS_C0015277" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_HI_TSK_LOG
-- ----------------------------
ALTER TABLE "ACT_HI_TSK_LOG" ADD CONSTRAINT "SYS_C0013910" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_TSK_LOG" ADD CONSTRAINT "SYS_C0013911" CHECK ("TASK_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_TSK_LOG" ADD CONSTRAINT "SYS_C0013912" CHECK ("TIME_STAMP_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_TSK_LOG" ADD CONSTRAINT "SYS_C0015275" CHECK ("TASK_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_TSK_LOG" ADD CONSTRAINT "SYS_C0015276" CHECK ("TIME_STAMP_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_HI_VARINST
-- ----------------------------
ALTER TABLE "ACT_HI_VARINST" ADD CONSTRAINT "SYS_C0015285" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_HI_VARINST
-- ----------------------------
ALTER TABLE "ACT_HI_VARINST" ADD CONSTRAINT "SYS_C0013913" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_VARINST" ADD CONSTRAINT "SYS_C0013914" CHECK ("NAME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_VARINST" ADD CONSTRAINT "SYS_C0015283" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_HI_VARINST" ADD CONSTRAINT "SYS_C0015284" CHECK ("NAME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_HI_VARINST
-- ----------------------------
CREATE INDEX "ACT_IDX_HI_PROCVAR_EXE"
  ON "ACT_HI_VARINST" ("EXECUTION_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_PROCVAR_NAME_TYPE"
  ON "ACT_HI_VARINST" ("VAR_TYPE_" ASC, "NAME_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_PROCVAR_PROC_INST"
  ON "ACT_HI_VARINST" ("PROC_INST_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_PROCVAR_TASK_ID"
  ON "ACT_HI_VARINST" ("TASK_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_VAR_SCOPE_ID_TYPE"
  ON "ACT_HI_VARINST" ("SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_HI_VAR_SUB_ID_TYPE"
  ON "ACT_HI_VARINST" ("SUB_SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_ID_BYTEARRAY
-- ----------------------------
ALTER TABLE "ACT_ID_BYTEARRAY" ADD CONSTRAINT "SYS_C0015378" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_ID_BYTEARRAY
-- ----------------------------
ALTER TABLE "ACT_ID_BYTEARRAY" ADD CONSTRAINT "SYS_C0013915" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_ID_GROUP
-- ----------------------------
ALTER TABLE "ACT_ID_GROUP" ADD CONSTRAINT "SYS_C0015379" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_ID_GROUP
-- ----------------------------
ALTER TABLE "ACT_ID_GROUP" ADD CONSTRAINT "SYS_C0013916" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_ID_INFO
-- ----------------------------
ALTER TABLE "ACT_ID_INFO" ADD CONSTRAINT "SYS_C0015382" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_ID_INFO
-- ----------------------------
ALTER TABLE "ACT_ID_INFO" ADD CONSTRAINT "SYS_C0013917" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_ID_MEMBERSHIP
-- ----------------------------
ALTER TABLE "ACT_ID_MEMBERSHIP" ADD CONSTRAINT "SYS_C0015380" PRIMARY KEY ("USER_ID_", "GROUP_ID_");

-- ----------------------------
-- Checks structure for table ACT_ID_MEMBERSHIP
-- ----------------------------
ALTER TABLE "ACT_ID_MEMBERSHIP" ADD CONSTRAINT "SYS_C0013918" CHECK ("USER_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_ID_MEMBERSHIP" ADD CONSTRAINT "SYS_C0013919" CHECK ("GROUP_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_ID_MEMBERSHIP
-- ----------------------------
CREATE INDEX "ACT_IDX_MEMB_GROUP"
  ON "ACT_ID_MEMBERSHIP" ("GROUP_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_MEMB_USER"
  ON "ACT_ID_MEMBERSHIP" ("USER_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_ID_PRIV
-- ----------------------------
ALTER TABLE "ACT_ID_PRIV" ADD CONSTRAINT "SYS_C0015387" PRIMARY KEY ("ID_");

-- ----------------------------
-- Uniques structure for table ACT_ID_PRIV
-- ----------------------------
ALTER TABLE "ACT_ID_PRIV" ADD CONSTRAINT "ACT_UNIQ_PRIV_NAME" UNIQUE ("NAME_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Checks structure for table ACT_ID_PRIV
-- ----------------------------
ALTER TABLE "ACT_ID_PRIV" ADD CONSTRAINT "SYS_C0013920" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_ID_PRIV" ADD CONSTRAINT "SYS_C0013921" CHECK ("NAME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_ID_PRIV" ADD CONSTRAINT "SYS_C0015385" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_ID_PRIV" ADD CONSTRAINT "SYS_C0015386" CHECK ("NAME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_ID_PRIV_MAPPING
-- ----------------------------
ALTER TABLE "ACT_ID_PRIV_MAPPING" ADD CONSTRAINT "SYS_C0015390" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_ID_PRIV_MAPPING
-- ----------------------------
ALTER TABLE "ACT_ID_PRIV_MAPPING" ADD CONSTRAINT "SYS_C0013922" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_ID_PRIV_MAPPING" ADD CONSTRAINT "SYS_C0013923" CHECK ("PRIV_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_ID_PRIV_MAPPING" ADD CONSTRAINT "SYS_C0015388" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_ID_PRIV_MAPPING" ADD CONSTRAINT "SYS_C0015389" CHECK ("PRIV_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_ID_PRIV_MAPPING
-- ----------------------------
CREATE INDEX "ACT_IDX_PRIV_GROUP"
  ON "ACT_ID_PRIV_MAPPING" ("GROUP_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_PRIV_MAPPING"
  ON "ACT_ID_PRIV_MAPPING" ("PRIV_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_PRIV_USER"
  ON "ACT_ID_PRIV_MAPPING" ("USER_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_ID_PROPERTY
-- ----------------------------
ALTER TABLE "ACT_ID_PROPERTY" ADD CONSTRAINT "SYS_C0015377" PRIMARY KEY ("NAME_");

-- ----------------------------
-- Checks structure for table ACT_ID_PROPERTY
-- ----------------------------
ALTER TABLE "ACT_ID_PROPERTY" ADD CONSTRAINT "SYS_C0013924" CHECK ("NAME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_ID_TOKEN
-- ----------------------------
ALTER TABLE "ACT_ID_TOKEN" ADD CONSTRAINT "SYS_C0015384" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_ID_TOKEN
-- ----------------------------
ALTER TABLE "ACT_ID_TOKEN" ADD CONSTRAINT "SYS_C0013925" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_ID_TOKEN" ADD CONSTRAINT "SYS_C0015383" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_ID_USER
-- ----------------------------
ALTER TABLE "ACT_ID_USER" ADD CONSTRAINT "SYS_C0015381" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_ID_USER
-- ----------------------------
ALTER TABLE "ACT_ID_USER" ADD CONSTRAINT "SYS_C0013926" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_PROCDEF_INFO
-- ----------------------------
ALTER TABLE "ACT_PROCDEF_INFO" ADD CONSTRAINT "SYS_C0015311" PRIMARY KEY ("ID_");

-- ----------------------------
-- Uniques structure for table ACT_PROCDEF_INFO
-- ----------------------------
ALTER TABLE "ACT_PROCDEF_INFO" ADD CONSTRAINT "ACT_UNIQ_INFO_PROCDEF" UNIQUE ("PROC_DEF_ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Checks structure for table ACT_PROCDEF_INFO
-- ----------------------------
ALTER TABLE "ACT_PROCDEF_INFO" ADD CONSTRAINT "SYS_C0013927" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_PROCDEF_INFO" ADD CONSTRAINT "SYS_C0013928" CHECK ("PROC_DEF_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_PROCDEF_INFO" ADD CONSTRAINT "SYS_C0015309" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_PROCDEF_INFO" ADD CONSTRAINT "SYS_C0015310" CHECK ("PROC_DEF_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_PROCDEF_INFO
-- ----------------------------
CREATE INDEX "ACT_IDX_PROCDEF_INFO_JSON"
  ON "ACT_PROCDEF_INFO" ("INFO_JSON_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_RE_DEPLOYMENT
-- ----------------------------
ALTER TABLE "ACT_RE_DEPLOYMENT" ADD CONSTRAINT "SYS_C0015290" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_RE_DEPLOYMENT
-- ----------------------------
ALTER TABLE "ACT_RE_DEPLOYMENT" ADD CONSTRAINT "SYS_C0013929" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_RE_MODEL
-- ----------------------------
ALTER TABLE "ACT_RE_MODEL" ADD CONSTRAINT "SYS_C0015292" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_RE_MODEL
-- ----------------------------
ALTER TABLE "ACT_RE_MODEL" ADD CONSTRAINT "SYS_C0013930" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RE_MODEL" ADD CONSTRAINT "SYS_C0015291" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_RE_MODEL
-- ----------------------------
CREATE INDEX "ACT_IDX_MODEL_DEPLOYMENT"
  ON "ACT_RE_MODEL" ("DEPLOYMENT_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_MODEL_SOURCE"
  ON "ACT_RE_MODEL" ("EDITOR_SOURCE_VALUE_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_MODEL_SOURCE_EXTRA"
  ON "ACT_RE_MODEL" ("EDITOR_SOURCE_EXTRA_VALUE_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_RE_PROCDEF
-- ----------------------------
ALTER TABLE "ACT_RE_PROCDEF" ADD CONSTRAINT "SYS_C0015306" PRIMARY KEY ("ID_");

-- ----------------------------
-- Uniques structure for table ACT_RE_PROCDEF
-- ----------------------------
ALTER TABLE "ACT_RE_PROCDEF" ADD CONSTRAINT "ACT_UNIQ_PROCDEF" UNIQUE ("KEY_", "VERSION_", "DERIVED_VERSION_", "TENANT_ID_") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Checks structure for table ACT_RE_PROCDEF
-- ----------------------------
ALTER TABLE "ACT_RE_PROCDEF" ADD CONSTRAINT "SYS_C0013931" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RE_PROCDEF" ADD CONSTRAINT "SYS_C0013932" CHECK ("KEY_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RE_PROCDEF" ADD CONSTRAINT "SYS_C0013933" CHECK ("VERSION_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RE_PROCDEF" ADD CONSTRAINT "SYS_C0013934" CHECK ("DERIVED_VERSION_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RE_PROCDEF" ADD CONSTRAINT "SYS_C0015300" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RE_PROCDEF" ADD CONSTRAINT "SYS_C0015301" CHECK ("KEY_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RE_PROCDEF" ADD CONSTRAINT "SYS_C0015302" CHECK ("VERSION_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RE_PROCDEF" ADD CONSTRAINT "SYS_C0015303" CHECK ("DERIVED_VERSION_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RE_PROCDEF" ADD CONSTRAINT "SYS_C0015304" CHECK (HAS_START_FORM_KEY_ IN (1,0)) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RE_PROCDEF" ADD CONSTRAINT "SYS_C0015305" CHECK (HAS_GRAPHICAL_NOTATION_ IN (1,0)) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_RU_ACTINST
-- ----------------------------
ALTER TABLE "ACT_RU_ACTINST" ADD CONSTRAINT "SYS_C0015319" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_RU_ACTINST
-- ----------------------------
ALTER TABLE "ACT_RU_ACTINST" ADD CONSTRAINT "SYS_C0013935" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_ACTINST" ADD CONSTRAINT "SYS_C0013936" CHECK ("PROC_DEF_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_ACTINST" ADD CONSTRAINT "SYS_C0013937" CHECK ("PROC_INST_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_ACTINST" ADD CONSTRAINT "SYS_C0013938" CHECK ("EXECUTION_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_ACTINST" ADD CONSTRAINT "SYS_C0013939" CHECK ("ACT_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_ACTINST" ADD CONSTRAINT "SYS_C0013940" CHECK ("ACT_TYPE_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_ACTINST" ADD CONSTRAINT "SYS_C0013941" CHECK ("START_TIME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_ACTINST" ADD CONSTRAINT "SYS_C0015312" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_ACTINST" ADD CONSTRAINT "SYS_C0015313" CHECK ("PROC_DEF_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_ACTINST" ADD CONSTRAINT "SYS_C0015314" CHECK ("PROC_INST_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_ACTINST" ADD CONSTRAINT "SYS_C0015315" CHECK ("EXECUTION_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_ACTINST" ADD CONSTRAINT "SYS_C0015316" CHECK ("ACT_ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_ACTINST" ADD CONSTRAINT "SYS_C0015317" CHECK ("ACT_TYPE_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_ACTINST" ADD CONSTRAINT "SYS_C0015318" CHECK ("START_TIME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_RU_ACTINST
-- ----------------------------
CREATE INDEX "ACT_IDX_RU_ACTI_END"
  ON "ACT_RU_ACTINST" ("END_TIME_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_RU_ACTI_EXEC"
  ON "ACT_RU_ACTINST" ("EXECUTION_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_RU_ACTI_EXEC_ACT"
  ON "ACT_RU_ACTINST" ("ACT_ID_" ASC, "EXECUTION_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_RU_ACTI_PROC"
  ON "ACT_RU_ACTINST" ("PROC_INST_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_RU_ACTI_PROC_ACT"
  ON "ACT_RU_ACTINST" ("ACT_ID_" ASC, "PROC_INST_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_RU_ACTI_START"
  ON "ACT_RU_ACTINST" ("START_TIME_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_RU_ACTI_TASK"
  ON "ACT_RU_ACTINST" ("TASK_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_RU_DEADLETTER_JOB
-- ----------------------------
ALTER TABLE "ACT_RU_DEADLETTER_JOB" ADD CONSTRAINT "SYS_C0015259" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_RU_DEADLETTER_JOB
-- ----------------------------
ALTER TABLE "ACT_RU_DEADLETTER_JOB" ADD CONSTRAINT "SYS_C0013942" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_DEADLETTER_JOB" ADD CONSTRAINT "SYS_C0013943" CHECK ("TYPE_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_DEADLETTER_JOB" ADD CONSTRAINT "SYS_C0015256" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_DEADLETTER_JOB" ADD CONSTRAINT "SYS_C0015257" CHECK ("TYPE_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_DEADLETTER_JOB" ADD CONSTRAINT "SYS_C0015258" CHECK (EXCLUSIVE_ IN (1,0)) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_RU_DEADLETTER_JOB
-- ----------------------------
CREATE INDEX "ACT_IDX_DJOB_CORRELATION_ID"
  ON "ACT_RU_DEADLETTER_JOB" ("CORRELATION_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_DJOB_CUSTOM_VAL_ID"
  ON "ACT_RU_DEADLETTER_JOB" ("CUSTOM_VALUES_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_DJOB_EXCEPTION"
  ON "ACT_RU_DEADLETTER_JOB" ("EXCEPTION_STACK_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_DJOB_EXECUTION_ID"
  ON "ACT_RU_DEADLETTER_JOB" ("EXECUTION_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_DJOB_PROC_DEF_ID"
  ON "ACT_RU_DEADLETTER_JOB" ("PROC_DEF_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_DJOB_PROC_INST_ID"
  ON "ACT_RU_DEADLETTER_JOB" ("PROCESS_INSTANCE_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_DJOB_SCOPE"
  ON "ACT_RU_DEADLETTER_JOB" ("SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_DJOB_SCOPE_DEF"
  ON "ACT_RU_DEADLETTER_JOB" ("SCOPE_DEFINITION_ID_" ASC, "SCOPE_TYPE_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_DJOB_SUB_SCOPE"
  ON "ACT_RU_DEADLETTER_JOB" ("SUB_SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_RU_ENTITYLINK
-- ----------------------------
ALTER TABLE "ACT_RU_ENTITYLINK" ADD CONSTRAINT "SYS_C0015240" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_RU_ENTITYLINK
-- ----------------------------
ALTER TABLE "ACT_RU_ENTITYLINK" ADD CONSTRAINT "SYS_C0013944" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_RU_ENTITYLINK
-- ----------------------------
CREATE INDEX "ACT_IDX_ENT_LNK_REF_SCOPE"
  ON "ACT_RU_ENTITYLINK" ("REF_SCOPE_ID_" ASC, "REF_SCOPE_TYPE_" ASC, "LINK_TYPE_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_ENT_LNK_ROOT_SCOPE"
  ON "ACT_RU_ENTITYLINK" ("ROOT_SCOPE_ID_" ASC, "ROOT_SCOPE_TYPE_" ASC, "LINK_TYPE_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_ENT_LNK_SCOPE"
  ON "ACT_RU_ENTITYLINK" ("SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC, "LINK_TYPE_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_ENT_LNK_SCOPE_DEF"
  ON "ACT_RU_ENTITYLINK" ("SCOPE_DEFINITION_ID_" ASC, "SCOPE_TYPE_" ASC, "LINK_TYPE_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_RU_EVENT_SUBSCR
-- ----------------------------
ALTER TABLE "ACT_RU_EVENT_SUBSCR" ADD CONSTRAINT "SYS_C0015289" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_RU_EVENT_SUBSCR
-- ----------------------------
ALTER TABLE "ACT_RU_EVENT_SUBSCR" ADD CONSTRAINT "SYS_C0013945" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_EVENT_SUBSCR" ADD CONSTRAINT "SYS_C0013946" CHECK ("EVENT_TYPE_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_EVENT_SUBSCR" ADD CONSTRAINT "SYS_C0013947" CHECK ("CREATED_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_EVENT_SUBSCR" ADD CONSTRAINT "SYS_C0015286" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_EVENT_SUBSCR" ADD CONSTRAINT "SYS_C0015287" CHECK ("EVENT_TYPE_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_EVENT_SUBSCR" ADD CONSTRAINT "SYS_C0015288" CHECK ("CREATED_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_RU_EVENT_SUBSCR
-- ----------------------------
CREATE INDEX "ACT_IDX_EVENT_SUBSCR"
  ON "ACT_RU_EVENT_SUBSCR" ("EXECUTION_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_EVENT_SUBSCR_CONFIG_"
  ON "ACT_RU_EVENT_SUBSCR" ("CONFIGURATION_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_EVENT_SUBSCR_SCOPEREF_"
  ON "ACT_RU_EVENT_SUBSCR" ("SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_RU_EXECUTION
-- ----------------------------
ALTER TABLE "ACT_RU_EXECUTION" ADD CONSTRAINT "SYS_C0015299" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_RU_EXECUTION
-- ----------------------------
ALTER TABLE "ACT_RU_EXECUTION" ADD CONSTRAINT "SYS_C0013948" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_EXECUTION" ADD CONSTRAINT "SYS_C0015293" CHECK (IS_ACTIVE_ IN (1,0)) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_EXECUTION" ADD CONSTRAINT "SYS_C0015294" CHECK (IS_CONCURRENT_ IN (1,0)) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_EXECUTION" ADD CONSTRAINT "SYS_C0015295" CHECK (IS_SCOPE_ IN (1,0)) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_EXECUTION" ADD CONSTRAINT "SYS_C0015296" CHECK (IS_EVENT_SCOPE_ IN (1,0)) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_EXECUTION" ADD CONSTRAINT "SYS_C0015297" CHECK (IS_MI_ROOT_ IN (1,0)) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_EXECUTION" ADD CONSTRAINT "SYS_C0015298" CHECK (IS_COUNT_ENABLED_ IN (1,0)) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_RU_EXECUTION
-- ----------------------------
CREATE INDEX "ACT_IDX_EXEC_BUSKEY"
  ON "ACT_RU_EXECUTION" ("BUSINESS_KEY_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_EXEC_REF_ID_"
  ON "ACT_RU_EXECUTION" ("REFERENCE_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_EXEC_ROOT"
  ON "ACT_RU_EXECUTION" ("ROOT_PROC_INST_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_EXE_PARENT"
  ON "ACT_RU_EXECUTION" ("PARENT_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_EXE_PROCDEF"
  ON "ACT_RU_EXECUTION" ("PROC_DEF_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_EXE_PROCINST"
  ON "ACT_RU_EXECUTION" ("PROC_INST_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_EXE_SUPER"
  ON "ACT_RU_EXECUTION" ("SUPER_EXEC_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_RU_EXTERNAL_JOB
-- ----------------------------
ALTER TABLE "ACT_RU_EXTERNAL_JOB" ADD CONSTRAINT "SYS_C0014269" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_RU_EXTERNAL_JOB
-- ----------------------------
ALTER TABLE "ACT_RU_EXTERNAL_JOB" ADD CONSTRAINT "SYS_C0014266" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_EXTERNAL_JOB" ADD CONSTRAINT "SYS_C0014267" CHECK ("TYPE_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_EXTERNAL_JOB" ADD CONSTRAINT "SYS_C0014268" CHECK (EXCLUSIVE_ IN (1,0)) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_RU_EXTERNAL_JOB
-- ----------------------------
CREATE INDEX "ACT_IDX_EJOB_CORRELATION_ID"
  ON "ACT_RU_EXTERNAL_JOB" ("CORRELATION_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_EJOB_CUSTOM_VAL_ID"
  ON "ACT_RU_EXTERNAL_JOB" ("CUSTOM_VALUES_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_EJOB_EXCEPTION"
  ON "ACT_RU_EXTERNAL_JOB" ("EXCEPTION_STACK_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_EJOB_SCOPE"
  ON "ACT_RU_EXTERNAL_JOB" ("SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_EJOB_SCOPE_DEF"
  ON "ACT_RU_EXTERNAL_JOB" ("SCOPE_DEFINITION_ID_" ASC, "SCOPE_TYPE_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_EJOB_SUB_SCOPE"
  ON "ACT_RU_EXTERNAL_JOB" ("SUB_SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_RU_HISTORY_JOB
-- ----------------------------
ALTER TABLE "ACT_RU_HISTORY_JOB" ADD CONSTRAINT "SYS_C0015261" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_RU_HISTORY_JOB
-- ----------------------------
ALTER TABLE "ACT_RU_HISTORY_JOB" ADD CONSTRAINT "SYS_C0013949" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_HISTORY_JOB" ADD CONSTRAINT "SYS_C0015260" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table ACT_RU_IDENTITYLINK
-- ----------------------------
ALTER TABLE "ACT_RU_IDENTITYLINK" ADD CONSTRAINT "SYS_C0015242" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_RU_IDENTITYLINK
-- ----------------------------
ALTER TABLE "ACT_RU_IDENTITYLINK" ADD CONSTRAINT "SYS_C0013950" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_RU_IDENTITYLINK
-- ----------------------------
CREATE INDEX "ACT_IDX_ATHRZ_PROCEDEF"
  ON "ACT_RU_IDENTITYLINK" ("PROC_DEF_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_IDENT_LNK_GROUP"
  ON "ACT_RU_IDENTITYLINK" ("GROUP_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_IDENT_LNK_SCOPE"
  ON "ACT_RU_IDENTITYLINK" ("SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_IDENT_LNK_SCOPE_DEF"
  ON "ACT_RU_IDENTITYLINK" ("SCOPE_TYPE_" ASC, "SCOPE_DEFINITION_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_IDENT_LNK_SUB_SCOPE"
  ON "ACT_RU_IDENTITYLINK" ("SUB_SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_IDENT_LNK_USER"
  ON "ACT_RU_IDENTITYLINK" ("USER_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_IDL_PROCINST"
  ON "ACT_RU_IDENTITYLINK" ("PROC_INST_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_TSKASS_TASK"
  ON "ACT_RU_IDENTITYLINK" ("TASK_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_RU_JOB
-- ----------------------------
ALTER TABLE "ACT_RU_JOB" ADD CONSTRAINT "SYS_C0015247" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_RU_JOB
-- ----------------------------
ALTER TABLE "ACT_RU_JOB" ADD CONSTRAINT "SYS_C0013951" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_JOB" ADD CONSTRAINT "SYS_C0013952" CHECK ("TYPE_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_JOB" ADD CONSTRAINT "SYS_C0015244" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_JOB" ADD CONSTRAINT "SYS_C0015245" CHECK ("TYPE_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_JOB" ADD CONSTRAINT "SYS_C0015246" CHECK (EXCLUSIVE_ IN (1,0)) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_RU_JOB
-- ----------------------------
CREATE INDEX "ACT_IDX_JOB_CORRELATION_ID"
  ON "ACT_RU_JOB" ("CORRELATION_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_JOB_CUSTOM_VAL_ID"
  ON "ACT_RU_JOB" ("CUSTOM_VALUES_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_JOB_EXCEPTION"
  ON "ACT_RU_JOB" ("EXCEPTION_STACK_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_JOB_EXECUTION_ID"
  ON "ACT_RU_JOB" ("EXECUTION_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_JOB_PROC_DEF_ID"
  ON "ACT_RU_JOB" ("PROC_DEF_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_JOB_PROC_INST_ID"
  ON "ACT_RU_JOB" ("PROCESS_INSTANCE_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_JOB_SCOPE"
  ON "ACT_RU_JOB" ("SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_JOB_SCOPE_DEF"
  ON "ACT_RU_JOB" ("SCOPE_DEFINITION_ID_" ASC, "SCOPE_TYPE_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_JOB_SUB_SCOPE"
  ON "ACT_RU_JOB" ("SUB_SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_RU_SUSPENDED_JOB
-- ----------------------------
ALTER TABLE "ACT_RU_SUSPENDED_JOB" ADD CONSTRAINT "SYS_C0015255" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_RU_SUSPENDED_JOB
-- ----------------------------
ALTER TABLE "ACT_RU_SUSPENDED_JOB" ADD CONSTRAINT "SYS_C0013953" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_SUSPENDED_JOB" ADD CONSTRAINT "SYS_C0013954" CHECK ("TYPE_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_SUSPENDED_JOB" ADD CONSTRAINT "SYS_C0015252" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_SUSPENDED_JOB" ADD CONSTRAINT "SYS_C0015253" CHECK ("TYPE_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_SUSPENDED_JOB" ADD CONSTRAINT "SYS_C0015254" CHECK (EXCLUSIVE_ IN (1,0)) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_RU_SUSPENDED_JOB
-- ----------------------------
CREATE INDEX "ACT_IDX_SJOB_CORRELATION_ID"
  ON "ACT_RU_SUSPENDED_JOB" ("CORRELATION_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_SJOB_CUSTOM_VAL_ID"
  ON "ACT_RU_SUSPENDED_JOB" ("CUSTOM_VALUES_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_SJOB_EXCEPTION"
  ON "ACT_RU_SUSPENDED_JOB" ("EXCEPTION_STACK_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_SJOB_EXECUTION_ID"
  ON "ACT_RU_SUSPENDED_JOB" ("EXECUTION_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_SJOB_PROC_DEF_ID"
  ON "ACT_RU_SUSPENDED_JOB" ("PROC_DEF_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_SJOB_PROC_INST_ID"
  ON "ACT_RU_SUSPENDED_JOB" ("PROCESS_INSTANCE_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_SJOB_SCOPE"
  ON "ACT_RU_SUSPENDED_JOB" ("SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_SJOB_SCOPE_DEF"
  ON "ACT_RU_SUSPENDED_JOB" ("SCOPE_DEFINITION_ID_" ASC, "SCOPE_TYPE_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_SJOB_SUB_SCOPE"
  ON "ACT_RU_SUSPENDED_JOB" ("SUB_SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_RU_TASK
-- ----------------------------
ALTER TABLE "ACT_RU_TASK" ADD CONSTRAINT "SYS_C0015271" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_RU_TASK
-- ----------------------------
ALTER TABLE "ACT_RU_TASK" ADD CONSTRAINT "SYS_C0013955" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_TASK" ADD CONSTRAINT "SYS_C0015270" CHECK (IS_COUNT_ENABLED_ IN (1,0)) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_RU_TASK
-- ----------------------------
CREATE INDEX "ACT_IDX_TASK_CREATE"
  ON "ACT_RU_TASK" ("CREATE_TIME_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_TASK_EXEC"
  ON "ACT_RU_TASK" ("EXECUTION_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_TASK_PROCDEF"
  ON "ACT_RU_TASK" ("PROC_DEF_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_TASK_PROCINST"
  ON "ACT_RU_TASK" ("PROC_INST_ID_" ASC)
  LOGGING
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_TASK_SCOPE"
  ON "ACT_RU_TASK" ("SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC)
  LOGGING
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_TASK_SCOPE_DEF"
  ON "ACT_RU_TASK" ("SCOPE_DEFINITION_ID_" ASC, "SCOPE_TYPE_" ASC)
  LOGGING
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_TASK_SUB_SCOPE"
  ON "ACT_RU_TASK" ("SUB_SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_RU_TIMER_JOB
-- ----------------------------
ALTER TABLE "ACT_RU_TIMER_JOB" ADD CONSTRAINT "SYS_C0015251" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_RU_TIMER_JOB
-- ----------------------------
ALTER TABLE "ACT_RU_TIMER_JOB" ADD CONSTRAINT "SYS_C0013956" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_TIMER_JOB" ADD CONSTRAINT "SYS_C0013957" CHECK ("TYPE_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_TIMER_JOB" ADD CONSTRAINT "SYS_C0015248" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_TIMER_JOB" ADD CONSTRAINT "SYS_C0015249" CHECK ("TYPE_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_TIMER_JOB" ADD CONSTRAINT "SYS_C0015250" CHECK (EXCLUSIVE_ IN (1,0)) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_RU_TIMER_JOB
-- ----------------------------
CREATE INDEX "ACT_IDX_TJOB_CORRELATION_ID"
  ON "ACT_RU_TIMER_JOB" ("CORRELATION_ID_" ASC)
  LOGGING
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_TJOB_CUSTOM_VAL_ID"
  ON "ACT_RU_TIMER_JOB" ("CUSTOM_VALUES_ID_" ASC)
  LOGGING
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_TJOB_DUEDATE"
  ON "ACT_RU_TIMER_JOB" ("DUEDATE_" ASC)
  LOGGING
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_TJOB_EXCEPTION"
  ON "ACT_RU_TIMER_JOB" ("EXCEPTION_STACK_ID_" ASC)
  LOGGING
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_TJOB_EXECUTION_ID"
  ON "ACT_RU_TIMER_JOB" ("EXECUTION_ID_" ASC)
  LOGGING
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_TJOB_PROC_DEF_ID"
  ON "ACT_RU_TIMER_JOB" ("PROC_DEF_ID_" ASC)
  LOGGING
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_TJOB_PROC_INST_ID"
  ON "ACT_RU_TIMER_JOB" ("PROCESS_INSTANCE_ID_" ASC)
  LOGGING
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_TJOB_SCOPE"
  ON "ACT_RU_TIMER_JOB" ("SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC)
  LOGGING
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_TJOB_SCOPE_DEF"
  ON "ACT_RU_TIMER_JOB" ("SCOPE_DEFINITION_ID_" ASC, "SCOPE_TYPE_" ASC)
  LOGGING
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_TJOB_SUB_SCOPE"
  ON "ACT_RU_TIMER_JOB" ("SUB_SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC)
  LOGGING
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table ACT_RU_VARIABLE
-- ----------------------------
ALTER TABLE "ACT_RU_VARIABLE" ADD CONSTRAINT "SYS_C0015281" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table ACT_RU_VARIABLE
-- ----------------------------
ALTER TABLE "ACT_RU_VARIABLE" ADD CONSTRAINT "SYS_C0013958" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_VARIABLE" ADD CONSTRAINT "SYS_C0013959" CHECK ("TYPE_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_VARIABLE" ADD CONSTRAINT "SYS_C0013960" CHECK ("NAME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_VARIABLE" ADD CONSTRAINT "SYS_C0015278" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_VARIABLE" ADD CONSTRAINT "SYS_C0015279" CHECK ("TYPE_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_RU_VARIABLE" ADD CONSTRAINT "SYS_C0015280" CHECK ("NAME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table ACT_RU_VARIABLE
-- ----------------------------
CREATE INDEX "ACT_IDX_RU_VAR_SCOPE_ID_TYPE"
  ON "ACT_RU_VARIABLE" ("SCOPE_ID_" ASC, "SCOPE_TYPE_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_RU_VAR_SUB_ID_TYPE"
  ON "ACT_RU_VARIABLE" ("SCOPE_TYPE_" ASC, "SUB_SCOPE_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_VARIABLE_TASK_ID"
  ON "ACT_RU_VARIABLE" ("TASK_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_VAR_BYTEARRAY"
  ON "ACT_RU_VARIABLE" ("BYTEARRAY_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_VAR_EXE"
  ON "ACT_RU_VARIABLE" ("EXECUTION_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "ACT_IDX_VAR_PROCINST"
  ON "ACT_RU_VARIABLE" ("PROC_INST_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table FLW_CHANNEL_DEFINITION
-- ----------------------------
ALTER TABLE "FLW_CHANNEL_DEFINITION" ADD CONSTRAINT "PK_FLW_CHANNEL_DEFINITION" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table FLW_CHANNEL_DEFINITION
-- ----------------------------
ALTER TABLE "FLW_CHANNEL_DEFINITION" ADD CONSTRAINT "SYS_C0014296" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table FLW_CHANNEL_DEFINITION
-- ----------------------------
CREATE UNIQUE INDEX "ACT_IDX_CHANNEL_DEF_UNIQ"
  ON "FLW_CHANNEL_DEFINITION" ("KEY_" ASC, "VERSION_" ASC, "TENANT_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table FLW_EVENT_DEFINITION
-- ----------------------------
ALTER TABLE "FLW_EVENT_DEFINITION" ADD CONSTRAINT "PK_FLW_EVENT_DEFINITION" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table FLW_EVENT_DEFINITION
-- ----------------------------
ALTER TABLE "FLW_EVENT_DEFINITION" ADD CONSTRAINT "SYS_C0014294" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table FLW_EVENT_DEFINITION
-- ----------------------------
CREATE UNIQUE INDEX "ACT_IDX_EVENT_DEF_UNIQ"
  ON "FLW_EVENT_DEFINITION" ("KEY_" ASC, "VERSION_" ASC, "TENANT_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Primary Key structure for table FLW_EVENT_DEPLOYMENT
-- ----------------------------
ALTER TABLE "FLW_EVENT_DEPLOYMENT" ADD CONSTRAINT "PK_FLW_EVENT_DEPLOYMENT" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table FLW_EVENT_DEPLOYMENT
-- ----------------------------
ALTER TABLE "FLW_EVENT_DEPLOYMENT" ADD CONSTRAINT "SYS_C0014290" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table FLW_EVENT_RESOURCE
-- ----------------------------
ALTER TABLE "FLW_EVENT_RESOURCE" ADD CONSTRAINT "PK_FLW_EVENT_RESOURCE" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table FLW_EVENT_RESOURCE
-- ----------------------------
ALTER TABLE "FLW_EVENT_RESOURCE" ADD CONSTRAINT "SYS_C0014292" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Checks structure for table FLW_EV_DATABASECHANGELOG
-- ----------------------------
ALTER TABLE "FLW_EV_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0014281" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "FLW_EV_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0014282" CHECK ("AUTHOR" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "FLW_EV_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0014283" CHECK ("FILENAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "FLW_EV_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0014284" CHECK ("DATEEXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "FLW_EV_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0014285" CHECK ("ORDEREXECUTED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "FLW_EV_DATABASECHANGELOG" ADD CONSTRAINT "SYS_C0014286" CHECK ("EXECTYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table FLW_EV_DATABASECHANGELOGLOCK
-- ----------------------------
ALTER TABLE "FLW_EV_DATABASECHANGELOGLOCK" ADD CONSTRAINT "PK_FLW_EV_DATABASECHANGELOGLOC" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table FLW_EV_DATABASECHANGELOGLOCK
-- ----------------------------
ALTER TABLE "FLW_EV_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0014287" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "FLW_EV_DATABASECHANGELOGLOCK" ADD CONSTRAINT "SYS_C0014288" CHECK ("LOCKED" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table FLW_RU_BATCH
-- ----------------------------
ALTER TABLE "FLW_RU_BATCH" ADD CONSTRAINT "SYS_C0014275" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table FLW_RU_BATCH
-- ----------------------------
ALTER TABLE "FLW_RU_BATCH" ADD CONSTRAINT "SYS_C0014272" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "FLW_RU_BATCH" ADD CONSTRAINT "SYS_C0014273" CHECK ("TYPE_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "FLW_RU_BATCH" ADD CONSTRAINT "SYS_C0014274" CHECK ("CREATE_TIME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table FLW_RU_BATCH_PART
-- ----------------------------
ALTER TABLE "FLW_RU_BATCH_PART" ADD CONSTRAINT "SYS_C0014279" PRIMARY KEY ("ID_");

-- ----------------------------
-- Checks structure for table FLW_RU_BATCH_PART
-- ----------------------------
ALTER TABLE "FLW_RU_BATCH_PART" ADD CONSTRAINT "SYS_C0014276" CHECK ("ID_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "FLW_RU_BATCH_PART" ADD CONSTRAINT "SYS_C0014277" CHECK ("TYPE_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "FLW_RU_BATCH_PART" ADD CONSTRAINT "SYS_C0014278" CHECK ("CREATE_TIME_" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table FLW_RU_BATCH_PART
-- ----------------------------
CREATE INDEX "FLW_IDX_BATCH_PART"
  ON "FLW_RU_BATCH_PART" ("BATCH_ID_" ASC) LOCAL
  LOGGING
  ONLINE
  NOSORT
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Foreign Keys structure for table ACT_APP_APPDEF
-- ----------------------------

-- ----------------------------
-- Foreign Keys structure for table ACT_APP_DEPLOYMENT_RESOURCE
-- ----------------------------

-- ----------------------------
-- Foreign Keys structure for table ACT_CMMN_CASEDEF
-- ----------------------------

-- ----------------------------
-- Foreign Keys structure for table ACT_CMMN_DEPLOYMENT_RESOURCE
-- ----------------------------

-- ----------------------------
-- Foreign Keys structure for table ACT_CMMN_RU_CASE_INST
-- ----------------------------

-- ----------------------------
-- Foreign Keys structure for table ACT_CMMN_RU_MIL_INST
-- ----------------------------

-- ----------------------------
-- Foreign Keys structure for table ACT_CMMN_RU_PLAN_ITEM_INST
-- ----------------------------

-- ----------------------------
-- Foreign Keys structure for table ACT_CMMN_RU_SENTRY_PART_INST
-- ----------------------------

-- ----------------------------
-- Foreign Keys structure for table ACT_DE_MODEL_RELATION
-- ----------------------------
ALTER TABLE "ACT_DE_MODEL_RELATION" ADD CONSTRAINT "FK_RELATION_CHILD" FOREIGN KEY ("MODEL_ID") REFERENCES "ACT_DE_MODEL" ("ID") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "ACT_DE_MODEL_RELATION" ADD CONSTRAINT "FK_RELATION_PARENT" FOREIGN KEY ("PARENT_MODEL_ID") REFERENCES "ACT_DE_MODEL" ("ID") NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Foreign Keys structure for table ACT_GE_BYTEARRAY
-- ----------------------------

-- ----------------------------
-- Foreign Keys structure for table ACT_ID_MEMBERSHIP
-- ----------------------------

-- ----------------------------
-- Foreign Keys structure for table ACT_ID_PRIV_MAPPING
-- ----------------------------

-- ----------------------------
-- Foreign Keys structure for table ACT_PROCDEF_INFO
-- ----------------------------

-- ----------------------------
-- Foreign Keys structure for table ACT_RE_MODEL
-- ----------------------------

-- ----------------------------
-- Foreign Keys structure for table ACT_RU_DEADLETTER_JOB
-- ----------------------------

-- ----------------------------
-- Foreign Keys structure for table ACT_RU_EVENT_SUBSCR
-- ----------------------------

-- ----------------------------
-- Foreign Keys structure for table ACT_RU_EXECUTION
-- ----------------------------

-- ----------------------------
-- Foreign Keys structure for table ACT_RU_EXTERNAL_JOB
-- ----------------------------

-- ----------------------------
-- Foreign Keys structure for table ACT_RU_IDENTITYLINK
-- ----------------------------

-- ----------------------------
-- Foreign Keys structure for table ACT_RU_JOB
-- ----------------------------

-- ----------------------------
-- Foreign Keys structure for table ACT_RU_SUSPENDED_JOB
-- ----------------------------

-- ----------------------------
-- Foreign Keys structure for table ACT_RU_TASK
-- ----------------------------

-- ----------------------------
-- Foreign Keys structure for table ACT_RU_TIMER_JOB
-- ----------------------------

-- ----------------------------
-- Foreign Keys structure for table ACT_RU_VARIABLE
-- ----------------------------

-- ----------------------------
-- Foreign Keys structure for table FLW_RU_BATCH_PART
-- ----------------------------
