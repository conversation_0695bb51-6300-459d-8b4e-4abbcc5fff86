/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.ms.illegalrecords.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import org.springblade.ms.illegalrecords.pojo.entity.IllegalRecordsEntity;
import org.springblade.ms.illegalrecords.pojo.vo.IllegalRecordsVO;
import org.springblade.ms.illegalrecords.excel.IllegalRecordsExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import java.util.List;

/**
 * 案件信息 服务类
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
public interface IIllegalRecordsService extends BaseService<IllegalRecordsEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page 分页参数
	 * @param illegalRecords 查询参数
	 * @return IPage<IllegalRecordsVO>
	 */
	IPage<IllegalRecordsVO> selectIllegalRecordsPage(IPage<IllegalRecordsVO> page, IllegalRecordsVO illegalRecords);


	List<IllegalRecordsVO> selectPage(IPage<IllegalRecordsVO> page, IllegalRecordsVO illegalRecords);

	/**
	 * 导出数据
	 *
	 * @param queryWrapper 查询条件
	 * @return List<IllegalRecordsExcel>
	 */
	List<IllegalRecordsExcel> exportIllegalRecords(Wrapper<IllegalRecordsEntity> queryWrapper);

	List<IllegalRecordsEntity> getListByCustCode(IllegalRecordsVO illegalRecords);

}
