package org.springblade.ms.priceStandards.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 涉案价格标准表
 * @TableName ms_price_standards
 */
@TableName(value ="ms_price_standards")
@Data
public class MsPriceStandards extends TenantEntity implements Serializable {
    /**
     * 类型：品名/品规（卷烟规格名称）/品牌/类别
     */
    private String stdType;

    /**
     * 通用字段：品名/品规（卷烟规格名称）/品牌/类别
     */
    private String productName;

    /**
     * 附件1专用: 类别
     */
    private String productCategory;

    /**
     * 附件1专用：条包条形码
     */
    private String barcode;

    /**
     * 通用字段：价格（建议零售价/平均零售价/参考价格）
     */
    private BigDecimal price;

    /**
     * 通用字段：价格单位
     */
    private String priceUnit;

    /**
     * 附件4专用：备注
     */
    private String remarks;

    /**
     * 通用字段：年份
     */
    private Integer priceYear;

    /**
     * 涉案物品类型（不映射到数据库）
     */
    @TableField(exist = false)
    private String evidenceType;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}