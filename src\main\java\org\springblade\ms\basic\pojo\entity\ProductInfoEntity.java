/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.ms.basic.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import java.io.Serial;

/**
 * 品规信息 实体类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@TableName("ms_product_info")
@Schema(description = "ProductInfo对象")
@EqualsAndHashCode(callSuper = true)
public class ProductInfoEntity extends TenantEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 产品id
	 */
	@Schema(description = "产品id")
	private String productUuid;
	/**
	 * 产品编码
	 */
	@Schema(description = "产品编码")
	private String productCode;
	/**
	 * 产品名称
	 */
	@Schema(description = "产品名称")
	private String productName;
	/**
	 * 国家产品id
	 */
	@Schema(description = "国家产品id")
	private String gjProductUuid;
	/**
	 * 国家产品名称
	 */
	@Schema(description = "国家产品名称")
	private String gjProductName;
	/**
	 * 产品类型
	 */
	@Schema(description = "产品类型")
	private String productType;
	/**
	 * 短产品名称
	 */
	@Schema(description = "短产品名称")
	private String supplyShortName;
	/**
	 * 品牌编码
	 */
	@Schema(description = "品牌编码")
	private String brandCode;
	/**
	 * 品牌名称
	 */
	@Schema(description = "品牌名称")
	private String brandName;
	/**
	 * 品牌类型
	 */
	@Schema(description = "品牌类型")
	private String brandType;
	/**
	 * 包装长度(mm)
	 */
	@Schema(description = "包装长度(mm)")
	private Integer length;
	/**
	 * 包装宽度(mm)
	 */
	@Schema(description = "包装宽度(mm)")
	private Integer width;
	/**
	 * 包装高度(mm)
	 */
	@Schema(description = "包装高度(mm)")
	private Integer height;
	/**
	 * 条码
	 */
	@Schema(description = "条码")
	private String stripCode;
	/**
	 * 是否常销烟
	 */
	@Schema(description = "是否常销烟")
	private Integer isOfften;
	/**
	 * 是否进口烟
	 */
	@Schema(description = "是否进口烟")
	private Integer importFlag;
	/**
	 * 是否骨干品牌
	 */
	@Schema(description = "是否骨干品牌")
	private Integer isMainProduct;
	/**
	 * 是否名优烟
	 */
	@Schema(description = "是否名优烟")
	private Integer isFamous;
	/**
	 * 是否省内烟
	 */
	@Schema(description = "是否省内烟")
	private Integer isProvince;
	/**
	 * 是否本省在销烟
	 */
	@Schema(description = "是否本省在销烟")
	private Integer isLocSale;
	/**
	 * 是否查扣烟启用
	 */
	@Schema(description = "是否查扣烟启用")
	private Integer isSeized;
	/**
	 * 是否特种烟
	 */
	@Schema(description = "是否特种烟")
	private Integer isSpecial;
	/**
	 * 是否雪茄烟
	 */
	@Schema(description = "是否雪茄烟")
	private Integer isCigar;
	/**
	 * 是否高端品牌
	 */
	@Schema(description = "是否高端品牌")
	private Integer isHighTierBrand;
	/**
	 * 是否有效
	 */
	@Schema(description = "是否有效")
	private Integer isActive;

}
