package org.springblade.ms.dingapp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.ms.basic.pojo.vo.YhytLicenseVO;
import org.springblade.ms.dingapp.vo.DingMapLicenseVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-01-24 00:07
 */
public interface IDingLicenseService {

    /**
     * 获取地图中展示的零售户列表
     * @param searchParam
     * @param longitude
     * @param latitude
     * @return
     */
    List<DingMapLicenseVO> getDingMapLicenseList(String searchParam, String formatParam, BigDecimal longitude, BigDecimal latitude);

    /**
     * 获取用于地图显示的零售户详情
     * @param yhytId
     * @return
     */
    DingMapLicenseVO getDingMapLicense(String yhytId, String licNo);


    IPage<DingMapLicenseVO> selectYhytPage(String searchParam, String formatParam, IPage<YhytLicenseVO> page,BigDecimal longitude,BigDecimal latitude);
}
