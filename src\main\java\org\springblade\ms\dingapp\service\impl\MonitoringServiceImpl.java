package org.springblade.ms.dingapp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.modules.system.pojo.entity.Dept;
import org.springblade.modules.system.pojo.entity.User;
import org.springblade.modules.system.service.IDeptService;
import org.springblade.modules.system.service.IUserService;
import org.springblade.ms.dingapp.service.IMonitoringService;
import org.springblade.ms.dingapp.util.TimeRangeUtil;
import org.springblade.ms.dingapp.vo.ChartDataVO;
import org.springblade.ms.dingapp.vo.MonitoringDataVO;
import org.springblade.ms.dingapp.vo.PersonnelRankingVO;
import org.springblade.ms.exploration.pojo.entity.ExplorationEntity;
import org.springblade.ms.exploration.service.IExplorationService;
import org.springblade.ms.itemidentify.pojo.entity.ItemIdentifyEntity;
import org.springblade.ms.itemidentify.pojo.entity.ItemIdentifyResultsEntity;
import org.springblade.ms.itemidentify.service.IItemIdentifyResultsService;
import org.springblade.ms.itemidentify.service.IItemIdentifyService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 监控服务实现类
 *
 * <AUTHOR> Name
 * @since 2025-05-01
 */
@Service
@AllArgsConstructor
@Slf4j
public class MonitoringServiceImpl implements IMonitoringService {


    /**
     * Redis缓存键前缀
     */
    private static final String REDIS_KEY_PREFIX = "ms:monitoring:";
    private final RedisTemplate<String, Object> redisTemplate;

    private final IUserService userService;
    private final IDeptService deptService;
    private final IExplorationService explorationService;
    private final IItemIdentifyService itemIdentifyService;
    private final IItemIdentifyResultsService itemIdentifyResultsService;

    /**
     * 获取目标部门ID列表（父部门及其所有子部门）
     *
     * @return 部门ID列表（字符串类型）
     */
    private List<String> getTargetDeptIds(String deptId) {
        List<Long> longDeptIds = new ArrayList<>();
        Long parentId = CommonConstant.PARENT_DEPT_ID;
        if (StringUtil.isNotBlank(deptId)) {
            parentId = Long.valueOf(deptId);
        }
        // 添加父部门ID
        longDeptIds.add(parentId);

        // 获取所有子部门
        List<Dept> childDepts = deptService.getDeptChild(parentId);
        if (childDepts != null && !childDepts.isEmpty()) {
            List<Long> childDeptIds = childDepts.stream()
                    .map(Dept::getId)
                    .collect(Collectors.toList());
            longDeptIds.addAll(childDeptIds);
        }

        // 将 Long 类型的部门ID转换为 String 类型
        return longDeptIds.stream()
                .map(String::valueOf)
                .collect(Collectors.toList());
    }

    /**
     * 获取目标部门下的所有用户
     *
     * @return 用户列表
     */
    private List<User> getUsersByTargetDepts(String deptId) {
        // 获取目标部门ID列表（字符串类型）
        List<String> deptIds = getTargetDeptIds(deptId);

        // 如果没有部门，返回空列表
        if (deptIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 构建用户查询条件
        LambdaQueryWrapper<User> userQueryWrapper = Wrappers.<User>lambdaQuery()
                .eq(User::getIsDeleted, 0)
                .in(User::getDeptId, deptIds);

        // 查询并返回用户列表
        return userService.list(userQueryWrapper);
    }

    /**
     * 获取符合条件的用户ID列表
     *
     * @param deptId 部门ID
     * @param userId 用户ID
     * @return 用户ID列表
     */
    private List<Long> getFilteredUserIds(String deptId, String userId) {
        // 获取目标部门下的所有用户
        List<User> deptUsers = getUsersByTargetDepts(deptId);

        // 如果没有用户，返回空列表
        if (deptUsers.isEmpty()) {
            return new ArrayList<>();
        }

        // 如果指定了用户ID，只返回该用户（如果该用户在目标部门中）
        if (null != userId) {
            Long userIdLong = Long.valueOf(userId);
            List<Long> deptUserIds = deptUsers.stream()
                    .map(User::getId)
                    .collect(Collectors.toList());

            if (deptUserIds.contains(userIdLong)) {
                return Collections.singletonList(userIdLong);
            } else {
                return new ArrayList<>();
            }
        }

//        // 如果指定了部门ID，只返回该部门下的用户（如果这些用户在目标部门中）
//        if (null != deptId) {
//            // 获取指定部门下的所有用户
//            LambdaQueryWrapper<User> userQueryWrapper = Wrappers.<User>lambdaQuery()
//                    .eq(User::getIsDeleted, 0)
//                    .eq(User::getDeptId, deptId);
//
//            List<User> specificDeptUsers = userService.list(userQueryWrapper);
//            List<Long> specificDeptUserIds = specificDeptUsers.stream()
//                    .map(User::getId)
//                    .collect(Collectors.toList());
//
//            if (!specificDeptUserIds.isEmpty()) {
//                List<Long> targetDeptUserIds = deptUsers.stream()
//                        .map(User::getId)
//                        .collect(Collectors.toList());
//
//                // 取交集，只保留同时在目标部门和指定部门中的用户
//                specificDeptUserIds.retainAll(targetDeptUserIds);
//                return specificDeptUserIds;
//            } else {
//                return new ArrayList<>();
//            }
//        }

        // 如果没有指定部门ID和用户ID，返回所有目标部门下的用户ID
        return deptUsers.stream()
                .map(User::getId)
                .collect(Collectors.toList());
    }

    /**
     * 每隔10分钟执行一次，更新当天的监控数据到Redis
     * 只保存day类型的数据，其他时间范围的数据通过聚合day数据计算得出
     */
    //@Scheduled(cron = "0 */5 * * * ?")
    public void scheduledUpdateMonitoringData() {
        log.info("开始执行定时任务，更新当天监控数据到Redis");

        try {
            // 获取当天日期
            LocalDate today = LocalDate.now();
            // 获取前一天日期
            LocalDate yesterday = today.minusDays(1);

            // 只更新day类型的数据
            String timeRange = "day";

            // 更新全局数据（不指定角色和用户）
            // 先更新前一天的数据，确保计算趋势时有前一天的数据
            updateDailyMonitoringDataToRedis(timeRange, null, null, yesterday);
            updateDailyInspectionDataToRedis(timeRange, null, null, yesterday);
            updateDailyRecognitionDataToRedis(timeRange, null, null, yesterday);
            updateDailyCigaretteDataToRedis(timeRange, null, null, yesterday);
            log.info("成功更新日期 {} 的全局监控数据", yesterday.format(DateTimeFormatter.ISO_LOCAL_DATE));

            // 更新当天的数据
            updateDailyMonitoringDataToRedis(timeRange, null, null, today);
            updateDailyInspectionDataToRedis(timeRange, null, null, today);
            updateDailyRecognitionDataToRedis(timeRange, null, null, today);
            updateDailyCigaretteDataToRedis(timeRange, null, null, today);
            log.info("成功更新日期 {} 的全局监控数据", today.format(DateTimeFormatter.ISO_LOCAL_DATE));

            // 获取parentId=PARENT_DEPT_ID下的所有部门
            List<Dept> depts = deptService.list(
                    Wrappers.<Dept>lambdaQuery()
                            .eq(Dept::getParentId, CommonConstant.PARENT_DEPT_ID)
                            .eq(Dept::getIsDeleted, 0)
            );

            log.info("开始更新特定部门的监控数据，找到 {} 个部门", depts.size());

            // 遍历所有部门
            for (Dept dept : depts) {
                String deptId = String.valueOf(dept.getId());
                log.info("正在更新部门 [{}] 的监控数据", dept.getDeptName());

                // 先更新前一天的部门数据
                updateDailyMonitoringDataToRedis(timeRange, deptId, null, yesterday);
                updateDailyInspectionDataToRedis(timeRange, deptId, null, yesterday);
                updateDailyRecognitionDataToRedis(timeRange, deptId, null, yesterday);
                updateDailyCigaretteDataToRedis(timeRange, deptId, null, yesterday);

                // 更新当天的部门数据
                updateDailyMonitoringDataToRedis(timeRange, deptId, null, today);
                updateDailyInspectionDataToRedis(timeRange, deptId, null, today);
                updateDailyRecognitionDataToRedis(timeRange, deptId, null, today);
                updateDailyCigaretteDataToRedis(timeRange, deptId, null, today);

                // 获取该部门下的所有用户
                List<User> users = userService.list(
                        Wrappers.<User>lambdaQuery()
                                .eq(User::getDeptId, deptId)
                                .eq(User::getIsDeleted, 0)
                );
                log.info("部门 [{}] 下有 {} 个用户", dept.getDeptName(), users.size());

                // 遍历该部门下的所有用户
                for (User user : users) {
                    String userId = String.valueOf(user.getId());
                    log.info("正在更新用户 [{}] 的监控数据", user.getRealName());

                    // 先更新前一天的用户数据
                    updateDailyMonitoringDataToRedis(timeRange, deptId, userId, yesterday);
                    updateDailyInspectionDataToRedis(timeRange, deptId, userId, yesterday);
                    updateDailyRecognitionDataToRedis(timeRange, deptId, userId, yesterday);
                    updateDailyCigaretteDataToRedis(timeRange, deptId, userId, yesterday);

                    // 更新当天的用户数据
                    updateDailyMonitoringDataToRedis(timeRange, deptId, userId, today);
                    updateDailyInspectionDataToRedis(timeRange, deptId, userId, today);
                    updateDailyRecognitionDataToRedis(timeRange, deptId, userId, today);
                    updateDailyCigaretteDataToRedis(timeRange, deptId, userId, today);
                }
            }

            log.info("特定部门和用户的监控数据更新完成");
        } catch (Exception e) {
            log.error("更新当天监控数据失败", e);
        }

        log.info("监控数据更新定时任务执行完成");
    }

    /**
     * 更新指定日期、角色和用户的监控数据到Redis
     */
    private void updateDailyMonitoringDataToRedis(String timeRange, String deptId, String userId, LocalDate date) {
        // 构建Redis缓存键
        String cacheKey = buildCacheKey(timeRange, deptId, userId, date);

        // 获取当前日期的时间范围
        LocalDateTime[] dateRange = getDateTimeRange(date);

        // 生成监控数据
        MonitoringDataVO vo = generateDailyMonitoringData(dateRange[0], dateRange[1], deptId, userId);

        // 保存到Redis
        saveToRedis(cacheKey, vo);

        log.debug("保存日期 {} 的监控数据到Redis缓存成功, key: {}", date.format(DateTimeFormatter.ISO_LOCAL_DATE), cacheKey);
    }

    /**
     * 更新指定日期、角色和用户的检客户统计数据到Redis
     */
    private void updateDailyInspectionDataToRedis(String timeRange, String deptId, String userId, LocalDate date) {
        // 构建Redis缓存键
        String cacheKey = buildCacheKey(timeRange, deptId, userId, date) + ":inspection";

        // 获取当前日期的时间范围
        LocalDateTime[] dateRange = getDateTimeRange(date);

        // 生成图表数据
        ChartDataVO vo = generateDailyInspectionChartData(dateRange[0], dateRange[1], deptId, userId);

        // 保存到Redis
        try {
            redisTemplate.opsForValue().set(cacheKey, vo);
            log.debug("保存日期 {} 的检客户统计数据到Redis缓存成功, key: {}", date.format(DateTimeFormatter.ISO_LOCAL_DATE), cacheKey);
        } catch (Exception e) {
            log.error("保存日期 {} 的检客户统计数据到Redis缓存失败, key: {}", date.format(DateTimeFormatter.ISO_LOCAL_DATE), cacheKey, e);
        }
    }

    /**
     * 更新指定日期、角色和用户的品规识别统计数据到Redis
     */
    private void updateDailyRecognitionDataToRedis(String timeRange, String deptId, String userId, LocalDate date) {
        // 构建Redis缓存键
        String cacheKey = buildCacheKey(timeRange, deptId, userId, date) + ":recognition";

        // 获取当前日期的时间范围
        LocalDateTime[] dateRange = getDateTimeRange(date);

        // 生成图表数据
        ChartDataVO vo = generateDailyRecognitionChartData(dateRange[0], dateRange[1], deptId, userId);

        // 保存到Redis
        try {
            redisTemplate.opsForValue().set(cacheKey, vo);
            log.debug("保存日期 {} 的品规识别统计数据到Redis缓存成功, key: {}", date.format(DateTimeFormatter.ISO_LOCAL_DATE), cacheKey);
        } catch (Exception e) {
            log.error("保存日期 {} 的品规识别统计数据到Redis缓存失败, key: {}", date.format(DateTimeFormatter.ISO_LOCAL_DATE), cacheKey, e);
        }
    }

    /**
     * 更新指定日期、角色和用户的正常/异常品规统计数据到Redis
     */
    private void updateDailyCigaretteDataToRedis(String timeRange, String deptId, String userId, LocalDate date) {
        // 构建Redis缓存键
        String cacheKey = buildCacheKey(timeRange, deptId, userId, date) + ":cigarette";

        // 获取当前日期的时间范围
        LocalDateTime[] dateRange = getDateTimeRange(date);

        // 生成图表数据
        ChartDataVO vo = generateDailyCigaretteChartData(dateRange[0], dateRange[1], deptId, userId);

        // 保存到Redis
        try {
            redisTemplate.opsForValue().set(cacheKey, vo);
            log.debug("保存日期 {} 的正常/异常品规统计数据到Redis缓存成功, key: {}", date.format(DateTimeFormatter.ISO_LOCAL_DATE), cacheKey);
        } catch (Exception e) {
            log.error("保存日期 {} 的正常/异常品规统计数据到Redis缓存失败, key: {}", date.format(DateTimeFormatter.ISO_LOCAL_DATE), cacheKey, e);
        }
    }

    /**
     * 获取指定日期的开始时间和结束时间
     *
     * @param date 日期
     * @return 时间范围数组 [开始时间, 结束时间]
     */
    private LocalDateTime[] getDateTimeRange(LocalDate date) {
        LocalDateTime startTime = date.atStartOfDay();
        LocalDateTime endTime = date.plusDays(1).atStartOfDay().minusNanos(1);
        return new LocalDateTime[]{startTime, endTime};
    }

    @Override
    public MonitoringDataVO getMonitoringData(String timeRange, LocalDateTime startTime, LocalDateTime endTime, String deptId, String userId) {
        // 如果是day类型，直接从Redis获取
        if ("day".equals(timeRange)) {
            // 构建Redis缓存键
            String cacheKey = buildCacheKey(timeRange, deptId, userId,  startTime.toLocalDate());

            // 从Redis缓存中获取数据
            MonitoringDataVO cachedData = getFromRedis(cacheKey);
            if (cachedData != null) {
                log.debug("从Redis缓存获取监控数据成功, key: {}", cacheKey);
                return cachedData;
            }

            // 缓存中没有数据，生成当天的数据并保存到Redis
            MonitoringDataVO vo = generateDailyMonitoringData(startTime, endTime, deptId, userId);
            saveToRedis(cacheKey, vo);

            return vo;
        } else {
            // 其他时间范围类型，通过聚合day数据计算得出
            return aggregateMonitoringData(timeRange, startTime, endTime, deptId, userId);
        }
    }

    /**
     * 通过聚合每日数据计算指定时间范围的监控数据
     *
     * @param timeRange 时间范围
     * @param deptId    部门ID
     * @param userId    用户ID
     * @return 聚合后的监控数据
     */
    private MonitoringDataVO aggregateMonitoringData(String timeRange, LocalDateTime startTime, LocalDateTime endTime, String deptId, String userId) {
        // 创建返回对象
        MonitoringDataVO vo = new MonitoringDataVO();

        // 获取上一个时间范围的时间区间
        LocalDateTime[] prevTimeRange = TimeRangeUtil.getPreviousTimeRange(timeRange, startTime);

        // 获取当前时间范围内的所有日期
        List<LocalDate> dates = getDatesInRange(startTime.toLocalDate(), endTime.toLocalDate());

        // 获取上一个时间范围内的所有日期
        List<LocalDate> prevDates = getDatesInRange(prevTimeRange[0].toLocalDate(), prevTimeRange[1].toLocalDate());

        // 初始化计数器
        int inspectionCount = 0;
        int recognitionCount = 0;
        int specCount = 0;
        int abnormalCigaretteCount = 0;

        int prevInspectionCount = 0;
        int prevRecognitionCount = 0;
        int prevSpecCount = 0;
        int prevAbnormalCigaretteCount = 0;

        // 系统登录人数 - 直接从数据库统计
        int loginCount = countLoginUsersByTimeRange(startTime, endTime, deptId, userId);

        // 前一个时间范围的系统登录人数 - 直接从数据库统计
        int prevLoginCount = countLoginUsersByTimeRange(prevTimeRange[0], prevTimeRange[1], deptId, userId);

        // 聚合当前时间范围内的每日数据（除了登录人数）
        for (LocalDate date : dates) {
            String cacheKey = buildCacheKey("day", deptId, userId, date);
            MonitoringDataVO dailyData = getFromRedis(cacheKey);

            if (dailyData != null) {
                // 累加数据（不包括登录人数）
                inspectionCount += dailyData.getInspectionCount();
                recognitionCount += dailyData.getRecognitionCount();
                specCount += dailyData.getSpecCount();
                abnormalCigaretteCount += dailyData.getAbnormalCigaretteCount();
            }
        }

        // 聚合上一个时间范围内的每日数据（除了登录人数）
        for (LocalDate date : prevDates) {
            String cacheKey = buildCacheKey("day", deptId, userId, date);
            MonitoringDataVO dailyData = getFromRedis(cacheKey);

            if (dailyData != null) {
                // 累加数据（不包括登录人数）
                prevInspectionCount += dailyData.getInspectionCount();
                prevRecognitionCount += dailyData.getRecognitionCount();
                prevSpecCount += dailyData.getSpecCount();
                prevAbnormalCigaretteCount += dailyData.getAbnormalCigaretteCount();
            }
        }

        // 临时处理
        if(null != userId && inspectionCount > 0) {
            loginCount = 1;
        }

        // 计算趋势
        double loginTrend = calculateTrend(loginCount, prevLoginCount);
        double inspectionTrend = calculateTrend(inspectionCount, prevInspectionCount);
        double recognitionTrend = calculateTrend(recognitionCount, prevRecognitionCount);
        double specTrend = calculateTrend(specCount, prevSpecCount);
        double abnormalCigaretteTrend = calculateTrend(abnormalCigaretteCount, prevAbnormalCigaretteCount);

        // 设置返回数据
        vo.setLoginCount(loginCount);
        vo.setLoginTrend(loginTrend);
        vo.setInspectionCount(inspectionCount);
        vo.setInspectionTrend(inspectionTrend);
        vo.setRecognitionCount(recognitionCount);
        vo.setRecognitionTrend(recognitionTrend);
        vo.setSpecCount(specCount);
        vo.setSpecTrend(specTrend);
        vo.setAbnormalCigaretteCount(abnormalCigaretteCount);
        vo.setAbnormalCigaretteTrend(abnormalCigaretteTrend);

        return vo;
    }

    /**
     * 获取指定日期范围内的所有日期
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 日期列表
     */
    private List<LocalDate> getDatesInRange(LocalDate startDate, LocalDate endDate) {
        List<LocalDate> dates = new ArrayList<>();
        LocalDate date = startDate;
        while (!date.isAfter(endDate)) {
            dates.add(date);
            date = date.plusDays(1);
        }
        return dates;
    }

    /**
     * 生成指定时间范围内的每日监控数据
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param deptId    部门ID
     * @param userId    用户ID
     * @return 监控数据
     */
    private MonitoringDataVO generateDailyMonitoringData(LocalDateTime startTime, LocalDateTime endTime, String deptId, String userId) {
        // 创建返回对象
        MonitoringDataVO vo = new MonitoringDataVO();

        // 获取上一个时间范围（前一天）
        LocalDateTime prevStartTime = startTime.minusDays(1);
        LocalDateTime prevEndTime = endTime.minusDays(1);

        int loginCount = countLoginUsersByTimeRange(startTime, endTime, deptId, userId);
        int prevLoginCount = countLoginUsersByTimeRange(prevStartTime, prevEndTime, deptId, userId);
        int inspectionCount = countByTimeRange(startTime, endTime, deptId, userId);
        int prevInspectionCount = countByTimeRange(prevStartTime, prevEndTime, deptId, userId);
        int recognitionCount = countRecognitionByTimeRange(startTime, endTime, deptId, userId);
        int prevRecognitionCount = countRecognitionByTimeRange(prevStartTime, prevEndTime, deptId, userId);
        int specCount = countSpecByTimeRange(startTime, endTime, deptId, userId);
        int prevSpecCount = countSpecByTimeRange(prevStartTime, prevEndTime, deptId, userId);
        int abnormalCigaretteCount = countAbnormalByTimeRange(startTime, endTime, deptId, userId);
        int prevAbnormalCigaretteCount = countAbnormalByTimeRange(prevStartTime, prevEndTime, deptId, userId);

        // 临时处理
        if(null != userId && inspectionCount > 0) {
            loginCount = 1;
        }

        double loginTrend = calculateTrend(loginCount, prevLoginCount);
        double inspectionTrend = calculateTrend(inspectionCount, prevInspectionCount);
        double recognitionTrend = calculateTrend(recognitionCount, prevRecognitionCount);
        double specTrend = calculateTrend(specCount, prevSpecCount);
        double abnormalCigaretteTrend = calculateTrend(abnormalCigaretteCount, prevAbnormalCigaretteCount);

        vo.setLoginCount(loginCount);
        vo.setLoginTrend(loginTrend);
        vo.setInspectionCount(inspectionCount);
        vo.setInspectionTrend(inspectionTrend);
        vo.setRecognitionCount(recognitionCount);
        vo.setRecognitionTrend(recognitionTrend);
        vo.setSpecCount(specCount);
        vo.setSpecTrend(specTrend);
        vo.setAbnormalCigaretteCount(abnormalCigaretteCount);
        vo.setAbnormalCigaretteTrend(abnormalCigaretteTrend);

        return vo;
    }

    /**
     * 生成指定时间范围内的每日检客户统计图表数据
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 图表数据
     */
    private ChartDataVO generateDailyInspectionChartData(LocalDateTime startTime, LocalDateTime endTime, String deptId, String userId) {
        // 创建返回对象
        ChartDataVO vo = new ChartDataVO();

        // 按小时统计
        List<String> xAxis = new ArrayList<>();
        for (int i = 0; i < 24; i++) {
            xAxis.add(String.format("%02d:00", i));
        }
        vo.setXAxis(xAxis);

        // 创建系列数据
        List<ChartDataVO.SeriesVO> series = new ArrayList<>();
        ChartDataVO.SeriesVO seriesVO = new ChartDataVO.SeriesVO();
        seriesVO.setName("检查零售户数");

        // 从数据库获取按小时统计的检客户数据
        List<Integer> hourlyData = countInspectionByHour(startTime, endTime, deptId, userId);
        seriesVO.setData(hourlyData);

        series.add(seriesVO);

        vo.setSeries(series);

        return vo;
    }

    /**
     * 通过聚合每日数据计算指定时间范围的检客户统计图表数据
     *
     * @param timeRange 时间范围
     * @param deptId    部门ID
     * @param userId    用户ID
     * @return 聚合后的图表数据
     */
    private ChartDataVO aggregateInspectionChartData(String timeRange, LocalDateTime startTime, LocalDateTime endTime, String deptId, String userId) {
        // 创建返回对象
        ChartDataVO vo = new ChartDataVO();

        // 根据时间范围获取X轴数据
        List<String> xAxis = TimeRangeUtil.getXAxisByTimeRange(timeRange);
        vo.setXAxis(xAxis);

        // 获取当前时间范围内的所有日期
        List<LocalDate> dates = getDatesInRange(startTime.toLocalDate(), endTime.toLocalDate());

        // 创建系列数据
        List<ChartDataVO.SeriesVO> series = new ArrayList<>();
        ChartDataVO.SeriesVO seriesVO = new ChartDataVO.SeriesVO();
        seriesVO.setName("检查零售户数");

        // 初始化数据数组
        Integer[] aggregatedData = new Integer[xAxis.size()];
        Arrays.fill(aggregatedData, 0);

        // 根据时间范围类型进行不同的聚合处理
        if ("week".equals(timeRange)) {
            // 周统计，按天聚合
            Map<Integer, Integer> dayOfWeekMap = new HashMap<>();

            // 遍历所有日期，从Redis获取每日数据并聚合
            for (LocalDate date : dates) {
                String cacheKey = buildCacheKey("day", deptId, userId, date) + ":inspection";
                try {
                    Object cachedData = redisTemplate.opsForValue().get(cacheKey);
                    if (cachedData != null) {
                        ChartDataVO dailyData = (ChartDataVO) cachedData;
                        // 获取当天是星期几（1-7，对应周一到周日）
                        int dayOfWeek = date.getDayOfWeek().getValue() - 1; // 转为0-6索引

                        // 累加当天的检客户数
                        if (dailyData.getSeries() != null && !dailyData.getSeries().isEmpty()) {
                            List<Integer> dailyValues = dailyData.getSeries().get(0).getData();
                            int dailySum = dailyValues.stream().mapToInt(Integer::intValue).sum();
                            dayOfWeekMap.put(dayOfWeek, dayOfWeekMap.getOrDefault(dayOfWeek, 0) + dailySum);
                        }
                    }
                } catch (Exception e) {
                    log.error("从Redis获取日检客户统计数据失败, key: {}", cacheKey, e);
                }
            }

            // 将聚合结果映射到返回数组
            for (int i = 0; i < 7; i++) {
                aggregatedData[i] = dayOfWeekMap.getOrDefault(i, 0);
            }
        } else if ("month".equals(timeRange)) {
            // 月统计，按天聚合到对应的日期
            Map<Integer, Integer> dayOfMonthMap = new HashMap<>();

            // 遍历所有日期，从Redis获取每日数据并聚合
            for (LocalDate date : dates) {
                String cacheKey = buildCacheKey("day", deptId, userId, date) + ":inspection";
                try {
                    Object cachedData = redisTemplate.opsForValue().get(cacheKey);
                    if (cachedData != null) {
                        ChartDataVO dailyData = (ChartDataVO) cachedData;
                        // 获取当天是几号（1-31）
                        int dayOfMonth = date.getDayOfMonth() - 1; // 转为0-30索引

                        // 累加当天的检客户数
                        if (dailyData.getSeries() != null && !dailyData.getSeries().isEmpty()) {
                            List<Integer> dailyValues = dailyData.getSeries().get(0).getData();
                            int dailySum = dailyValues.stream().mapToInt(Integer::intValue).sum();
                            dayOfMonthMap.put(dayOfMonth, dayOfMonthMap.getOrDefault(dayOfMonth, 0) + dailySum);
                        }
                    }
                } catch (Exception e) {
                    log.error("从Redis获取日检客户统计数据失败, key: {}", cacheKey, e);
                }
            }

            // 将聚合结果映射到返回数组（只取当月的天数）
            int daysInMonth = dates.get(dates.size() - 1).getDayOfMonth();
            for (int i = 0; i < daysInMonth; i++) {
                if (i < aggregatedData.length) {
                    aggregatedData[i] = dayOfMonthMap.getOrDefault(i, 0);
                }
            }
        } else {
            // 季度或年统计，按月或季度聚合
            // 计算时间范围的总天数
            long totalDays = java.time.temporal.ChronoUnit.DAYS.between(startTime, endTime) + 1;

            // 按日期聚合数据
            Map<LocalDate, Integer> dateCountMap = new HashMap<>();

            // 遍历所有日期，从Redis获取每日数据并聚合
            for (LocalDate date : dates) {
                String cacheKey = buildCacheKey("day", deptId, userId, date) + ":inspection";
                try {
                    Object cachedData = redisTemplate.opsForValue().get(cacheKey);
                    if (cachedData != null) {
                        ChartDataVO dailyData = (ChartDataVO) cachedData;

                        // 累加当天的检客户数
                        if (dailyData.getSeries() != null && !dailyData.getSeries().isEmpty()) {
                            List<Integer> dailyValues = dailyData.getSeries().get(0).getData();
                            int dailySum = dailyValues.stream().mapToInt(Integer::intValue).sum();
                            dateCountMap.put(date, dateCountMap.getOrDefault(date, 0) + dailySum);
                        }
                    }
                } catch (Exception e) {
                    log.error("从Redis获取日检客户统计数据失败, key: {}", cacheKey, e);
                }
            }

            // 根据时间范围类型，将统计结果映射到返回数组
            LocalDate currentDate = startTime.toLocalDate();
            int step = (int) Math.max(1, totalDays / xAxis.size());

            for (int i = 0; i < xAxis.size(); i++) {
                int count = 0;
                LocalDate tempDate = currentDate;

                // 聚合step天的数据
                for (int j = 0; j < step && tempDate.isBefore(endTime.toLocalDate().plusDays(1)); j++) {
                    count += dateCountMap.getOrDefault(tempDate, 0);
                    tempDate = tempDate.plusDays(1);
                }

                aggregatedData[i] = count;
                currentDate = tempDate;
            }
        }

        // 如果Redis中没有足够的数据，则使用数据库查询作为备选方案
        boolean hasData = Arrays.stream(aggregatedData).anyMatch(count -> count > 0);
        if (!hasData) {
            log.info("Redis中没有足够的检客户统计数据，使用数据库查询作为备选方案");
            List<Integer> dbData = countInspectionByDate(startTime, endTime, deptId, userId, xAxis.size());
            seriesVO.setData(dbData);
        } else {
            seriesVO.setData(Arrays.asList(aggregatedData));
        }

        series.add(seriesVO);
        vo.setSeries(series);

        return vo;
    }

    @Override
    public ChartDataVO getInspectionData(String timeRange, LocalDateTime startTime, LocalDateTime endTime, String deptId, String userId) {
        // 如果是day类型，直接从Redis获取
        if ("day".equals(timeRange)) {
            // 构建Redis缓存键
            String cacheKey = buildCacheKey(timeRange, deptId, userId, startTime.toLocalDate()) + ":inspection";

            // 从Redis缓存中获取数据
            try {
                Object cachedData = redisTemplate.opsForValue().get(cacheKey);
                if (cachedData != null) {
                    log.debug("从Redis缓存获取检客户统计数据成功, key: {}", cacheKey);
                    return (ChartDataVO) cachedData;
                }
            } catch (Exception e) {
                log.error("从Redis获取检客户统计数据失败, key: {}", cacheKey, e);
            }

            // 缓存中没有数据，生成当天的数据并保存到Redis
            ChartDataVO vo = generateDailyInspectionChartData(startTime, endTime, deptId, userId);

            try {
                redisTemplate.opsForValue().set(cacheKey, vo);
                log.debug("保存检客户统计数据到Redis缓存成功, key: {}", cacheKey);
            } catch (Exception e) {
                log.error("保存检客户统计数据到Redis缓存失败, key: {}", cacheKey, e);
            }

            return vo;
        } else {
            // 其他时间范围类型，通过聚合day数据计算得出
            return aggregateInspectionChartData(timeRange, startTime, endTime, deptId, userId);
        }
    }

    /**
     * 生成指定时间范围内的每日品规识别统计图表数据
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 图表数据
     */
    private ChartDataVO generateDailyRecognitionChartData(LocalDateTime startTime, LocalDateTime endTime, String deptId, String userId) {
        // 创建返回对象
        ChartDataVO vo = new ChartDataVO();

        // 按小时统计
        List<String> xAxis = new ArrayList<>();
        for (int i = 0; i < 24; i++) {
            xAxis.add(String.format("%02d:00", i));
        }
        vo.setXAxis(xAxis);

        // 创建系列数据
        List<ChartDataVO.SeriesVO> series = new ArrayList<>();

        // 品规识别次数
        ChartDataVO.SeriesVO seriesVO1 = new ChartDataVO.SeriesVO();
        seriesVO1.setName("品规识别次数");
        List<Integer> recognitionData = countRecognitionByHour(startTime, endTime, deptId, userId);
        seriesVO1.setData(recognitionData);
        series.add(seriesVO1);

        // 识别品规数
        ChartDataVO.SeriesVO seriesVO2 = new ChartDataVO.SeriesVO();
        seriesVO2.setName("识别品规数");
        List<Integer> specData = countSpecByHour(startTime, endTime, deptId, userId);
        seriesVO2.setData(specData);
        series.add(seriesVO2);

        vo.setSeries(series);

        return vo;
    }


    @Override
    public ChartDataVO getRecognitionData(String timeRange, LocalDateTime startTime, LocalDateTime endTime, String deptId, String userId) {
        // 如果是day类型，直接从Redis获取
        if ("day".equals(timeRange)) {
            // 构建Redis缓存键
            String cacheKey = buildCacheKey(timeRange, deptId, userId, startTime.toLocalDate()) + ":recognition";

            // 从Redis缓存中获取数据
            try {
                Object cachedData = redisTemplate.opsForValue().get(cacheKey);
                if (cachedData != null) {
                    log.debug("从Redis缓存获取品规识别统计数据成功, key: {}", cacheKey);
                    return (ChartDataVO) cachedData;
                }
            } catch (Exception e) {
                log.error("从Redis获取品规识别统计数据失败, key: {}", cacheKey, e);
            }

            // 缓存中没有数据，生成当天的数据并保存到Redis
            ChartDataVO vo = generateDailyRecognitionChartData(startTime, endTime, deptId, userId);

            try {
                redisTemplate.opsForValue().set(cacheKey, vo);
                log.debug("保存品规识别统计数据到Redis缓存成功, key: {}", cacheKey);
            } catch (Exception e) {
                log.error("保存品规识别统计数据到Redis缓存失败, key: {}", cacheKey, e);
            }

            return vo;
        } else {
            // 其他时间范围类型，通过聚合day数据计算得出
            return aggregateRecognitionChartData(timeRange, startTime, endTime, deptId, userId);
        }
    }

    /**
     * 生成指定时间范围内的每日正常/异常品规统计图表数据
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param deptId    部门ID
     * @param userId    用户ID
     * @return 图表数据
     */
    private ChartDataVO generateDailyCigaretteChartData(LocalDateTime startTime, LocalDateTime endTime, String deptId, String userId) {
        // 创建返回对象
        ChartDataVO vo = new ChartDataVO();

        // 按小时统计
        List<String> xAxis = new ArrayList<>();
        for (int i = 0; i < 24; i++) {
            xAxis.add(String.format("%02d:00", i));
        }
        vo.setXAxis(xAxis);

        // 创建系列数据
        List<ChartDataVO.SeriesVO> series = new ArrayList<>();

        // 正常品规数 - 从数据库查询
        ChartDataVO.SeriesVO seriesVO1 = new ChartDataVO.SeriesVO();
        seriesVO1.setName("正常品规数");
        List<Integer> normalData = countNormalCigaretteByHour(startTime, endTime, deptId, userId);
        seriesVO1.setData(normalData);
        series.add(seriesVO1);

        // 异常品规数
        ChartDataVO.SeriesVO seriesVO2 = new ChartDataVO.SeriesVO();
        seriesVO2.setName("异常品规数");
        List<Integer> abnormalData = countAbnormalCigaretteByHour(startTime, endTime, deptId, userId);
        seriesVO2.setData(abnormalData);
        series.add(seriesVO2);

        vo.setSeries(series);

        return vo;
    }

    /**
     * 按小时统计指定时间范围内的正常品规数量
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param deptId    部门ID
     * @param userId    用户ID
     * @return 按小时统计的正常品规数量列表（24小时）
     */
    private List<Integer> countNormalCigaretteByHour(LocalDateTime startTime, LocalDateTime endTime, String deptId, String userId) {
        try {
            // 初始化24小时的统计数组
            Integer[] hourlyData = new Integer[24];
            Arrays.fill(hourlyData, 0);

            // 构建查询条件
            LocalDate startDate = startTime.toLocalDate();
            LocalDate endDate = endTime.toLocalDate();

            // 获取符合条件的用户ID列表
            List<Long> userIds = getFilteredUserIds(deptId, userId);

            // 查询正常品规记录（collisionType不为"假烟"或"私烟"或为null）
            List<ItemIdentifyResultsEntity> resultsList = itemIdentifyResultsService.list(
                    Wrappers.<ItemIdentifyResultsEntity>lambdaQuery()
                            .ge(ItemIdentifyResultsEntity::getIdentifyDate, startDate)
                            .le(ItemIdentifyResultsEntity::getIdentifyDate, endDate)
                            .eq(ItemIdentifyResultsEntity::getCollisionType, "正常")
                            .in(Func.isNotEmpty(userIds), ItemIdentifyResultsEntity::getCreateUser, userIds)
            );

            // 按小时统计
            for (ItemIdentifyResultsEntity result : resultsList) {
                // 获取创建时间
                java.util.Date createDate = result.getCreateTime();
                if (createDate != null) {
                    // 转换为LocalDateTime
                    LocalDateTime createTime = createDate.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime();
                    int hour = createTime.getHour();
                    hourlyData[hour] += 1;
                }
            }

            return Arrays.asList(hourlyData);
        } catch (Exception e) {
            log.error("按小时统计正常品规数量失败: {} 至 {}", startTime, endTime, e);
            // 返回全0的数组
            Integer[] emptyData = new Integer[24];
            Arrays.fill(emptyData, 0);
            return Arrays.asList(emptyData);
        }
    }

    /**
     * 按小时统计指定时间范围内的异常品规数量
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param deptId    部门ID
     * @param userId    用户ID
     * @return 按小时统计的异常品规数量列表（24小时）
     */
    private List<Integer> countAbnormalCigaretteByHour(LocalDateTime startTime, LocalDateTime endTime, String deptId, String userId) {
        try {
            // 初始化24小时的统计数组
            Integer[] hourlyData = new Integer[24];
            Arrays.fill(hourlyData, 0);

            // 构建查询条件
            LocalDate startDate = startTime.toLocalDate();
            LocalDate endDate = endTime.toLocalDate();

            // 获取符合条件的用户ID列表
            List<Long> userIds = getFilteredUserIds(deptId, userId);

            // 查询异常品规记录（collisionType为"假烟"或"私烟"）
            List<ItemIdentifyResultsEntity> resultsList = itemIdentifyResultsService.list(
                    Wrappers.<ItemIdentifyResultsEntity>lambdaQuery()
                            .ge(ItemIdentifyResultsEntity::getIdentifyDate, startDate)
                            .le(ItemIdentifyResultsEntity::getIdentifyDate, endDate)
                            .in(ItemIdentifyResultsEntity::getCollisionType, Arrays.asList("非烟", "假烟", "私烟", "非烟,假烟"))
                            .in(Func.isNotEmpty(userIds), ItemIdentifyResultsEntity::getCreateUser, userIds)
            );

            // 按小时统计
            for (ItemIdentifyResultsEntity result : resultsList) {
                // 获取创建时间
                java.util.Date createDate = result.getCreateTime();
                if (createDate != null) {
                    // 转换为LocalDateTime
                    LocalDateTime createTime = createDate.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime();
                    int hour = createTime.getHour();
                    hourlyData[hour] += 1;
                }
            }

            return Arrays.asList(hourlyData);
        } catch (Exception e) {
            log.error("按小时统计异常品规数量失败: {} 至 {}", startTime, endTime, e);
            // 返回全0的数组
            Integer[] emptyData = new Integer[24];
            Arrays.fill(emptyData, 0);
            return Arrays.asList(emptyData);
        }
    }

    /**
     * 按小时统计指定时间范围内的品规识别次数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param deptId    部门ID
     * @param userId    用户ID
     * @return 按小时统计的品规识别次数列表（24小时）
     */
    private List<Integer> countRecognitionByHour(LocalDateTime startTime, LocalDateTime endTime, String deptId, String userId) {
        try {
            // 初始化24小时的统计数组
            Integer[] hourlyData = new Integer[24];
            Arrays.fill(hourlyData, 0);

            // 构建查询条件
            LocalDate startDate = startTime.toLocalDate();
            LocalDate endDate = endTime.toLocalDate();

            // 获取符合条件的用户ID列表
            List<Long> userIds = getFilteredUserIds(deptId, userId);

            // 查询品规识别记录
            List<ItemIdentifyEntity> identifyList = itemIdentifyService.list(
                    Wrappers.<ItemIdentifyEntity>lambdaQuery()
                            .ge(ItemIdentifyEntity::getIdentifyDate, startDate)
                            .le(ItemIdentifyEntity::getIdentifyDate, endDate)
                            .in(Func.isNotEmpty(userIds), ItemIdentifyEntity::getCreateUser, userIds)
            );

            // 按小时统计
            for (ItemIdentifyEntity identify : identifyList) {
                // 获取创建时间
                java.util.Date createDate = identify.getCreateTime();
                if (createDate != null) {
                    // 转换为LocalDateTime
                    LocalDateTime createTime = createDate.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime();
                    int hour = createTime.getHour();
                    hourlyData[hour]++;
                }
            }

            return Arrays.asList(hourlyData);
        } catch (Exception e) {
            log.error("按小时统计品规识别次数失败: {} 至 {}", startTime, endTime, e);
            // 返回全0的数组
            Integer[] emptyData = new Integer[24];
            Arrays.fill(emptyData, 0);
            return Arrays.asList(emptyData);
        }
    }

    /**
     * 按小时统计指定时间范围内的品规识别记录数量
     * 通过查询ms_item_identify_results表，按小时统计记录数量
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param deptId    部门ID
     * @param userId    用户ID
     * @return 按小时统计的品规识别记录数量列表（24小时）
     */
    private List<Integer> countSpecByHour(LocalDateTime startTime, LocalDateTime endTime, String deptId, String userId) {
        try {
            // 初始化24小时的统计数组
            Integer[] hourlyData = new Integer[24];
            Arrays.fill(hourlyData, 0);

            // 构建查询条件
            LocalDate startDate = startTime.toLocalDate();
            LocalDate endDate = endTime.toLocalDate();

            // 获取符合条件的用户ID列表
            List<Long> userIds = getFilteredUserIds(deptId, userId);

            // 查询品规识别结果记录
            List<ItemIdentifyResultsEntity> resultsList = itemIdentifyResultsService.list(
                    Wrappers.<ItemIdentifyResultsEntity>lambdaQuery()
                            .ge(ItemIdentifyResultsEntity::getIdentifyDate, startDate)
                            .le(ItemIdentifyResultsEntity::getIdentifyDate, endDate)
                            .in(Func.isNotEmpty(userIds), ItemIdentifyResultsEntity::getCreateUser, userIds)
            );

            // 按小时统计记录数量
            for (ItemIdentifyResultsEntity result : resultsList) {
                // 获取创建时间
                java.util.Date createDate = result.getCreateTime();

                if (createDate != null) {
                    // 转换为LocalDateTime
                    LocalDateTime createTime = createDate.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime();
                    int hour = createTime.getHour();

                    // 每条记录计数为1
                    hourlyData[hour]++;
                }
            }

            return Arrays.asList(hourlyData);
        } catch (Exception e) {
            log.error("按小时统计识别品规数失败: {} 至 {}", startTime, endTime, e);
            // 返回全0的数组
            Integer[] emptyData = new Integer[24];
            Arrays.fill(emptyData, 0);
            return Arrays.asList(emptyData);
        }
    }

    @Override
    public ChartDataVO getCigaretteData(String timeRange, LocalDateTime startTime, LocalDateTime endTime, String deptId, String userId) {
        // 如果是day类型，直接从Redis获取
        if ("day".equals(timeRange)) {
            // 构建Redis缓存键
            String cacheKey = buildCacheKey(timeRange, deptId, userId, startTime.toLocalDate()) + ":cigarette";

            // 从Redis缓存中获取数据
            try {
                Object cachedData = redisTemplate.opsForValue().get(cacheKey);
                if (cachedData != null) {
                    log.debug("从Redis缓存获取正常/异常品规统计数据成功, key: {}", cacheKey);
                    return (ChartDataVO) cachedData;
                }
            } catch (Exception e) {
                log.error("从Redis获取正常/异常品规统计数据失败, key: {}", cacheKey, e);
            }

            // 缓存中没有数据，生成当天的数据并保存到Redis
            ChartDataVO vo = generateDailyCigaretteChartData(startTime, endTime, deptId, userId);

            try {
                redisTemplate.opsForValue().set(cacheKey, vo);
                log.debug("保存正常/异常品规统计数据到Redis缓存成功, key: {}", cacheKey);
            } catch (Exception e) {
                log.error("保存正常/异常品规统计数据到Redis缓存失败, key: {}", cacheKey, e);
            }

            return vo;
        } else {
            // 其他时间范围类型，通过聚合day数据计算得出
            return aggregateCigaretteChartData(timeRange, startTime, endTime, deptId, userId);
        }
    }

    /**
     * 通过聚合每日数据计算指定时间范围的品规识别统计图表数据
     *
     * @param timeRange 时间范围
     * @param deptId    部门ID
     * @param userId    用户ID
     * @return 聚合后的图表数据
     */
    private ChartDataVO aggregateRecognitionChartData(String timeRange, LocalDateTime startTime, LocalDateTime endTime, String deptId, String userId) {
        // 创建返回对象
        ChartDataVO vo = new ChartDataVO();

        // 根据时间范围获取X轴数据
        List<String> xAxis = TimeRangeUtil.getXAxisByTimeRange(timeRange);
        vo.setXAxis(xAxis);

        // 获取当前时间范围内的所有日期
        List<LocalDate> dates = getDatesInRange(startTime.toLocalDate(), endTime.toLocalDate());

        // 创建系列数据
        List<ChartDataVO.SeriesVO> series = new ArrayList<>();

        // 品规识别次数
        ChartDataVO.SeriesVO seriesVO1 = new ChartDataVO.SeriesVO();
        seriesVO1.setName("品规识别次数");

        // 识别品规数
        ChartDataVO.SeriesVO seriesVO2 = new ChartDataVO.SeriesVO();
        seriesVO2.setName("识别品规数");

        // 初始化数据数组
        Integer[] aggregatedData1 = new Integer[xAxis.size()];
        Integer[] aggregatedData2 = new Integer[xAxis.size()];
        Arrays.fill(aggregatedData1, 0);
        Arrays.fill(aggregatedData2, 0);

        // 根据时间范围类型进行不同的聚合处理
        if ("week".equals(timeRange)) {
            // 周统计，按天聚合
            Map<Integer, Integer> dayOfWeekMap1 = new HashMap<>();
            Map<Integer, Integer> dayOfWeekMap2 = new HashMap<>();

            // 遍历所有日期，从Redis获取每日数据并聚合
            for (LocalDate date : dates) {
                String cacheKey = buildCacheKey("day", deptId, userId, date) + ":recognition";
                try {
                    Object cachedData = redisTemplate.opsForValue().get(cacheKey);
                    if (cachedData != null) {
                        ChartDataVO dailyData = (ChartDataVO) cachedData;
                        // 获取当天是星期几（1-7，对应周一到周日）
                        int dayOfWeek = date.getDayOfWeek().getValue() - 1; // 转为0-6索引

                        // 累加当天的品规识别次数和识别品规数
                        if (dailyData.getSeries() != null && dailyData.getSeries().size() >= 2) {
                            // 品规识别次数
                            List<Integer> dailyValues1 = dailyData.getSeries().get(0).getData();
                            int dailySum1 = dailyValues1.stream().mapToInt(Integer::intValue).sum();
                            dayOfWeekMap1.put(dayOfWeek, dayOfWeekMap1.getOrDefault(dayOfWeek, 0) + dailySum1);

                            // 识别品规数
                            List<Integer> dailyValues2 = dailyData.getSeries().get(1).getData();
                            int dailySum2 = dailyValues2.stream().mapToInt(Integer::intValue).sum();
                            dayOfWeekMap2.put(dayOfWeek, dayOfWeekMap2.getOrDefault(dayOfWeek, 0) + dailySum2);
                        }
                    }
                } catch (Exception e) {
                    log.error("从Redis获取日品规识别统计数据失败, key: {}", cacheKey, e);
                }
            }

            // 将聚合结果映射到返回数组
            for (int i = 0; i < 7; i++) {
                aggregatedData1[i] = dayOfWeekMap1.getOrDefault(i, 0);
                aggregatedData2[i] = dayOfWeekMap2.getOrDefault(i, 0);
            }
        } else if ("month".equals(timeRange)) {
            // 月统计，按天聚合到对应的日期
            Map<Integer, Integer> dayOfMonthMap1 = new HashMap<>();
            Map<Integer, Integer> dayOfMonthMap2 = new HashMap<>();

            // 遍历所有日期，从Redis获取每日数据并聚合
            for (LocalDate date : dates) {
                String cacheKey = buildCacheKey("day", deptId, userId, date) + ":recognition";
                try {
                    Object cachedData = redisTemplate.opsForValue().get(cacheKey);
                    if (cachedData != null) {
                        ChartDataVO dailyData = (ChartDataVO) cachedData;
                        // 获取当天是几号（1-31）
                        int dayOfMonth = date.getDayOfMonth() - 1; // 转为0-30索引

                        // 累加当天的品规识别次数和识别品规数
                        if (dailyData.getSeries() != null && dailyData.getSeries().size() >= 2) {
                            // 品规识别次数
                            List<Integer> dailyValues1 = dailyData.getSeries().get(0).getData();
                            int dailySum1 = dailyValues1.stream().mapToInt(Integer::intValue).sum();
                            dayOfMonthMap1.put(dayOfMonth, dayOfMonthMap1.getOrDefault(dayOfMonth, 0) + dailySum1);

                            // 识别品规数
                            List<Integer> dailyValues2 = dailyData.getSeries().get(1).getData();
                            int dailySum2 = dailyValues2.stream().mapToInt(Integer::intValue).sum();
                            dayOfMonthMap2.put(dayOfMonth, dayOfMonthMap2.getOrDefault(dayOfMonth, 0) + dailySum2);
                        }
                    }
                } catch (Exception e) {
                    log.error("从Redis获取日品规识别统计数据失败, key: {}", cacheKey, e);
                }
            }

            // 将聚合结果映射到返回数组（只取当月的天数）
            int daysInMonth = dates.get(dates.size() - 1).getDayOfMonth();
            for (int i = 0; i < daysInMonth; i++) {
                if (i < aggregatedData1.length) {
                    aggregatedData1[i] = dayOfMonthMap1.getOrDefault(i, 0);
                    aggregatedData2[i] = dayOfMonthMap2.getOrDefault(i, 0);
                }
            }
        } else {
            // 季度或年统计，按月或季度聚合
            // 计算时间范围的总天数
            long totalDays = java.time.temporal.ChronoUnit.DAYS.between(startTime, endTime) + 1;

            // 按日期聚合数据
            Map<LocalDate, Integer> dateCountMap1 = new HashMap<>();
            Map<LocalDate, Integer> dateCountMap2 = new HashMap<>();

            // 遍历所有日期，从Redis获取每日数据并聚合
            for (LocalDate date : dates) {
                String cacheKey = buildCacheKey("day", deptId, userId, date) + ":recognition";
                try {
                    Object cachedData = redisTemplate.opsForValue().get(cacheKey);
                    if (cachedData != null) {
                        ChartDataVO dailyData = (ChartDataVO) cachedData;

                        // 累加当天的品规识别次数和识别品规数
                        if (dailyData.getSeries() != null && dailyData.getSeries().size() >= 2) {
                            // 品规识别次数
                            List<Integer> dailyValues1 = dailyData.getSeries().get(0).getData();
                            int dailySum1 = dailyValues1.stream().mapToInt(Integer::intValue).sum();
                            dateCountMap1.put(date, dateCountMap1.getOrDefault(date, 0) + dailySum1);

                            // 识别品规数
                            List<Integer> dailyValues2 = dailyData.getSeries().get(1).getData();
                            int dailySum2 = dailyValues2.stream().mapToInt(Integer::intValue).sum();
                            dateCountMap2.put(date, dateCountMap2.getOrDefault(date, 0) + dailySum2);
                        }
                    }
                } catch (Exception e) {
                    log.error("从Redis获取日品规识别统计数据失败, key: {}", cacheKey, e);
                }
            }

            // 根据时间范围类型，将统计结果映射到返回数组
            LocalDate currentDate = startTime.toLocalDate();
            int step = (int) Math.max(1, totalDays / xAxis.size());

            for (int i = 0; i < xAxis.size(); i++) {
                int count1 = 0;
                int count2 = 0;
                LocalDate tempDate = currentDate;

                // 聚合step天的数据
                for (int j = 0; j < step && tempDate.isBefore(endTime.toLocalDate().plusDays(1)); j++) {
                    count1 += dateCountMap1.getOrDefault(tempDate, 0);
                    count2 += dateCountMap2.getOrDefault(tempDate, 0);
                    tempDate = tempDate.plusDays(1);
                }

                aggregatedData1[i] = count1;
                aggregatedData2[i] = count2;
                currentDate = tempDate;
            }
        }

        // 如果Redis中没有足够的数据，则使用数据库查询作为备选方案
        boolean hasData1 = Arrays.stream(aggregatedData1).anyMatch(count -> count > 0);
        boolean hasData2 = Arrays.stream(aggregatedData2).anyMatch(count -> count > 0);

        if (!hasData1) {
            log.info("Redis中没有足够的品规识别次数统计数据，使用数据库查询作为备选方案");
            // 从数据库中统计品规识别次数
            int recognitionCount = countRecognitionByTimeRange(startTime, endTime, deptId, userId);
            // 将总数平均分配到各个时间点
            List<Integer> dbData = new ArrayList<>();
            int avgCount = recognitionCount / xAxis.size();
            for (int i = 0; i < xAxis.size(); i++) {
                dbData.add(avgCount);
            }
            seriesVO1.setData(dbData);
        } else {
            seriesVO1.setData(Arrays.asList(aggregatedData1));
        }

        if (!hasData2) {
            log.info("Redis中没有足够的识别品规数统计数据，使用数据库查询作为备选方案");
            // 从数据库中统计识别品规数
            int specCount = countSpecByTimeRange(startTime, endTime, deptId, userId);
            // 将总数平均分配到各个时间点
            List<Integer> dbData = new ArrayList<>();
            int avgCount = specCount / xAxis.size();
            for (int i = 0; i < xAxis.size(); i++) {
                dbData.add(avgCount);
            }
            seriesVO2.setData(dbData);
        } else {
            seriesVO2.setData(Arrays.asList(aggregatedData2));
        }

        series.add(seriesVO1);
        series.add(seriesVO2);
        vo.setSeries(series);

        return vo;
    }

    /**
     * 通过聚合每日数据计算指定时间范围的正常/异常品规统计图表数据
     *
     * @param timeRange 时间范围
     * @param deptId    部门ID
     * @param userId    用户ID
     * @return 聚合后的图表数据
     */
    private ChartDataVO aggregateCigaretteChartData(String timeRange,  LocalDateTime startTime, LocalDateTime endTime, String deptId, String userId) {
        // 创建返回对象
        ChartDataVO vo = new ChartDataVO();

        // 根据时间范围获取X轴数据
        List<String> xAxis = TimeRangeUtil.getXAxisByTimeRange(timeRange);
        vo.setXAxis(xAxis);

        // 获取当前时间范围内的所有日期
        List<LocalDate> dates = getDatesInRange(startTime.toLocalDate(), endTime.toLocalDate());

        // 创建系列数据
        List<ChartDataVO.SeriesVO> series = new ArrayList<>();

        // 正常品规数
        ChartDataVO.SeriesVO seriesVO1 = new ChartDataVO.SeriesVO();
        seriesVO1.setName("正常品规数");

        // 异常品规数
        ChartDataVO.SeriesVO seriesVO2 = new ChartDataVO.SeriesVO();
        seriesVO2.setName("异常品规数");

        // 初始化数据数组
        Integer[] aggregatedData1 = new Integer[xAxis.size()];
        Integer[] aggregatedData2 = new Integer[xAxis.size()];
        Arrays.fill(aggregatedData1, 0);
        Arrays.fill(aggregatedData2, 0);

        // 根据时间范围类型进行不同的聚合处理
        if ("week".equals(timeRange)) {
            // 周统计，按天聚合
            Map<Integer, Integer> dayOfWeekMap1 = new HashMap<>();
            Map<Integer, Integer> dayOfWeekMap2 = new HashMap<>();

            // 遍历所有日期，从Redis获取每日数据并聚合
            for (LocalDate date : dates) {
                String cacheKey = buildCacheKey("day", deptId, userId, date) + ":cigarette";
                try {
                    Object cachedData = redisTemplate.opsForValue().get(cacheKey);
                    if (cachedData != null) {
                        ChartDataVO dailyData = (ChartDataVO) cachedData;
                        // 获取当天是星期几（1-7，对应周一到周日）
                        int dayOfWeek = date.getDayOfWeek().getValue() - 1; // 转为0-6索引

                        // 累加当天的正常品规数和异常品规数
                        if (dailyData.getSeries() != null && dailyData.getSeries().size() >= 2) {
                            // 正常品规数
                            List<Integer> dailyValues1 = dailyData.getSeries().get(0).getData();
                            int dailySum1 = dailyValues1.stream().mapToInt(Integer::intValue).sum();
                            dayOfWeekMap1.put(dayOfWeek, dayOfWeekMap1.getOrDefault(dayOfWeek, 0) + dailySum1);

                            // 异常品规数
                            List<Integer> dailyValues2 = dailyData.getSeries().get(1).getData();
                            int dailySum2 = dailyValues2.stream().mapToInt(Integer::intValue).sum();
                            dayOfWeekMap2.put(dayOfWeek, dayOfWeekMap2.getOrDefault(dayOfWeek, 0) + dailySum2);
                        }
                    }
                } catch (Exception e) {
                    log.error("从Redis获取日正常/异常品规统计数据失败, key: {}", cacheKey, e);
                }
            }

            // 将聚合结果映射到返回数组
            for (int i = 0; i < 7; i++) {
                aggregatedData1[i] = dayOfWeekMap1.getOrDefault(i, 0);
                aggregatedData2[i] = dayOfWeekMap2.getOrDefault(i, 0);
            }
        } else if ("month".equals(timeRange)) {
            // 月统计，按天聚合到对应的日期
            Map<Integer, Integer> dayOfMonthMap1 = new HashMap<>();
            Map<Integer, Integer> dayOfMonthMap2 = new HashMap<>();

            // 遍历所有日期，从Redis获取每日数据并聚合
            for (LocalDate date : dates) {
                String cacheKey = buildCacheKey("day", deptId, userId, date) + ":cigarette";
                try {
                    Object cachedData = redisTemplate.opsForValue().get(cacheKey);
                    if (cachedData != null) {
                        ChartDataVO dailyData = (ChartDataVO) cachedData;
                        // 获取当天是几号（1-31）
                        int dayOfMonth = date.getDayOfMonth() - 1; // 转为0-30索引

                        // 累加当天的正常品规数和异常品规数
                        if (dailyData.getSeries() != null && dailyData.getSeries().size() >= 2) {
                            // 正常品规数
                            List<Integer> dailyValues1 = dailyData.getSeries().get(0).getData();
                            int dailySum1 = dailyValues1.stream().mapToInt(Integer::intValue).sum();
                            dayOfMonthMap1.put(dayOfMonth, dayOfMonthMap1.getOrDefault(dayOfMonth, 0) + dailySum1);

                            // 异常品规数
                            List<Integer> dailyValues2 = dailyData.getSeries().get(1).getData();
                            int dailySum2 = dailyValues2.stream().mapToInt(Integer::intValue).sum();
                            dayOfMonthMap2.put(dayOfMonth, dayOfMonthMap2.getOrDefault(dayOfMonth, 0) + dailySum2);
                        }
                    }
                } catch (Exception e) {
                    log.error("从Redis获取日正常/异常品规统计数据失败, key: {}", cacheKey, e);
                }
            }

            // 将聚合结果映射到返回数组（只取当月的天数）
            int daysInMonth = dates.get(dates.size() - 1).getDayOfMonth();
            for (int i = 0; i < daysInMonth; i++) {
                if (i < aggregatedData1.length) {
                    aggregatedData1[i] = dayOfMonthMap1.getOrDefault(i, 0);
                    aggregatedData2[i] = dayOfMonthMap2.getOrDefault(i, 0);
                }
            }
        } else {
            // 季度或年统计，按月或季度聚合
            // 计算时间范围的总天数
            long totalDays = java.time.temporal.ChronoUnit.DAYS.between(startTime, endTime) + 1;

            // 按日期聚合数据
            Map<LocalDate, Integer> dateCountMap1 = new HashMap<>();
            Map<LocalDate, Integer> dateCountMap2 = new HashMap<>();

            // 遍历所有日期，从Redis获取每日数据并聚合
            for (LocalDate date : dates) {
                String cacheKey = buildCacheKey("day", deptId, userId, date) + ":cigarette";
                try {
                    Object cachedData = redisTemplate.opsForValue().get(cacheKey);
                    if (cachedData != null) {
                        ChartDataVO dailyData = (ChartDataVO) cachedData;

                        // 累加当天的正常品规数和异常品规数
                        if (dailyData.getSeries() != null && dailyData.getSeries().size() >= 2) {
                            // 正常品规数
                            List<Integer> dailyValues1 = dailyData.getSeries().get(0).getData();
                            int dailySum1 = dailyValues1.stream().mapToInt(Integer::intValue).sum();
                            dateCountMap1.put(date, dateCountMap1.getOrDefault(date, 0) + dailySum1);

                            // 异常品规数
                            List<Integer> dailyValues2 = dailyData.getSeries().get(1).getData();
                            int dailySum2 = dailyValues2.stream().mapToInt(Integer::intValue).sum();
                            dateCountMap2.put(date, dateCountMap2.getOrDefault(date, 0) + dailySum2);
                        }
                    }
                } catch (Exception e) {
                    log.error("从Redis获取日正常/异常品规统计数据失败, key: {}", cacheKey, e);
                }
            }

            // 根据时间范围类型，将统计结果映射到返回数组
            LocalDate currentDate = startTime.toLocalDate();
            int step = (int) Math.max(1, totalDays / xAxis.size());

            for (int i = 0; i < xAxis.size(); i++) {
                int count1 = 0;
                int count2 = 0;
                LocalDate tempDate = currentDate;

                // 聚合step天的数据
                for (int j = 0; j < step && tempDate.isBefore(endTime.toLocalDate().plusDays(1)); j++) {
                    count1 += dateCountMap1.getOrDefault(tempDate, 0);
                    count2 += dateCountMap2.getOrDefault(tempDate, 0);
                    tempDate = tempDate.plusDays(1);
                }

                aggregatedData1[i] = count1;
                aggregatedData2[i] = count2;
                currentDate = tempDate;
            }
        }

        // 如果Redis中没有足够的数据，则使用数据库查询作为备选方案
        boolean hasData1 = Arrays.stream(aggregatedData1).anyMatch(count -> count > 0);
        boolean hasData2 = Arrays.stream(aggregatedData2).anyMatch(count -> count > 0);

        if (!hasData1) {
            log.info("Redis中没有足够的正常品规数统计数据，使用数据库查询作为备选方案");
            // 从数据库中统计正常品规数据
            int normalCount = countNormalByTimeRange(startTime, endTime, deptId, userId);
            // 将总数平均分配到各个时间点
            List<Integer> dbData = new ArrayList<>();
            int avgCount = normalCount / xAxis.size();
            for (int i = 0; i < xAxis.size(); i++) {
                dbData.add(avgCount);
            }
            seriesVO1.setData(dbData);
        } else {
            seriesVO1.setData(Arrays.asList(aggregatedData1));
        }

        if (!hasData2) {
            log.info("Redis中没有足够的异常品规数统计数据，使用数据库查询作为备选方案");
            // 从数据库中统计异常品规数据
            int abnormalCount = countAbnormalByTimeRange(startTime, endTime, deptId, userId);
            // 将总数平均分配到各个时间点
            List<Integer> dbData = new ArrayList<>();
            int avgCount = abnormalCount / xAxis.size();
            for (int i = 0; i < xAxis.size(); i++) {
                dbData.add(avgCount);
            }
            seriesVO2.setData(dbData);
        } else {
            seriesVO2.setData(Arrays.asList(aggregatedData2));
        }

        series.add(seriesVO1);
        series.add(seriesVO2);
        vo.setSeries(series);

        return vo;
    }

    /**
     * 计算环比趋势
     *
     * @param current 当前值
     * @param previous 上一个值
     * @return 环比趋势（百分比）
     */
    private double calculateTrend(int current, int previous) {
        if (previous == 0) {
            if(current == 0) {
                return 0.0;
            }
            return 100.0;
        }
        return Math.round((current - previous) * 100.0 / previous * 10) / 10.0;
    }

    /**
     * 构建Redis缓存键
     * 对于day类型，使用日期格式作为key的一部分，例如：timeRange:20250101:deptId:12345:userId:000000
     * 其他时间范围类型不保存到Redis，而是通过聚合day数据计算得出
     *
     * @param timeRange   时间范围
     * @param deptId      部门ID
     * @param userId      人员ID
     * @param date        日期（可选，如果为null则使用当前日期）
     * @return Redis缓存键
     */
    private String buildCacheKey(String timeRange, String deptId, String userId, LocalDate date) {
        StringBuilder keyBuilder = new StringBuilder(REDIS_KEY_PREFIX);

        // 只有day类型的数据会保存到Redis，并且包含日期信息
        if ("day".equals(timeRange)) {
            // 如果未指定日期，则使用当前日期
            LocalDate keyDate = date != null ? date : LocalDate.now();
            // 格式化为yyyyMMdd格式
            String dateStr = keyDate.format(java.time.format.DateTimeFormatter.BASIC_ISO_DATE);
            keyBuilder.append(dateStr);
        } else {
            // 其他时间范围类型不会直接保存到Redis，这里只是为了兼容现有代码
            keyBuilder.append(timeRange);
        }

        if (Func.isNotEmpty(deptId)) {
            keyBuilder.append(":deptId:").append(deptId);
        } else {
            keyBuilder.append(":deptId:all");
        }

        if (Func.isNotEmpty(userId)) {
            keyBuilder.append(":userId:").append(userId);
        } else {
            keyBuilder.append(":userId:all");
        }

        return keyBuilder.toString();
    }

    /**
     * 从Redis缓存中获取数据
     *
     * @param cacheKey Redis缓存键
     * @return 缓存的监控数据，如果不存在则返回null
     */
    private MonitoringDataVO getFromRedis(String cacheKey) {
        try {
            Object cachedData = redisTemplate.opsForValue().get(cacheKey);
            if (cachedData != null) {
                return (MonitoringDataVO) cachedData;
            }
        } catch (Exception e) {
            log.error("从Redis获取监控数据失败, key: {}", cacheKey, e);
        }
        return null;
    }

    /**
     * 将数据保存到Redis缓存
     *
     * @param cacheKey Redis缓存键
     * @param data     要缓存的数据
     */
    private void saveToRedis(String cacheKey, MonitoringDataVO data) {
        try {
            redisTemplate.opsForValue().set(cacheKey, data);
            log.debug("保存监控数据到Redis缓存成功, key: {}", cacheKey);
        } catch (Exception e) {
            log.error("保存监控数据到Redis缓存失败, key: {}", cacheKey, e);
        }
    }

    /**
     * 统计指定时间范围内登录的用户数
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param deptId 部门ID
     * @param userId 用户ID
     * @return 指定时间范围内登录的用户数
     */
    private int countLoginUsersByTimeRange(LocalDateTime startTime, LocalDateTime endTime, String deptId, String userId) {
        try {
            // 获取符合条件的用户ID列表
            List<Long> filteredUserIds = getFilteredUserIds(deptId, userId);

            // 如果没有符合条件的用户，直接返回0
            if (filteredUserIds.isEmpty()) {
                return 0;
            }

            // 构建查询条件
            LambdaQueryWrapper<User> queryWrapper = Wrappers.<User>lambdaQuery()
                    .ge(User::getLastLoginTime, startTime)
                    .le(User::getLastLoginTime, endTime)
                    .eq(User::getIsDeleted, 0)
                    .in(User::getId, filteredUserIds);

            // 执行查询并返回结果
            long count = userService.count(queryWrapper);
            return (int) count;
        } catch (Exception e) {
            log.error("统计时间范围内登录用户数失败: {} 至 {}", startTime, endTime, e);
            return 0;
        }
    }

    /**
     * 统计指定时间范围内的检查零售户数量
     * 通过查询ms_item_identify表，关联ms_exploration表，统计不同的license_id数量
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param deptId    部门ID
     * @param userId    用户ID
     * @return 检查零售户数量
     */
    private int countByTimeRange(LocalDateTime startTime, LocalDateTime endTime, String deptId, String userId) {
        try {
            // 构建查询条件
            LocalDate startDate = startTime.toLocalDate();
            LocalDate endDate = endTime.toLocalDate();

            // 获取符合条件的用户ID列表
            List<Long> filteredUserIds = getFilteredUserIds(deptId, userId);

            // 如果没有符合条件的用户，直接返回0
            if (filteredUserIds.isEmpty()) {
                return 0;
            }

            // 查询品规识别记录，关联勘查表，统计不同的零售户ID数量
            // 1. 先获取时间范围内的品规识别记录
            List<ItemIdentifyEntity> identifyList = itemIdentifyService.list(
                    Wrappers.<ItemIdentifyEntity>lambdaQuery()
                            .ge(ItemIdentifyEntity::getIdentifyDate, startDate)
                            .le(ItemIdentifyEntity::getIdentifyDate, endDate)
                            .in(ItemIdentifyEntity::getCreateUser, filteredUserIds)
            );

            if (identifyList.isEmpty()) {
                return 0;
            }

            // 2. 获取所有关联的勘查ID
            List<Long> explorationIds = identifyList.stream()
                    .map(ItemIdentifyEntity::getExplorationId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            if (explorationIds.isEmpty()) {
                return 0;
            }

            // 3. 查询这些勘查ID对应的勘查记录，统计不同的零售户ID数量
            List<ExplorationEntity> explorationList = explorationService.list(
                    Wrappers.<ExplorationEntity>lambdaQuery()
                            .in(ExplorationEntity::getId, explorationIds)
            );

            // 4. 统计不同的零售户ID数量
            long count = explorationList.stream()
                    .map(ExplorationEntity::getLicenseId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .count();

            return (int) count;
        } catch (Exception e) {
            log.error("统计时间范围内的检查零售户数量失败: {} 至 {}", startTime, endTime, e);
            return 0;
        }
    }

    /**
     * 统计指定时间范围内的品规识别次数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param deptId    部门ID
     * @param userId    用户ID
     * @return 品规识别次数
     */
    private int countRecognitionByTimeRange(LocalDateTime startTime, LocalDateTime endTime, String deptId, String userId) {
        try {
            // 构建查询条件
            LocalDate startDate = startTime.toLocalDate();
            LocalDate endDate = endTime.toLocalDate();

            // 获取符合条件的用户ID列表
            List<Long> filteredUserIds = getFilteredUserIds(deptId, userId);

            // 如果没有符合条件的用户，直接返回0
            if (filteredUserIds.isEmpty()) {
                return 0;
            }

            // 统计品规识别次数
            long count = itemIdentifyService.count(
                    Wrappers.<ItemIdentifyEntity>lambdaQuery()
                            .ge(ItemIdentifyEntity::getIdentifyDate, startDate)
                            .le(ItemIdentifyEntity::getIdentifyDate, endDate)
                            .in(ItemIdentifyEntity::getCreateUser, filteredUserIds)
            );
            return (int) count;
        } catch (Exception e) {
            log.error("统计时间范围内的品规识别次数失败: {} 至 {}", startTime, endTime, e);
            return 0;
        }
    }

    /**
     * 统计指定时间范围内的品规识别记录数量
     * 通过查询ms_item_identify_results表，统计记录数量
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param deptId    部门ID
     * @param userId    用户ID
     * @return 品规识别记录数量
     */
    private int countSpecByTimeRange(LocalDateTime startTime, LocalDateTime endTime, String deptId, String userId) {
        try {
            // 构建查询条件
            LocalDate startDate = startTime.toLocalDate();
            LocalDate endDate = endTime.toLocalDate();

            // 获取符合条件的用户ID列表
            List<Long> filteredUserIds = getFilteredUserIds(deptId, userId);

            // 如果没有符合条件的用户，直接返回0
            if (filteredUserIds.isEmpty()) {
                return 0;
            }

            // 统计品规识别记录数量
            List<ItemIdentifyResultsEntity> resultsList = itemIdentifyResultsService.list(
                    Wrappers.<ItemIdentifyResultsEntity>lambdaQuery()
                            .ge(ItemIdentifyResultsEntity::getIdentifyDate, startDate)
                            .le(ItemIdentifyResultsEntity::getIdentifyDate, endDate)
                            .in(ItemIdentifyResultsEntity::getCreateUser, filteredUserIds)
            );

            // 统计记录数量
            return resultsList.size();
        } catch (Exception e) {
            log.error("统计时间范围内的品规总数量失败: {} 至 {}", startTime, endTime, e);
            return 0;
        }
    }

    /**
     * 统计指定时间范围内的正常品规数量
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param deptId    部门ID
     * @param userId    用户ID
     * @return 正常品规数量
     */
    private int countNormalByTimeRange(LocalDateTime startTime, LocalDateTime endTime, String deptId, String userId) {
        try {
            // 构建查询条件
            LocalDate startDate = startTime.toLocalDate();
            LocalDate endDate = endTime.toLocalDate();

            // 获取符合条件的用户ID列表
            List<Long> filteredUserIds = getFilteredUserIds(deptId, userId);

            // 如果没有符合条件的用户，直接返回0
            if (filteredUserIds.isEmpty()) {
                return 0;
            }

            // 查询正常品规记录（collisionType不为"假烟"或"私烟"或为null）
            List<ItemIdentifyResultsEntity> resultsList = itemIdentifyResultsService.list(
                    Wrappers.<ItemIdentifyResultsEntity>lambdaQuery()
                            .ge(ItemIdentifyResultsEntity::getIdentifyDate, startDate)
                            .le(ItemIdentifyResultsEntity::getIdentifyDate, endDate)
                            .notIn(ItemIdentifyResultsEntity::getCollisionType, Arrays.asList("非烟", "假烟", "私烟", "非烟,假烟"))
                            .in(ItemIdentifyResultsEntity::getCreateUser, filteredUserIds)
            );

            // 统计正常品规的数量
            return resultsList.size();
        } catch (Exception e) {
            log.error("统计时间范围内的正常品规数量失败: {} 至 {}", startTime, endTime, e);
            return 0;
        }
    }

    /**
     * 统计指定时间范围内的异常品规数量
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param deptId    部门ID
     * @param userId    用户ID
     * @return 异常品规数量
     */
    private int countAbnormalByTimeRange(LocalDateTime startTime, LocalDateTime endTime, String deptId, String userId) {
        try {
            // 构建查询条件
            LocalDate startDate = startTime.toLocalDate();
            LocalDate endDate = endTime.toLocalDate();

            // 获取符合条件的用户ID列表
            List<Long> filteredUserIds = getFilteredUserIds(deptId, userId);

            // 如果没有符合条件的用户，直接返回0
            if (filteredUserIds.isEmpty()) {
                return 0;
            }

            // 查询异常品规记录（collisionType为"假烟"或"私烟"）
            List<ItemIdentifyResultsEntity> resultsList = itemIdentifyResultsService.list(
                    Wrappers.<ItemIdentifyResultsEntity>lambdaQuery()
                            .ge(ItemIdentifyResultsEntity::getIdentifyDate, startDate)
                            .le(ItemIdentifyResultsEntity::getIdentifyDate, endDate)
                            .in(ItemIdentifyResultsEntity::getCollisionType, Arrays.asList("非烟", "假烟", "私烟", "非烟,假烟"))
                            .in(ItemIdentifyResultsEntity::getCreateUser, filteredUserIds)
            );

            // 统计异常品规的数量
            return resultsList.size();
        } catch (Exception e) {
            log.error("统计时间范围内的异常品规数量失败: {} 至 {}", startTime, endTime, e);
            return 0;
        }
    }

    /**
     * 按小时统计指定时间范围内的检客户数量
     * 通过查询ms_item_identify表，关联ms_exploration表，统计不同的license_id数量
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param deptId    部门ID
     * @param userId    用户ID
     * @return 按小时统计的检客户数量列表（24小时）
     */
    private List<Integer> countInspectionByHour(LocalDateTime startTime, LocalDateTime endTime, String deptId, String userId) {
        try {
            // 初始化24小时的统计数组
            Integer[] hourlyData = new Integer[24];
            Arrays.fill(hourlyData, 0);

            // 构建查询条件
            LocalDate startDate = startTime.toLocalDate();
            LocalDate endDate = endTime.toLocalDate();

            // 获取符合条件的用户ID列表
            List<Long> filteredUserIds = getFilteredUserIds(deptId, userId);

            // 如果没有符合条件的用户，直接返回空数组
            if (filteredUserIds.isEmpty()) {
                return Arrays.asList(hourlyData);
            }

            // 查询品规识别记录，关联勘查表，按小时统计不同的零售户ID数量
            // 1. 先获取时间范围内的品规识别记录
            List<ItemIdentifyEntity> identifyList = itemIdentifyService.list(
                    Wrappers.<ItemIdentifyEntity>lambdaQuery()
                            .ge(ItemIdentifyEntity::getIdentifyDate, startDate)
                            .le(ItemIdentifyEntity::getIdentifyDate, endDate)
                            .in(ItemIdentifyEntity::getCreateUser, filteredUserIds)
            );

            if (identifyList.isEmpty()) {
                return Arrays.asList(hourlyData);
            }

            // 2. 获取所有关联的勘查ID
            List<Long> explorationIds = identifyList.stream()
                    .map(ItemIdentifyEntity::getExplorationId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            if (explorationIds.isEmpty()) {
                return Arrays.asList(hourlyData);
            }

            // 3. 查询这些勘查ID对应的勘查记录
            List<ExplorationEntity> explorationList = explorationService.list(
                    Wrappers.<ExplorationEntity>lambdaQuery()
                            .in(ExplorationEntity::getId, explorationIds)
            );

            // 4. 按小时统计不同的零售户ID数量
            Map<Integer, Set<Long>> hourlyLicenseIds = new HashMap<>();
            for (int i = 0; i < 24; i++) {
                hourlyLicenseIds.put(i, new HashSet<>());
            }

            for (ExplorationEntity exploration : explorationList) {
                if (exploration.getLicenseId() != null) {
                    java.util.Date createTime = exploration.getCreateTime();
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(createTime);
                    int hour = calendar.get(Calendar.HOUR_OF_DAY);
                    hourlyLicenseIds.get(hour).add(exploration.getLicenseId());
                }
            }

            // 5. 转换为数组
            for (int i = 0; i < 24; i++) {
                hourlyData[i] = hourlyLicenseIds.get(i).size();
            }

            return Arrays.asList(hourlyData);
        } catch (Exception e) {
            log.error("按小时统计检客户数量失败: {} 至 {}", startTime, endTime, e);
            // 返回全0的数组
            Integer[] emptyData = new Integer[24];
            Arrays.fill(emptyData, 0);
            return Arrays.asList(emptyData);
        }
    }

    /**
     * 按日期统计指定时间范围内的检客户数量
     * 通过查询ms_item_identify表，关联ms_exploration表，统计不同的license_id数量
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param deptId    部门ID
     * @param userId    用户ID
     * @param size      返回数据的大小
     * @return 按日期统计的检客户数量列表
     */
    private List<Integer> countInspectionByDate(LocalDateTime startTime, LocalDateTime endTime, String deptId, String userId, int size) {
        try {
            // 初始化统计数组
            Integer[] dailyData = new Integer[size];
            Arrays.fill(dailyData, 0);

            // 构建查询条件
            LocalDate startDate = startTime.toLocalDate();
            LocalDate endDate = endTime.toLocalDate();

            // 获取符合条件的用户ID列表
            List<Long> filteredUserIds = getFilteredUserIds(deptId, userId);

            // 如果没有符合条件的用户，直接返回空数组
            if (filteredUserIds.isEmpty()) {
                return Arrays.asList(dailyData);
            }

            // 查询品规识别记录，关联勘查表，按日期统计不同的零售户ID数量
            // 1. 先获取时间范围内的品规识别记录
            List<ItemIdentifyEntity> identifyList = itemIdentifyService.list(
                    Wrappers.<ItemIdentifyEntity>lambdaQuery()
                            .ge(ItemIdentifyEntity::getIdentifyDate, startDate)
                            .le(ItemIdentifyEntity::getIdentifyDate, endDate)
                            .in(ItemIdentifyEntity::getCreateUser, filteredUserIds)
            );

            if (identifyList.isEmpty()) {
                return Arrays.asList(dailyData);
            }

            // 2. 获取所有关联的勘查ID
            List<Long> explorationIds = identifyList.stream()
                    .map(ItemIdentifyEntity::getExplorationId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            if (explorationIds.isEmpty()) {
                return Arrays.asList(dailyData);
            }

            // 3. 查询这些勘查ID对应的勘查记录
            List<ExplorationEntity> explorationList = explorationService.list(
                    Wrappers.<ExplorationEntity>lambdaQuery()
                            .in(ExplorationEntity::getId, explorationIds)
            );

            // 计算时间范围的总天数
            long totalDays = java.time.temporal.ChronoUnit.DAYS.between(startTime, endTime) + 1;

            // 4. 按日期统计不同的零售户ID数量
            Map<LocalDate, Set<Long>> dateLicenseIdsMap = new HashMap<>();
            for (ExplorationEntity exploration : explorationList) {
                if (exploration.getLicenseId() != null) {
                    java.util.Date explorationDate = exploration.getExplorationDate();
                    LocalDate localDate = new java.sql.Date(explorationDate.getTime()).toLocalDate();

                    // 确保日期键存在
                    if (!dateLicenseIdsMap.containsKey(localDate)) {
                        dateLicenseIdsMap.put(localDate, new HashSet<>());
                    }

                    // 添加零售户ID
                    dateLicenseIdsMap.get(localDate).add(exploration.getLicenseId());
                }
            }

            // 5. 转换为按日期统计的Map
            Map<LocalDate, Integer> dateCountMap = new HashMap<>();
            for (Map.Entry<LocalDate, Set<Long>> entry : dateLicenseIdsMap.entrySet()) {
                dateCountMap.put(entry.getKey(), entry.getValue().size());
            }

            // 6. 根据时间范围类型，将统计结果映射到返回数组
            LocalDate currentDate = startTime.toLocalDate();
            int step = (int) Math.max(1, totalDays / size);

            for (int i = 0; i < size; i++) {
                int count = 0;
                LocalDate tempDate = currentDate;

                // 聚合step天的数据
                for (int j = 0; j < step && tempDate.isBefore(endTime.toLocalDate().plusDays(1)); j++) {
                    count += dateCountMap.getOrDefault(tempDate, 0);
                    tempDate = tempDate.plusDays(1);
                }

                dailyData[i] = count;
                currentDate = tempDate;
            }

            return Arrays.asList(dailyData);
        } catch (Exception e) {
            log.error("按日期统计检客户数量失败: {} 至 {}", startTime, endTime, e);
            // 返回全0的数组
            Integer[] emptyData = new Integer[size];
            Arrays.fill(emptyData, 0);
            return Arrays.asList(emptyData);
        }
    }

    @Override
    public List<PersonnelRankingVO> getPersonnelRanking(String timeRange, LocalDateTime startTime, LocalDateTime endTime, String deptId) {
        try {
            // 获取符合条件的用户ID列表
            List<Long> filteredUserIds = getFilteredUserIds(deptId, null);

            // 获取部门下的所有用户
            List<User> deptUsers = userService.list(
                    Wrappers.<User>lambdaQuery()
                            .eq(User::getIsDeleted, 0)
                            .in(Func.isNotEmpty(filteredUserIds), User::getId, filteredUserIds)
            );

            // 创建结果列表
            List<PersonnelRankingVO> result = new ArrayList<>();

            // 遍历部门下的所有用户，获取每个用户的统计数据
            for (User user : deptUsers) {
                String userId2 = String.valueOf(user.getId());

                // 获取用户的监控数据
                MonitoringDataVO monitoringData = getMonitoringData(timeRange, startTime, endTime, deptId, userId2);

                // 创建人员排名VO对象
                PersonnelRankingVO rankingVO = new PersonnelRankingVO();

                // 获取部门名称
                if (user.getDeptId() != null) {
                    Dept dept = deptService.getById(Long.valueOf(user.getDeptId()));
                    if (dept != null) {
                        rankingVO.setDeptName(dept.getDeptName());
                    }
                }

                rankingVO.setUserId(userId2);
                rankingVO.setName(user.getRealName());
                rankingVO.setLoginCount(monitoringData.getLoginCount());
                rankingVO.setInspectionCount(monitoringData.getInspectionCount());
                rankingVO.setRecognitionCount(monitoringData.getRecognitionCount());
                rankingVO.setSpecCount(monitoringData.getSpecCount());

                // 添加到结果列表
                result.add(rankingVO);
            }

            // 多级排序：检查零售户数量降序 > 登录次数降序 > 部门名称升序 > 姓名升序
            result.sort(
                Comparator.comparing(PersonnelRankingVO::getInspectionCount, Comparator.nullsLast(Comparator.reverseOrder()))
                    .thenComparing(PersonnelRankingVO::getLoginCount, Comparator.nullsLast(Comparator.reverseOrder()))
                    .thenComparing(PersonnelRankingVO::getDeptName, Comparator.nullsLast(Comparator.naturalOrder()))
                    .thenComparing(PersonnelRankingVO::getName, Comparator.nullsLast(Comparator.naturalOrder()))
            );

            // 设置排名
            for (int i = 0; i < result.size(); i++) {
                result.get(i).setRanking(i + 1);
            }

            return result;
        } catch (Exception e) {
            log.error("获取人员排名数据失败: {} 至 {}, 部门ID: {}", startTime, endTime, deptId, e);
            return new ArrayList<>();
        }
    }
}
