package org.springblade.ms.platform12345.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 查询督办工单请求参数
 */
@Data
@Schema(description = "查询督办工单请求参数")
public class QuerySuperviseOrderRequest {
    
    @Schema(description = "开始时间，格式：YYYY-MM-DD HH24:MI:SS")
    private String startTime;
    
    @Schema(description = "结束时间，格式：YYYY-MM-DD HH24:MI:SS")
    private String endTime;
    
    @Schema(description = "加密校验串")
    private String signature;
    
    @Schema(description = "时间戳")
    private String timestamp;
    
    @Schema(description = "应用ID")
    private String appid;
} 