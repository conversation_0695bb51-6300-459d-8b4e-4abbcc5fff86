import httpApi from "/utils/http/httpApi";
Page({
  data: {
    items:[{
      title: '许可证',
      content: '',
      badge:'',
      text:0,
    },
    {
      title: '违法记录',
      content: 'desc',
      badge:false,
      text:0,
    },
    {
      title: '举报投诉',
      content: 'desc',
      badge:false,
      text:0,
    },{
      title: '品规识别',
      content: 'desc',
      badge:false,
      text:0,
    }],
    currentPage:'',
    isView:false,
    licMapData:{}, //详细数据
    licData:{}, //
  },
  async onLoad(options) {
    if(options&&options.view){
      this.setData({
        isView:options.view
      })
    }
    await dd.getStorage(
      {
        key:"licData",
        success:res=>{
          this.setData({
            licData:JSON.parse(res.data)
          })
          
        }
    })
    // console.log(this.data.licData,"xkz-data")
    const res = await httpApi.request({
      url: `/api/dingapp/license/getDingMapLicense?licNo=${this.data.licData.licNo}&yhytId=${this.data.licData.licId}`,
      method: 'get',
    })
    this.setData({licMapData:res.data})
    // console.log(this.data.licMapData)

    this.setData({
      currentPage:this.data.items[0].title
    })

    //气泡处理
    const res1 = await httpApi.request({
      url: `/api/dingapp/illegalrecords/getTotalCount?licNo=${this.data.licData.licNo}&yhytId=${res.data.yhytLicenseVO.id}`,
      method: 'get',
    })
    if(res1.data){
      let count = res1.data
      if(count.illegalRecordCount>0){
        this.setData({
          'items[1].text':count.illegalRecordCount,
          'items[1].badge':true
        })
      }
      if(count.reportComplaintCount>0){
        this.setData({
          'items[2].text':count.reportComplaintCount,
          'items[2].badge':true
      })
      }
      if(count.identCount>0){
        this.setData({
          'items[3].text':count.identCount,
          'items[3].badge':true
        })
      }
    }
  },
  handleTabsChange(e){
    this.setData({
      currentPage:this.data.items[e].title
    })
  }
});
