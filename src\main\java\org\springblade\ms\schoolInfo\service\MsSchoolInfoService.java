package org.springblade.ms.schoolInfo.service;

import org.springblade.core.mp.base.BaseService;
import org.springblade.ms.schoolInfo.pojo.entity.MsSchoolInfo;

import java.math.BigDecimal;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ms_school_info】的数据库操作Service
* @createDate 2025-03-12 17:47:40
*/
public interface MsSchoolInfoService extends BaseService<MsSchoolInfo> {

    /**
     * 查询附近的学校信息
     *
     * @param longitude 经度
     * @param latitude 纬度
     * @param radius 半径（米）
     * @param searchParam 搜索参数（学校名称）
     * @return 学校信息列表，按距离排序
     */
    List<MsSchoolInfo> getNearbySchools(BigDecimal longitude, BigDecimal latitude, Double radius, String searchParam);
}
