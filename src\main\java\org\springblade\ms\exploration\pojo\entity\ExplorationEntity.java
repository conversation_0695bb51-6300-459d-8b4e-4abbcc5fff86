/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.ms.exploration.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import java.math.BigDecimal;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import java.io.Serial;

/**
 * 勘查记录 实体类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@TableName("ms_exploration")
@Schema(description = "Exploration对象")
@EqualsAndHashCode(callSuper = true)
public class ExplorationEntity extends TenantEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 零售户 ID
	 */
	@Schema(description = "零售户 ID")
	private Long licenseId;
	/**
	 * 勘查日期
	 */
	@Schema(description = "勘查日期")
	private Date explorationDate;
	/**
	 * 创建方式（拍照、搜索）
	 */
	@Schema(description = "创建方式（拍照、搜索）")
	private String createType;
	/**
	 * 勘查对象类型（RETAILER-零售户、UNLICENSED-无证户、SCHOOL-学校）
	 */
	@Schema(description = "勘查对象类型（RETAILER-零售户、UNLICENSED-无证户、SCHOOL-学校）")
	private String objectType;
	/**
	 * 经度（最新）
	 */
	@Schema(description = "经度（最新）")
	private BigDecimal longitude;
	/**
	 * 纬度（最新）
	 */
	@Schema(description = "纬度（最新）")
	private BigDecimal latitude;

}
