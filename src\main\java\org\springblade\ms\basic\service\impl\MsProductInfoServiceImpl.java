package org.springblade.ms.basic.service.impl;

import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.ms.basic.mapper.MsProductInfoMapper;
import org.springblade.ms.basic.pojo.entity.MsProductInfo;
import org.springblade.ms.basic.service.MsProductInfoService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR> @description 针对表【ms_product_info(品规信息表（定时同步）)】的数据库操作Service实现
* @createDate 2025-03-11 20:43:50
*/
@Service
public class MsProductInfoServiceImpl extends BaseServiceImpl<MsProductInfoMapper, MsProductInfo> implements MsProductInfoService {

}




