package org.springblade.ms.dingapp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.ms.illegalrecords.pojo.entity.IllegalRecordsEntity;
import org.springblade.ms.illegalrecords.pojo.vo.IllegalRecordsVO;
import org.springblade.ms.reportcomplaint.pojo.entity.ReportIllegalLabelEntity;

import java.util.List;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-02-16 18:39
 */
public interface IDingIllegalRecordsService {

    IPage<IllegalRecordsVO> selectPage(IPage<ReportIllegalLabelEntity> page, String custCode);

    List<IllegalRecordsEntity> getListByCustCode(String custCode);

}
