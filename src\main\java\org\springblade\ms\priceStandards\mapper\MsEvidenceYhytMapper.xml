<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.ms.priceStandards.mapper.MsEvidenceYhytMapper">

    <resultMap id="BaseResultMap" type="org.springblade.ms.priceStandards.pojo.vo.MsEvidenceYhytVO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="yhytId" column="yhyt_id" jdbcType="BIGINT"/>
            <result property="priceStandardsId" column="price_standards_id" jdbcType="BIGINT"/>
            <result property="productName" column="product_name" jdbcType="VARCHAR"/>
            <result property="stdType" column="std_type" jdbcType="VARCHAR"/>
            <result property="selectedQuantity" column="selected_quantity" jdbcType="NUMERIC"/>
            <result property="currentUnitPrice" column="current_unit_price" jdbcType="NUMERIC"/>
            <result property="selectionTime" column="selection_time" jdbcType="TIMESTAMP"/>
            <result property="createUser" column="create_user" jdbcType="BIGINT"/>
            <result property="createDept" column="create_dept" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateUser" column="update_user" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="enforcementAgency" column="enforcement_agency" jdbcType="VARCHAR"/>
            <result property="caseTime" column="case_time" jdbcType="TIMESTAMP"/>
            <result property="address" column="address" jdbcType="VARCHAR"/>
            <result property="detailedAddress" column="detailed_address" jdbcType="VARCHAR"/>
            <result property="jointEnforcementAgency" column="joint_enforcement_agency" jdbcType="VARCHAR"/>
            <result property="caseReason" column="case_reason" jdbcType="VARCHAR"/>
            <result property="partyInvolved" column="party_involved" jdbcType="VARCHAR"/>
            <result property="licenseNo" column="license_no" jdbcType="VARCHAR"/>
            <result property="packageQty2" column="package_qty2" jdbcType="BIGINT"/>
            <result property="evidenceType" column="evidence_type" jdbcType="VARCHAR"/>
            <result property="priceSource" column="price_source" jdbcType="VARCHAR"/>
            <result property="source" column="source" jdbcType="VARCHAR"/>
            <result property="isZhanjiangDeployment" column="is_zhanjiang_deployment" jdbcType="INTEGER"/>
            <result property="transportDetails" column="transport_details" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,yhyt_id,price_standards_id,
        product_name,std_type,selected_quantity,title,enforcement_agency,
        current_unit_price,selection_time,create_user,
        create_dept,create_time,update_user,
        update_time,status,is_deleted,
        tenant_id,case_time,address,
        detailed_address,joint_enforcement_agency,case_reason,
        party_involved,license_no,evidence_type,price_source,
        source,is_zhanjiang_deployment,transport_details
    </sql>


    <select id="selectPage" resultMap="BaseResultMap">
        SELECT mey_ranked.*, myl.lic_no, myl.company_name
        FROM (
        SELECT
        mey.*,
        ROW_NUMBER() OVER (PARTITION BY mey.selection_time ORDER BY mey.selection_time desc) as rn
        FROM ms_evidence_yhyt mey
        WHERE mey.is_deleted = 0
        <if test="entity.createUser != null">
            and mey.create_user = #{entity.createUser}
        </if>

        ) AS mey_ranked
        LEFT JOIN ms_yhyt_license myl ON mey_ranked.yhyt_id = myl.id
         <where>
<!--             <if test="entity != null and entity.searchText != null and entity.searchText.trim() != ''">-->
<!--                 AND (myl.lic_no LIKE CONCAT('%', #{entity.searchText}, '%')-->
<!--                 OR myl.company_name LIKE CONCAT('%', #{entity.searchText}, '%'))-->
<!--             </if>-->
             <if test="entity != null and entity.searchText != null and entity.searchText.trim() != ''">
                 AND mey_ranked.title LIKE CONCAT('%', #{entity.searchText}, '%')
             </if>
            <if test="true">
                AND mey_ranked.rn = 1
            </if>
         </where>

    </select>

    <resultMap id="ExportResultMap" type="org.springblade.ms.priceStandards.pojo.vo.MsEvidenceYhytExportVO" extends="BaseResultMap">
        <result property="barcode" column="barcode" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="selectEvidenceWithBarcode" resultMap="ExportResultMap">
        SELECT DISTINCT e.*, p.barcode,i.package_qty2
        FROM ms_evidence_yhyt e
        LEFT JOIN ms_price_standards p ON e.price_standards_id = p.id and p.is_deleted = 0
        left join ms_product_info_yangjiang i on i.strip_code = p.barcode
        ${ew.customSqlSegment}
    </select>

</mapper>
