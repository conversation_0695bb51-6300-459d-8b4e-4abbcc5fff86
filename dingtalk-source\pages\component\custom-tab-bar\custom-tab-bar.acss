.tabBarMain {
    height: 10vh;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    align-items: flex-end;
    justify-content: space-evenly;
    position: relative;
    z-index: 200;
}

.mapTab {
    height: 100%;
    width: 50px;
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    align-items: center;
    margin-top: 5px;
}

.mapTabImg {
    height: 20px;
    width: 20px;
    background-image: url('/image/map.png');
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    margin-top: 10px;
}

.mapTabFont {
    margin-top: 5px;
    font-size: small;
}

.createTab {
    height: 110%;
    width: 100px;
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    align-items: center;
    margin-bottom: 25px;
}

.createTabImg {
    height: 80px;
    width: 80px;
    background-image: url('/image/scan_hl.svg');
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
}

.createTabFont {
    margin-top: -13px;
    font-size: small;
}

.mineTab {
    height: 100%;
    width: 50px;
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    align-items: center;
}

.mineTabImg {
    height: 20px;
    width: 18px;
    background-image: url('/image/wode.png');
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    margin-top: 10px;
}

.mineTabFont {
    margin-top: 5px;
    font-size: small;
}


.activity-dialog-footer {
    padding-top: 64rpx;
    padding-bottom: 64rpx;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-direction: row;
}

.searchBar {
    padding: 4px;
    border-top-left-radius: 12px;
    border-bottom-left-radius: 12px;
    flex: 1;
}

.searchBar input {
    padding: 4px;
    background-color: #f7f9fa;
}

.searchBtn {
    background-color: #1c88f9;
    color: white;
    text-align: center;
    border-top-right-radius: 12px;
    border-bottom-right-radius: 12px;
    padding: 19rpx 15rpx;
}