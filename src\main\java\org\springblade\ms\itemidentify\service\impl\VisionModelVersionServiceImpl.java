/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.ms.itemidentify.service.impl;

import org.springblade.ms.itemidentify.pojo.entity.VisionModelVersionEntity;
import org.springblade.ms.itemidentify.pojo.vo.VisionModelVersionVO;
import org.springblade.ms.itemidentify.excel.VisionModelVersionExcel;
import org.springblade.ms.itemidentify.mapper.VisionModelVersionMapper;
import org.springblade.ms.itemidentify.service.IVisionModelVersionService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 品规模型版本 服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Service
public class VisionModelVersionServiceImpl extends BaseServiceImpl<VisionModelVersionMapper, VisionModelVersionEntity> implements IVisionModelVersionService {

	@Override
	public IPage<VisionModelVersionVO> selectVisionModelVersionPage(IPage<VisionModelVersionVO> page, VisionModelVersionVO visionModelVersion) {
		return page.setRecords(baseMapper.selectVisionModelVersionPage(page, visionModelVersion));
	}


	@Override
	public List<VisionModelVersionExcel> exportVisionModelVersion(Wrapper<VisionModelVersionEntity> queryWrapper) {
		List<VisionModelVersionExcel> visionModelVersionList = baseMapper.exportVisionModelVersion(queryWrapper);
		//visionModelVersionList.forEach(visionModelVersion -> {
		//	visionModelVersion.setTypeName(DictCache.getValue(DictEnum.YES_NO, VisionModelVersion.getType()));
		//});
		return visionModelVersionList;
	}

}
