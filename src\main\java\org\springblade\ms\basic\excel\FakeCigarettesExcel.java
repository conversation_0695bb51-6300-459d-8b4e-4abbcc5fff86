/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.ms.basic.excel;


import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;
import java.io.Serial;


/**
 * 查获假烟统计表 Excel实体类
 *
 * <AUTHOR>
 * @since 2025-04-20
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class FakeCigarettesExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("主键")
	private Long id;
	/**
	 * 供应商名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("供应商名称")
	private String supplyName;
	/**
	 * 商品唯一标识
	 */
	@ColumnWidth(20)
	@ExcelProperty("商品唯一标识")
	private String goodsUuid;
	/**
	 * 商品全称
	 */
	@ColumnWidth(20)
	@ExcelProperty("商品全称")
	private String goodsName;
	/**
	 * 品牌名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("品牌名称")
	private String brandName;
	/**
	 * 走私数量（单位：万条）
	 */
	@ColumnWidth(20)
	@ExcelProperty("走私数量（单位：万条）")
	private BigDecimal smuggleQty;
	/**
	 * 真实数量（单位：万条）
	 */
	@ColumnWidth(20)
	@ExcelProperty("真实数量（单位：万条）")
	private BigDecimal truthQty;
	/**
	 * 假冒数量（单位：万条）
	 */
	@ColumnWidth(20)
	@ExcelProperty("假冒数量（单位：万条）")
	private BigDecimal fakeQty;
	/**
	 * 总数量（单位：万条）
	 */
	@ColumnWidth(20)
	@ExcelProperty("总数量（单位：万条）")
	private BigDecimal totalQty;
	/**
	 * 是否删除：0表示未删除，1表示已删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否删除：0表示未删除，1表示已删除")
	private Integer isDeleted;
	/**
	 * 租户ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户ID")
	private String tenantId;

}
