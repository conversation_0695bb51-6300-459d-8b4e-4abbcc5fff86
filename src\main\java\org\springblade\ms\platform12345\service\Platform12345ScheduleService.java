package org.springblade.ms.platform12345.service;

import cn.hutool.core.date.DateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springblade.ms.platform12345.dto.QueryMajorOrderRequest;
import org.springblade.ms.platform12345.utils.Platform12345Client;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 12345平台定时任务服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
@ConditionalOnProperty(name = "platform.12345.schedule.enabled", havingValue = "true", matchIfMissing = false)
public class Platform12345ScheduleService {

    private final Platform12345Service platform12345Service;
    private final Platform12345Client platform12345Client;
    private final Platform12345DataService platform12345DataService;

    /**
     * 定时查询12345平台主办工单
     * 每小时执行一次，查询当前时间往前15天的工单数据
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void scheduledQueryMajorOrder() {
        try {
            log.info("开始执行定时任务：查询12345平台主办工单");

            // 计算查询时间范围：当前时间往前15天
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime fifteenDaysAgo = now.minusDays(15);
            String startTime = fifteenDaysAgo.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            String endTime = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

            log.info("查询时间范围：{} 到 {}", startTime, endTime);

            // 执行查询和保存
            queryAndSaveMajorOrder(startTime, endTime);

            log.info("定时任务执行完成：查询12345平台主办工单");
        } catch (Exception e) {
            log.error("定时任务执行失败：查询12345平台主办工单", e);
        }
    }

    /**
     * 查询并保存主办工单数据
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    public void queryAndSaveMajorOrder(String startTime, String endTime) {
        try {
            log.info("查询12345平台主办工单，开始时间：{}，结束时间：{}", startTime, endTime);

            // 生成签名信息
            Platform12345Client.SignatureInfo signatureInfo = platform12345Client.generateSignature();

            // 构建请求
            QueryMajorOrderRequest request = new QueryMajorOrderRequest();
            request.setStartTime(startTime);
            request.setEndTime(endTime);
            request.setSignature(signatureInfo.getSignature());
            request.setTimestamp(signatureInfo.getTimestamp());
            request.setAppid(signatureInfo.getAppid());

            // 调用服务
            String result = platform12345Service.queryMajorOrder(request);

            log.info("查询12345平台主办工单成功，返回数据长度：{}", result != null ? result.length() : 0);

            // 同步保存工单数据
            try {
                R saveResult = platform12345DataService.saveProWoList(result);
                log.info("保存工单数据结果: {}", saveResult.isSuccess() ? "成功" : "失败");
                if (saveResult.isSuccess() && saveResult.getData() != null) {
                    log.info("本次保存工单数量: {}", saveResult.getData());
                }
            } catch (Exception e) {
                log.error("保存工单数据失败", e);
                throw e; // 重新抛出异常，让调用方知道保存失败
            }

        } catch (Exception e) {
            log.error("查询12345平台主办工单失败", e);
            throw e; // 重新抛出异常
        }
    }


}
