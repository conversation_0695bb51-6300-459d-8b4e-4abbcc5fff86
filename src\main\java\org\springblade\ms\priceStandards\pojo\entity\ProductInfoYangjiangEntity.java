package org.springblade.ms.priceStandards.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 
 * @TableName ms_product_info_yangjiang
 */
@TableName(value ="ms_product_info_yangjiang")
@Data
public class ProductInfoYangjiangEntity  implements Serializable {
    /**
     * 
     */
    private String company;

    /**
     * 
     */
    private String name;

    /**
     * 
     */
    private String photoname;

    /**
     * 
     */
    private Integer photo;

    /**
     * 
     */
    private Integer type1;

    /**
     * 
     */
    private String type2;

    /**
     * 
     */
    private String barcode2;

    /**
     * 
     */
    private String barcode;

    /**
     * 
     */
    private Double lprice;

    /**
     * 
     */
    private Double pprice;

    /**
     * 
     */
    private String type3;

    /**
     * 
     */
    private String mulu;

    /**
     * 
     */
    private Integer xilie;

    /**
     * 
     */
    private String ctime;

    /**
     * 
     */
    private Integer mlid;
    private Integer id;

    /**
     * 
     */
    private Integer mulu1;

    /**
     * 
     */
    private String mulu2;

    /**
     * 
     */
    private String zs;

    /**
     * 
     */
    private String unit;

    /**
     * 
     */
    private String type4;

    /**
     * 
     */
    private String type5;

    /**
     * 
     */
    private String danwei;

    /**
     * 
     */
    private String memo;

    /**
     * 
     */
    private String jy1;

    /**
     * 
     */
    private Double jy2;

    /**
     * 
     */
    private Double jy3;

    /**
     * 
     */
    private String jy4;

    /**
     * 
     */
    private String jy5;

    /**
     * 
     */
    private String jy6;

    private Long packageQty2;
    private String evidenceType;
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}