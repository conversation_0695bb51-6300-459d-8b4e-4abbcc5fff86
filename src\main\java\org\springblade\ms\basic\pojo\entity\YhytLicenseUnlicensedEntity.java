package org.springblade.ms.basic.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 
 * @TableName ms_yhyt_license_unlicensed
 */
@TableName(value ="ms_yhyt_license_unlicensed")
@Data
@EqualsAndHashCode(callSuper = true)
public class YhytLicenseUnlicensedEntity extends TenantEntity implements Serializable {
    /**
     * 
     */
    private String retailerUuid;

    /**
     * 
     */
    private String licNo;

    /**
     * 
     */
    private String licType;

    /**
     * 
     */
    private String oldLicNo;

    /**
     * 
     */
    private String custCode;

    /**
     * 
     */
    private String companyName;

    /**
     * 
     */
    private String ecoType;

    /**
     * 
     */
    private String contractPerson;

    /**
     * 
     */
    private String retailTel;

    /**
     * 
     */
    private String retailTelBack;

    /**
     * 
     */
    private String businessAddr;

    /**
     * 
     */
    private Date validateStart;

    /**
     * 
     */
    private Date validateEnd;

    /**
     * 
     */
    private String licStatus;

    /**
     * 
     */
    private Date invalidTime;

    /**
     * 
     */
    private String orgName;

    /**
     * 
     */
    private Integer isHaveBusinessLic;

    /**
     * 
     */
    private String businessLicNo;

    /**
     * 
     */
    private String businessValidType;

    /**
     * 
     */
    private Date businessValidStart;

    /**
     * 
     */
    private Date businessValidEnd;

    /**
     * 
     */
    private String registeredStatus;

    /**
     * 
     */
    private String specialType;

    /**
     * 
     */
    private String specialTypeOther;

    /**
     * 
     */
    private String busiType;

    /**
     * 
     */
    private String busiSubType;

    /**
     * 
     */
    private String envType;

    /**
     * 
     */
    private BigDecimal longitude;

    /**
     * 
     */
    private BigDecimal latitude;

    /**
     * 
     */
    private String ecoSubType;

    /**
     * 
     */
    private Integer isValidate;

    /**
     * 
     */
    private String shopSign;

    /**
     * 
     */
    private String consumerNeed;

    /**
     * 
     */
    private String storeBrand;

    /**
     * 
     */
    private String managerScope;

    /**
     * 
     */
    private String busiSizeCode;

    /**
     * 
     */
    private String busiSizeName;

    /**
     * 
     */
    private String adscriptionCode;

    /**
     * 
     */
    private String adscriptionName;

    /**
     * 
     */
    private String bizFormat;

    /**
     * 
     */
    private String supplyStatus;

    /**
     * 
     */
    private String supplyOrgUuid;

    /**
     * 
     */
    private String supplyCompanyCode;

    /**
     * 
     */
    private String supplyCompanyName;

    /**
     * 
     */
    private String operateStatus;

    /**
     * 
     */
    private String tapPosition;

    /**
     * 
     */
    private Boolean hasSchoolNearby;

    /**
     * 
     */
    private String managerName;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}