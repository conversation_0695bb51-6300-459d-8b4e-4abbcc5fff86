<view class="mainApp">
  <ant-tabs items="{{items}}" onChange="handleTabsChange">
    <view
       slot="title"
       slot-scope="item">
       
       <view a:if="{{item.value.badge}}">
         <ant-badge type="number" position="top-right"  bgColor="#1677FF" text="{{item.value.text}}" >{{item.value.title}}</ant-badge>
       </view>
       <view a:else>{{item.value.title}}</view>
     </view>
   </ant-tabs>
  <view class="content">
    <view a:if="{{ currentPage == '许可证' }}" style="overflow:hidden;">
      <xkz  yhytData="{{licMapData.yhytLicenseVO}}" isView="{{isView}}" filePath="{{licMapData.photoPathList && licMapData.photoPathList.length > 0 ? licMapData.photoPathList[0].filthPath: '/image/picture-icon.svg'}}"></xkz>
    </view>
    <view a:if="{{ currentPage == '违法记录'  }}">
     <wfjl yhytId="{{licMapData.yhytLicenseVO.id}}" />
    </view>
    <view a:if="{{ currentPage == '举报投诉' }}">
      <jbts licNo="{{licMapData.licNo}}"></jbts>
    </view>
    <view a:if="{{ currentPage == '品规识别' }}">
     <pgsb isView="{{isView}}" licData ="{{licData}}" />
    </view>
  </view>
</view>