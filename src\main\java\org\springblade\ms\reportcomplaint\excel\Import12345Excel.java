package org.springblade.ms.reportcomplaint.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-01-16 11:32
 */
@Data
public class Import12345Excel implements Serializable {

    @ExcelProperty("工单编号")
    private String workOrderNumber;

    @ExcelProperty("诉求标题")
    private String appealTitle;

    @ExcelProperty("涉事主体")
    private String involvedSubject;

    @ExcelProperty("事发地点")
    private String incidentLocation;

    @ExcelProperty("事项分类一级")
    private String categoryLevelOne;

    @ExcelProperty("事项分类二级")
    private String categoryLevelTwo;

    @ExcelProperty("事项分类三级")
    private String categoryLevelThree;

    @ExcelProperty("所属部门")
    private String department;

    @ExcelProperty("市民诉求")
    private String citizenAppeal;

    @ExcelProperty("受理时间")
    private String acceptanceTime;

    @ExcelProperty("工单状态")
    private String workOrderStatus;

    @ExcelProperty("回复内容")
    private String replyContent;

    @ExcelProperty("提交办结时间")
    private String handleNoteDate;

    @ExcelProperty("工单类型")
    private String rollType;

}
