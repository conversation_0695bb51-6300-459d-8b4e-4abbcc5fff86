<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.ms.schoolInfo.mapper.MsSchoolInfoMapper">

    <resultMap id="BaseResultMap" type="org.springblade.ms.schoolInfo.pojo.entity.MsSchoolInfo">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="schoolCode" column="school_code" jdbcType="VARCHAR"/>
            <result property="campusCode" column="campus_code" jdbcType="VARCHAR"/>
            <result property="schoolName" column="school_name" jdbcType="VARCHAR"/>
            <result property="isInclusiveKindergarten" column="is_inclusive_kindergarten" jdbcType="VARCHAR"/>
            <result property="socialCreditCode" column="social_credit_code" jdbcType="VARCHAR"/>
            <result property="industryCategoryCode" column="industry_category_code" jdbcType="VARCHAR"/>
            <result property="industryCategoryName" column="industry_category_name" jdbcType="VARCHAR"/>
            <result property="mainSchoolTypeCode" column="main_school_type_code" jdbcType="VARCHAR"/>
            <result property="mainSchoolTypeName" column="main_school_type_name" jdbcType="VARCHAR"/>
            <result property="isIndependentSetEthnicSchool" column="is_independent_set_ethnic_school" jdbcType="VARCHAR"/>
            <result property="addressCode" column="address_code" jdbcType="VARCHAR"/>
            <result property="addressName" column="address_name" jdbcType="VARCHAR"/>
            <result property="urbanRuralCode" column="urban_rural_code" jdbcType="VARCHAR"/>
            <result property="urbanRuralCategory" column="urban_rural_category" jdbcType="VARCHAR"/>
            <result property="longitude" column="longitude" jdbcType="NUMERIC"/>
            <result property="latitude" column="latitude" jdbcType="NUMERIC"/>
            <result property="localEducationAuthorityCode" column="local_education_authority_code" jdbcType="VARCHAR"/>
            <result property="localEducationAuthorityName" column="local_education_authority_name" jdbcType="VARCHAR"/>
            <result property="superiorDepartment" column="superior_department" jdbcType="VARCHAR"/>
            <result property="superiorDepartmentCode" column="superior_department_code" jdbcType="VARCHAR"/>
            <result property="superiorDepartmentName" column="superior_department_name" jdbcType="VARCHAR"/>
            <result property="schoolOperatorCode" column="school_operator_code" jdbcType="VARCHAR"/>
            <result property="schoolOperatorName" column="school_operator_name" jdbcType="VARCHAR"/>
            <result property="operatorDetailName" column="operator_detail_name" jdbcType="VARCHAR"/>
            <result property="schoolEnglishName" column="school_english_name" jdbcType="VARCHAR"/>
            <result property="principalName" column="principal_name" jdbcType="VARCHAR"/>
            <result property="officePhone" column="office_phone" jdbcType="VARCHAR"/>
            <result property="faxNumber" column="fax_number" jdbcType="VARCHAR"/>
            <result property="postalCode" column="postal_code" jdbcType="VARCHAR"/>
            <result property="institutionEmail" column="institution_email" jdbcType="VARCHAR"/>
            <result property="formFilledBy" column="form_filled_by" jdbcType="VARCHAR"/>
            <result property="statisticsOfficerName" column="statistics_officer_name" jdbcType="VARCHAR"/>
            <result property="statisticsContactNumber" column="statistics_contact_number" jdbcType="VARCHAR"/>
            <result property="primaryDurationCode" column="primary_duration_code" jdbcType="VARCHAR"/>
            <result property="primaryDurationName" column="primary_duration_name" jdbcType="VARCHAR"/>
            <result property="primaryEntryAgeCode" column="primary_entry_age_code" jdbcType="VARCHAR"/>
            <result property="primaryEntryAgeName" column="primary_entry_age_name" jdbcType="VARCHAR"/>
            <result property="juniorHighDurationCode" column="junior_high_duration_code" jdbcType="VARCHAR"/>
            <result property="juniorHighDurationName" column="junior_high_duration_name" jdbcType="VARCHAR"/>
            <result property="juniorHighEntryAgeCode" column="junior_high_entry_age_code" jdbcType="VARCHAR"/>
            <result property="juniorHighEntryAgeName" column="junior_high_entry_age_name" jdbcType="VARCHAR"/>
            <result property="directSchoolLevel" column="direct_school_level" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,school_code,campus_code,
        school_name,is_inclusive_kindergarten,social_credit_code,
        industry_category_code,industry_category_name,main_school_type_code,
        main_school_type_name,is_independent_set_ethnic_school,address_code,
        address_name,urban_rural_code,urban_rural_category,
        longitude,latitude,local_education_authority_code,
        local_education_authority_name,superior_department,superior_department_code,
        superior_department_name,school_operator_code,school_operator_name,
        operator_detail_name,school_english_name,principal_name,
        office_phone,fax_number,postal_code,
        institution_email,form_filled_by,statistics_officer_name,
        statistics_contact_number,primary_duration_code,primary_duration_name,
        primary_entry_age_code,primary_entry_age_name,junior_high_duration_code,
        junior_high_duration_name,junior_high_entry_age_code,junior_high_entry_age_name,
        direct_school_level
    </sql>

    <!-- 查询附近的学校信息 -->
    <select id="getNearbySchools" resultMap="BaseResultMap">
        SELECT
            id,
            school_code,
            school_name,
        social_credit_code,
        industry_category_name,
            address_name,
        principal_name,office_phone,
            longitude,
            latitude,
            office_phone,
            6371000 * 2 * ASIN(
                SQRT(
                    POWER(SIN((RADIANS(latitude - #{latitude})) / 2), 2) +
                    COS(RADIANS(#{latitude})) * COS(RADIANS(latitude)) *
                    POWER(SIN((RADIANS(longitude - #{longitude})) / 2), 2)
                )
            ) AS distance
        FROM ms_school_info
        <where>
            <if test="true">
                is_deleted = 0
            </if>
            AND longitude IS NOT NULL AND latitude IS NOT NULL
            <if test="searchParam != null and searchParam != ''">
                AND school_name LIKE CONCAT('%', #{searchParam}, '%')
            </if>
            <if test="radius != null and radius > 0">
                AND 6371000 * 2 * ASIN(
                    SQRT(
                        POWER(SIN((RADIANS(latitude - #{latitude})) / 2), 2) +
                        COS(RADIANS(#{latitude})) * COS(RADIANS(latitude)) *
                        POWER(SIN((RADIANS(longitude - #{longitude})) / 2), 2)
                    )
                ) &lt;= #{radius}
            </if>
        </where>
        ORDER BY distance ASC
    </select>
</mapper>
