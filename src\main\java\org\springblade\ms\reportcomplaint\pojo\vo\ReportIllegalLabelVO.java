package org.springblade.ms.reportcomplaint.pojo.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.ms.basic.pojo.entity.ProductInfoEntity;
import org.springblade.ms.basic.pojo.entity.YhytLicenseEntity;
import org.springblade.ms.basic.pojo.vo.ProductInfoVO;
import org.springblade.ms.basic.pojo.vo.YhytLicenseVO;
import org.springblade.ms.reportcomplaint.pojo.entity.ReportIllegalLabelEntity;

import java.util.List;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-01-16 23:43
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReportIllegalLabelVO extends ReportIllegalLabelEntity {

    private ProductInfoEntity productInfo;

    private YhytLicenseEntity yhytLicense;

}
