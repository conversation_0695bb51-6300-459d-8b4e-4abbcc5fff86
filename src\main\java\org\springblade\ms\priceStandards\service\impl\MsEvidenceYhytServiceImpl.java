package org.springblade.ms.priceStandards.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.oss.MinioTemplate;
import org.springblade.core.oss.model.BladeFile;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.ms.basic.pojo.entity.UploadFileEntity;
import org.springblade.ms.basic.service.IUploadFileService;
import org.springblade.ms.dingapp.dto.EvidenceSubmitDTO;
import org.springblade.ms.dingapp.vo.DingMinioResult;
import org.springblade.ms.itemidentify.pojo.vo.ItemIdentifyVO;
import org.springblade.ms.priceStandards.excel.MsEvidenceYhytExcel;
import org.springblade.ms.priceStandards.pojo.entity.MsEvidenceYhyt;
import org.springblade.ms.priceStandards.pojo.entity.MsPriceStandards;
import org.springblade.ms.priceStandards.pojo.vo.MsEvidenceYhytExportVO;
import org.springblade.ms.priceStandards.pojo.vo.MsEvidenceYhytVO;
import org.springblade.ms.priceStandards.service.MsEvidenceYhytService;
import org.springblade.ms.priceStandards.mapper.MsEvidenceYhytMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.hutool.core.util.StrUtil;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

/**
* <AUTHOR> @description 针对表【ms_evidence_yhyt(涉案物品表)】的数据库操作Service实现
* @createDate 2025-04-10 10:09:30
*/
@Service
@RequiredArgsConstructor
@Slf4j
public class MsEvidenceYhytServiceImpl extends BaseServiceImpl<MsEvidenceYhytMapper, MsEvidenceYhyt>
    implements MsEvidenceYhytService{

    private final MinioTemplate minioTemplate;
    private final IUploadFileService uploadFileService;


    @Override
    public Boolean saveData(List<MsEvidenceYhyt> list, EvidenceSubmitDTO submitDTO) {
        if(list.isEmpty()){
            return false;
        }
        QueryWrapper<MsEvidenceYhyt> qw = new QueryWrapper<>();
        qw.eq("is_deleted",0);
        //修改时删除旧数据
        if(list.get(0).getSelectionTime() != null){
            qw.eq("selection_time",list.get(0).getSelectionTime());
            List<MsEvidenceYhyt> msEvidenceYhyts = baseMapper.selectList(qw);
            if(!msEvidenceYhyts.isEmpty()){
                this.deleteLogic(msEvidenceYhyts.stream().map(MsEvidenceYhyt::getId).toList());
            }
        }
        list.forEach(item->{
            item.setSelectionTime(LocalDateTime.now().truncatedTo(java.time.temporal.ChronoUnit.SECONDS));

            // 使用Hutool的StrUtil进行判空校验，更简洁高效
            // StrUtil.isNotBlank会检查字符串是否为null、空字符串或仅包含空白字符

            if (StrUtil.isNotBlank(submitDTO.getTitle())) {
                item.setTitle(submitDTO.getTitle());
            }

            if (StrUtil.isNotBlank(submitDTO.getEnforcementAgency())) {
                item.setEnforcementAgency(submitDTO.getEnforcementAgency());
            }

            if (StrUtil.isNotBlank(submitDTO.getCaseTime())) {
                item.setCaseTime(submitDTO.getCaseTime());
            }

            if (StrUtil.isNotBlank(submitDTO.getAddress())) {
                item.setAddress(submitDTO.getAddress());
            }

            if (StrUtil.isNotBlank(submitDTO.getDetailedAddress())) {
                item.setDetailedAddress(submitDTO.getDetailedAddress());
            }

            if (StrUtil.isNotBlank(submitDTO.getJointEnforcementAgency())) {
                item.setJointEnforcementAgency(submitDTO.getJointEnforcementAgency());
            }

            if (StrUtil.isNotBlank(submitDTO.getCaseReason())) {
                item.setCaseReason(submitDTO.getCaseReason());
            }

            if (StrUtil.isNotBlank(submitDTO.getPartyInvolved())) {
                item.setPartyInvolved(submitDTO.getPartyInvolved());
            }

            if (StrUtil.isNotBlank(submitDTO.getLicenseNo())) {
                item.setLicenseNo(submitDTO.getLicenseNo());
            }
        });
        return this.saveOrUpdateBatch(list);
    }

    @Override
    public IPage<MsEvidenceYhytVO> pageList(IPage<MsEvidenceYhytVO> page, MsEvidenceYhytVO msEvidenceYhyt) {
//        BladeUser bladexUser = AuthUtil.getUser();
//        msEvidenceYhyt.setCreateUser(bladexUser.getUserId());
        return page.setRecords(baseMapper.selectPage(page,msEvidenceYhyt));
    }

    @Override
    public List<MsEvidenceYhytExportVO> listWithBarcode(Wrapper<MsEvidenceYhyt> queryWrapper) {
        return baseMapper.selectEvidenceWithBarcode(queryWrapper);
    }

    @Override
    public List<MsEvidenceYhytExcel> convertToExcelList(List<MsEvidenceYhytExportVO> dataList) {
        List<MsEvidenceYhytExcel> excelList = new ArrayList<>();

        // 转换为Excel实体
        for (int i = 0; i < dataList.size(); i++) {
            MsEvidenceYhytExportVO item = dataList.get(i);
            MsEvidenceYhytExcel excel = new MsEvidenceYhytExcel();
            BeanUtils.copyProperties(item, excel);

            // 设置序号，从1开始
            excel.setSerialNumber(String.valueOf(i + 1));

            // 计算金额（数量 × 单价）
            if (item.getSelectedQuantity() != null && item.getCurrentUnitPrice() != null) {
                BigDecimal amount = item.getSelectedQuantity().multiply(item.getCurrentUnitPrice());
                excel.setAmount(amount);
            }

            // 条形码已经在联表查询中获取，无需再次查询

            excelList.add(excel);
        }

        return excelList;
    }

    @Override
    public ByteArrayOutputStream generateExcelFile(List<MsEvidenceYhytExcel> excelList) {
        // 计算总金额
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (MsEvidenceYhytExcel excel : excelList) {
            if (excel.getAmount() != null) {
                totalAmount = totalAmount.add(excel.getAmount());
            }
        }

        // 计算品种数量（不重复的品规数量）
        Set<String> productNameSet = new HashSet<>();
        for (MsEvidenceYhytExcel excel : excelList) {
            if (excel.getProductName() != null && !excel.getProductName().isEmpty()) {
                productNameSet.add(excel.getProductName());
            }
        }
        int productCount = productNameSet.size();

        // 计算物品数量的总和
        BigDecimal totalQuantity = BigDecimal.ZERO;
        for (MsEvidenceYhytExcel excel : excelList) {
            if (excel.getSelectedQuantity() != null) {
                totalQuantity = totalQuantity.add(excel.getSelectedQuantity());
            }
        }
        // 将总数量转为字符串（如果有小数部分，保留）
        String totalQuantityStr = totalQuantity.stripTrailingZeros().toPlainString();

        // 设置合计信息：共XX个品种，共xxx条，共计XXX元
        final String totalAmountFormatted = totalAmount.setScale(2, java.math.RoundingMode.HALF_UP).toString();
        final String summaryText = "共" + productCount + "个品种，共" + totalQuantityStr + "条，共计" + totalAmountFormatted + "元";

        // 创建自定义单元格写入处理器，用于在合并单元格中写入文本
        CellWriteHandler summaryHandler = new CellWriteHandler() {
            @Override
            public void afterCellDispose(CellWriteHandlerContext context) {
                // 只处理最后一行的第一列
                if (context.getRowIndex() == excelList.size() + 1 && context.getColumnIndex() == 0) {
                    Cell cell = context.getCell();
                    Row row = cell.getRow();

                    // 设置单元格内容
                    cell.setCellValue(summaryText);

                    // 设置单元格样式
                    CellStyle cellStyle = context.getWriteWorkbookHolder().getWorkbook().createCellStyle();
                    cellStyle.setAlignment(HorizontalAlignment.CENTER);
                    cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                    cellStyle.setBorderBottom(BorderStyle.THIN);
                    cellStyle.setBorderLeft(BorderStyle.THIN);
                    cellStyle.setBorderRight(BorderStyle.THIN);
                    cellStyle.setBorderTop(BorderStyle.THIN);

                    // 应用样式
                    cell.setCellStyle(cellStyle);
                }
            }
        };

        // 获取当前行数，即数据行数+表头行数(1)
        int currentRow = excelList.size() + 1;

        // 创建合计行
        MsEvidenceYhytExcel totalExcel = new MsEvidenceYhytExcel();
        totalExcel.setSerialNumber(null);
        totalExcel.setProductName("");
        totalExcel.setSelectedQuantity(null);
        totalExcel.setCurrentUnitPrice(null);
        totalExcel.setAmount(null);

        // 将数据行和合计行合并
        List<MsEvidenceYhytExcel> allData = new ArrayList<>(excelList);
        allData.add(totalExcel);

        // 生成Excel文件并写入到ByteArrayOutputStream
        ByteArrayOutputStream bos = new ByteArrayOutputStream();

        // 设置样式
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 12);
        headWriteFont.setBold(true);
        headWriteCellStyle.setWriteFont(headWriteFont);
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);

        HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);

        // 使用EasyExcel直接写入到ByteArrayOutputStream
        try (ExcelWriter excelWriter = EasyExcel.write(bos, MsEvidenceYhytExcel.class)
                .registerWriteHandler(horizontalCellStyleStrategy)
//                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .registerWriteHandler(summaryHandler)
                .build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet("涉案物品数据表").build();

            // 写入所有数据（包括数据行和合计行）
            excelWriter.write(allData, writeSheet);

            // 合并整行单元格，从第一列到最后一列
            try {
                // 使用POI的API直接操作Sheet对象
                excelWriter.writeContext().writeSheetHolder().getSheet().addMergedRegion(
                    // 合并所有列（0-5）
                    new CellRangeAddress(currentRow, currentRow, 0, 5)
                );
            } catch (Exception e) {
                // 如果合并失败，不影响整体导出
                e.printStackTrace();
            }
        }

        return bos;
    }

    @Override
    public R<DingMinioResult> uploadExcelToMinio(ByteArrayOutputStream bos, Map<String, Object> evidenceYhyt,Long evidenceYhytId) {
        try {
            // 生成唯一文件名
            String fileName = "evidence_yhyt_" + DateUtil.time() + "_" + UUID.randomUUID().toString().substring(0, 8) + ".xlsx";
            String objName = "evidence_yhyt";

            // 上传到MinIO
            ByteArrayInputStream bis = new ByteArrayInputStream(bos.toByteArray());
            BladeFile bladeFile = minioTemplate.putFile(objName + "/" + fileName, bis);

            // 构建返回结果
            DingMinioResult result = new DingMinioResult();
            if (bladeFile != null) {
                String path = "/minio/" + minioTemplate.getBucket().name() + "/" + bladeFile.getName();

                // 保存文件信息到数据库
                UploadFileEntity uploadFileEntity = new UploadFileEntity();
                uploadFileEntity.setFileName(fileName);
                uploadFileEntity.setFilthPath(path);
                uploadFileEntity.setFileSize(BigDecimal.valueOf(bos.size() / 1024.0)); // 转换为KB

                // 如果有yhytId，则关联到该零售户
//                if (evidenceYhyt != null && evidenceYhyt.get("yhytId") != null) {
//                    String yhytIdStr = (String) evidenceYhyt.get("yhytId");
//                    // 检查是否为空字符串或非数字
//                    if (yhytIdStr != null && !yhytIdStr.trim().isEmpty()) {
//                        try {
//                            Long yhytId = Long.parseLong(yhytIdStr);
//                            uploadFileEntity.setObjId(yhytId);
//                        } catch (NumberFormatException e) {
//                            // 数字解析失败，记录日志但不中断导出
//                            log.error("解析yhytId失败: " + yhytIdStr, e);
//                        }
//                    }
//                }
                uploadFileEntity.setObjId(evidenceYhytId);
                uploadFileEntity.setObjName(objName);

                // 保存到数据库
                uploadFileService.save(uploadFileEntity);

                // 设置返回结果
                result.setFilename(fileName);
                result.setPath(path);
                result.setId(String.valueOf(uploadFileEntity.getId()));

                return R.data(result);
            } else {
                return R.fail("导出失败");
            }
        } catch (Exception e) {
            return R.fail("上传文件失败: " + e.getMessage());
        }
    }

    @Override
    public R<DingMinioResult> exportExcel(Map<String, Object> evidenceYhyt) {
        try {
            // 构建查询条件
            QueryWrapper<MsEvidenceYhyt> qw = new QueryWrapper<>();
            if (evidenceYhyt != null) {
                BladeUser user = AuthUtil.getUser();
                qw.eq("e.create_user",user.getUserId());

                // 处理 selectionTime
                if (evidenceYhyt.get("selectionTime") != null) {
                    String selectionTimeStr = (String) evidenceYhyt.get("selectionTime");
                    // 检查是否为空字符串
                    if (selectionTimeStr != null && !selectionTimeStr.trim().isEmpty()) {
                        try {
                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                            LocalDateTime selectionTime = LocalDateTime.parse(selectionTimeStr, formatter);
                            qw.eq("selection_time", selectionTime);
                        } catch (Exception e) {
                            // 日期解析失败，记录日志但不中断导出
                            log.error("解析selectionTime失败: " + selectionTimeStr, e);
                        }
                    }
                }
            }

            // 1. 查询数据（包含条形码）
            List<MsEvidenceYhytExportVO> dataList = this.listWithBarcode(qw);

            // 2. 转换为Excel实体
            List<MsEvidenceYhytExcel> excelList = this.convertToExcelList(dataList);

            // 3. 生成Excel文件
            ByteArrayOutputStream bos = this.generateExcelFile(excelList);

            // 4. 上传到MinIO并返回结果
            return this.uploadExcelToMinio(bos, evidenceYhyt,dataList.get(0).getId());

        } catch (Exception e) {
            return R.fail("导出Excel失败: " + e.getMessage());
        }
    }

    @Override
    public R<Boolean> deleteByIds(List<Long> ids) {
        try {
            if (ids == null || ids.isEmpty()) {
                return R.fail("删除失败: 未提供ID列表");
            }

            // 执行批量删除
            boolean success = this.removeByIds(ids);

            if (success) {
                return R.success("删除成功");
            } else {
                return R.fail("删除失败");
            }
        } catch (Exception e) {
            log.error("删除涉案物品失败", e);
            return R.fail("删除失败: " + e.getMessage());
        }
    }

    @Override
    public R<Boolean>   deleteByYhytIdAndTime( String selectionTimeStr,Long userId) {
        try {
            // 构建查询条件
            QueryWrapper<MsEvidenceYhyt> queryWrapper = new QueryWrapper<>();
            queryWrapper
                    .eq("create_user",userId);

            // 如果提供了选择时间，则添加到查询条件中
            if (selectionTimeStr != null && !selectionTimeStr.trim().isEmpty()) {
                try {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    LocalDateTime selectionTime = LocalDateTime.parse(selectionTimeStr, formatter);
                    queryWrapper.eq("selection_time", selectionTime);
                } catch (Exception e) {
                    log.error("解析selectionTime失败: " + selectionTimeStr, e);
                    return R.fail("删除失败: 选择时间格式不正确");
                }
            }
            List<MsEvidenceYhyt> list = this.list(queryWrapper);

            // 执行删除
            boolean success = this.deleteLogic(list.stream().map(MsEvidenceYhyt::getId).toList());

            if (success) {
                return R.success("删除成功");
            } else {
                return R.fail("删除失败");
            }
        } catch (Exception e) {
            log.error("删除涉案物品失败", e);
            return R.fail("删除失败: " + e.getMessage());
        }
    }
}




