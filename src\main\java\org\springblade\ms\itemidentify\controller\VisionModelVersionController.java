/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.ms.itemidentify.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.ms.itemidentify.pojo.entity.VisionModelVersionEntity;
import org.springblade.ms.itemidentify.pojo.vo.VisionModelVersionVO;
import org.springblade.ms.itemidentify.excel.VisionModelVersionExcel;
import org.springblade.ms.itemidentify.wrapper.VisionModelVersionWrapper;
import org.springblade.ms.itemidentify.service.IVisionModelVersionService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 品规模型版本 控制器
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@RestController
@AllArgsConstructor
@RequestMapping("ms-visionmodelversion/visionModelVersion")
@Tag(name = "品规模型版本", description = "品规模型版本接口")
public class VisionModelVersionController extends BladeController {

	private final IVisionModelVersionService visionModelVersionService;

	/**
	 * 品规模型版本 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入visionModelVersion")
	public R<VisionModelVersionVO> detail(VisionModelVersionEntity visionModelVersion) {
		VisionModelVersionEntity detail = visionModelVersionService.getOne(Condition.getQueryWrapper(visionModelVersion));
		return R.data(VisionModelVersionWrapper.build().entityVO(detail));
	}
	/**
	 * 品规模型版本 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入visionModelVersion")
	public R<IPage<VisionModelVersionVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> visionModelVersion, Query query) {
		IPage<VisionModelVersionEntity> pages = visionModelVersionService.page(Condition.getPage(query), Condition.getQueryWrapper(visionModelVersion, VisionModelVersionEntity.class));
		return R.data(VisionModelVersionWrapper.build().pageVO(pages));
	}

	/**
	 * 品规模型版本 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入visionModelVersion")
	public R<IPage<VisionModelVersionVO>> page(VisionModelVersionVO visionModelVersion, Query query) {
		IPage<VisionModelVersionVO> pages = visionModelVersionService.selectVisionModelVersionPage(Condition.getPage(query), visionModelVersion);
		return R.data(pages);
	}

	/**
	 * 品规模型版本 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入visionModelVersion")
	public R save(@Valid @RequestBody VisionModelVersionEntity visionModelVersion) {
		return R.status(visionModelVersionService.save(visionModelVersion));
	}

	/**
	 * 品规模型版本 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入visionModelVersion")
	public R update(@Valid @RequestBody VisionModelVersionEntity visionModelVersion) {
		return R.status(visionModelVersionService.updateById(visionModelVersion));
	}

	/**
	 * 品规模型版本 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入visionModelVersion")
	public R submit(@Valid @RequestBody VisionModelVersionEntity visionModelVersion) {
		return R.status(visionModelVersionService.saveOrUpdate(visionModelVersion));
	}

	/**
	 * 品规模型版本 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(visionModelVersionService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@GetMapping("/export-visionModelVersion")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入visionModelVersion")
	public void exportVisionModelVersion(@Parameter(hidden = true) @RequestParam Map<String, Object> visionModelVersion, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<VisionModelVersionEntity> queryWrapper = Condition.getQueryWrapper(visionModelVersion, VisionModelVersionEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(VisionModelVersion::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(VisionModelVersionEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<VisionModelVersionExcel> list = visionModelVersionService.exportVisionModelVersion(queryWrapper);
		ExcelUtil.export(response, "品规模型版本数据" + DateUtil.time(), "品规模型版本数据表", list, VisionModelVersionExcel.class);
	}

}
