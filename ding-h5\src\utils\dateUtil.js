/**
 * 根据日期获取对应的星期几
 * @param {string} dateString 日期字符串，格式为 YYYY-MM-DD
 * @returns {string} 星期几
 */
export function getWeekByDate(dateString) {
  const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
  const date = new Date(dateString)
  return weekDays[date.getDay()]
}

/**
 * 计算剩余天数（使用北京时间）
 * @param {string} targetDateString 目标日期字符串，格式为 YYYY-MM-DD
 * @returns {number} 剩余天数，负数表示已过期
 */
export function calculateRemainingDays(targetDateString) {
  if (!targetDateString) return 0

  // 获取北京时间（UTC+8）
  const now = new Date()
  const beijingTime = new Date(now.getTime() + (8 * 60 * 60 * 1000))

  // 设置为当天的开始时间（00:00:00）
  const today = new Date(beijingTime.getFullYear(), beijingTime.getMonth(), beijingTime.getDate())

  // 解析目标日期
  const targetDate = new Date(targetDateString)

  // 计算时间差（毫秒）
  const timeDiff = targetDate.getTime() - today.getTime()

  // 转换为天数
  const daysDiff = Math.ceil(timeDiff / (1000 * 60 * 60 * 24))

  return daysDiff
}

/**
 * 格式化剩余天数显示
 * @param {number} days 剩余天数
 * @returns {string} 格式化后的显示文本
 */
export function formatRemainingDays(days) {
  if (days > 0) {
    return `剩余${days}天`
  } else if (days === 0) {
    return '今日到期'
  } else {
    return `已过期${Math.abs(days)}天`
  }
}