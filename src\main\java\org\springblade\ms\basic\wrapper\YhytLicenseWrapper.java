/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.ms.basic.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.ms.basic.enums.CstScope;
import org.springblade.ms.basic.pojo.entity.YhytLicenseEntity;
import org.springblade.ms.basic.pojo.vo.YhytLicenseVO;
import java.util.Objects;

/**
 * 零售户信息 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
public class YhytLicenseWrapper extends BaseEntityWrapper<YhytLicenseEntity, YhytLicenseVO>  {

	public static YhytLicenseWrapper build() {
		return new YhytLicenseWrapper();
 	}

	@Override
	public YhytLicenseVO entityVO(YhytLicenseEntity yhytLicense) {
		YhytLicenseVO yhytLicenseVO = Objects.requireNonNull(BeanUtil.copyProperties(yhytLicense, YhytLicenseVO.class));
		if (yhytLicense != null && yhytLicense.getManagerScope() != null) {
			try {
				String description = CstScope.getDescriptionsFromCodes(yhytLicense.getManagerScope());
				yhytLicenseVO.setManagerScopeName(description);
			} catch (IllegalArgumentException e) {
				System.out.println(e.getMessage());
			}
		}
		//User createUser = UserCache.getUser(yhytLicense.getCreateUser());
		//User updateUser = UserCache.getUser(yhytLicense.getUpdateUser());
		//yhytLicenseVO.setCreateUserName(createUser.getName());
		//yhytLicenseVO.setUpdateUserName(updateUser.getName());

		return yhytLicenseVO;
	}


}
