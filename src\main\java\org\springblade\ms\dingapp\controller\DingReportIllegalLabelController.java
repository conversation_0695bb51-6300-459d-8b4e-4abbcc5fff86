package org.springblade.ms.dingapp.controller;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.ms.dingapp.dto.ReportIllegalLabelSubmitDTO;
import org.springblade.ms.platform12345.config.Platform12345Properties;
import org.springblade.ms.platform12345.dto.AcceptOrderRequest;
import org.springblade.ms.platform12345.dto.CompleteOrderRequest;
import org.springblade.ms.platform12345.enums.ReplyTypeEnum;
import org.springblade.ms.platform12345.service.Platform12345Service;
import org.springblade.ms.platform12345.utils.Platform12345Client;
import org.springblade.ms.reportcomplaint.pojo.entity.ReportComplaintEntity;
import org.springblade.ms.reportcomplaint.service.IReportComplaintService;
import org.springblade.ms.reportcomplaint.service.IReportIllegalLabelService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@RestController
@AllArgsConstructor
@Slf4j
@RequestMapping("/dingapp/reportIllegalLabel")
public class DingReportIllegalLabelController {

    private final IReportIllegalLabelService reportIllegalLabelService;
    private final IReportComplaintService reportComplaintService;
    private final Platform12345Service platform12345Service;
    private final Platform12345Client platform12345Client;
    private final Platform12345Properties properties;



    /**
     * 工单信息 新增或修改
     */
    @PostMapping("/submit")
    @Transactional
    public R submit(@Valid @RequestBody ReportIllegalLabelSubmitDTO submitDTO) {
        // 先保存或更新标签信息
        boolean result = reportIllegalLabelService.saveOrUpdate(submitDTO.getObjId(), submitDTO.getReportIllegalLabelList(),null);
        BladeUser user = AuthUtil.getUser();
        // 更新工单处理结果和状态
        if (result) {
            ReportComplaintEntity reportComplaint = reportComplaintService.getById(submitDTO.getObjId());
            if (reportComplaint != null) {
                if(StrUtil.isNotBlank(reportComplaint.getDataSource()) && reportComplaint.getDataSource().equals("12345")){
                    if(!reportComplaint.getRollStatus().equals("职能局审核") || !reportComplaint.getComplaintStatus().equals("待处理")){
                        return R.fail("该工单无法处理，请联系管理员");
                    }
                    if(StrUtil.isBlank(submitDTO.getHandleResult())){
                        return R.fail("回复内容不能为空");
                    }

                  //办结工单
                    // 生成签名信息
                    Platform12345Client.SignatureInfo signatureInfo = platform12345Client.generateSignature();

                    // 构建请求
                    CompleteOrderRequest request = new CompleteOrderRequest();
                    request.setSignature(signatureInfo.getSignature());
                    request.setTimestamp(signatureInfo.getTimestamp());
                    request.setAppid(signatureInfo.getAppid());

                    // 设置必填参数
                    request.setProWoId(reportComplaint.getRollNumber());
                    request.setProWoCode(String.valueOf(reportComplaint.getId()));
                    request.setReplyDetails(submitDTO.getHandleResult());
                    request.setContent(submitDTO.getHandleResult());

                    request.setContactPhone(submitDTO.getContactPhone());
                    request.setSortManage(ReplyTypeEnum.getCodeByValue(submitDTO.getSortManage()));
                    request.setIsAccept("1");
                    LocalDateTime now = LocalDateTime.now();
                    // 定义日期时间格式
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    // 格式化当前时间
                    String formattedDate = now.format(formatter);
                    request.setDealTime(formattedDate);
                    request.setDealUserNo(properties.getDealUserNo());

                    // 调用办结
                    String s = platform12345Service.completeOrder(request);
                    JSONObject entries = JSONUtil.parseObj(s);
                    if(entries.getInt("resultCode") != 0){
                        log.error("办结接口返回错误:{}",s);
                        return R.fail("办结接口处理异常");
                    }else{
                        log.info("办结工单处理完成");
                    }
                }
                reportComplaint.setHandleResult(submitDTO.getHandleResult());
                reportComplaint.setRollHandleResult(submitDTO.getSortManage());
                reportComplaint.setContactPhone(submitDTO.getContactPhone());
                if(ObjUtil.isNotNull(user)){
                    reportComplaint.setAuditUser(user.getUserId());
                }
                // 更新工单状态
                reportComplaint.setComplaintStatus("已处理");
                // 更新工单信息
                reportComplaintService.updateById(reportComplaint);
            }
        }

        return R.status(result);
    }
}
