/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.ms.illegalrecords.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.AllArgsConstructor;
import org.springblade.ms.illegalrecords.pojo.entity.IllegalRecordsEntity;
import org.springblade.ms.illegalrecords.pojo.vo.IllegalRecordsVO;
import org.springblade.ms.illegalrecords.excel.IllegalRecordsExcel;
import org.springblade.ms.illegalrecords.mapper.IllegalRecordsMapper;
import org.springblade.ms.illegalrecords.service.IIllegalRecordsService;
import org.springblade.ms.reportcomplaint.pojo.entity.ReportIllegalLabelEntity;
import org.springblade.ms.reportcomplaint.pojo.vo.ReportIllegalLabelVO;
import org.springblade.ms.reportcomplaint.service.IReportIllegalLabelService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;

import java.util.ArrayList;
import java.util.List;

/**
 * 案件信息 服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Service
@AllArgsConstructor
public class IllegalRecordsServiceImpl extends BaseServiceImpl<IllegalRecordsMapper, IllegalRecordsEntity> implements IIllegalRecordsService {

	private final IReportIllegalLabelService reportIllegalLabelService;

	@Override
	public IPage<IllegalRecordsVO> selectIllegalRecordsPage(IPage<IllegalRecordsVO> page, IllegalRecordsVO illegalRecords) {
		List<IllegalRecordsVO> illegalRecordsVOS = selectPage(page, illegalRecords);
		return page.setRecords(illegalRecordsVOS);
	}

	@Override
	public List<IllegalRecordsVO> selectPage(IPage<IllegalRecordsVO> page, IllegalRecordsVO illegalRecords) {
		List<IllegalRecordsVO> illegalRecordsVOS = baseMapper.selectIllegalRecordsPage(page, illegalRecords);
		// 获取标签
		for (IllegalRecordsVO illegalRecordsVO : illegalRecordsVOS) {
			List<ReportIllegalLabelVO> reportIllegalLabelVOS = reportIllegalLabelService.selectListByObjId(illegalRecordsVO.getId());
			illegalRecordsVO.setReportIllegalLabelList(reportIllegalLabelVOS);
		}

		return illegalRecordsVOS;
	}
	public List<IllegalRecordsEntity> getListByCustCode(IllegalRecordsVO illegalRecords) {
			ReportIllegalLabelVO vo=new ReportIllegalLabelVO();
			QueryWrapper<ReportIllegalLabelEntity> queryWrapper = new QueryWrapper<>();
		   	queryWrapper.eq("label_id",illegalRecords.getCustCode());
			queryWrapper.eq("label_type","案件");
			List<IllegalRecordsEntity> list=new ArrayList<>();
			List<ReportIllegalLabelEntity> reportIllegalLabelVOS = reportIllegalLabelService.list(queryWrapper);
			for (ReportIllegalLabelEntity reportIllegalLabelVO : reportIllegalLabelVOS) {
				IllegalRecordsEntity illegalRecordsEntity = this.getById(reportIllegalLabelVO.getObjId());
				list.add(illegalRecordsEntity);
			}
		return list;
	}

	@Override
	public List<IllegalRecordsExcel> exportIllegalRecords(Wrapper<IllegalRecordsEntity> queryWrapper) {
		List<IllegalRecordsExcel> illegalRecordsList = baseMapper.exportIllegalRecords(queryWrapper);
		//illegalRecordsList.forEach(illegalRecords -> {
		//	illegalRecords.setTypeName(DictCache.getValue(DictEnum.YES_NO, IllegalRecords.getType()));
		//});
		return illegalRecordsList;
	}

}
