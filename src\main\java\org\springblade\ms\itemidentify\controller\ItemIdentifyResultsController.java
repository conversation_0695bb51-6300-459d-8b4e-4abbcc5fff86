/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.ms.itemidentify.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.ms.itemidentify.pojo.entity.ItemIdentifyResultsEntity;
import org.springblade.ms.itemidentify.pojo.vo.ItemIdentifyResultsVO;
import org.springblade.ms.itemidentify.excel.ItemIdentifyResultsExcel;
import org.springblade.ms.itemidentify.wrapper.ItemIdentifyResultsWrapper;
import org.springblade.ms.itemidentify.service.IItemIdentifyResultsService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 品规识别结果 控制器
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@RestController
@AllArgsConstructor
@RequestMapping("ms-itemidentifyresults/itemIdentifyResults")
@Tag(name = "品规识别结果", description = "品规识别结果接口")
public class ItemIdentifyResultsController extends BladeController {

	private final IItemIdentifyResultsService itemIdentifyResultsService;

	/**
	 * 品规识别结果 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入itemIdentifyResults")
	public R<ItemIdentifyResultsVO> detail(ItemIdentifyResultsEntity itemIdentifyResults) {
		ItemIdentifyResultsEntity detail = itemIdentifyResultsService.getOne(Condition.getQueryWrapper(itemIdentifyResults));
		return R.data(ItemIdentifyResultsWrapper.build().entityVO(detail));
	}
	/**
	 * 品规识别结果 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入itemIdentifyResults")
	public R<IPage<ItemIdentifyResultsVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> itemIdentifyResults, Query query) {
		IPage<ItemIdentifyResultsEntity> pages = itemIdentifyResultsService.page(Condition.getPage(query), Condition.getQueryWrapper(itemIdentifyResults, ItemIdentifyResultsEntity.class));
		return R.data(ItemIdentifyResultsWrapper.build().pageVO(pages));
	}

	/**
	 * 品规识别结果 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入itemIdentifyResults")
	public R<IPage<ItemIdentifyResultsVO>> page(ItemIdentifyResultsVO itemIdentifyResults, Query query) {
		IPage<ItemIdentifyResultsVO> pages = itemIdentifyResultsService.selectItemIdentifyResultsPage(Condition.getPage(query), itemIdentifyResults);
		return R.data(pages);
	}

	/**
	 * 品规识别结果 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入itemIdentifyResults")
	public R save(@Valid @RequestBody ItemIdentifyResultsEntity itemIdentifyResults) {
		return R.status(itemIdentifyResultsService.save(itemIdentifyResults));
	}

	/**
	 * 品规识别结果 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入itemIdentifyResults")
	public R update(@Valid @RequestBody ItemIdentifyResultsEntity itemIdentifyResults) {
		return R.status(itemIdentifyResultsService.updateById(itemIdentifyResults));
	}

	/**
	 * 品规识别结果 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入itemIdentifyResults")
	public R submit(@Valid @RequestBody ItemIdentifyResultsEntity itemIdentifyResults) {
		return R.status(itemIdentifyResultsService.saveOrUpdate(itemIdentifyResults));
	}

	/**
	 * 品规识别结果 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(itemIdentifyResultsService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@GetMapping("/export-itemIdentifyResults")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入itemIdentifyResults")
	public void exportItemIdentifyResults(@Parameter(hidden = true) @RequestParam Map<String, Object> itemIdentifyResults, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<ItemIdentifyResultsEntity> queryWrapper = Condition.getQueryWrapper(itemIdentifyResults, ItemIdentifyResultsEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(ItemIdentifyResults::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(ItemIdentifyResultsEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<ItemIdentifyResultsExcel> list = itemIdentifyResultsService.exportItemIdentifyResults(queryWrapper);
		ExcelUtil.export(response, "品规识别结果数据" + DateUtil.time(), "品规识别结果数据表", list, ItemIdentifyResultsExcel.class);
	}

}
