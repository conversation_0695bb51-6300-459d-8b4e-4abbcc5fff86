import buildURL from '../helpers/buildURL'
import buildFullPath from '../core/buildFullPath'
import settle from '../core/settle'
import { isUndefined } from "../utils"

/**
 * 返回可选值存在的配置
 * @param {Array} keys - 可选值数组
 * @param {Object} config2 - 配置
 * @return {{}} - 存在的配置项
 */
const mergeKeys = (keys, config2) => {
  let config = {}
  keys.forEach(prop => {
    if (!isUndefined(config2[prop])) {
      config[prop] = config2[prop]
    }
  })
  return config
}
export default (config) => {
  return new Promise((resolve, reject) => {
    let fullPath = buildURL(buildFullPath(config.baseURL, config.url), config.params)
    const _config = {
      url: fullPath,
      headers: config.headers,
      complete: (response) => {
        config.fullPath = fullPath
        response.config = config
        try {
          // 对可能字符串不是json 的情况容错
          if (typeof response.data === 'string') {
            response.data = JSON.parse(response.data)
          }
          // eslint-disable-next-line no-empty
        } catch (e) {
        }
        settle(resolve, reject, response)
      }
    }
    let requestTask
    if (config.method === 'UPLOAD') {
      // delete _config.headers['content-type']
      // delete _config.headers['Content-Type']
      _config.headers['Content-Type'] = "multipart/form-data";
      let otherConfig = {
        filePath: config.filePath,
        name: config.name
      }
      const optionalKeys = [
        'formData'
      ]
      requestTask = dd.uploadFile({..._config, ...otherConfig, ...mergeKeys(optionalKeys, config)})
    } else if (config.method === 'DOWNLOAD') {
      requestTask = dd.downloadFile(_config)
    } else {
      const optionalKeys = [
        'data',
        'method',
        'timeout',
        'dataType',
        'responseType',
      ]
      requestTask = dd.httpRequest({
        ..._config,
        ...mergeKeys(optionalKeys, config), 
        success: resolve,
        fail: reject
        })
    }
    if (config.getTask) {
      config.getTask(requestTask, config)
    }
  })
}
