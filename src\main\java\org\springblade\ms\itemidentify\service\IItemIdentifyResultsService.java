/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.ms.itemidentify.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import org.springblade.ms.itemidentify.pojo.dto.ItemIdentifyResultsDTO;
import org.springblade.ms.itemidentify.pojo.entity.ItemIdentifyResultsEntity;
import org.springblade.ms.itemidentify.pojo.vo.ItemIdentifyResultsVO;
import org.springblade.ms.itemidentify.pojo.vo.ItemIdentifyResultsWithRetailerVO;
import org.springblade.ms.itemidentify.excel.ItemIdentifyResultsExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import java.util.List;

/**
 * 品规识别结果 服务类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
public interface IItemIdentifyResultsService extends BaseService<ItemIdentifyResultsEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page 分页参数
	 * @param itemIdentifyResults 查询参数
	 * @return IPage<ItemIdentifyResultsVO>
	 */
	IPage<ItemIdentifyResultsVO> selectItemIdentifyResultsPage(IPage<ItemIdentifyResultsVO> page, ItemIdentifyResultsVO itemIdentifyResults);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper 查询条件
	 * @return List<ItemIdentifyResultsExcel>
	 */
	List<ItemIdentifyResultsExcel> exportItemIdentifyResults(Wrapper<ItemIdentifyResultsEntity> queryWrapper);

	List<ItemIdentifyResultsVO> listAll(ItemIdentifyResultsDTO dto);

	IPage<ItemIdentifyResultsVO> countListGroupByDate(IPage<ItemIdentifyResultsVO> page, ItemIdentifyResultsDTO dto);

	/**
	 * 获取当前用户的所有品规识别记录，包含零售户信息
	 *
	 * @param page 分页参数
	 * @param dto 查询参数
	 * @return IPage<ItemIdentifyResultsWithRetailerVO>
	 */
	IPage<ItemIdentifyResultsWithRetailerVO> listAllWithRetailerInfo(IPage<ItemIdentifyResultsWithRetailerVO> page, ItemIdentifyResultsDTO dto);
}
