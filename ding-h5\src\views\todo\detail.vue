<template>
  <div class="todo-detail">
    <!-- 顶部标题栏 -->
    <!-- <div class="header">
      <van-nav-bar
        title="待办详情"
        left-arrow
        @click-left="router.back()"
      />
    </div> -->

    <!-- 内容区域 -->
    <div class="content">
      <!-- 基本信息卡片 -->
      <div class="info-card">
        <div class="title-row">
          <span class="title">{{ todoData.eventTitle }}</span>
          <van-tag :type="getStatusType(todoData.status)">{{ todoData.complaintStatus }}</van-tag>
        </div>

        <div class="info-row">
          <span class="label">涉事主体:</span>
          <span class="value">{{ todoData.eventTarget }}</span>
        </div>

        <div class="info-row">
          <span class="label">举报投诉日期：</span>
          <span class="value">{{ todoData.rollDate }}</span>
        </div>

        <div class="info-row">
          <span class="label">工单类型：</span>
          <span class="value">{{ todoData.rollType }}</span>
        </div>

        <div class="info-row">
          <span class="label">问题分类：</span>
          <span class="value">{{ todoData.eventType }}</span>
        </div>

        <div class="info-row">
          <span class="label">处理中队：</span>
          <span class="value">{{ todoData.deptName }}</span>
        </div>
      </div>

      <!-- 详细描述卡片 -->
      <div class="desc-card">
        <div class="card-title">详细描述</div>
        <div class="desc-content">{{ todoData.eventContent }}</div>
      </div>

      <div style="border-radius: 8px;overflow: hidden;">
        <!-- 零售户选择 -->
        <LicenseSearchSelect v-model="licenseIdList" @select="handleLicenseSelect" :selected-items="licenseItems" label="零售户" placeholder="请选择"/>

        <!-- 品规选择已屏蔽 -->
        <!-- <ItemSearchSelect v-model="itemIdList" :selected-items="itemItems" @select="handleItemSelect" label="品规" placeholder="请选择"  /> -->

        <!-- 处理结果输入 - 使用与零售户选择相同的样式，标签和输入框在同一行 -->
        <!-- 只有当dataSource为"12345"时才显示以下字段 -->
        <template v-if="todoData.dataSource === '12345'">
          <div class="search-select-wrapper result-wrapper">
            <div class="result-row">
              <div class="search-select-label">处理结果</div>
              <van-field
                v-model="handleResult"
                type="textarea"
                placeholder="请输入处理结果"
                rows="3"
                class="result-field"
              />
            </div>
          </div>

          <!-- 问题处理分类选择 -->
          <div class="search-select-wrapper result-wrapper sort-manage-wrapper">
            <div class="result-row">
              <div class="search-select-label">处理完成情形</div>
              <div class="custom-select-field" @click="showSortManagePicker = true">
                <div class="select-content" :class="{ 'placeholder': !sortManage }">
                  {{ sortManage || '请选择处理完成情形' }}
                </div>
                <van-icon name="arrow" class="select-arrow" />
              </div>
            </div>
          </div>

          <!-- 联系电话输入 -->
          <div class="search-select-wrapper result-wrapper contact-phone-wrapper">
            <div class="result-row">
              <div class="search-select-label">联系电话</div>
              <van-field
                v-model="contactPhone"
                type="tel"
                placeholder="请输入联系电话"
                class="contact-phone-field"
              />
            </div>
          </div>
        </template>
      </div>

    </div>

    <!-- 底部操作按钮 - 只有未处理状态才显示提交按钮 -->
    <div class="footer" v-if="todoData.complaintStatus !== '已处理'">
      <van-button
        type="primary"
        block
        @click="handleProcess"
        :loading="isSubmitting"
        :disabled="isSubmitting"
      >
        {{ isSubmitting ? '提交中...' : '提交' }}
      </van-button>
    </div>

    <!-- 问题处理分类选择器 -->
    <van-popup v-model:show="showSortManagePicker" position="bottom">
      <van-picker
        :columns="sortManageOptions"
        @confirm="onSortManageConfirm"
        @cancel="showSortManagePicker = false"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showSuccessToast, showFailToast } from 'vant'
import { http } from '@/utils/http'
// 品规选择已屏蔽，但保留导入以便将来可能恢复
// import ItemSearchSelect from '@/components/ItemSearchSelect.vue'
import LicenseSearchSelect from '@/components/LicenseSearchSelect.vue'

// 防抖函数
const debounce = (func, delay) => {
  let timeoutId
  return (...args) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func.apply(this, args), delay)
  }
}


const route = useRoute()
const router = useRouter()

// 待办数据
const todoData = ref({
  id: '',
  title: '',
  description: '',
  complaintStatus:'',
  createTime: '',
  deadline: '',
  handler: '',
  attachments: []
})

// 获取状态类型
const getStatusType = () => {
  // 修改判断条件：需要选择零售户、填写处理结果和选择问题处理分类
  return todoData.value.complaintStatus ==='已处理'  ? 'success' : 'warning'
}

// 获取状态文本
const getStatusText = () => {
  return todoData.value.complaintStatus || '待处理'
}





// 提交状态
const isSubmitting = ref(false)

// 处理待办的实际逻辑
const handleProcessLogic = async () => {
  if (isSubmitting.value) {
    return // 如果正在提交，直接返回
  }

  isSubmitting.value = true

  try {
    // 验证必填项
    if (licenseIdList.value.length === 0) {
      showFailToast('请选择零售户')
      return
    }

    // 只有当dataSource为"12345"时才验证以下字段
    if (todoData.value.dataSource === '12345') {
      if (handleResult.value.trim() === '') {
        showFailToast('请输入处理结果')
        return
      }

      if (sortManage.value.trim() === '') {
        showFailToast('请选择问题处理分类')
        return
      }

      if (contactPhone.value.trim() === '') {
        showFailToast('请输入联系电话')
        return
      }
    }

    // 构建提交数据
    let data = {
      objId: todoData.value.id,
      reportIllegalLabelList: [] // 标签列表
    }

    // 只有当dataSource为"12345"时才添加以下字段
    if (todoData.value.dataSource === '12345') {
      data.handleResult = handleResult.value // 将处理结果添加到提交数据中
      data.sortManage = sortManage.value // 添加问题处理分类
      data.contactPhone = contactPhone.value // 添加联系电话
    }

    // 添加零售户标签
    if(licenseIdList.value.length > 0) {
      data.reportIllegalLabelList = licenseIdList.value.map(id => ({
        labelType: '零售户',
        labelId: id,
        objId: todoData.value.id,
        objType: '工单'
      }))
    }

    // 一次性提交所有数据（标签和处理结果）
    await http.post('/api/dingapp/reportIllegalLabel/submit', data)

    showSuccessToast('提交成功')
    router.back()
  } catch (error) {
    showFailToast('提交失败')
    console.error('提交失败:', error)
  } finally {
    isSubmitting.value = false
  }
}

// 使用防抖的处理函数
const handleProcess = debounce(handleProcessLogic, 300)
const licenseIdList = ref([])
const itemIdList = ref([]) // 保留但不再使用
const licenseItems = ref([])
const itemItems = ref([]) // 保留但不再使用
const handleResult = ref('') // 新增处理结果字段
const contactPhone = ref('') // 新增联系电话字段

// 问题处理分类相关
const sortManage = ref('')
const showSortManagePicker = ref(false)
const sortManageOptions = [
  { text: '所提问题已经解决或基本解决', value: '所提问题已经解决或基本解决' },
  { text: '问题正在解决或列入规划逐步解决', value: '问题正在解决或列入规划逐步解决' },
  { text: '因目前条件限制或政策不允许等因素暂时不能解决的', value: '因目前条件限制或政策不允许等因素暂时不能解决的' }
]

// 处理问题处理分类选择确认
const onSortManageConfirm = ({ selectedOptions }) => {
  sortManage.value = selectedOptions[0]?.text || ''
  showSortManagePicker.value = false
}
// 获取待办详情
const getTodoDetail = async () => {
  try {
    const res = await http.get(`/api/dingapp/reportcomplaint/detail`,{
      params: {
        id: route.query.id
      }
    })
    if (res?.data) {
      todoData.value = res.data

      // 设置处理结果（如果有）
      if (res.data.handleResult) {
        handleResult.value = res.data.handleResult
      }

      // 设置问题处理分类（如果有）
      if (res.data.rollHandleResult) {
        sortManage.value = res.data.rollHandleResult
      }

      // 设置联系电话（如果有）
      if (res.data.contactPhone) {
        contactPhone.value = res.data.contactPhone
      }

      // 处理标签数据
      const { licenseList, itemList } = res.data.reportIllegalLabelList.reduce(
        (acc, obj) => {
          if (obj.labelType === '零售户') {
            acc.licenseList.push(obj);
          } else if(obj.labelType === '品规') {
            acc.itemList.push(obj);
          }
          return acc;
        },
        { licenseList: [], itemList: [] }
      );

      // 提取零售户信息
      licenseItems.value = licenseList.map(item => ({
        value: item.labelId,
        text: item.yhytLicense?.companyName || '未知零售户',
        selected: true
      }));
      licenseIdList.value = licenseItems.value.map(item => item.value);

      // 品规信息已不再使用，但保留代码以便将来可能恢复
      itemItems.value = itemList.map(item => ({
        value: item.labelId,
        text: item.productInfo?.productName || '未知品规',
        selected: true
      }));
      itemIdList.value = itemItems.value.map(item => item.value);
    }
  } catch (error) {
    showFailToast('获取详情失败')
    console.error('获取详情失败:', error)
  }
}


// 处理零售户选择
const handleLicenseSelect = (selectedItems) => {
  // 更新licenseItems以保持UI显示同步
  licenseItems.value = selectedItems;
  // 提取ID列表用于提交
  licenseIdList.value = selectedItems.map(item => item.value);
}

onMounted(() => {

  if (route.query.id) {
    getTodoDetail()
  }
})
</script>

<style lang="scss" scoped>
.todo-detail {
  min-height: 100vh;
  background-color: #f7f8fa;
  display: flex;
  flex-direction: column;
}

.content {
  flex: 1;
  padding: 16px;
}

.info-card,
.desc-card,
.attachment-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.title-row {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;

  .title {
    font-size: 18px;
    font-weight: bold;
    color: #323233;
    flex: 1;
    margin-right: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  :deep(.van-tag) {
    flex-shrink: 0;
    margin-top: 4px;
  }
}

.info-row {
  display: flex;
  margin-bottom: 8px;
  font-size: 14px;
  align-items: flex-start;
  line-height: 1.4;

  .label {
    color: #969799;
    width: 100px;
    flex-shrink: 0;
    padding: 4px 0;
  }

  .value {
    color: #323233;
    flex: 1;
    padding: 4px 0;
  }
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  color: #323233;
  margin-bottom: 12px;
}

.desc-content {
  font-size: 14px;
  color: #323233;
  line-height: 1.5;
}

.attachment-list {
  .attachment-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
    cursor: pointer;

    .file-icon {
      font-size: 20px;
      color: #1989fa;
      margin-right: 8px;
    }

    .file-name {
      font-size: 14px;
      color: #323233;
    }
  }
}

// 处理结果输入框样式
.search-select-wrapper {
  background-color: #fff;
  padding: 16px;

  .search-select-label {
    font-size: 14px;
    color: #646566;
    margin-bottom: 8px;
  }

  // 修复van-field的边距问题
  :deep(.van-cell) {
    padding: 0;
    margin: 0;
    width: 100%;
  }

  // 处理结果输入框样式
  .result-field {
    :deep(.van-field__control) {
      min-height: 60px;
      background-color: #f7f8fa;
      border-radius: 4px;
      padding: 8px;
    }

    // 确保文本可以换行显示
    :deep(.van-field__body) {
      white-space: normal !important;
      word-break: break-all;
    }
  }

  // 选择字段样式
  .select-field {
    :deep(.van-field__control) {
      background-color: #f7f8fa;
      border-radius: 4px;
      padding: 8px;
    }

    // 确保选择字段的文本可以换行显示
    :deep(.van-field__body) {
      white-space: normal !important;
      word-break: break-all;
    }

    :deep(.van-field__control) {
      white-space: normal !important;
      word-wrap: break-word;
      line-height: 1.4;
    }
  }
}

// 处理结果行样式 - 确保标签和输入框在同一行
.result-wrapper {
  .result-row {
    display: flex;
    align-items: flex-start;

    .search-select-label {
      width: 80px;
      flex-shrink: 0;
      margin-top: 12px; // 垂直居中对齐
      margin-bottom: 0;
      margin-right: 12px;
    }

    .result-field,
    .select-field,
    .contact-phone-field {
      flex: 1;
    }
  }
}

// 问题处理分类样式 - 移除底部边框
.sort-manage-wrapper {
  border-bottom: none !important;

  // 自定义选择字段样式
  .custom-select-field {
    display: flex;
    align-items: flex-start;
    background-color: #f7f8fa;
    border-radius: 4px;
    padding: 8px 12px;
    cursor: pointer;
    min-height: 40px;
    flex: 1;

    .select-content {
      flex: 1;
      color: #323233;
      font-size: 14px;
      line-height: 1.4;
      white-space: normal;
      word-wrap: break-word;
      word-break: break-all;
      padding-right: 8px;

      &.placeholder {
        color: #c8c9cc;
      }
    }

    .select-arrow {
      color: #c8c9cc;
      font-size: 12px;
      margin-top: 2px;
      flex-shrink: 0;
      transform: rotate(0deg);
      transition: transform 0.3s;
    }
  }
}

// 联系电话样式 - 移除底部边框
.contact-phone-wrapper {
  border-bottom: none !important;

  .contact-phone-field {
    :deep(.van-field__control) {
      background-color: #f7f8fa;
      border-radius: 4px;
      padding: 8px;
    }
  }
}

.footer {
  padding: 16px;
  background-color: #fff;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
}
</style>