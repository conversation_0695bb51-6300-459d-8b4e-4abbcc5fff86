package org.springblade.ms.basic.pojo.entity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 品规信息表（定时同步）
 * @TableName ms_product_info
 */
@TableName(value ="ms_product_info")
@Data
@Schema(description = "ms_product_info对象")
@EqualsAndHashCode(callSuper = true)
public class MsProductInfo extends TenantEntity implements Serializable {
    /**
     * 产品id
     */
    @TableField(value = "product_uuid")
    private String productUuid;

    /**
     * 产品编码
     */
    @TableField(value = "product_code")
    private String productCode;

    /**
     * 产品名称
     */
    @TableField(value = "product_name")
    private String productName;

    /**
     * 国家产品id
     */
    @TableField(value = "gj_product_uuid")
    private String gjProductUuid;

    /**
     * 国家产品名称
     */
    @TableField(value = "gj_product_name")
    private String gjProductName;

    /**
     * 产品类型
     */
    @TableField(value = "product_type")
    private String productType;

    /**
     * 短产品名称
     */
    @TableField(value = "supply_short_name")
    private String supplyShortName;

    /**
     * 品牌编码
     */
    @TableField(value = "brand_code")
    private String brandCode;

    /**
     * 品牌名称
     */
    @TableField(value = "brand_name")
    private String brandName;

    /**
     * 品牌类型
     */
    @TableField(value = "brand_type")
    private String brandType;

    /**
     * 包装长度(mm)
     */
    @TableField(value = "length")
    private Integer length;

    /**
     * 包装宽度(mm)
     */
    @TableField(value = "width")
    private Integer width;

    /**
     * 包装高度(mm)
     */
    @TableField(value = "height")
    private Integer height;

    /**
     * 条码
     */
    @TableField(value = "strip_code")
    private String stripCode;

    /**
     * 是否常销烟
     */
    @TableField(value = "is_offten")
    private Integer isOfften;

    /**
     * 是否进口烟
     */
    @TableField(value = "import_flag")
    private Integer importFlag;

    /**
     * 是否骨干品牌
     */
    @TableField(value = "is_main_product")
    private Integer isMainProduct;

    /**
     * 是否名优烟
     */
    @TableField(value = "is_famous")
    private Integer isFamous;

    /**
     * 是否省内烟
     */
    @TableField(value = "is_province")
    private Integer isProvince;

    /**
     * 是否本省在销烟
     */
    @TableField(value = "is_loc_sale")
    private Integer isLocSale;

    /**
     * 是否查扣烟启用
     */
    @TableField(value = "is_seized")
    private Integer isSeized;

    /**
     * 是否特种烟
     */
    @TableField(value = "is_special")
    private Integer isSpecial;

    /**
     * 是否雪茄烟
     */
    @TableField(value = "is_cigar")
    private Integer isCigar;

    /**
     * 是否高端品牌
     */
    @TableField(value = "is_high_tier_brand")
    private Integer isHighTierBrand;

    /**
     * 是否有效
     */
    @TableField(value = "is_active")
    private Integer isActive;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}