package org.springblade.ms.dingapp.dto;

import jakarta.validation.Valid;
import lombok.Data;
import org.springblade.ms.priceStandards.pojo.entity.MsEvidenceYhyt;

import java.util.List;

/**
 * 涉案物品提交DTO
 *
 * <AUTHOR>
 */
@Data
public class EvidenceSubmitDTO {
    /**
     * 标题
     */
    private String title;
    
    /**
     * 执法机构
     */
    private String enforcementAgency;
    
    /**
     * 案发时间
     */
    private String caseTime;
    
    /**
     * 地址
     */
    private String address;
    
    /**
     * 详细地址
     */
    private String detailedAddress;
    
    /**
     * 联合执法单位
     */
    private String jointEnforcementAgency;
    
    /**
     * 案由
     */
    private String caseReason;
    
    /**
     * 当事人
     */
    private String partyInvolved;
    
    /**
     * 许可证号
     */
    private String licenseNo;
    
    /**
     * 涉案物品列表
     */
    @Valid
    private List<MsEvidenceYhyt> priceStandardsList;
}
