package org.springblade.ms.dingapp.vo;

import lombok.Data;
import org.springblade.ms.basic.pojo.entity.UploadFileEntity;
import org.springblade.ms.exploration.pojo.vo.ExplorationVO;

import java.io.Serializable;
import java.util.List;

/**
 * 钉钉勘查记录VO
 * <AUTHOR> [sijun.zeng]
 * @date 2025-02-12 09:44
 */
@Data
public class DingExplorationVO implements Serializable {

    private ExplorationVO explorationVO;

    private DingMapLicenseVO dingMapLicenseVO;

}
