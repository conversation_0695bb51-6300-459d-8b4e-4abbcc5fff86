INSERT INTO blade_menu(id, parent_id, code, name, alias, path, source, sort, category, action, is_open, remark, is_deleted)
VALUES ('1879453808280051714', '1879362395018162177', 'reportComplaint', '工单信息', 'menu', '/reportcomplaint/reportComplaint', NULL, 1, 1, 0, 1, NULL, 0);
INSERT INTO blade_menu(id, parent_id, code, name, alias, path, source, sort, category, action, is_open, remark, is_deleted)
VALUES ('1879453808280051715', '1879453808280051714', 'reportComplaint_add', '新增', 'add', '/reportcomplaint/reportComplaint/add', 'plus', 1, 2, 1, 1, NULL, 0);
INSERT INTO blade_menu(id, parent_id, code, name, alias, path, source, sort, category, action, is_open, remark, is_deleted)
VALUES ('1879453808280051716', '1879453808280051714', 'reportComplaint_edit', '修改', 'edit', '/reportcomplaint/reportComplaint/edit', 'form', 2, 2, 2, 1, NULL, 0);
INSERT INTO blade_menu(id, parent_id, code, name, alias, path, source, sort, category, action, is_open, remark, is_deleted)
VALUES ('1879453808280051717', '1879453808280051714', 'reportComplaint_delete', '删除', 'delete', '/api/ms-reportcomplaint/reportComplaint/remove', 'delete', 3, 2, 3, 1, NULL, 0);
INSERT INTO blade_menu(id, parent_id, code, name, alias, path, source, sort, category, action, is_open, remark, is_deleted)
VALUES ('1879453808280051718', '1879453808280051714', 'reportComplaint_view', '查看', 'view', '/reportcomplaint/reportComplaint/view', 'file-text', 4, 2, 2, 1, NULL, 0);
