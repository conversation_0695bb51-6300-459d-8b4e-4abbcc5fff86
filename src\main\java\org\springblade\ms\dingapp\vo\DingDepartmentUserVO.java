package org.springblade.ms.dingapp.vo;

import lombok.Data;

import java.util.List;

/**
 * 钉钉部门用户信息VO
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Data
public class DingDepartmentUserVO {

    /**
     * 用户ID
     */
    private String userid;

    /**
     * 用户姓名
     */
    private String name;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 是否隐藏手机号
     */
    private Boolean hideMobile;

    /**
     * 座机号
     */
    private String telephone;

    /**
     * 工号
     */
    private String jobNumber;

    /**
     * 职位
     */
    private String title;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 企业邮箱
     */
    private String orgEmail;

    /**
     * 办公地点
     */
    private String workPlace;

    /**
     * 备注
     */
    private String remark;

    /**
     * 所属部门ID列表
     */
    private List<Long> deptIdList;

    /**
     * 在对应的部门中的排序
     */
    private List<Long> deptOrderList;

    /**
     * 扩展属性
     */
    private Object extension;

    /**
     * 是否为企业的高管
     */
    private Boolean senior;

    /**
     * 入职时间
     */
    private Long hiredDate;

    /**
     * 员工类型
     */
    private String empType;

    /**
     * 用户状态
     */
    private Integer state;

    /**
     * 用户在当前开发者企业账号范围内的唯一标识
     */
    private String unionid;

    /**
     * 用户角色列表
     */
    private List<Object> roleList;

    /**
     * 是否专属帐号
     */
    private Boolean exclusive;

    /**
     * 专属帐号类型
     */
    private String exclusiveType;

    /**
     * 国际电话区号
     */
    private String stateCode;

    /**
     * 员工在当前企业内的唯一标识
     */
    private String openid;

    /**
     * 管理范围
     */
    private String managerUserid;

    /**
     * 是否为部门的主管
     */
    private Boolean leader;

    /**
     * 部门内的排序
     */
    private Long order;

    /**
     * 是否为部门的主管
     */
    private Boolean boss;

    /**
     * 用户的部门信息
     */
    private List<DeptInfo> deptList;

    /**
     * 部门信息内部类
     */
    @Data
    public static class DeptInfo {
        /**
         * 部门ID
         */
        private Long deptId;

        /**
         * 是否为部门主管
         */
        private Boolean leader;

        /**
         * 在部门内的排序
         */
        private Long order;
    }
}
