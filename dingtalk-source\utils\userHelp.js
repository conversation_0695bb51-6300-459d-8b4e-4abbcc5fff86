import {setting} from "/common/setting";
import httpApi from "/utils/http/httpApi";


export function ddLoginAsync() {
    console.log(setting.corpId)
    return new Promise((resolve, reject) => {
        dd.getAuthCode({
            corpId: setting.corpId,
        })
        .then((res) => {
            console.log(res.authCode)
            let authCode = res.authCode
            httpApi.request({
                url: '/api/dingapp/user/login',
                method: 'POST',
                data: {
                    authCode: authCode
                }
            }).then((res) => {
                console.log('登录成功', res);
                handleLoginSuccess(res);
                resolve();
            }).catch((err) => {
                console.log(err);
                console.log('登录失败');
                // 这里要判断登录失败的原因
                reject(err);
            }) 
        }) 
    })
}

/**
 * 处理登录成功后, 系统返回的用户信息, 这时会记录全局变量的登录态和用户信息, 以供Http调用
 * 登录成功跳转到主页会携带关联记录ID, 看需处理
 * @param userInfo
 * @param redirect
 */
export function handleLoginSuccess(userInfo, redirect) {
    let app = getApp();
	app.globalData.userInfo = userInfo;
	app.globalData.accessToken = userInfo.access_token;
	app.globalData.refreshToken = userInfo.refresh_token;
	app.globalData.isLogin = true;
	console.log(getApp().globalData);
    dd.showToast({
        type: 'success',
        content: '登录成功',
        duration: 1000
    })
	setTimeout(() => {
		if (redirect) {
			dd.redirectTo({url: redirect});
		} else {
			dd.reLaunch({url: '/pages/map/map'});
		}
	}, 1000);
}

/**
 * 刷新token
 */
export function refreshToken(refresh_token) {
    return httpApi.request({
        url: '/api/blade-auth/oauth/token',
        method: 'POST',
        headers: {
            'Tenant-Id': '000000',
        },
        params: {
            tenantId: '000000',
            refresh_token: refresh_token,
            grant_type: 'refresh_token',
            scope: 'all',
        },
    })
}