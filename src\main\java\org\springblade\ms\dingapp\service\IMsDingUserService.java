package org.springblade.ms.dingapp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springblade.ms.dingapp.entity.MsDingUserEntity;

import java.util.List;
import java.util.Map;

/**
 * 钉钉用户信息 服务类
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
public interface IMsDingUserService extends IService<MsDingUserEntity> {

    /**
     * 批量保存或更新钉钉用户信息
     *
     * @param userInfoList 用户信息列表
     * @return 保存结果
     */
    boolean saveOrUpdateBatch(List<Map<String, Object>> userInfoList);

    /**
     * 根据钉钉用户ID查询用户信息
     *
     * @param userid 钉钉用户ID
     * @return 用户信息
     */
    MsDingUserEntity getByUserid(String userid);

    /**
     * 批量根据钉钉用户ID查询用户信息
     *
     * @param userIds 钉钉用户ID列表
     * @return 用户信息Map，key为userid，value为用户实体
     */
    Map<String, MsDingUserEntity> getByUserids(List<String> userIds);

}
