package org.springblade.ms.schoolInfo.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.AllArgsConstructor;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.ms.schoolInfo.mapper.MsSchoolInfoMapper;
import org.springblade.ms.schoolInfo.pojo.entity.MsSchoolInfo;
import org.springblade.ms.schoolInfo.service.MsSchoolInfoService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
* <AUTHOR> @description 针对表【ms_school_info】的数据库操作Service实现
* @createDate 2025-03-12 17:47:40
*/
@Service
@AllArgsConstructor
public class MsSchoolInfoServiceImpl extends BaseServiceImpl<MsSchoolInfoMapper, MsSchoolInfo>
    implements MsSchoolInfoService {

    @Override
    public List<MsSchoolInfo> getNearbySchools(BigDecimal longitude, BigDecimal latitude, Double radius, String searchParam) {
        // 参数校验
        if (longitude == null || latitude == null) {
            return new ArrayList<>();
        }

        // 如果没有指定半径，默认为1000米
        if (radius == null || radius <= 0) {
            radius = 1000.0;
        }

        // 直接调用Mapper中的方法，在数据库层面进行空间查询
        // 这样可以在数据库层面实现距离计算和过滤，提高效率
        return baseMapper.getNearbySchools(longitude, latitude, radius, searchParam);
    }
}
