/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.ms.basic.excel;


import lombok.Data;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;
import java.io.Serial;


/**
 * 品规信息 Excel实体类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class ProductInfoExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("主键")
	private Long id;
	/**
	 * 产品id
	 */
	@ColumnWidth(20)
	@ExcelProperty("产品id")
	private String productUuid;
	/**
	 * 产品编码
	 */
	@ColumnWidth(20)
	@ExcelProperty("产品编码")
	private String productCode;
	/**
	 * 产品名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("产品名称")
	private String productName;
	/**
	 * 国家产品id
	 */
	@ColumnWidth(20)
	@ExcelProperty("国家产品id")
	private String gjProductUuid;
	/**
	 * 国家产品名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("国家产品名称")
	private String gjProductName;
	/**
	 * 产品类型
	 */
	@ColumnWidth(20)
	@ExcelProperty("产品类型")
	private String productType;
	/**
	 * 短产品名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("短产品名称")
	private String supplyShortName;
	/**
	 * 品牌编码
	 */
	@ColumnWidth(20)
	@ExcelProperty("品牌编码")
	private String brandCode;
	/**
	 * 品牌名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("品牌名称")
	private String brandName;
	/**
	 * 品牌类型
	 */
	@ColumnWidth(20)
	@ExcelProperty("品牌类型")
	private String brandType;
	/**
	 * 包装长度(mm)
	 */
	@ColumnWidth(20)
	@ExcelProperty("包装长度(mm)")
	private Integer length;
	/**
	 * 包装宽度(mm)
	 */
	@ColumnWidth(20)
	@ExcelProperty("包装宽度(mm)")
	private Integer width;
	/**
	 * 包装高度(mm)
	 */
	@ColumnWidth(20)
	@ExcelProperty("包装高度(mm)")
	private Integer height;
	/**
	 * 条码
	 */
	@ColumnWidth(20)
	@ExcelProperty("条码")
	private String stripCode;
	/**
	 * 是否常销烟
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否常销烟")
	private Integer isOfften;
	/**
	 * 是否进口烟
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否进口烟")
	private Integer importFlag;
	/**
	 * 是否骨干品牌
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否骨干品牌")
	private Integer isMainProduct;
	/**
	 * 是否名优烟
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否名优烟")
	private Integer isFamous;
	/**
	 * 是否省内烟
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否省内烟")
	private Integer isProvince;
	/**
	 * 是否本省在销烟
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否本省在销烟")
	private Integer isLocSale;
	/**
	 * 是否查扣烟启用
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否查扣烟启用")
	private Integer isSeized;
	/**
	 * 是否特种烟
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否特种烟")
	private Integer isSpecial;
	/**
	 * 是否雪茄烟
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否雪茄烟")
	private Integer isCigar;
	/**
	 * 是否高端品牌
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否高端品牌")
	private Integer isHighTierBrand;
	/**
	 * 是否有效
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否有效")
	private Integer isActive;
	/**
	 * 是否已删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否已删除")
	private Integer isDeleted;

}
