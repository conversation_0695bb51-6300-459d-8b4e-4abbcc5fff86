package org.springblade.ms.platform12345.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 工单处理完成请求参数
 */
@Data
@Schema(description = "工单处理完成请求参数")
public class CompleteOrderRequest {

    @Schema(description = "加密校验串")
    private String signature;

    @Schema(description = "时间戳")
    private String timestamp;

    @Schema(description = "应用ID")
    private String appid;

    @Schema(description = "热线系统工单ID")
    private String proWoId;

    @Schema(description = "热线系统工单编号")
    private String proWoCode;

    @Schema(description = "回复市民内容")
    private String replyDetails;

    @Schema(description = "回复热线内容")
    private String content;

    @Schema(description = "是否出具书面回复：1-出具，0-不出具")
    private String isWrittenReply;

    @Schema(description = "不出具书面回复的理由")
    private String nowrittenReplyReason;

    @Schema(description = "回复方式")
    private String replyType;

    @Schema(description = "邮寄单号")
    private String emsNo;

    @Schema(description = "投诉举报类处理完成情形")
    private String sortManage;

    @Schema(description = "核查情况")
    private String checkCase;

    @Schema(description = "是否采纳：1-采纳，0-不采纳，2-不确定")
    private String isAccept;

    @Schema(description = "处理时间：YYYY-MM-DD HH24:MI:SS")
    private String dealTime;

    @Schema(description = "处理人工号")
    private String dealUserNo;

    @Schema(description = "办理时间（市场主体工单或者省网渠道工单必填）")
    private String accpetDate;

    @Schema(description = "答复类型（市场主体工单或者省网渠道工单必填）")
    private String publicFeedback;

    @Schema(description = "法律法规政策依据（市场主体工单或者省网渠道工单必填）")
    private String lawGist;

    @Schema(description = "回复市民方式（市场主体工单或者省网渠道工单必填）")
    private String evaluateResults;

    @Schema(description = "联系电话（市场主体工单或者省网渠道工单必填）")
    private String contactPhone;
}
