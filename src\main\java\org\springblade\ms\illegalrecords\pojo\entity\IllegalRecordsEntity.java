/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.ms.illegalrecords.pojo.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDate;
import java.util.Date;
import java.math.BigDecimal;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;

/**
 * 案件信息 实体类
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
@TableName("ms_Illegal_records")
@Schema(description = "IllegalRecords对象")
@EqualsAndHashCode(callSuper = true)
public class IllegalRecordsEntity extends TenantEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 案件uuid
	 */
	@Schema(description = "案件uuid")
	private String caseUuid;
	/**
	 * 案件名称
	 */
	@Schema(description = "案件名称")
	private String caseName;
	/**
	 * 案件编号
	 */
	@Schema(description = "案件编号")
	private String caseCode;
	/**
	 * 案件所属年度
	 */
	@Schema(description = "案件所属年度")
	private Integer caseYear;
	/**
	 * 案发时间
	 */
	@Schema(description = "案发时间")
	private Date caseDate;
	/**
	 * 案发地点
	 */
	@Schema(description = "案发地点")
	private String casePlace;
	/**
	 * gis经度
	 */
	@Schema(description = "gis经度")
	private BigDecimal placeGisX;
	/**
	 * gis纬度
	 */
	@Schema(description = "gis纬度")
	private BigDecimal placeGisY;
	/**
	 * 案由
	 */
	@Schema(description = "案由")
	private String caseOfAction;
	/**
	 * 案件性质
	 */
	@Schema(description = "案件性质")
	private String caseProperty;
	/**
	 * 立案号
	 */
	@Schema(description = "立案号")
	private String regDocNo;
	/**
	 * 立案时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@Schema(description = "立案时间")
	private LocalDate regTime;
	/**
	 * 上报日期
	 */
	@Schema(description = "上报日期")
	private Date reportDate;
	/**
	 * 案件类型
	 */
	@Schema(description = "案件类型")
	private String caseType;
	/**
	 * 是否结案
	 */
	@Schema(description = "是否结案")
	private Integer isFinshed;
	/**
	 * 结案日期
	 */
	@Schema(description = "结案日期")
	private Date finshDate;
	/**
	 * 是否12313
	 */
	@Schema(description = "是否12313")
	private Integer isReportHotline;
	/**
	 * 12313受理编号
	 */
	@Schema(description = "12313受理编号")
	private String applyCode;
	/**
	 * 行政处罚日期
	 */
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	@Schema(description = "行政处罚日期")
	private Date punishDecideDate;

	@Schema(description = "决定处罚文书号")
	private String decideFullNo;


}
