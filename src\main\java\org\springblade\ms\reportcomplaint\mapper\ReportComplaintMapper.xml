<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.ms.reportcomplaint.mapper.ReportComplaintMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="reportComplaintResultMap" type="org.springblade.ms.reportcomplaint.pojo.entity.ReportComplaintEntity">
        <result column="id" property="id"/>
        <result column="roll_number" property="rollNumber"/>
        <result column="roll_way" property="rollWay"/>
        <result column="roll_type" property="rollType"/>
        <result column="event_type" property="eventType"/>
        <result column="roll_degree" property="rollDegree"/>
        <result column="event_region" property="eventRegion"/>
        <result column="roll_time" property="rollTime"/>
        <result column="roll_date" property="rollDate"/>
        <result column="event_addr" property="eventAddr"/>
        <result column="event_content" property="eventContent"/>
        <result column="handle_rel_expire_time" property="handleRelExpireTime"/>
        <result column="roll_status" property="rollStatus"/>
        <result column="handle_type" property="handleType"/>
        <result column="license_code" property="licenseCode"/>
        <result column="roll_handle_result" property="rollHandleResult"/>
        <result column="handle_note_date" property="handleNoteDate"/>
        <result column="city_org_code" property="cityOrgCode"/>
        <result column="city_org_name" property="cityOrgName"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="handle_result" property="handleResult"/>
        <result column="complaint_status" property="complaintStatus"/>
        <result column="data_source" property="dataSource"/>
        <result column="audit_user" property="auditUser"/>
        <result column="user_id" property="userId"/>
        <result column="dept_id" property="deptId"/>
        <result column="has_license" property="hasLicense"/>
        <result column="contact_phone" property="contactPhone"/>
    </resultMap>

    <resultMap id="reportComplaintVOResultMap" type="org.springblade.ms.reportcomplaint.pojo.vo.ReportComplaintVO"/>

    <select id="selectReportComplaintPage" resultMap="reportComplaintVOResultMap">
        select mrc.*,blade_dept.dept_name from ms_report_complaint mrc
        left join blade_dept on blade_dept.id = mrc.dept_id
        where mrc.is_deleted = 0
        <if test="query.id != null and query.id != ''">
            and mrc.id::text like concat('%',#{query.id},'%')
        </if>
        <if test="query.complaintStatus != null and query.complaintStatus != ''">
            and mrc.complaint_status like concat('%',#{query.complaintStatus},'%')
        </if>
        <if test="query.eventTitle != null and query.eventTitle != ''">
            and mrc.event_title like concat('%',#{query.eventTitle},'%')
        </if>
        <if test="query.handleRelExpireTime != null and query.handleRelExpireTime != ''">
            and mrc.handle_rel_expire_time =  #{query.handleRelExpireTime}
        </if>
        order by  mrc.roll_date desc,mrc.update_time desc
    </select>


    <select id="exportReportComplaint" resultType="org.springblade.ms.reportcomplaint.excel.ReportComplaintExcel">
        SELECT * FROM ms_report_complaint ${ew.customSqlSegment}
    </select>

    <select id="getReportComplaintByRollNumber" resultMap="reportComplaintResultMap">
        SELECT * FROM ms_report_complaint WHERE is_deleted = 0 and roll_number = #{rollNumber}
    </select>
</mapper>
