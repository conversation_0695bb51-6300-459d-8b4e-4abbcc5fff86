package org.springblade.ms.priceStandards.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import org.springblade.ms.priceStandards.pojo.entity.MsEvidenceYhyt;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springblade.ms.priceStandards.pojo.vo.MsEvidenceYhytExportVO;
import org.springblade.ms.priceStandards.pojo.vo.MsEvidenceYhytVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ms_evidence_yhyt(涉案物品表)】的数据库操作Mapper
* @createDate 2025-04-10 10:09:30
* @Entity priceStandards.MsEvidenceYhyt
*/
public interface MsEvidenceYhytMapper extends BaseMapper<MsEvidenceYhyt> {

    List<MsEvidenceYhytVO> selectPage(IPage page, @Param("entity") MsEvidenceYhytVO msEvidenceYhyt);

    /**
     * 查询涉案物品列表，包含条形码信息
     * @param queryWrapper 查询条件
     * @return 涉案物品列表，包含条形码信息
     */
    List<MsEvidenceYhytExportVO> selectEvidenceWithBarcode(@Param(Constants.WRAPPER) Wrapper<MsEvidenceYhyt> queryWrapper);
}




