/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.ms.itemidentify.excel;


import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;
import java.io.Serial;


/**
 * 品规识别结果 Excel实体类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class ItemIdentifyResultsExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("主键")
	private Long id;
	/**
	 * 品规名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("品规名称")
	private String itemName;
	/**
	 * 品规码
	 */
	@ColumnWidth(20)
	@ExcelProperty("品规码")
	private String itemCode;
	/**
	 * 准确率
	 */
	@ColumnWidth(20)
	@ExcelProperty("准确率")
	private BigDecimal accuracy;
	/**
	 * 数量
	 */
	@ColumnWidth(20)
	@ExcelProperty("数量")
	private Integer quantity;
	/**
	 * 对碰类别（非烟、假烟、私烟、正常）
	 */
	@ColumnWidth(20)
	@ExcelProperty("对碰类别（非烟、假烟、私烟、正常）")
	private String collisionType;
	/**
	 * 识别日期
	 */
	@ColumnWidth(20)
	@ExcelProperty("识别日期")
	private Date identifyDate;
	/**
	 * 勘查表 ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("勘查表 ID")
	private Long explorationId;
	/**
	 * 品规识别 ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("品规识别 ID")
	private Long identifyId;
	/**
	 * 租户ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户ID")
	private String tenantId;
	/**
	 * 是否已删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否已删除")
	private Integer isDeleted;

}
