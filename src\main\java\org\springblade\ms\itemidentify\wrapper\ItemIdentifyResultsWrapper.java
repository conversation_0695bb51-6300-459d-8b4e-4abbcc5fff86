/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.ms.itemidentify.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.ms.itemidentify.pojo.entity.ItemIdentifyResultsEntity;
import org.springblade.ms.itemidentify.pojo.vo.ItemIdentifyResultsVO;
import java.util.Objects;

/**
 * 品规识别结果 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
public class ItemIdentifyResultsWrapper extends BaseEntityWrapper<ItemIdentifyResultsEntity, ItemIdentifyResultsVO>  {

	public static ItemIdentifyResultsWrapper build() {
		return new ItemIdentifyResultsWrapper();
 	}

	@Override
	public ItemIdentifyResultsVO entityVO(ItemIdentifyResultsEntity itemIdentifyResults) {
		ItemIdentifyResultsVO itemIdentifyResultsVO = Objects.requireNonNull(BeanUtil.copyProperties(itemIdentifyResults, ItemIdentifyResultsVO.class));

		//User createUser = UserCache.getUser(itemIdentifyResults.getCreateUser());
		//User updateUser = UserCache.getUser(itemIdentifyResults.getUpdateUser());
		//itemIdentifyResultsVO.setCreateUserName(createUser.getName());
		//itemIdentifyResultsVO.setUpdateUserName(updateUser.getName());

		return itemIdentifyResultsVO;
	}


}
