<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.ms.basic.mapper.YhytLicenseMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="yhytLicenseResultMap" type="org.springblade.ms.basic.pojo.entity.YhytLicenseEntity">
        <result column="id" property="id"/>
        <result column="retailer_uuid" property="retailerUuid"/>
        <result column="lic_no" property="licNo"/>
        <result column="lic_type" property="licType"/>
        <result column="old_lic_no" property="oldLicNo"/>
        <result column="cust_code" property="custCode"/>
        <result column="company_name" property="companyName"/>
        <result column="eco_type" property="ecoType"/>
        <result column="contract_person" property="contractPerson"/>
        <result column="retail_tel" property="retailTel"/>
        <result column="retail_tel_back" property="retailTelBack"/>
        <result column="business_addr" property="businessAddr"/>
        <result column="validate_start" property="validateStart"/>
        <result column="validate_end" property="validateEnd"/>
        <result column="lic_status" property="licStatus"/>
        <result column="invalid_time" property="invalidTime"/>
        <result column="org_name" property="orgName"/>
        <result column="is_have_business_lic" property="isHaveBusinessLic"/>
        <result column="business_lic_no" property="businessLicNo"/>
        <result column="business_valid_type" property="businessValidType"/>
        <result column="business_valid_start" property="businessValidStart"/>
        <result column="business_valid_end" property="businessValidEnd"/>
        <result column="registered_status" property="registeredStatus"/>
        <result column="special_type" property="specialType"/>
        <result column="special_type_other" property="specialTypeOther"/>
        <result column="busi_type" property="busiType"/>
        <result column="busi_sub_type" property="busiSubType"/>
        <result column="env_type" property="envType"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="eco_sub_type" property="ecoSubType"/>
        <result column="is_validate" property="isValidate"/>
        <result column="shop_sign" property="shopSign"/>
        <result column="consumer_need" property="consumerNeed"/>
        <result column="store_brand" property="storeBrand"/>
        <result column="manager_scope" property="managerScope"/>
        <result column="busi_size_code" property="busiSizeCode"/>
        <result column="busi_size_name" property="busiSizeName"/>
        <result column="adscription_code" property="adscriptionCode"/>
        <result column="adscription_name" property="adscriptionName"/>
        <result column="biz_format" property="bizFormat"/>
        <result column="supply_status" property="supplyStatus"/>
        <result column="supply_org_uuid" property="supplyOrgUuid"/>
        <result column="supply_company_code" property="supplyCompanyCode"/>
        <result column="supply_company_name" property="supplyCompanyName"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="operate_status" property="operateStatus"/>
        <result column="order_weekday" property="orderWeekday"/>
    </resultMap>

    <resultMap id="yhytLicenseVOResultMap" type="org.springblade.ms.basic.pojo.vo.YhytLicenseVO" extends="yhytLicenseResultMap">
    </resultMap>

    <select id="selectYhytLicensePage" resultMap="yhytLicenseVOResultMap">
        select * from ms_yhyt_license
        <where>
            <if test="true">
                is_deleted = 0
            </if>
            <if test="param.licNo != null and param.licNo != ''">
                and lic_no = #{param.licNo}
            </if>
            <if test="param.companyName != null and param.companyName != ''">
                and company_name like concat('%',#{param.companyName},'%')
            </if>
            <if test="param.bizFormat != null and param.bizFormat != ''">
                and biz_format = #{param.bizFormat}
            </if>
        </where>
        order by update_time desc
    </select>


    <select id="exportYhytLicense" resultType="org.springblade.ms.basic.excel.YhytLicenseExcel">
        SELECT * FROM ms_yhyt_license ${ew.customSqlSegment}
    </select>

    <select id="getSelectionList" resultMap="yhytLicenseResultMap">
        select id, company_name, business_addr  from ms_yhyt_license
        <where>
            <if test="true">
                is_deleted = 0
            </if>
            <if test="name != null and name != ''">
                and company_name like concat('%',#{name},'%')
            </if>
        </where>
    </select>

    <select id="getDingMapList" resultMap="yhytLicenseResultMap">
        SELECT * FROM (
        <!-- 有证零售户查询 -->
        SELECT
            id,
            retailer_uuid,
            cust_code,
            lic_no,
            company_name,
            lic_no,
            business_addr,
            longitude,
            latitude,
            biz_format,
            '1' as data_source  <!-- 标记数据来源为有证零售户 -->
        FROM
            ms_yhyt_license
        <where>
            <if test="true">
                is_deleted = 0
            </if>
            <if test="param.companyName != null and param.companyName != ''">
                and company_name like concat('%',#{param.companyName},'%')
            </if>
            <if test="param.licNo != null and param.licNo != ''">
                and lic_no = #{param.licNo}
            </if>
            <if test="param.bizFormat != null and param.bizFormat != ''">
                and biz_format = #{param.bizFormat}
            </if>
        </where>
        UNION ALL
        <!-- 无证零售户查询 -->
        SELECT
            id,
            retailer_uuid,
            cust_code,
            lic_no,
            company_name,
            '无证' as lic_no,  <!-- 无证零售户没有许可证号，设置为"无证" -->
            business_addr,
            longitude,
            latitude,
            '无证零售户' as biz_format,  <!-- 设置默认业态 -->
            '2' as data_source  <!-- 标记数据来源为无证零售户 -->
        FROM
            ms_yhyt_license_unlicensed
        <where>
            <if test="true">
                is_deleted = 0
            </if>
            <if test="param.companyName != null and param.companyName != ''">
                and (
                company_name like concat('%',#{param.companyName},'%')
                or business_addr like concat('%',#{param.companyName},'%')
                or manager_name like concat('%',#{param.companyName},'%')
                )
            </if>
            <if test="param.bizFormat != null and param.bizFormat != ''">
                and biz_format = #{param.bizFormat}
            </if>
            <!-- 无证零售户没有许可证号，所以不添加licNo条件 -->
            <!-- 如果需要按业态筛选，可以添加自定义逻辑 -->
        </where>
        ) AS combined_data
        <!-- 如果需要排序，可以在这里添加ORDER BY子句 -->
    </select>

    <select id="getFormatList" resultType="java.lang.String">
        select biz_format from ms_yhyt_license
        where is_deleted = 0 and biz_format is not null
        group by biz_format
    </select>

    <select id="selectYhytLicensePageByDistance" resultMap="yhytLicenseVOResultMap">
    SELECT * FROM (
        <!-- 有证零售户查询 -->
        SELECT
            id,
            retailer_uuid,
            cust_code,
            lic_no,
            company_name,
            business_addr,
            biz_format,
            manager_name,
            latitude,
            longitude,
            create_time,
            create_user,
            update_time,
            update_user,
            is_deleted,
            6371000 * 2 * ASIN(
                SQRT(
                    POWER(SIN((RADIANS(latitude - #{param.latitude})) / 2), 2) +
                    COS(RADIANS(#{param.latitude})) * COS(RADIANS(latitude)) *
                    POWER(SIN((RADIANS(longitude - #{param.longitude})) / 2), 2)
                )
            ) AS distance,
            '1' as data_source
        FROM ms_yhyt_license
        <where>
            <if test="true">
                is_deleted = 0
            </if>
            <if test="param.licNo != null and param.licNo != ''">
                AND lic_no like CONCAT('%', #{param.licNo}, '%')
            </if>
            <if test="param.companyName != null and param.companyName != ''">
                AND (
                    company_name LIKE CONCAT('%', #{param.companyName}, '%') OR
                    business_addr LIKE CONCAT('%', #{param.companyName}, '%') OR
                    manager_name LIKE CONCAT('%', #{param.companyName}, '%')
                )
            </if>
            <if test="param.bizFormat != null and param.bizFormat != '' and param.bizFormat != '无证零售户'">
                AND biz_format = #{param.bizFormat}
            </if>
            <if test="param.bizFormat != null and param.bizFormat == '无证零售户'">
                AND 1=0
            </if>
        </where>
        UNION ALL
        <!-- 无证零售户查询 -->
        SELECT
            id,
            retailer_uuid,
            cust_code,
            '无证' as lic_no,
            company_name,
            business_addr,
            '无证零售户' as biz_format,
            manager_name,
            latitude,
            longitude,
            create_time,
            create_user,
            update_time,
            update_user,
            is_deleted,
            6371000 * 2 * ASIN(
                SQRT(
                    POWER(SIN((RADIANS(latitude - #{param.latitude})) / 2), 2) +
                    COS(RADIANS(#{param.latitude})) * COS(RADIANS(latitude)) *
                    POWER(SIN((RADIANS(longitude - #{param.longitude})) / 2), 2)
                )
            ) AS distance,
            '2' as data_source
        FROM ms_yhyt_license_unlicensed
        <where>
            <if test="true">
                is_deleted = 0
            </if>
            <if test="param.companyName != null and param.companyName != ''">
                AND (
                    company_name LIKE CONCAT('%', #{param.companyName}, '%') OR
                    business_addr LIKE CONCAT('%', #{param.companyName}, '%') OR
                    manager_name LIKE CONCAT('%', #{param.companyName}, '%')
                )
            </if>
            <if test="param.licNo != null and param.licNo != ''">
                AND lic_no like CONCAT('%', #{param.licNo}, '%')
            </if>
            <if test="param.bizFormat != null and param.bizFormat != '' and param.bizFormat != '无证零售户'">
                AND 1=0
            </if>
        </where>
    ) AS combined_data
    ORDER BY distance ASC
</select>

</mapper>
