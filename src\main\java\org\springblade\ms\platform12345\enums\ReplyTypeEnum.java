package org.springblade.ms.platform12345.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 答复类型枚举 A5.13
 */
@Getter
@AllArgsConstructor
public enum ReplyTypeEnum {

    /**
     * 所提问题已经解决或基本解决
     */
    SOLVED("A", "所提问题已经解决或基本解决"),

    /**
     * 问题正在解决或列入规划逐步解决
     */
    IN_PROGRESS("B", "问题正在解决或列入规划逐步解决"),

    /**
     * 因目前条件限制或政策不允许等因素暂时不能解决的
     */
    CANNOT_SOLVE("C", "因目前条件限制或政策不允许等因素暂时不能解决的");

    /**
     * 代码
     */
    private final String code;

    /**
     * 值
     */
    private final String value;

    /**
     * 根据代码获取枚举
     *
     * @param code 代码
     * @return 枚举
     */
    public static ReplyTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (ReplyTypeEnum item : ReplyTypeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 根据代码获取值
     *
     * @param code 代码
     * @return 值
     */
    public static String getValueByCode(String code) {
        ReplyTypeEnum item = getByCode(code);
        return item != null ? item.getValue() : null;
    }

    /**
     * 根据值获取枚举
     *
     * @param value 值
     * @return 枚举
     */
    public static ReplyTypeEnum getByValue(String value) {
        if (value == null) {
            return null;
        }
        for (ReplyTypeEnum item : ReplyTypeEnum.values()) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 根据值获取代码
     *
     * @param value 值
     * @return 代码
     */
    public static String getCodeByValue(String value) {
        ReplyTypeEnum item = getByValue(value);
        return item != null ? item.getCode() : null;
    }
}
