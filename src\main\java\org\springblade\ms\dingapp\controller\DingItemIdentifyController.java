package org.springblade.ms.dingapp.controller;


import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.ms.basic.service.IUploadFileService;
import org.springblade.ms.dingapp.service.IDingItemIdentifyService;
import org.springblade.ms.itemidentify.pojo.dto.ItemIdentifyDTO;
import org.springblade.ms.itemidentify.pojo.entity.ItemIdentifyEntity;
import org.springblade.ms.itemidentify.pojo.entity.ItemIdentifyResultsEntity;
import org.springblade.ms.itemidentify.pojo.entity.MsRecentRetailerOrderProduct;
import org.springblade.ms.itemidentify.pojo.vo.ItemIdentifyVO;
import org.springblade.ms.itemidentify.service.IItemIdentifyResultsService;
import org.springblade.ms.itemidentify.service.IItemIdentifyService;
import org.springblade.ms.itemidentify.service.MsRecentRetailerOrderProductService;
import org.springblade.ms.itemidentify.wrapper.ItemIdentifyWrapper;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

@RestController
@AllArgsConstructor
    @RequestMapping("/dingapp/itemIdentify")
public class DingItemIdentifyController {
    private final IItemIdentifyService itemIdentifyService;
    private final IUploadFileService uploadFileService;
    private final IDingItemIdentifyService dingItemIdentifyService;
    private final IItemIdentifyResultsService iItemIdentifyResultsService;
    private final MsRecentRetailerOrderProductService recentRetailerOrderProductService;
    /**
     * 品规识别记录 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description  = "传入itemIdentify")
    public R<ItemIdentifyVO> detail(ItemIdentifyEntity itemIdentify) {
        QueryWrapper<ItemIdentifyEntity> queryWrapper = Condition.getQueryWrapper(itemIdentify);
        queryWrapper.select("id","identify_date","exploration_id","file_id","type", "'order'","result_file_id", "is_recognition_bad","tenant_id", "create_user",
                "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted");
        ItemIdentifyEntity detail = itemIdentifyService.getOne(queryWrapper);
        return R.data(ItemIdentifyWrapper.build().entityVO(detail));
    }
    /**
     * 品规识别记录 分页
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description  = "传入itemIdentify")
    public R<IPage<ItemIdentifyVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> itemIdentify, Query query) {
        IPage<ItemIdentifyEntity> pages = itemIdentifyService.page(Condition.getPage(query), Condition.getQueryWrapper(itemIdentify, ItemIdentifyEntity.class));
        return R.data(ItemIdentifyWrapper.build().pageVO(pages));
    }

    /**
     * 品规识别详情
     */
    @GetMapping("/listByExplorationId")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "详情", description  = "传入explorationId")
    public R<List<ItemIdentifyVO>> listByExplorationId( ItemIdentifyDTO itemIdentifyEntity) {
        return R.data(itemIdentifyService.getListByExplorationId(itemIdentifyEntity));
    }

    /**
     * 品规识别记录 自定义分页
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "分页", description  = "传入itemIdentify")
    public R<IPage<ItemIdentifyVO>> page(ItemIdentifyVO itemIdentify, Query query) {
        IPage<ItemIdentifyVO> pages = itemIdentifyService.selectItemIdentifyPage(Condition.getPage(query), itemIdentify);
        return R.data(pages);
    }



    @PostMapping("/upload")
    public R<List<ItemIdentifyResultsEntity>> upload(
            @RequestParam(name = "objId",required = false) Long objId,
            @RequestParam(name = "explorationId") Long explorationId,
            @RequestParam(name = "type", required = false) String type,
            @RequestParam(name = "objName") String objName,
            @RequestParam(name = "extName", required = false) String extName,
            @RequestParam(name = "file", required = false) MultipartFile file,
            @RequestParam(name = "customerCode", required = false) String customerCode,
            HttpServletRequest request
    ) {
        JSONObject res = null;
        try {
            res = uploadFileService.dingUploadFile(objId, "ItemIdentify", file, request, extName);

            List<ItemIdentifyResultsEntity> itemIdentifyResultsEntityList = dingItemIdentifyService.getDetect(file, explorationId, type, Long.parseLong( res.getByPath("data.id").toString()), objId, objName, false, false, 1280, customerCode, request);
            return R.data(itemIdentifyResultsEntityList);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return R.success("上传失败");
    }

    @GetMapping("/remove")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "删除识别图片和数据", description  = "传入id")
    public R<Boolean> remove( @RequestParam Long itemIdentifyId) {
        Boolean b = dingItemIdentifyService.removeById(itemIdentifyId);
        return R.data(b);
    }

    @GetMapping("/updateIsBad")
    public R<Boolean> updateIsRecognitionBad(ItemIdentifyEntity itemIdentifyEntity){

        boolean b = itemIdentifyService.saveOrUpdate(itemIdentifyEntity);
        return R.data(b);
    }


    @GetMapping("/orderList")
    public R<List<MsRecentRetailerOrderProduct>> getRecentRetailerOrderProduct(String customerCode){
        return R.data(recentRetailerOrderProductService.listByCustomerCode(customerCode));
    }
}
