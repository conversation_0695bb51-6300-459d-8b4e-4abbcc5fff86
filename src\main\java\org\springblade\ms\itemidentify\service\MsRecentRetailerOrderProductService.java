package org.springblade.ms.itemidentify.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springblade.core.mp.base.BaseService;
import org.springblade.ms.itemidentify.pojo.entity.MsRecentRetailerOrderProduct;

import java.util.List;

/**
* <AUTHOR> @description 针对表【ms_recent_retailer_order_product(近半年零售户订货品规)】的数据库操作Service
* @createDate 2025-03-11 19:15:51
*/
public interface MsRecentRetailerOrderProductService extends BaseService<MsRecentRetailerOrderProduct> {

    /**
     * 根据客户编码查询近半年零售户订货品规
     *
     * @param customerCode 客户编码
     * @return 零售户订货品规列表
     */
    List<MsRecentRetailerOrderProduct> listByCustomerCode(String customerCode);
}
