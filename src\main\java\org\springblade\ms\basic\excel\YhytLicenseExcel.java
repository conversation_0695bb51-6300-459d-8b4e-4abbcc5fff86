/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.ms.basic.excel;


import lombok.Data;

import java.time.LocalDate;
import java.util.Date;
import java.math.BigDecimal;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;
import java.io.Serial;


/**
 * 零售户信息 Excel实体类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class YhytLicenseExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("主键")
	private Long id;
	/**
	 * 零售户uuid
	 */
	@ColumnWidth(20)
	@ExcelProperty("零售户uuid")
	private String retailerUuid;
	/**
	 * 许可证编号
	 */
	@ColumnWidth(20)
	@ExcelProperty("许可证编号")
	private String licNo;
	/**
	 * 许可证种类
	 */
	@ColumnWidth(20)
	@ExcelProperty("许可证种类")
	private String licType;
	/**
	 * 老许可证号
	 */
	@ColumnWidth(20)
	@ExcelProperty("老许可证号")
	private String oldLicNo;
	/**
	 * 客户编码
	 */
	@ColumnWidth(20)
	@ExcelProperty("客户编码")
	private String custCode;
	/**
	 * 企业名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("企业名称")
	private String companyName;
	/**
	 * 企业经济类型，字典：l_eco_type
	 */
	@ColumnWidth(20)
	@ExcelProperty("企业经济类型，字典：l_eco_type")
	private String ecoType;
	/**
	 * 联系人
	 */
	@ColumnWidth(20)
	@ExcelProperty("联系人")
	private String contractPerson;
	/**
	 * 联系电话
	 */
	@ColumnWidth(20)
	@ExcelProperty("联系电话")
	private String retailTel;
	/**
	 * 备用电话
	 */
	@ColumnWidth(20)
	@ExcelProperty("备用电话")
	private String retailTelBack;
	/**
	 * 经营地址
	 */
	@ColumnWidth(20)
	@ExcelProperty("经营地址")
	private String businessAddr;
	/**
	 * 许可证有效期限起
	 */
	@ColumnWidth(20)
	@ExcelProperty("许可证有效期限起")
	private Date validateStart;
	/**
	 * 许可证有效期限止
	 */
	@ColumnWidth(20)
	@ExcelProperty("许可证有效期限止")
	private Date validateEnd;
	/**
	 * 许可证状态，字典：l_lic_status
	 */
	@ColumnWidth(20)
	@ExcelProperty("许可证状态，字典：l_lic_status")
	private String licStatus;
	/**
	 * 失效时间（许可证注销时间）
	 */
	@ColumnWidth(20)
	@ExcelProperty("失效时间（许可证注销时间）")
	private Date invalidTime;
	/**
	 * 片区名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("片区名称")
	private String orgName;
	/**
	 * 是否有工商营业执照
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否有工商营业执照")
	private Integer isHaveBusinessLic;
	/**
	 * 工商营业执照编码（统一社会信用代码证）
	 */
	@ColumnWidth(20)
	@ExcelProperty("工商营业执照编码（统一社会信用代码证）")
	private String businessLicNo;
	/**
	 * 营业执照有效期类型，字典：l_business_valid_type
	 */
	@ColumnWidth(20)
	@ExcelProperty("营业执照有效期类型，字典：l_business_valid_type")
	private String businessValidType;
	/**
	 * 营业执照有效期起
	 */
	@ColumnWidth(20)
	@ExcelProperty("营业执照有效期起")
	private LocalDate businessValidStart;
	/**
	 * 营业执照有效期止
	 */
	@ColumnWidth(20)
	@ExcelProperty("营业执照有效期止")
	private LocalDate businessValidEnd;
	/**
	 * 工商营业执照状态 ，字典：l_business_registered_status
	 */
	@ColumnWidth(20)
	@ExcelProperty("工商营业执照状态 ，字典：l_business_registered_status")
	private String registeredStatus;
	/**
	 * 群体类型
	 */
	@ColumnWidth(20)
	@ExcelProperty("群体类型")
	private String specialType;
	/**
	 * 其他特殊群体说明
	 */
	@ColumnWidth(20)
	@ExcelProperty("其他特殊群体说明")
	private String specialTypeOther;
	/**
	 * 商圈，字典：l_busi_type
	 */
	@ColumnWidth(20)
	@ExcelProperty("商圈，字典：l_busi_type")
	private String busiType;
	/**
	 * 次级商圈：入口：retailder_busi_sub_type
	 */
	@ColumnWidth(20)
	@ExcelProperty("次级商圈：入口：retailder_busi_sub_type")
	private String busiSubType;
	/**
	 * 地理环境，字典：l_env_type
	 */
	@ColumnWidth(20)
	@ExcelProperty("地理环境，字典：l_env_type")
	private String envType;
	/**
	 * 经度
	 */
	@ColumnWidth(20)
	@ExcelProperty("经度")
	private BigDecimal longitude;
	/**
	 * 纬度
	 */
	@ColumnWidth(20)
	@ExcelProperty("纬度")
	private BigDecimal latitude;
	/**
	 * 经济类型-子类（个人经营、家庭经营）
	 */
	@ColumnWidth(20)
	@ExcelProperty("经济类型-子类（个人经营、家庭经营）")
	private String ecoSubType;
	/**
	 * 许可证是否有效
	 */
	@ColumnWidth(20)
	@ExcelProperty("许可证是否有效")
	private Integer isValidate;
	/**
	 * 店铺招牌
	 */
	@ColumnWidth(20)
	@ExcelProperty("店铺招牌")
	private String shopSign;
	/**
	 * 消费需求
	 */
	@ColumnWidth(20)
	@ExcelProperty("消费需求")
	private String consumerNeed;
	/**
	 * 连锁企业品牌
	 */
	@ColumnWidth(20)
	@ExcelProperty("连锁企业品牌")
	private String storeBrand;
	/**
	 * 许可证经营范围多选，字典：l_manager_scope
	 */
	@ColumnWidth(20)
	@ExcelProperty("许可证经营范围多选，字典：l_manager_scope")
	private String managerScope;
	/**
	 * 经营规模
	 */
	@ColumnWidth(20)
	@ExcelProperty("经营规模")
	private String busiSizeCode;
	/**
	 * 经营规模名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("经营规模名称")
	private String busiSizeName;
	/**
	 * 经营场地权属
	 */
	@ColumnWidth(20)
	@ExcelProperty("经营场地权属")
	private String adscriptionCode;
	/**
	 * 经营场地权属名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("经营场地权属名称")
	private String adscriptionName;
	/**
	 * 经营业态
	 */
	@ColumnWidth(20)
	@ExcelProperty("经营业态")
	private String bizFormat;
	/**
	 * 供货状态，字典：l_supply_status
	 */
	@ColumnWidth(20)
	@ExcelProperty("供货状态，字典：l_supply_status")
	private String supplyStatus;
	/**
	 * 供货单位编号，表s_org_base_info
	 */
	@ColumnWidth(20)
	@ExcelProperty("供货单位编号，表s_org_base_info")
	private String supplyOrgUuid;
	/**
	 * 供货单位编号
	 */
	@ColumnWidth(20)
	@ExcelProperty("供货单位编号")
	private String supplyCompanyCode;
	/**
	 * 供货单位名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("供货单位名称")
	private String supplyCompanyName;
	/**
	 * 是否已删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否已删除")
	private Integer isDeleted;
	/**
	 * 租户ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户ID")
	private String tenantId;

}
