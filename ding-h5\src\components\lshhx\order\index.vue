<template>
  <div class="order-main">
    <!-- 搜索框 -->
    <div class="search-container">
      <van-search
        v-model="searchValue"
        placeholder="请输入商品名称或编码"
        @search="handleSearch"
        @clear="handleSearch"
        shape="round"
        clearable
      />
    </div>

    <van-empty v-if="filteredList.length === 0" description="数据为空" />
    <div v-else>
      <van-list
        v-model:loading="listLoading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
      >
        <div v-for="item in displayList" :key="item.id">
          <div class="info-card">
            <div class="flex">
              <span class="text">商品编码:</span>
              <span class="text text-content">{{ item.productCode || '无' }}</span>
            </div>
            <div class="flex" style="margin-top: 10px">
              <span class="text">商品名称:</span>
              <span class="text text-content">{{ item.productName }}</span>
            </div>

            <!-- 最近一次订货信息 -->
            <div class="section-title">最近一次订货</div>
            <div class="flex" style="margin-top: 8px">
              <span class="text">订货日期:</span>
              <span class="text text-content">{{ formatDate(item.recentBizDate) || '无' }}</span>
            </div>
            <div class="flex" style="margin-top: 8px">
              <span class="text">订货数量:</span>
              <span class="text text-content">{{ formatNumber(item.recentQty) || '0' }}条</span>
            </div>
            <div class="flex" style="margin-top: 8px">
              <span class="text">订货金额:</span>
              <span class="text text-content">{{ formatMoney(item.recentWithTaxAmount) || '0.00' }}元</span>
            </div>
<!--            <div class="flex" style="margin-top: 8px">-->
<!--              <span class="text">是否到货:</span>-->
<!--              <span class="text text-content">{{ item.isRecentArrived == 1 ? '是' : '否' }}</span>-->
<!--            </div>-->

            <div class="section-title">近一年订货统计</div>
            <div class="flex" style="margin-top: 8px">
              <span class="text">订货数量:</span>
              <span class="text text-content">{{ formatNumber(item.totalQty) || '0' }}条</span>
            </div>
            <div class="flex" style="margin-top: 8px">
              <span class="text">订货金额:</span>
              <span class="text text-content">{{ formatMoney(item.totalWithTaxAmount) || '0.00' }}元</span>
            </div>
            <div class="flex" style="margin-top: 8px">
              <span class="text">订货次数:</span>
              <span class="text text-content">{{ formatNumber(item.totalOrdTimes) || '0' }}次</span>
            </div>
          </div>
        </div>
      </van-list>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { http } from '@/utils/http'
import { showFailToast, Search as VanSearch, List as VanList } from 'vant'

const props = defineProps({
  custCode: {
    type: String,
    default: ''
  }
})

const dataList = ref([])
const loading = ref(false)
const searchValue = ref('')
const pageSize = 10
const listLoading = ref(false)
const finished = ref(false)
const displayList = ref([])
const currentIndex = ref(0)

// 根据搜索条件过滤数据
const filteredList = computed(() => {
  if (!searchValue.value) {
    return dataList.value
  }

  const keyword = searchValue.value.toLowerCase()
  return dataList.value.filter(item => {
    return (
      (item.productCode && item.productCode.toLowerCase().includes(keyword)) ||
      (item.productName && item.productName.toLowerCase().includes(keyword))
    )
  })
})

// 搜索处理函数
const handleSearch = () => {
  // 重置滚动加载状态
  displayList.value = []
  currentIndex.value = 0
  finished.value = false

  // 触发加载
  onLoad()
}

// 监听搜索值变化，自动触发搜索
watch(searchValue, () => {
  handleSearch()
})

// 滚动加载更多数据
const onLoad = () => {
  // 模拟异步加载
  setTimeout(() => {
    const start = currentIndex.value
    const end = start + pageSize

    // 获取当前批次的数据
    const newItems = filteredList.value.slice(start, end)

    // 添加到显示列表
    displayList.value.push(...newItems)

    // 更新索引
    currentIndex.value = end

    // 判断是否加载完成
    if (displayList.value.length >= filteredList.value.length) {
      finished.value = true
    }

    // 加载完成
    listLoading.value = false
  }, 300)
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return ''
  try {
    const date = new Date(dateStr)
    if (isNaN(date.getTime())) return dateStr
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  } catch (error) {
    return dateStr
  }
}

// 格式化数字（添加千分位分隔符）
const formatNumber = (num) => {
  if (!num && num !== 0) return ''
  return Number(num).toLocaleString('zh-CN')
}

// 格式化金额（保留两位小数并添加千分位分隔符）
const formatMoney = (amount) => {
  if (!amount && amount !== 0) return ''
  return Number(amount).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 4
  })
}

const loadOrderData = async () => {
  try {
    loading.value = true
    console.log('Loading orders with custCode:', props.custCode)

    const res = await http.get('/api/dingapp/itemIdentify/orderList', {
      params: {
        customerCode: props.custCode
      }
    })

    if (res.data) {
      dataList.value = res.data
      console.log('订单数据加载成功，共', dataList.value.length, '条记录')

      // 初始化显示列表
      handleSearch()
    }
  } catch (error) {
    console.error('获取订单记录失败:', error)
    showFailToast('获取订单记录失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  console.log('Order component mounted with custCode:', props.custCode)
  loadOrderData()
})
</script>

<style scoped>
.order-main {
  padding: 15px;
  background-color: white;
  height: 100vh;
  overflow-y: auto;
}

.search-container {
  margin-bottom: 15px;
}

.title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.title-two {
  font-size: 14px;
  color: #999;
}

.info-card {
  background-color: #f7f8fa;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.flex {
  display: flex;
  align-items: center;
  line-height: 1.5;
}

.text {
  font-size: 14px;
  color: #333;
  min-width: 80px; /* 标题的最小宽度 */
  display: inline-block;
}

.text-content {
  flex: 1;
  word-break: break-all;
  display: inline-block;
}

.section-title {
  font-size: 15px;
  font-weight: 600;
  color: #1989fa;
  margin: 15px 0 8px 0;
  padding-bottom: 5px;
  border-bottom: 1px solid #e8e8e8;
}

/* 自定义搜索框样式 */
:deep(.van-search) {
  padding: 10px 0;
}

:deep(.van-search__content) {
  background-color: #f7f8fa;
}

/* 自定义列表加载样式 */
:deep(.van-list__loading) {
  padding: 16px 0;
  text-align: center;
}

:deep(.van-list__finished-text) {
  padding: 16px 0;
  font-size: 14px;
  color: #969799;
  text-align: center;
}
</style>
