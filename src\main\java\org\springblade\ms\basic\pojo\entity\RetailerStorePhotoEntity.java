package org.springblade.ms.basic.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.io.Serial;
import java.time.LocalDate;

@Data
@TableName("ms_retailer_store_photo")
@Schema(description = "ms_retailer_store_photo对象")
@EqualsAndHashCode(callSuper = true)
public class RetailerStorePhotoEntity extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 零售户 ID
     */
    @Schema(description = "零售户 ID")
    private Long licenseId;
    /**
     * 勘查 ID
     */
    @Schema(description = "勘查 ID")
    private Long explorationId;
    /**
     * 文件 ID
     */
    @Schema(description = "文件 ID")
    private Long fileId;
    /**
     * 录入日期
     */
    @Schema(description = "录入日期")
    private LocalDate createDate;
    /**
     * 照片对象类型（RETAILER-零售户、UNLICENSED-无证户、SCHOOL-学校）
     */
    @Schema(description = "照片对象类型（RETAILER-零售户、UNLICENSED-无证户、SCHOOL-学校）")
    private String objectType;
}
