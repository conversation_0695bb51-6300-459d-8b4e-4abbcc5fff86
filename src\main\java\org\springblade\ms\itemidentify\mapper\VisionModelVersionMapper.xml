<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.ms.itemidentify.mapper.VisionModelVersionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="visionModelVersionResultMap" type="org.springblade.ms.itemidentify.pojo.entity.VisionModelVersionEntity">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="version" property="version"/>
        <result column="url" property="url"/>
        <result column="remark" property="remark"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectVisionModelVersionPage" resultMap="visionModelVersionResultMap">
        select * from ms_vision_model_version where is_deleted = 0
    </select>


    <select id="exportVisionModelVersion" resultType="org.springblade.ms.itemidentify.excel.VisionModelVersionExcel">
        SELECT * FROM ms_vision_model_version ${ew.customSqlSegment}
    </select>

</mapper>
