package org.springblade.ms.dingapp.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 钉钉工作通知DTO
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Data
@Schema(description = "钉钉工作通知DTO")
public class DingNotificationDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 接收者的用户ID列表，最大列表长度：100
     */
    @Schema(description = "接收者的用户ID列表")
    private List<String> userIds;

    /**
     * 接收者的部门ID列表，最大列表长度：20
     */
    @Schema(description = "接收者的部门ID列表")
    private List<String> deptIds;

    /**
     * 是否发送给企业全部用户
     */
    @Schema(description = "是否发送给企业全部用户")
    private Boolean toAllUser;

    /**
     * 消息标题
     */
    @Schema(description = "消息标题")
    private String title;

    /**
     * 消息内容
     */
    @Schema(description = "消息内容")
    private String content;

    /**
     * 消息描述（用于链接类型消息）
     */
    @Schema(description = "消息描述")
    private String text;

    /**
     * 点击消息跳转的URL（用于链接类型消息）
     */
    @Schema(description = "点击消息跳转的URL")
    private String messageUrl;

    /**
     * 图片URL（用于链接类型消息）
     */
    @Schema(description = "图片URL")
    private String picUrl;

    /**
     * Markdown格式的消息内容
     */
    @Schema(description = "Markdown格式的消息内容")
    private String markdownText;

    /**
     * 发送任务ID
     */
    @Schema(description = "发送任务ID")
    private Long taskId;
}
