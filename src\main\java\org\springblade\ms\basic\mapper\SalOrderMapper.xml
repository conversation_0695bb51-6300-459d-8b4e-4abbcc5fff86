<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.ms.basic.mapper.SalOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="salOrderResultMap" type="org.springblade.ms.basic.pojo.entity.SalOrderEntity">
        <result column="id" property="id"/>
        <result column="order_uuid" property="orderUuid"/>
        <result column="sale_type" property="saleType"/>
        <result column="bill_code" property="billCode"/>
        <result column="biz_date" property="bizDate"/>
        <result column="detail_count" property="detailCount"/>
        <result column="customer_uuid" property="customerUuid"/>
        <result column="customer_code" property="customerCode"/>
        <result column="customer_name" property="customerName"/>
        <result column="cust_master_tel" property="custMasterTel"/>
        <result column="sale_depart_uuid" property="saleDepartUuid"/>
        <result column="sale_depart_name" property="saleDepartName"/>
        <result column="sale_county_name" property="saleCountyName"/>
        <result column="saler_name" property="salerName"/>
        <result column="is_audit" property="isAudit"/>
        <result column="is_be_return" property="isBeReturn"/>
        <result column="is_return" property="isReturn"/>
        <result column="is_payed" property="isPayed"/>
        <result column="is_arrived" property="isArrived"/>
        <result column="is_tax_inv" property="isTaxInv"/>
        <result column="is_active" property="isActive"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>


    <select id="selectSalOrderPage" resultMap="salOrderResultMap">
        select * from ms_sal_order where is_deleted = 0
    </select>


    <select id="exportSalOrder" resultType="org.springblade.ms.basic.excel.SalOrderExcel">
        SELECT * FROM ms_sal_order ${ew.customSqlSegment}
    </select>

</mapper>
