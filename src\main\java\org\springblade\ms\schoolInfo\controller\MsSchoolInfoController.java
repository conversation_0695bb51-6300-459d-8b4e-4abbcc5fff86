package org.springblade.ms.schoolInfo.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.ms.schoolInfo.pojo.entity.MsSchoolInfo;
import org.springblade.ms.schoolInfo.service.MsSchoolInfoService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;

/**
 * 学校信息 控制器
 *
 * <AUTHOR> Assistant
 * @since 2025-03-20
 */
@RestController
@AllArgsConstructor
@RequestMapping("ms-schoolinfo/schoolInfo")
@Tag(name = "学校信息", description = "学校信息接口")
public class MsSchoolInfoController extends BladeController {

    private final MsSchoolInfoService schoolInfoService;

    /**
     * 查询附近的学校信息
     *
     * @param longitude   经度
     * @param latitude    纬度
     * @param radius      半径（米），默认1000米
     * @param searchParam 搜索参数（学校名称）
     * @return 学校信息列表，按距离排序
     */
    @GetMapping("/nearby")
    @Operation(summary = "查询附近学校", description = "传入经纬度和半径")
    public R<List<MsSchoolInfo>> getNearbySchools(
            @RequestParam(required = true) BigDecimal longitude,
            @RequestParam(required = true) BigDecimal latitude,
            @RequestParam(required = false) Double radius,
            @RequestParam(required = false) String searchParam) {
        
        return R.data(schoolInfoService.getNearbySchools(longitude, latitude, radius, searchParam));
    }
}
