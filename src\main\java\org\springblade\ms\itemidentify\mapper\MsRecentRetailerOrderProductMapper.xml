<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.ms.itemidentify.mapper.MsRecentRetailerOrderProductMapper">

    <resultMap id="BaseResultMap" type="org.springblade.ms.itemidentify.pojo.entity.MsRecentRetailerOrderProduct">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="customerUuid" column="customer_uuid" jdbcType="VARCHAR"/>
            <result property="customerCode" column="customer_code" jdbcType="VARCHAR"/>
            <result property="customerName" column="customer_name" jdbcType="VARCHAR"/>
            <result property="productUuid" column="product_uuid" jdbcType="VARCHAR"/>
            <result property="productCode" column="product_code" jdbcType="VARCHAR"/>
            <result property="productName" column="product_name" jdbcType="VARCHAR"/>
            <result property="qty" column="qty" jdbcType="NUMERIC"/>
            <result property="createUser" column="create_user" jdbcType="BIGINT"/>
            <result property="createDept" column="create_dept" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateUser" column="update_user" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>

            <!-- 新增字段 -->
            <result property="isRecentArrived" column="is_recent_arrived" jdbcType="INTEGER"/>
            <result property="recentQty" column="recent_qty" jdbcType="INTEGER"/>
            <result property="recentBizDate" column="recent_biz_date" jdbcType="VARCHAR"/>
            <result property="recentWithTaxAmount" column="recent_with_tax_amount" jdbcType="NUMERIC"/>
            <result property="recentReqQty" column="recent_req_qty" jdbcType="INTEGER"/>
            <result property="totalOrdTimes" column="total_ord_tims" jdbcType="INTEGER"/>
            <result property="totalQty" column="total_qty" jdbcType="INTEGER"/>
            <result property="totalWithTaxAmount" column="total_with_tax_amount" jdbcType="NUMERIC"/>
            <result property="totalReqQty" column="total_req_qty" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,customer_uuid,customer_code,
        customer_name,product_uuid,product_code,
        product_name,qty,create_user,
        create_dept,create_time,update_user,
        update_time,status,is_deleted,
        tenant_id, is_recent_arrived, recent_qty,
        recent_biz_date, recent_with_tax_amount,
        recent_req_qty, total_ord_tims,
        total_qty, total_with_tax_amount,
        total_req_qty
    </sql>
</mapper>
