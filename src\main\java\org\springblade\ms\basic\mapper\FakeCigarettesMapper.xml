<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.ms.basic.mapper.FakeCigarettesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="fakeCigarettesResultMap" type="org.springblade.ms.basic.pojo.entity.FakeCigarettesEntity">
        <result column="id" property="id"/>
        <result column="supply_name" property="supplyName"/>
        <result column="goods_uuid" property="goodsUuid"/>
        <result column="goods_name" property="goodsName"/>
        <result column="brand_name" property="brandName"/>
        <result column="smuggle_qty" property="smuggleQty"/>
        <result column="truth_qty" property="truthQty"/>
        <result column="fake_qty" property="fakeQty"/>
        <result column="total_qty" property="totalQty"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>


    <select id="selectFakeCigarettesPage" resultMap="fakeCigarettesResultMap">
        select * from ms_fake_cigarettes where is_deleted = 0
    </select>


    <select id="exportFakeCigarettes" resultType="org.springblade.ms.basic.excel.FakeCigarettesExcel">
        SELECT * FROM ms_fake_cigarettes ${ew.customSqlSegment}
    </select>

</mapper>
