/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.ms.exploration.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.ms.exploration.pojo.entity.ExplorationEntity;
import org.springblade.ms.exploration.pojo.vo.ExplorationVO;
import org.springblade.ms.exploration.excel.ExplorationExcel;
import org.springblade.ms.exploration.wrapper.ExplorationWrapper;
import org.springblade.ms.exploration.service.IExplorationService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 勘查记录 控制器
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@RestController
@AllArgsConstructor
@RequestMapping("ms-exploration/exploration")
@Tag(name = "勘查记录", description = "勘查记录接口")
public class ExplorationController extends BladeController {

	private final IExplorationService explorationService;

	/**
	 * 勘查记录 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入exploration")
	public R<ExplorationVO> detail(ExplorationEntity exploration) {
		ExplorationEntity detail = explorationService.getOne(Condition.getQueryWrapper(exploration));
		return R.data(ExplorationWrapper.build().entityVO(detail));
	}
	/**
	 * 勘查记录 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入exploration")
	public R<IPage<ExplorationVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> exploration, Query query) {
		IPage<ExplorationEntity> pages = explorationService.page(Condition.getPage(query), Condition.getQueryWrapper(exploration, ExplorationEntity.class));
		return R.data(ExplorationWrapper.build().pageVO(pages));
	}

	/**
	 * 勘查记录 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入exploration")
	public R<IPage<ExplorationVO>> page(ExplorationVO exploration, Query query) {
		IPage<ExplorationVO> pages = explorationService.selectExplorationPage(Condition.getPage(query), exploration);
		return R.data(pages);
	}

	/**
	 * 勘查记录 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入exploration")
	public R save(@Valid @RequestBody ExplorationEntity exploration) {
		return R.status(explorationService.save(exploration));
	}

	/**
	 * 勘查记录 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入exploration")
	public R update(@Valid @RequestBody ExplorationEntity exploration) {
		return R.status(explorationService.updateById(exploration));
	}

	/**
	 * 勘查记录 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入exploration")
	public R submit(@Valid @RequestBody ExplorationEntity exploration) {
		return R.status(explorationService.saveOrUpdate(exploration));
	}

	/**
	 * 勘查记录 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(explorationService.deleteLogic(Func.toLongList(ids)));
	}

}
