package org.springblade.ms.dingapp.controller;


import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.ms.basic.service.IUploadFileService;
import org.springblade.ms.dingapp.service.IDingItemIdentifyService;
import org.springblade.ms.itemidentify.pojo.dto.ItemIdentifyResultsDTO;
import org.springblade.ms.itemidentify.pojo.entity.ItemIdentifyEntity;
import org.springblade.ms.itemidentify.pojo.entity.ItemIdentifyResultsEntity;
import org.springblade.ms.itemidentify.pojo.vo.ItemIdentifyResultsVO;
import org.springblade.ms.itemidentify.pojo.vo.ItemIdentifyResultsWithRetailerVO;
import org.springblade.ms.itemidentify.pojo.vo.ItemIdentifyVO;
import org.springblade.ms.itemidentify.service.IItemIdentifyResultsService;
import org.springblade.ms.itemidentify.service.IItemIdentifyService;
import org.springblade.ms.itemidentify.wrapper.ItemIdentifyWrapper;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

@RestController
@AllArgsConstructor
    @RequestMapping("/dingapp/itemIdentifyResults")
public class DingItemIdentifyResultsController {
    private final IItemIdentifyResultsService iItemIdentifyResultsService;

    /**
     * 品规识别记录
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "品规识别记录", description  = "explorationId,identifyDate")
    public R<List<ItemIdentifyResultsVO>> list( ItemIdentifyResultsDTO dto) {
        List<ItemIdentifyResultsVO> itemIdentifyResultsVOS = iItemIdentifyResultsService.listAll(dto);
        return R.data(itemIdentifyResultsVOS);
    }


    /**
     * 品规识别记录统计 分页
     */
    @GetMapping("/countList")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description  = "licId")
    public R<IPage<ItemIdentifyResultsVO>> countList(ItemIdentifyResultsDTO dto,Query query) {

        return R.data(iItemIdentifyResultsService.countListGroupByDate(Condition.getPage(query),dto));
    }

    /**
     * 获取当前用户的所有品规识别记录，包含零售户信息
     */
    @GetMapping("/userRecords")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "获取当前用户的所有品规识别记录", description = "分页查询，包含零售户信息")
    public R<IPage<ItemIdentifyResultsWithRetailerVO>> getUserRecords(ItemIdentifyResultsDTO dto, Query query) {
        return R.data(iItemIdentifyResultsService.listAllWithRetailerInfo(Condition.getPage(query), dto));
    }

}
