/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.system.utils;

import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 密码生成工具类
 *
 * <AUTHOR>
 */
public class PasswordUtil {

    private static final String LOWERCASE_CHARS = "abcdefghijklmnopqrstuvwxyz";
    private static final String UPPERCASE_CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private static final String DIGITS = "**********";
    private static final String SPECIAL_CHARS = "!@#$%^&*()_-+=<>?";

    private static final SecureRandom RANDOM = new SecureRandom();

    /**
     * 生成符合密码策略的随机密码
     *
     * @param length 密码长度
     * @return 随机密码
     */
    public static String generateStrongPassword(int length) {
        if (length < 12) {
            length = 12; // 确保密码长度至少为12位
        }

        // 确保密码包含至少一个大写字母、一个小写字母、一个数字和一个特殊字符
        StringBuilder password = new StringBuilder(length);

        // 添加至少一个大写字母
        password.append(UPPERCASE_CHARS.charAt(RANDOM.nextInt(UPPERCASE_CHARS.length())));

        // 添加至少一个小写字母
        password.append(LOWERCASE_CHARS.charAt(RANDOM.nextInt(LOWERCASE_CHARS.length())));

        // 添加至少一个数字
        password.append(DIGITS.charAt(RANDOM.nextInt(DIGITS.length())));

        // 添加至少一个特殊字符
        password.append(SPECIAL_CHARS.charAt(RANDOM.nextInt(SPECIAL_CHARS.length())));

        // 添加剩余的随机字符
        String allChars = LOWERCASE_CHARS + UPPERCASE_CHARS + DIGITS + SPECIAL_CHARS;
        for (int i = 4; i < length; i++) {
            password.append(allChars.charAt(RANDOM.nextInt(allChars.length())));
        }

        // 打乱字符顺序
        List<Character> charList = new ArrayList<>();
        for (char c : password.toString().toCharArray()) {
            charList.add(c);
        }
        Collections.shuffle(charList, RANDOM);

        StringBuilder shuffled = new StringBuilder(length);
        for (char c : charList) {
            shuffled.append(c);
        }

        return shuffled.toString();
    }

    public static boolean validatePasswordComplexity(String password) {

        // 检查密码长度
        if (password.length() < 6 || password.length() > 12) {
            return false;
        }

        // 检查是否包含大写字母
        if (!Pattern.compile("[A-Z]").matcher(password).find()) {
            return false;
        }

        // 检查是否包含小写字母
        if (!Pattern.compile("[a-z]").matcher(password).find()) {
            return false;
        }

        // 检查是否包含数字
        if (!Pattern.compile("\\d").matcher(password).find()) {
            return false;
        }

        // 检查是否包含特殊字符
        if (!Pattern.compile("[^a-zA-Z0-9]").matcher(password).find()) {
            return false;
        }

        return true;
    }
}
