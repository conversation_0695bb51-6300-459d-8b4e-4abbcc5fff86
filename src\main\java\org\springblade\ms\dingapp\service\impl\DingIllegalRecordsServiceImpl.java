package org.springblade.ms.dingapp.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.utils.Func;
import org.springblade.ms.basic.pojo.entity.YhytLicenseEntity;
import org.springblade.ms.basic.service.IYhytLicenseService;
import org.springblade.ms.basic.service.YhytLicenseUnlicensedService;
import org.springblade.ms.common.utils.ApiCenterUtil;
import org.springblade.ms.common.utils.ThreeTierDataFetcher;
import org.springblade.ms.dingapp.service.IDingIllegalRecordsService;
import org.springblade.ms.illegalrecords.pojo.entity.IllegalRecordsEntity;
import org.springblade.ms.illegalrecords.pojo.vo.IllegalRecordsVO;
import org.springblade.ms.illegalrecords.service.IIllegalRecordsService;
import org.springblade.ms.reportcomplaint.mapper.ReportIllegalLabelMapper;
import org.springblade.ms.reportcomplaint.pojo.entity.ReportIllegalLabelEntity;
import org.springblade.ms.reportcomplaint.pojo.vo.ReportIllegalLabelVO;
import org.springblade.ms.reportcomplaint.service.IReportIllegalLabelService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-02-16 18:39
 */
@Service
@Slf4j
public class DingIllegalRecordsServiceImpl implements IDingIllegalRecordsService {

    private final IYhytLicenseService yhytLicenseService;
    private final IIllegalRecordsService illegalRecordsService;
    private final ReportIllegalLabelMapper reportIllegalLabelMapper;
    private final RedisTemplate<String, Object> redisTemplate;

    private static final String REDIS_KEY_PREFIX = "ms:illegalRecords:custCode:";
    private static final int REDIS_EXPIRE_DAYS = 1; // 1天过期
    private static final long CACHE_REFRESH_INTERVAL = 8 * 60 * 60 * 1000; // 8小时刷新间隔

    private final ThreeTierDataFetcher<IllegalRecordsEntity, String> dataFetcher;

    /**
     * 构造方法，初始化数据获取器
     */
    public DingIllegalRecordsServiceImpl(IYhytLicenseService yhytLicenseService, IIllegalRecordsService illegalRecordsService, ReportIllegalLabelMapper reportIllegalLabelMapper, RedisTemplate<String, Object> redisTemplate) {
        this.yhytLicenseService = yhytLicenseService;
        this.illegalRecordsService = illegalRecordsService;
        this.reportIllegalLabelMapper = reportIllegalLabelMapper;
        this.redisTemplate = redisTemplate;

        // 初始化缓存配置
        ThreeTierDataFetcher.CacheConfig cacheConfig = ThreeTierDataFetcher.CacheConfig.builder()
                .keyPrefix(REDIS_KEY_PREFIX)
                .expireDays(REDIS_EXPIRE_DAYS)
                .refreshIntervalMs(CACHE_REFRESH_INTERVAL)
                .apiTimeoutSeconds(5)
                .dbTimeoutSeconds(3)
                .build();

        // 初始化数据获取器
        this.dataFetcher = new ThreeTierDataFetcher<>(
                redisTemplate,
                cacheConfig,
                custCode -> REDIS_KEY_PREFIX + custCode,  // 键生成器
                this::getFromApi,                         // API数据获取器
                this::getFromDatabase                     // 数据库数据获取器
        );
    }

    @Override
    public IPage<IllegalRecordsVO> selectPage(IPage<ReportIllegalLabelEntity> page, String yhytId) {
        ReportIllegalLabelVO vo = new ReportIllegalLabelVO();

        List<IllegalRecordsVO> list=new ArrayList<>();
        List<ReportIllegalLabelVO> reportIllegalLabelVOS = reportIllegalLabelMapper.listLegalPageBycustCode(page, yhytId);


//        List<ReportIllegalLabelEntity> reportIllegalLabelVOS = reportIllegalLabelService.list(queryWrapper);
        for (ReportIllegalLabelEntity reportIllegalLabelVO : reportIllegalLabelVOS) {
            IllegalRecordsEntity illegalRecordsEntity = illegalRecordsService.getById(reportIllegalLabelVO.getObjId());
            IllegalRecordsVO vo1 = new IllegalRecordsVO();
            BeanUtil.copyProperties(illegalRecordsEntity,vo1);
            list.add(vo1);
        }

        IPage<IllegalRecordsVO> illegalRecordsVOPage = new Page<>();
        BeanUtil.copyProperties(page, illegalRecordsVOPage);

        //排序
        list.sort(Comparator.comparing(IllegalRecordsVO::getRegTime).reversed());
        illegalRecordsVOPage.setRecords(list);
        return illegalRecordsVOPage;
    }


    /**
     * 根据客户编码获取案件信息列表
     * 使用三层查询模式：Redis > API > 数据库
     *
     * @param custCode 客户编码
     * @return 案件信息列表
     */
    @Override
    public List<IllegalRecordsEntity> getListByCustCode(String custCode) {
        if (Func.isEmpty(custCode)) {
            return new ArrayList<>();
        }

        // 使用通用数据获取器获取数据
        return dataFetcher.fetchData(custCode);
    }

    /**
     * 从API获取数据
     *
     * @param custCode 客户编码
     * @return 案件信息列表
     */
    private List<IllegalRecordsEntity> getFromApi(String custCode) {
        YhytLicenseEntity yhytLicenseEntity = yhytLicenseService.getById(custCode);
        String custUuid = yhytLicenseEntity.getRetailerUuid();

        List<IllegalRecordsEntity> resultList = new ArrayList<>();
        try {
            // 第一个API调用 - 获取案件基本信息
            Map<String, Object> sv25127YP3Y9Params = new HashMap<>();
            sv25127YP3Y9Params.put("LSHBS", custUuid);
            sv25127YP3Y9Params.put("PAGENUM", 1);
            sv25127YP3Y9Params.put("PAGESIZE", 1000);

            String sv25127YP3Y9Response = ApiCenterUtil.send("sv25127YP3Y9", sv25127YP3Y9Params);
            if (Func.isEmpty(sv25127YP3Y9Response)) {
                log.warn("API sv25127YP3Y9 返回为空，custUuid: {}", custUuid);
                return resultList;
            }

            JSONObject sv25127YP3Y9Json = JSONUtil.parseObj(sv25127YP3Y9Response);
            JSONObject sv25127YP3Y9Data = sv25127YP3Y9Json.getJSONObject("data");

            if (sv25127YP3Y9Data == null || sv25127YP3Y9Data.getInt("errcode", -1) != 0) {
                log.warn("API sv25127YP3Y9 返回错误，custUuid: {}, errcode: {}, errmsg: {}",
                        custUuid,
                        sv25127YP3Y9Data != null ? sv25127YP3Y9Data.getInt("errcode", -1) : "unknown",
                        sv25127YP3Y9Data != null ? sv25127YP3Y9Data.getStr("errmsg", "") : "unknown");
                return resultList;
            }

            JSONArray sv25127YP3Y9Items = sv25127YP3Y9Data.getJSONObject("data").getJSONArray("data");
            if (sv25127YP3Y9Items == null || sv25127YP3Y9Items.isEmpty()) {
                log.info("API sv25127YP3Y9 未返回数据，custUuid: {}", custUuid);
                return resultList;
            }

            // 处理每个案件
            for (int i = 0; i < sv25127YP3Y9Items.size(); i++) {
                JSONObject sv25127YP3Y9Item = sv25127YP3Y9Items.getJSONObject(i);
                String caseUuid = sv25127YP3Y9Item.getStr("AJBS");

                // 第二个API调用 - 获取案件详细信息
                Map<String, Object> sv2512720VUKParams = new HashMap<>();
                sv2512720VUKParams.put("AJUUID", caseUuid);
                sv2512720VUKParams.put("PAGENUM", 1);
                sv2512720VUKParams.put("PAGESIZE", 1000);

                String sv2512720VUKResponse = ApiCenterUtil.send("sv2512720VUK", sv2512720VUKParams);
                if (Func.isEmpty(sv2512720VUKResponse)) {
                    log.warn("API sv2512720VUK 返回为空，caseUuid: {}", caseUuid);
                    continue;
                }

                JSONObject sv2512720VUKJson = JSONUtil.parseObj(sv2512720VUKResponse);
                JSONObject sv2512720VUKData = sv2512720VUKJson.getJSONObject("data");

                if (sv2512720VUKData == null || sv2512720VUKData.getInt("errcode", -1) != 0) {
                    log.warn("API sv2512720VUK 返回错误，caseUuid: {}, errcode: {}, errmsg: {}",
                            caseUuid,
                            sv2512720VUKData != null ? sv2512720VUKData.getInt("errcode", -1) : "unknown",
                            sv2512720VUKData != null ? sv2512720VUKData.getStr("errmsg", "") : "unknown");
                    continue;
                }

                JSONArray sv2512720VUKItems = sv2512720VUKData.getJSONObject("data").getJSONArray("data");
                if (sv2512720VUKItems == null || sv2512720VUKItems.isEmpty()) {
                    log.info("API sv2512720VUK 未返回数据，caseUuid: {}", caseUuid);
                    continue;
                }

                // 处理案件详情
                for (int j = 0; j < sv2512720VUKItems.size(); j++) {
                    JSONObject sv2512720VUKItem = sv2512720VUKItems.getJSONObject(j);

                    // 创建案件实体
                    IllegalRecordsEntity entity = new IllegalRecordsEntity();

                    // 设置客户编码
                    entity.setCaseUuid(sv2512720VUKItem.getStr("AJUUID"));
                    entity.setCaseName(sv2512720VUKItem.getStr("AJMC"));
                    entity.setCaseCode(sv2512720VUKItem.getStr("AJBH"));
                    entity.setCaseYear(sv2512720VUKItem.getInt("AJSSND"));

                    // 处理日期字段
                    try {
                        String caseDateStr = sv2512720VUKItem.getStr("AFSJ");
                        if (!Func.isEmpty(caseDateStr) && !caseDateStr.equals("\\N")) {
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            entity.setCaseDate(sdf.parse(caseDateStr));
                        }
                    } catch (Exception e) {
                        log.warn("解析案发时间失败: {}", e.getMessage());
                    }

                    entity.setCasePlace(sv2512720VUKItem.getStr("AFDD"));

                    // 处理经纬度
                    String placeGisX = sv2512720VUKItem.getStr("GISJD");
                    if (!Func.isEmpty(placeGisX) && !placeGisX.equals("\\N")) {
                        try {
                            entity.setPlaceGisX(new BigDecimal(placeGisX));
                        } catch (Exception e) {
                            log.warn("解析经度失败: {}", e.getMessage());
                        }
                    }

                    String placeGisY = sv2512720VUKItem.getStr("GISWD");
                    if (!Func.isEmpty(placeGisY) && !placeGisY.equals("\\N")) {
                        try {
                            entity.setPlaceGisY(new BigDecimal(placeGisY));
                        } catch (Exception e) {
                            log.warn("解析纬度失败: {}", e.getMessage());
                        }
                    }

                    entity.setCaseOfAction(sv2512720VUKItem.getStr("AY"));
                    entity.setCaseProperty(sv2512720VUKItem.getStr("AJXZ"));
                    entity.setRegDocNo(sv2512720VUKItem.getStr("LAH"));

                    // 处理立案时间
                    try {
                        String regTimeStr = sv2512720VUKItem.getStr("LASJ");
                        if (!Func.isEmpty(regTimeStr) && !regTimeStr.equals("\\N")) {
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                            entity.setRegTime(sdf.parse(regTimeStr).toInstant()
                                    .atZone(TimeZone.getDefault().toZoneId())
                                    .toLocalDate());
                        }
                    } catch (Exception e) {
                        log.warn("解析立案时间失败: {}", e.getMessage());
                    }

                    // 处理上报日期
                    try {
                        String reportDateStr = sv2512720VUKItem.getStr("SBRQ");
                        if (!Func.isEmpty(reportDateStr) && !reportDateStr.equals("\\N")) {
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            entity.setReportDate(sdf.parse(reportDateStr));
                        }
                    } catch (Exception e) {
                        log.warn("解析上报日期失败: {}", e.getMessage());
                    }

                    entity.setCaseType(sv2512720VUKItem.getStr("AJLX"));

                    // 处理是否结案
                    String isFinshedStr = sv2512720VUKItem.getStr("SFJA");
                    if (!Func.isEmpty(isFinshedStr) && !isFinshedStr.equals("\\N")) {
                        entity.setIsFinshed(Integer.parseInt(isFinshedStr));
                    }

                    // 处理结案日期
                    try {
                        String finshDateStr = sv2512720VUKItem.getStr("JARQ");
                        if (!Func.isEmpty(finshDateStr) && !finshDateStr.equals("\\N")) {
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            entity.setFinshDate(sdf.parse(finshDateStr));
                        }
                    } catch (Exception e) {
                        log.warn("解析结案日期失败: {}", e.getMessage());
                    }

                    // 处理是否12313
                    String isReportHotlineStr = sv2512720VUKItem.getStr("SF12313");
                    if (!Func.isEmpty(isReportHotlineStr) && !isReportHotlineStr.equals("\\N")) {
                        entity.setIsReportHotline(Integer.parseInt(isReportHotlineStr));
                    }

                    entity.setApplyCode(sv2512720VUKItem.getStr("SLBH"));

                    // 处理行政处罚日期
                    try {
                        String punishDecideDateStr = sv2512720VUKItem.getStr("XZCFRQ");
                        if (!Func.isEmpty(punishDecideDateStr) && !punishDecideDateStr.equals("\\N")) {
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            entity.setPunishDecideDate(sdf.parse(punishDecideDateStr));
                        }
                    } catch (Exception e) {
                        log.warn("解析行政处罚日期失败: {}", e.getMessage());
                    }

                    entity.setDecideFullNo(sv2512720VUKItem.getStr("JDCFWSH"));

                    resultList.add(entity);
                }
            }

        } catch (Exception e) {
            log.error("从API获取数据失败，custCode: {}", custUuid, e);
        }

        return resultList;
    }

    /**
     * 从数据库获取数据
     *
     * @param custCode 客户编码
     * @return 案件信息列表
     */
    private List<IllegalRecordsEntity> getFromDatabase(String custCode) {
        try {
            // 查询关联标签
            ReportIllegalLabelEntity reportIllegalLabelEntity = new ReportIllegalLabelEntity();
            reportIllegalLabelEntity.setLabelId(Long.valueOf(custCode));
            reportIllegalLabelEntity.setLabelType("案件");

            // 直接使用baseMapper查询，避免使用selectList方法
            List<ReportIllegalLabelVO> reportIllegalLabelList = reportIllegalLabelMapper.selectList(reportIllegalLabelEntity);
            if (reportIllegalLabelList.isEmpty()) {
                return new ArrayList<>();
            }

            // 获取所有关联的案件ID
            List<Long> objIds = reportIllegalLabelList.stream()
                    .map(ReportIllegalLabelEntity::getObjId)
                    .toList();

            // 查询案件信息
            List<IllegalRecordsEntity> resultList = new ArrayList<>();
            for (Long objId : objIds) {
                IllegalRecordsEntity entity =  illegalRecordsService.getById(objId);
                if (entity != null) {
                    resultList.add(entity);
                }
            }

            return resultList;
        } catch (Exception e) {
            log.error("从数据库获取数据失败，custCode: {}", custCode, e);
            return new ArrayList<>();
        }
    }
}
