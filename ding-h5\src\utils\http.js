import axios from 'axios'
import { Toast, showFailToast } from 'vant'
import { useUserStore } from '@/stores/user'
import router from '@/router'

// 用于存储正在刷新token的Promise，避免重复刷新
let isRefreshing = false
let failedQueue = []

// 处理队列中的请求
const processQueue = (error, token = null) => {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error)
    } else {
      prom.resolve(token)
    }
  })

  failedQueue = []
}

export const http = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
  timeout: 60000,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8',
    'Blade-Requested-With': 'BladeHttpRequest'
  }
})

http.interceptors.request.use(
  config => {
    const userStore = useUserStore()
    if (userStore.accessToken) {
      config.headers['Blade-Auth'] = 'bearer ' + userStore.accessToken
    }
    config.headers['Authorization'] = 'Basic ' + btoa('rider:rider_secret')

    // 处理POST请求数据
    if (config.method === 'post' && config.data && !(config.data instanceof FormData)) {
      config.data = JSON.stringify(config.data)
    }

    // 处理GET请求参数
    if (config.method === 'get' && config.params) {
      const params = new URLSearchParams(config.params)
      config.url = `${config.url}${config.url.includes('?') ? '&' : '?'}${params.toString()}`
      config.params = undefined
    }

    // 开发环境下处理URL
    // if (process.env.NODE_ENV === 'development') {
    //   const url = config.url
    //   if (url.startsWith('/api/')) {
    //     config.url = url.substring(4)
    //   }
    // }

    return config
  },
  error => {
    return Promise.reject(error)
  }
)

http.interceptors.response.use(
  response => {
    // 处理文件下载
    if (response.config.responseType === 'blob') {
      const contentType = response.headers['content-type']
      if (contentType && contentType.includes('application/json')) {
        return new Promise((resolve, reject) => {
          const reader = new FileReader()
          reader.onload = () => {
            const result = JSON.parse(reader.result)
            if (result.code !== 200) {
              showFailToast(result.msg || '请求失败')
              reject(result)
            } else {
              resolve(response)
            }
          }
          reader.readAsText(response.data)
        })
      }
      return response
    }

    if (response.data.access_token || response.data.key) {
      return response.data
    }

    // // 检查业务层面的401错误（token过期）
    // if (response.data.code === 401) {
    //   const userStore = useUserStore()
    //   userStore.isLogin = false
    //   userStore.clearUserInfo()
    //   showFailToast(response.data.msg || '请求未授权，请重新登录')
    //   // 跳转到登录页
    //   setTimeout(() => {
    //     router.replace('/login')
    //   }, 1500)
    //   return Promise.reject(response)
    // }

    if (response.data.code !== 200) {
      showFailToast(response.data.msg || '请求失败')
      return Promise.reject(response)
    }
    return response.data
  },
  error => {
    const originalRequest = error.config
    const userStore = useUserStore()

    // 检查HTTP状态码401
    if (error.response?.status === 401 && !originalRequest._retry) {
      if (isRefreshing) {
        // 如果正在刷新token，将请求加入队列
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject })
        }).then(token => {
          originalRequest.headers['Blade-Auth'] = 'bearer ' + token
          return http(originalRequest)
        }).catch(err => {
          return Promise.reject(err)
        })
      }

      originalRequest._retry = true
      isRefreshing = true

      // 尝试刷新token
      if (userStore.refreshToken) {
        return new Promise((resolve, reject) => {
          userStore.refreshAccessToken()
            .then(newAccessToken => {
              // 刷新成功，处理队列中的请求
              processQueue(null, newAccessToken)

              // 重试原始请求
              originalRequest.headers['Blade-Auth'] = 'bearer ' + newAccessToken
              resolve(http(originalRequest))
            })
            .catch(refreshError => {
              // 刷新失败，清除用户信息并跳转登录页
              console.error('刷新token失败:', refreshError)
              processQueue(refreshError, null)
              userStore.isLogin = false
              userStore.clearUserInfo()
              showFailToast('登录已过期，请重新登录')
              setTimeout(() => {
                router.replace('/login')
              }, 1500)
              reject(refreshError)
            })
            .finally(() => {
              isRefreshing = false
            })
        })
      } else {
        // 没有refreshToken，直接跳转登录页
        userStore.isLogin = false
        userStore.clearUserInfo()
        showFailToast('登录已过期，请重新登录')
        setTimeout(() => {
          router.replace('/login')
        }, 1500)
      }
    }

    // 其他错误处理
    if (error.response?.data) {
      showFailToast(error.response.data)
    }

    return Promise.reject(error)
  }
)