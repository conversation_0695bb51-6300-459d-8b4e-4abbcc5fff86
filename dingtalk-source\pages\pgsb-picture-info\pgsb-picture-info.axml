<view style="padding-bottom:40px">
  <ant-container >
    <image mode="scaleToFill" src="{{imgurl}}" style="width:100%;height:200px; display:inline-block;margin:0 auto;" />    
    
  </ant-container>
  
    <ant-container
      title="对碰结果"
      className="list"
    >
      <ant-table
        dataSource="{{collisionResult}}"
        columns="{{collistionColumn}}"
      >
      <!-- <view
        slot="item"
        slot-scope="props"
      >
        <view a:if="{{props.item.dataIndex === 'item'}}">
          {{props.row.index+1}}
        </view>
        <view a:if="{{props.item.dataIndex === 'name'}}">
          {{props.item.value}}
        </view>
        <view a:if="{{props.item.dataIndex === 'type'}}">
          {{props.item.value}}
        </view>
      </view> -->
    </ant-table>
    </ant-container>
    <ant-container
      title="识别结果"
      className="list"
    >
      <ant-table
        dataSource="{{identificationResult}}"
        columns="{{identificationColumn}}"
      >
        <!-- <view
          slot="item"
          slot-scope="props"
        >
          <view a:if="{{props.item.dataIndex === 'item'}}">
            {{props.row.index+1}}
          </view>
          <view a:if="{{props.item.dataIndex === 'name'}}">
            {{props.item.value}}
          </view>
 
        </view> -->
      </ant-table>
      <view style="display:flex;align-items:center">
        <text style="line-height:40px">识别结果较差：</text>
        <ant-radio-group
          options="{{options}}"
          value="{{checked}}"
          position="horizontal"
          onChange="checkOnChange"
        ></ant-radio-group>
      </view>
    </ant-container>
</view>