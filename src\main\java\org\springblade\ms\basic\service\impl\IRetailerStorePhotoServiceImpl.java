/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.ms.basic.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.ms.basic.excel.SalOrderExcel;
import org.springblade.ms.basic.mapper.RetailerStorePhotoMapper;
import org.springblade.ms.basic.mapper.SalOrderMapper;
import org.springblade.ms.basic.pojo.entity.RetailerStorePhotoEntity;
import org.springblade.ms.basic.pojo.entity.SalOrderEntity;
import org.springblade.ms.basic.pojo.vo.SalOrderVO;
import org.springblade.ms.basic.service.IRetailerStorePhotoService;
import org.springblade.ms.basic.service.ISalOrderService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 零售户门店照片表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-03
 */
@Service
public class IRetailerStorePhotoServiceImpl extends BaseServiceImpl<RetailerStorePhotoMapper, RetailerStorePhotoEntity> implements IRetailerStorePhotoService {



}
