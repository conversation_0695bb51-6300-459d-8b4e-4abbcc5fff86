package org.springblade.ms.priceStandards.excel;

import lombok.Data;

import java.time.LocalDateTime;
import java.math.BigDecimal;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;
import java.io.Serial;

/**
 * 涉案物品表 Excel实体类
 *
 * <AUTHOR>
 * @since 2025-04-11
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class MsEvidenceYhytExcel implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 序号
     */
    @ColumnWidth(10)
    @ExcelProperty("序号")
    private String serialNumber;

    /**
     * 物品名称（冗余）
     */
    @ColumnWidth(25)
    @ExcelProperty("品规")
    private String productName;

    /**
     * 条形码
     */
    @ColumnWidth(25)
    @ExcelProperty("条形码")
    private String barcode;


    /**
     * 用户选择的物品数量
     */
    @ColumnWidth(15)
    @ExcelProperty("数量")
    private BigDecimal selectedQuantity;

    /**
     * 当前单价，允许修改
     */
    @ColumnWidth(15)
    @ExcelProperty("单价")
    private BigDecimal currentUnitPrice;

    /**
     * 金额（数量 × 单价）
     */
    @ColumnWidth(15)
    @ExcelProperty("金额")
    private BigDecimal amount;

}
