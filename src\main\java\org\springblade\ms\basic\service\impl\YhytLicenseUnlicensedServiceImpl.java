package org.springblade.ms.basic.service.impl;

import org.springblade.ms.basic.pojo.entity.YhytLicenseUnlicensedEntity;
import org.springblade.ms.basic.service.YhytLicenseUnlicensedService;
import org.springblade.ms.basic.mapper.YhytLicenseUnlicensedMapper;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

/**
* <AUTHOR> @description 针对表【ms_yhyt_license_unlicensed】的数据库操作Service实现
* @createDate 2025-04-20 17:55:31
*/
@Service
public class YhytLicenseUnlicensedServiceImpl extends BaseServiceImpl<YhytLicenseUnlicensedMapper, YhytLicenseUnlicensedEntity>
    implements YhytLicenseUnlicensedService{

}




