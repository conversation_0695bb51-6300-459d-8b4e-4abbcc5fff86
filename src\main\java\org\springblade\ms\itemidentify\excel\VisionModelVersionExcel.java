/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.ms.itemidentify.excel;


import lombok.Data;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;
import java.io.Serial;


/**
 * 品规模型版本 Excel实体类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class VisionModelVersionExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private Long id;
	/**
	 * 版本名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("版本名称")
	private String name;
	/**
	 * 版本号
	 */
	@ColumnWidth(20)
	@ExcelProperty("版本号")
	private String version;
	/**
	 * 调用地址
	 */
	@ColumnWidth(20)
	@ExcelProperty("调用地址")
	private String url;
	/**
	 * 备注
	 */
	@ColumnWidth(20)
	@ExcelProperty("备注")
	private String remark;
	/**
	 * 租户ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户ID")
	private String tenantId;
	/**
	 * 是否已删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否已删除")
	private Integer isDeleted;

}
