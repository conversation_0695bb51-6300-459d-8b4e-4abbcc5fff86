CREATE TABLE "BLADEX"."FLW_RU_BATCH_PART"
(
 "ID_" VARCHAR(64) NOT NULL,
 "REV_" INT NULL,
 "BATCH_ID_" VARCHAR(64) NULL,
 "TYPE_" VARCHAR(64) NOT NULL,
 "SCOPE_ID_" VARCHAR(64) NULL,
 "SUB_SCOPE_ID_" VARCHAR(64) NULL,
 "SCOPE_TYPE_" VARCHAR(64) NULL,
 "SEARCH_KEY_" VARCHAR(255) NULL,
 "SEARCH_KEY2_" VARCHAR(255) NULL,
 "CREATE_TIME_" TIMESTAMP(0) NOT NULL,
 "COMPLETE_TIME_" TIMESTAMP(0) NULL,
 "STATUS_" VARCHAR(255) NULL,
 "RESULT_DOC_ID_" VARCHAR(64) NULL,
 "TENANT_ID_" VARCHAR(255) DEFAULT ''
 NULL
);

CREATE TABLE "BLADEX"."FLW_RU_BATCH"
(
 "ID_" VARCHAR(64) NOT NULL,
 "REV_" INT NULL,
 "TYPE_" VARCHAR(64) NOT NULL,
 "SEARCH_KEY_" VARCHAR(255) NULL,
 "SEARCH_KEY2_" VARCHAR(255) NULL,
 "CREATE_TIME_" TIMESTAMP(0) NOT NULL,
 "COMPLETE_TIME_" TIMESTAMP(0) NULL,
 "STATUS_" VARCHAR(255) NULL,
 "BATCH_DOC_ID_" VARCHAR(64) NULL,
 "TENANT_ID_" VARCHAR(255) DEFAULT ''
 NULL
);

CREATE TABLE "BLADEX"."FLW_EV_DATABASECHANGELOGLOCK"
(
 "ID" INT NOT NULL,
 "LOCKED" TINYINT NOT NULL,
 "LOCKGRANTED" TIMESTAMP(0) NULL,
 "LOCKEDBY" VARCHAR(255) NULL
);

CREATE TABLE "BLADEX"."FLW_EV_DATABASECHANGELOG"
(
 "ID" VARCHAR(255) NOT NULL,
 "AUTHOR" VARCHAR(255) NOT NULL,
 "FILENAME" VARCHAR(255) NOT NULL,
 "DATEEXECUTED" TIMESTAMP(0) NOT NULL,
 "ORDEREXECUTED" INT NOT NULL,
 "EXECTYPE" VARCHAR(10) NOT NULL,
 "MD5SUM" VARCHAR(35) NULL,
 "DESCRIPTION" VARCHAR(255) NULL,
 "COMMENTS" VARCHAR(255) NULL,
 "TAG" VARCHAR(255) NULL,
 "LIQUIBASE" VARCHAR(20) NULL,
 "CONTEXTS" VARCHAR(255) NULL,
 "LABELS" VARCHAR(255) NULL,
 "DEPLOYMENT_ID" VARCHAR(10) NULL
);

CREATE TABLE "BLADEX"."FLW_EVENT_RESOURCE"
(
 "ID_" VARCHAR(255) NOT NULL,
 "NAME_" VARCHAR(255) NULL,
 "DEPLOYMENT_ID_" VARCHAR(255) NULL,
 "RESOURCE_BYTES_" BLOB NULL
);

CREATE TABLE "BLADEX"."FLW_EVENT_DEPLOYMENT"
(
 "ID_" VARCHAR(255) NOT NULL,
 "NAME_" VARCHAR(255) NULL,
 "CATEGORY_" VARCHAR(255) NULL,
 "DEPLOY_TIME_" TIMESTAMP(0) NULL,
 "TENANT_ID_" VARCHAR(255) NULL,
 "PARENT_DEPLOYMENT_ID_" VARCHAR(255) NULL
);

CREATE TABLE "BLADEX"."FLW_EVENT_DEFINITION"
(
 "ID_" VARCHAR(255) NOT NULL,
 "NAME_" VARCHAR(255) NULL,
 "VERSION_" INT NULL,
 "KEY_" VARCHAR(255) NULL,
 "CATEGORY_" VARCHAR(255) NULL,
 "DEPLOYMENT_ID_" VARCHAR(255) NULL,
 "TENANT_ID_" VARCHAR(255) NULL,
 "RESOURCE_NAME_" VARCHAR(255) NULL,
 "DESCRIPTION_" VARCHAR(255) NULL
);

CREATE TABLE "BLADEX"."FLW_CHANNEL_DEFINITION"
(
 "ID_" VARCHAR(255) NOT NULL,
 "NAME_" VARCHAR(255) NULL,
 "VERSION_" INT NULL,
 "KEY_" VARCHAR(255) NULL,
 "CATEGORY_" VARCHAR(255) NULL,
 "DEPLOYMENT_ID_" VARCHAR(255) NULL,
 "CREATE_TIME_" TIMESTAMP(0) NULL,
 "TENANT_ID_" VARCHAR(255) NULL,
 "RESOURCE_NAME_" VARCHAR(255) NULL,
 "DESCRIPTION_" VARCHAR(255) NULL,
 "TYPE_" VARCHAR(255) NULL,
 "IMPLEMENTATION_" VARCHAR(255) NULL
);

CREATE TABLE "BLADEX"."ACT_RU_VARIABLE"
(
 "ID_" VARCHAR(64) NOT NULL,
 "REV_" INT NULL,
 "TYPE_" VARCHAR(255) NOT NULL,
 "NAME_" VARCHAR(255) NOT NULL,
 "EXECUTION_ID_" VARCHAR(64) NULL,
 "PROC_INST_ID_" VARCHAR(64) NULL,
 "TASK_ID_" VARCHAR(64) NULL,
 "SCOPE_ID_" VARCHAR(255) NULL,
 "SUB_SCOPE_ID_" VARCHAR(255) NULL,
 "SCOPE_TYPE_" VARCHAR(255) NULL,
 "BYTEARRAY_ID_" VARCHAR(64) NULL,
 "DOUBLE_" DOUBLE NULL,
 "LONG_" BIGINT NULL,
 "TEXT_" VARCHAR(3900) NULL,
 "TEXT2_" VARCHAR(3900) NULL,
 "META_INFO_" VARCHAR(3900) NULL
);

CREATE TABLE "BLADEX"."ACT_RU_TIMER_JOB"
(
 "ID_" VARCHAR(64) NOT NULL,
 "REV_" INT NULL,
 "TYPE_" VARCHAR(255) NOT NULL,
 "LOCK_EXP_TIME_" TIMESTAMP(0) NULL,
 "LOCK_OWNER_" VARCHAR(255) NULL,
 "EXCLUSIVE_" TINYINT NULL,
 "EXECUTION_ID_" VARCHAR(64) NULL,
 "PROCESS_INSTANCE_ID_" VARCHAR(64) NULL,
 "PROC_DEF_ID_" VARCHAR(64) NULL,
 "ELEMENT_ID_" VARCHAR(255) NULL,
 "ELEMENT_NAME_" VARCHAR(255) NULL,
 "SCOPE_ID_" VARCHAR(255) NULL,
 "SUB_SCOPE_ID_" VARCHAR(255) NULL,
 "SCOPE_TYPE_" VARCHAR(255) NULL,
 "SCOPE_DEFINITION_ID_" VARCHAR(255) NULL,
 "RETRIES_" INT NULL,
 "EXCEPTION_STACK_ID_" VARCHAR(64) NULL,
 "EXCEPTION_MSG_" VARCHAR(3900) NULL,
 "DUEDATE_" TIMESTAMP(0) NULL,
 "REPEAT_" VARCHAR(255) NULL,
 "HANDLER_TYPE_" VARCHAR(255) NULL,
 "HANDLER_CFG_" VARCHAR(3900) NULL,
 "CUSTOM_VALUES_ID_" VARCHAR(64) NULL,
 "CREATE_TIME_" TIMESTAMP(0) NULL,
 "TENANT_ID_" VARCHAR(255) DEFAULT ''
 NULL,
 "CATEGORY_" VARCHAR(255) NULL,
 "CORRELATION_ID_" VARCHAR(255) NULL
);

CREATE TABLE "BLADEX"."ACT_RU_TASK"
(
 "ID_" VARCHAR(64) NOT NULL,
 "REV_" INT NULL,
 "EXECUTION_ID_" VARCHAR(64) NULL,
 "PROC_INST_ID_" VARCHAR(64) NULL,
 "PROC_DEF_ID_" VARCHAR(64) NULL,
 "TASK_DEF_ID_" VARCHAR(64) NULL,
 "SCOPE_ID_" VARCHAR(255) NULL,
 "SUB_SCOPE_ID_" VARCHAR(255) NULL,
 "SCOPE_TYPE_" VARCHAR(255) NULL,
 "SCOPE_DEFINITION_ID_" VARCHAR(255) NULL,
 "NAME_" VARCHAR(255) NULL,
 "PARENT_TASK_ID_" VARCHAR(64) NULL,
 "DESCRIPTION_" VARCHAR(3900) NULL,
 "TASK_DEF_KEY_" VARCHAR(255) NULL,
 "OWNER_" VARCHAR(255) NULL,
 "ASSIGNEE_" VARCHAR(255) NULL,
 "DELEGATION_" VARCHAR(64) NULL,
 "PRIORITY_" INT NULL,
 "CREATE_TIME_" TIMESTAMP(0) NULL,
 "DUE_DATE_" TIMESTAMP(0) NULL,
 "CATEGORY_" VARCHAR(255) NULL,
 "SUSPENSION_STATE_" INT NULL,
 "TENANT_ID_" VARCHAR(255) DEFAULT ''
 NULL,
 "FORM_KEY_" VARCHAR(255) NULL,
 "CLAIM_TIME_" TIMESTAMP(0) NULL,
 "IS_COUNT_ENABLED_" TINYINT NULL,
 "VAR_COUNT_" INT NULL,
 "ID_LINK_COUNT_" INT NULL,
 "SUB_TASK_COUNT_" INT NULL,
 "PROPAGATED_STAGE_INST_ID_" VARCHAR(255) NULL,
 "STATE_" VARCHAR(255) NULL,
 "IN_PROGRESS_TIME_" TIMESTAMP(0) NULL,
 "IN_PROGRESS_STARTED_BY_" VARCHAR(255) NULL,
 "CLAIMED_BY_" VARCHAR(255) NULL,
 "SUSPENDED_TIME_" TIMESTAMP(0) NULL,
 "SUSPENDED_BY_" VARCHAR(255) NULL,
 "IN_PROGRESS_DUE_DATE_" TIMESTAMP(0) NULL
);

CREATE TABLE "BLADEX"."ACT_RU_SUSPENDED_JOB"
(
 "ID_" VARCHAR(64) NOT NULL,
 "REV_" INT NULL,
 "TYPE_" VARCHAR(255) NOT NULL,
 "EXCLUSIVE_" TINYINT NULL,
 "EXECUTION_ID_" VARCHAR(64) NULL,
 "PROCESS_INSTANCE_ID_" VARCHAR(64) NULL,
 "PROC_DEF_ID_" VARCHAR(64) NULL,
 "ELEMENT_ID_" VARCHAR(255) NULL,
 "ELEMENT_NAME_" VARCHAR(255) NULL,
 "SCOPE_ID_" VARCHAR(255) NULL,
 "SUB_SCOPE_ID_" VARCHAR(255) NULL,
 "SCOPE_TYPE_" VARCHAR(255) NULL,
 "SCOPE_DEFINITION_ID_" VARCHAR(255) NULL,
 "RETRIES_" INT NULL,
 "EXCEPTION_STACK_ID_" VARCHAR(64) NULL,
 "EXCEPTION_MSG_" VARCHAR(3900) NULL,
 "DUEDATE_" TIMESTAMP(0) NULL,
 "REPEAT_" VARCHAR(255) NULL,
 "HANDLER_TYPE_" VARCHAR(255) NULL,
 "HANDLER_CFG_" VARCHAR(3900) NULL,
 "CUSTOM_VALUES_ID_" VARCHAR(64) NULL,
 "CREATE_TIME_" TIMESTAMP(0) NULL,
 "TENANT_ID_" VARCHAR(255) DEFAULT ''
 NULL,
 "CATEGORY_" VARCHAR(255) NULL,
 "CORRELATION_ID_" VARCHAR(255) NULL
);

CREATE TABLE "BLADEX"."ACT_RU_JOB"
(
 "ID_" VARCHAR(64) NOT NULL,
 "REV_" INT NULL,
 "TYPE_" VARCHAR(255) NOT NULL,
 "LOCK_EXP_TIME_" TIMESTAMP(0) NULL,
 "LOCK_OWNER_" VARCHAR(255) NULL,
 "EXCLUSIVE_" TINYINT NULL,
 "EXECUTION_ID_" VARCHAR(64) NULL,
 "PROCESS_INSTANCE_ID_" VARCHAR(64) NULL,
 "PROC_DEF_ID_" VARCHAR(64) NULL,
 "ELEMENT_ID_" VARCHAR(255) NULL,
 "ELEMENT_NAME_" VARCHAR(255) NULL,
 "SCOPE_ID_" VARCHAR(255) NULL,
 "SUB_SCOPE_ID_" VARCHAR(255) NULL,
 "SCOPE_TYPE_" VARCHAR(255) NULL,
 "SCOPE_DEFINITION_ID_" VARCHAR(255) NULL,
 "RETRIES_" INT NULL,
 "EXCEPTION_STACK_ID_" VARCHAR(64) NULL,
 "EXCEPTION_MSG_" VARCHAR(3900) NULL,
 "DUEDATE_" TIMESTAMP(0) NULL,
 "REPEAT_" VARCHAR(255) NULL,
 "HANDLER_TYPE_" VARCHAR(255) NULL,
 "HANDLER_CFG_" VARCHAR(3900) NULL,
 "CUSTOM_VALUES_ID_" VARCHAR(64) NULL,
 "CREATE_TIME_" TIMESTAMP(0) NULL,
 "TENANT_ID_" VARCHAR(255) DEFAULT ''
 NULL,
 "CATEGORY_" VARCHAR(255) NULL,
 "CORRELATION_ID_" VARCHAR(255) NULL
);

CREATE TABLE "BLADEX"."ACT_RU_IDENTITYLINK"
(
 "ID_" VARCHAR(64) NOT NULL,
 "REV_" INT NULL,
 "GROUP_ID_" VARCHAR(255) NULL,
 "TYPE_" VARCHAR(255) NULL,
 "USER_ID_" VARCHAR(255) NULL,
 "TASK_ID_" VARCHAR(64) NULL,
 "PROC_INST_ID_" VARCHAR(64) NULL,
 "PROC_DEF_ID_" VARCHAR(64) NULL,
 "SCOPE_ID_" VARCHAR(255) NULL,
 "SCOPE_TYPE_" VARCHAR(255) NULL,
 "SCOPE_DEFINITION_ID_" VARCHAR(255) NULL,
 "SUB_SCOPE_ID_" VARCHAR(255) NULL
);

CREATE TABLE "BLADEX"."ACT_RU_HISTORY_JOB"
(
 "ID_" VARCHAR(64) NOT NULL,
 "REV_" INT NULL,
 "LOCK_EXP_TIME_" TIMESTAMP(0) NULL,
 "LOCK_OWNER_" VARCHAR(255) NULL,
 "RETRIES_" INT NULL,
 "EXCEPTION_STACK_ID_" VARCHAR(64) NULL,
 "EXCEPTION_MSG_" VARCHAR(3900) NULL,
 "HANDLER_TYPE_" VARCHAR(255) NULL,
 "HANDLER_CFG_" VARCHAR(3900) NULL,
 "CUSTOM_VALUES_ID_" VARCHAR(64) NULL,
 "ADV_HANDLER_CFG_ID_" VARCHAR(64) NULL,
 "CREATE_TIME_" TIMESTAMP(0) NULL,
 "SCOPE_TYPE_" VARCHAR(255) NULL,
 "TENANT_ID_" VARCHAR(255) DEFAULT ''
 NULL
);

CREATE TABLE "BLADEX"."ACT_RU_EXTERNAL_JOB"
(
 "ID_" VARCHAR(64) NOT NULL,
 "REV_" INT NULL,
 "CATEGORY_" VARCHAR(255) NULL,
 "TYPE_" VARCHAR(255) NOT NULL,
 "LOCK_EXP_TIME_" TIMESTAMP(0) NULL,
 "LOCK_OWNER_" VARCHAR(255) NULL,
 "EXCLUSIVE_" TINYINT NULL,
 "EXECUTION_ID_" VARCHAR(64) NULL,
 "PROCESS_INSTANCE_ID_" VARCHAR(64) NULL,
 "PROC_DEF_ID_" VARCHAR(64) NULL,
 "ELEMENT_ID_" VARCHAR(255) NULL,
 "ELEMENT_NAME_" VARCHAR(255) NULL,
 "SCOPE_ID_" VARCHAR(255) NULL,
 "SUB_SCOPE_ID_" VARCHAR(255) NULL,
 "SCOPE_TYPE_" VARCHAR(255) NULL,
 "SCOPE_DEFINITION_ID_" VARCHAR(255) NULL,
 "RETRIES_" INT NULL,
 "EXCEPTION_STACK_ID_" VARCHAR(64) NULL,
 "EXCEPTION_MSG_" VARCHAR(3900) NULL,
 "DUEDATE_" TIMESTAMP(0) NULL,
 "REPEAT_" VARCHAR(255) NULL,
 "HANDLER_TYPE_" VARCHAR(255) NULL,
 "HANDLER_CFG_" VARCHAR(3900) NULL,
 "CUSTOM_VALUES_ID_" VARCHAR(64) NULL,
 "CREATE_TIME_" TIMESTAMP(0) NULL,
 "TENANT_ID_" VARCHAR(255) DEFAULT ''
 NULL,
 "CORRELATION_ID_" VARCHAR(255) NULL
);

CREATE TABLE "BLADEX"."ACT_RU_EXECUTION"
(
 "ID_" VARCHAR(64) NOT NULL,
 "REV_" INT NULL,
 "PROC_INST_ID_" VARCHAR(64) NULL,
 "BUSINESS_KEY_" VARCHAR(255) NULL,
 "PARENT_ID_" VARCHAR(64) NULL,
 "PROC_DEF_ID_" VARCHAR(64) NULL,
 "SUPER_EXEC_" VARCHAR(64) NULL,
 "ROOT_PROC_INST_ID_" VARCHAR(64) NULL,
 "ACT_ID_" VARCHAR(255) NULL,
 "IS_ACTIVE_" TINYINT NULL,
 "IS_CONCURRENT_" TINYINT NULL,
 "IS_SCOPE_" TINYINT NULL,
 "IS_EVENT_SCOPE_" TINYINT NULL,
 "IS_MI_ROOT_" TINYINT NULL,
 "SUSPENSION_STATE_" INT NULL,
 "CACHED_ENT_STATE_" INT NULL,
 "TENANT_ID_" VARCHAR(255) DEFAULT ''
 NULL,
 "NAME_" VARCHAR(255) NULL,
 "START_ACT_ID_" VARCHAR(255) NULL,
 "START_TIME_" TIMESTAMP(0) NULL,
 "START_USER_ID_" VARCHAR(255) NULL,
 "LOCK_TIME_" TIMESTAMP(0) NULL,
 "IS_COUNT_ENABLED_" TINYINT NULL,
 "EVT_SUBSCR_COUNT_" INT NULL,
 "TASK_COUNT_" INT NULL,
 "JOB_COUNT_" INT NULL,
 "TIMER_JOB_COUNT_" INT NULL,
 "SUSP_JOB_COUNT_" INT NULL,
 "DEADLETTER_JOB_COUNT_" INT NULL,
 "VAR_COUNT_" INT NULL,
 "ID_LINK_COUNT_" INT NULL,
 "CALLBACK_ID_" VARCHAR(255) NULL,
 "CALLBACK_TYPE_" VARCHAR(255) NULL,
 "REFERENCE_ID_" VARCHAR(255) NULL,
 "REFERENCE_TYPE_" VARCHAR(255) NULL,
 "PROPAGATED_STAGE_INST_ID_" VARCHAR(255) NULL,
 "LOCK_OWNER_" VARCHAR(255) NULL,
 "EXTERNAL_WORKER_JOB_COUNT_" INT NULL,
 "BUSINESS_STATUS_" VARCHAR(255) NULL
);

CREATE TABLE "BLADEX"."ACT_RU_EVENT_SUBSCR"
(
 "ID_" VARCHAR(64) NOT NULL,
 "REV_" INT NULL,
 "EVENT_TYPE_" VARCHAR(255) NOT NULL,
 "EVENT_NAME_" VARCHAR(255) NULL,
 "EXECUTION_ID_" VARCHAR(64) NULL,
 "PROC_INST_ID_" VARCHAR(64) NULL,
 "ACTIVITY_ID_" VARCHAR(64) NULL,
 "CONFIGURATION_" VARCHAR(255) NULL,
 "CREATED_" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "PROC_DEF_ID_" VARCHAR(64) NULL,
 "SUB_SCOPE_ID_" VARCHAR(64) NULL,
 "SCOPE_ID_" VARCHAR(64) NULL,
 "SCOPE_DEFINITION_ID_" VARCHAR(64) NULL,
 "SCOPE_TYPE_" VARCHAR(64) NULL,
 "TENANT_ID_" VARCHAR(255) DEFAULT ''
 NULL,
 "LOCK_TIME_" TIMESTAMP(0) NULL,
 "LOCK_OWNER_" VARCHAR(255) NULL,
 "SCOPE_DEFINITION_KEY_" VARCHAR(255) NULL
);

CREATE TABLE "BLADEX"."ACT_RU_ENTITYLINK"
(
 "ID_" VARCHAR(64) NOT NULL,
 "REV_" INT NULL,
 "CREATE_TIME_" TIMESTAMP(0) NULL,
 "LINK_TYPE_" VARCHAR(255) NULL,
 "SCOPE_ID_" VARCHAR(255) NULL,
 "SCOPE_TYPE_" VARCHAR(255) NULL,
 "SCOPE_DEFINITION_ID_" VARCHAR(255) NULL,
 "REF_SCOPE_ID_" VARCHAR(255) NULL,
 "REF_SCOPE_TYPE_" VARCHAR(255) NULL,
 "REF_SCOPE_DEFINITION_ID_" VARCHAR(255) NULL,
 "HIERARCHY_TYPE_" VARCHAR(255) NULL,
 "ROOT_SCOPE_ID_" VARCHAR(255) NULL,
 "ROOT_SCOPE_TYPE_" VARCHAR(255) NULL,
 "SUB_SCOPE_ID_" VARCHAR(255) NULL,
 "PARENT_ELEMENT_ID_" VARCHAR(255) NULL
);

CREATE TABLE "BLADEX"."ACT_RU_DEADLETTER_JOB"
(
 "ID_" VARCHAR(64) NOT NULL,
 "REV_" INT NULL,
 "TYPE_" VARCHAR(255) NOT NULL,
 "EXCLUSIVE_" TINYINT NULL,
 "EXECUTION_ID_" VARCHAR(64) NULL,
 "PROCESS_INSTANCE_ID_" VARCHAR(64) NULL,
 "PROC_DEF_ID_" VARCHAR(64) NULL,
 "ELEMENT_ID_" VARCHAR(255) NULL,
 "ELEMENT_NAME_" VARCHAR(255) NULL,
 "SCOPE_ID_" VARCHAR(255) NULL,
 "SUB_SCOPE_ID_" VARCHAR(255) NULL,
 "SCOPE_TYPE_" VARCHAR(255) NULL,
 "SCOPE_DEFINITION_ID_" VARCHAR(255) NULL,
 "EXCEPTION_STACK_ID_" VARCHAR(64) NULL,
 "EXCEPTION_MSG_" VARCHAR(3900) NULL,
 "DUEDATE_" TIMESTAMP(0) NULL,
 "REPEAT_" VARCHAR(255) NULL,
 "HANDLER_TYPE_" VARCHAR(255) NULL,
 "HANDLER_CFG_" VARCHAR(3900) NULL,
 "CUSTOM_VALUES_ID_" VARCHAR(64) NULL,
 "CREATE_TIME_" TIMESTAMP(0) NULL,
 "TENANT_ID_" VARCHAR(255) DEFAULT ''
 NULL,
 "CATEGORY_" VARCHAR(255) NULL,
 "CORRELATION_ID_" VARCHAR(255) NULL
);

CREATE TABLE "BLADEX"."ACT_RU_ACTINST"
(
 "ID_" VARCHAR(64) NOT NULL,
 "REV_" INT DEFAULT 1
 NULL,
 "PROC_DEF_ID_" VARCHAR(64) NOT NULL,
 "PROC_INST_ID_" VARCHAR(64) NOT NULL,
 "EXECUTION_ID_" VARCHAR(64) NOT NULL,
 "ACT_ID_" VARCHAR(255) NOT NULL,
 "TASK_ID_" VARCHAR(64) NULL,
 "CALL_PROC_INST_ID_" VARCHAR(64) NULL,
 "ACT_NAME_" VARCHAR(255) NULL,
 "ACT_TYPE_" VARCHAR(255) NOT NULL,
 "ASSIGNEE_" VARCHAR(255) NULL,
 "START_TIME_" TIMESTAMP(0) NOT NULL,
 "END_TIME_" TIMESTAMP(0) NULL,
 "DURATION_" BIGINT NULL,
 "DELETE_REASON_" VARCHAR(3900) NULL,
 "TENANT_ID_" VARCHAR(255) DEFAULT ''
 NULL,
 "TRANSACTION_ORDER_" INT NULL
);

CREATE TABLE "BLADEX"."ACT_RE_PROCDEF"
(
 "ID_" VARCHAR(64) NOT NULL,
 "REV_" INT NULL,
 "CATEGORY_" VARCHAR(255) NULL,
 "NAME_" VARCHAR(255) NULL,
 "KEY_" VARCHAR(255) NOT NULL,
 "VERSION_" INT NOT NULL,
 "DEPLOYMENT_ID_" VARCHAR(64) NULL,
 "RESOURCE_NAME_" VARCHAR(3900) NULL,
 "DGRM_RESOURCE_NAME_" VARCHAR(3900) NULL,
 "DESCRIPTION_" VARCHAR(3900) NULL,
 "HAS_START_FORM_KEY_" TINYINT NULL,
 "HAS_GRAPHICAL_NOTATION_" TINYINT NULL,
 "SUSPENSION_STATE_" INT NULL,
 "TENANT_ID_" VARCHAR(255) DEFAULT ''
 NULL,
 "ENGINE_VERSION_" VARCHAR(255) NULL,
 "DERIVED_FROM_" VARCHAR(64) NULL,
 "DERIVED_FROM_ROOT_" VARCHAR(64) NULL,
 "DERIVED_VERSION_" INT DEFAULT 0
 NOT NULL
);

CREATE TABLE "BLADEX"."ACT_RE_MODEL"
(
 "ID_" VARCHAR(64) NOT NULL,
 "REV_" INT NULL,
 "NAME_" VARCHAR(255) NULL,
 "KEY_" VARCHAR(255) NULL,
 "CATEGORY_" VARCHAR(255) NULL,
 "CREATE_TIME_" TIMESTAMP(0) NULL,
 "LAST_UPDATE_TIME_" TIMESTAMP(0) NULL,
 "VERSION_" INT NULL,
 "META_INFO_" VARCHAR(3900) NULL,
 "DEPLOYMENT_ID_" VARCHAR(64) NULL,
 "EDITOR_SOURCE_VALUE_ID_" VARCHAR(64) NULL,
 "EDITOR_SOURCE_EXTRA_VALUE_ID_" VARCHAR(64) NULL,
 "TENANT_ID_" VARCHAR(255) DEFAULT ''
 NULL
);

CREATE TABLE "BLADEX"."ACT_RE_DEPLOYMENT"
(
 "ID_" VARCHAR(64) NOT NULL,
 "NAME_" VARCHAR(255) NULL,
 "CATEGORY_" VARCHAR(255) NULL,
 "KEY_" VARCHAR(255) NULL,
 "TENANT_ID_" VARCHAR(255) DEFAULT ''
 NULL,
 "DEPLOY_TIME_" TIMESTAMP(0) NULL,
 "DERIVED_FROM_" VARCHAR(64) NULL,
 "DERIVED_FROM_ROOT_" VARCHAR(64) NULL,
 "PARENT_DEPLOYMENT_ID_" VARCHAR(255) NULL,
 "ENGINE_VERSION_" VARCHAR(255) NULL
);

CREATE TABLE "BLADEX"."ACT_PROCDEF_INFO"
(
 "ID_" VARCHAR(64) NOT NULL,
 "PROC_DEF_ID_" VARCHAR(64) NOT NULL,
 "REV_" INT NULL,
 "INFO_JSON_ID_" VARCHAR(64) NULL
);

CREATE TABLE "BLADEX"."ACT_ID_USER"
(
 "ID_" VARCHAR(64) NOT NULL,
 "REV_" INT NULL,
 "FIRST_" VARCHAR(255) NULL,
 "LAST_" VARCHAR(255) NULL,
 "DISPLAY_NAME_" VARCHAR(255) NULL,
 "EMAIL_" VARCHAR(255) NULL,
 "PWD_" VARCHAR(255) NULL,
 "PICTURE_ID_" VARCHAR(64) NULL,
 "TENANT_ID_" VARCHAR(255) DEFAULT ''
 NULL
);

CREATE TABLE "BLADEX"."ACT_ID_TOKEN"
(
 "ID_" VARCHAR(64) NOT NULL,
 "REV_" INT NULL,
 "TOKEN_VALUE_" VARCHAR(255) NULL,
 "TOKEN_DATE_" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "IP_ADDRESS_" VARCHAR(255) NULL,
 "USER_AGENT_" VARCHAR(255) NULL,
 "USER_ID_" VARCHAR(255) NULL,
 "TOKEN_DATA_" VARCHAR(2000) NULL
);

CREATE TABLE "BLADEX"."ACT_ID_PROPERTY"
(
 "NAME_" VARCHAR(64) NOT NULL,
 "VALUE_" VARCHAR(300) NULL,
 "REV_" INT NULL
);

CREATE TABLE "BLADEX"."ACT_ID_PRIV_MAPPING"
(
 "ID_" VARCHAR(64) NOT NULL,
 "PRIV_ID_" VARCHAR(64) NOT NULL,
 "USER_ID_" VARCHAR(255) NULL,
 "GROUP_ID_" VARCHAR(255) NULL
);

CREATE TABLE "BLADEX"."ACT_ID_PRIV"
(
 "ID_" VARCHAR(64) NOT NULL,
 "NAME_" VARCHAR(255) NOT NULL
);

CREATE TABLE "BLADEX"."ACT_ID_MEMBERSHIP"
(
 "USER_ID_" VARCHAR(64) NOT NULL,
 "GROUP_ID_" VARCHAR(64) NOT NULL
);

CREATE TABLE "BLADEX"."ACT_ID_INFO"
(
 "ID_" VARCHAR(64) NOT NULL,
 "REV_" INT NULL,
 "USER_ID_" VARCHAR(64) NULL,
 "TYPE_" VARCHAR(64) NULL,
 "KEY_" VARCHAR(255) NULL,
 "VALUE_" VARCHAR(255) NULL,
 "PASSWORD_" BLOB NULL,
 "PARENT_ID_" VARCHAR(255) NULL
);

CREATE TABLE "BLADEX"."ACT_ID_GROUP"
(
 "ID_" VARCHAR(64) NOT NULL,
 "REV_" INT NULL,
 "NAME_" VARCHAR(255) NULL,
 "TYPE_" VARCHAR(255) NULL
);

CREATE TABLE "BLADEX"."ACT_ID_BYTEARRAY"
(
 "ID_" VARCHAR(64) NOT NULL,
 "REV_" INT NULL,
 "NAME_" VARCHAR(255) NULL,
 "BYTES_" BLOB NULL
);

CREATE TABLE "BLADEX"."ACT_HI_VARINST"
(
 "ID_" VARCHAR(64) NOT NULL,
 "REV_" INT DEFAULT 1
 NULL,
 "PROC_INST_ID_" VARCHAR(64) NULL,
 "EXECUTION_ID_" VARCHAR(64) NULL,
 "TASK_ID_" VARCHAR(64) NULL,
 "NAME_" VARCHAR(255) NOT NULL,
 "VAR_TYPE_" VARCHAR(100) NULL,
 "SCOPE_ID_" VARCHAR(255) NULL,
 "SUB_SCOPE_ID_" VARCHAR(255) NULL,
 "SCOPE_TYPE_" VARCHAR(255) NULL,
 "BYTEARRAY_ID_" VARCHAR(64) NULL,
 "DOUBLE_" DOUBLE NULL,
 "LONG_" BIGINT NULL,
 "TEXT_" VARCHAR(3900) NULL,
 "TEXT2_" VARCHAR(3900) NULL,
 "CREATE_TIME_" TIMESTAMP(0) NULL,
 "LAST_UPDATED_TIME_" TIMESTAMP(0) NULL,
 "META_INFO_" VARCHAR(3900) NULL
);

CREATE TABLE "BLADEX"."ACT_HI_TSK_LOG"
(
 "ID_" BIGINT IDENTITY(1,1) NOT NULL,
 "TYPE_" VARCHAR(64) NULL,
 "TASK_ID_" VARCHAR(64) NOT NULL,
 "TIME_STAMP_" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "USER_ID_" VARCHAR(255) NULL,
 "DATA_" VARCHAR(3900) NULL,
 "EXECUTION_ID_" VARCHAR(64) NULL,
 "PROC_INST_ID_" VARCHAR(64) NULL,
 "PROC_DEF_ID_" VARCHAR(64) NULL,
 "SCOPE_ID_" VARCHAR(255) NULL,
 "SCOPE_DEFINITION_ID_" VARCHAR(255) NULL,
 "SUB_SCOPE_ID_" VARCHAR(255) NULL,
 "SCOPE_TYPE_" VARCHAR(255) NULL,
 "TENANT_ID_" VARCHAR(255) DEFAULT ''
 NULL
);

CREATE TABLE "BLADEX"."ACT_HI_TASKINST"
(
 "ID_" VARCHAR(64) NOT NULL,
 "REV_" INT DEFAULT 1
 NULL,
 "PROC_DEF_ID_" VARCHAR(64) NULL,
 "TASK_DEF_ID_" VARCHAR(64) NULL,
 "TASK_DEF_KEY_" VARCHAR(255) NULL,
 "PROC_INST_ID_" VARCHAR(64) NULL,
 "EXECUTION_ID_" VARCHAR(64) NULL,
 "SCOPE_ID_" VARCHAR(255) NULL,
 "SUB_SCOPE_ID_" VARCHAR(255) NULL,
 "SCOPE_TYPE_" VARCHAR(255) NULL,
 "SCOPE_DEFINITION_ID_" VARCHAR(255) NULL,
 "NAME_" VARCHAR(255) NULL,
 "PARENT_TASK_ID_" VARCHAR(64) NULL,
 "DESCRIPTION_" VARCHAR(3900) NULL,
 "OWNER_" VARCHAR(255) NULL,
 "ASSIGNEE_" VARCHAR(255) NULL,
 "START_TIME_" TIMESTAMP(0) NOT NULL,
 "CLAIM_TIME_" TIMESTAMP(0) NULL,
 "END_TIME_" TIMESTAMP(0) NULL,
 "DURATION_" BIGINT NULL,
 "DELETE_REASON_" VARCHAR(3900) NULL,
 "PRIORITY_" INT NULL,
 "DUE_DATE_" TIMESTAMP(0) NULL,
 "FORM_KEY_" VARCHAR(255) NULL,
 "CATEGORY_" VARCHAR(255) NULL,
 "TENANT_ID_" VARCHAR(255) DEFAULT ''
 NULL,
 "LAST_UPDATED_TIME_" TIMESTAMP(0) NULL,
 "PROPAGATED_STAGE_INST_ID_" VARCHAR(255) NULL,
 "STATE_" VARCHAR(255) NULL,
 "IN_PROGRESS_TIME_" TIMESTAMP(0) NULL,
 "IN_PROGRESS_STARTED_BY_" VARCHAR(255) NULL,
 "CLAIMED_BY_" VARCHAR(255) NULL,
 "SUSPENDED_TIME_" TIMESTAMP(0) NULL,
 "SUSPENDED_BY_" VARCHAR(255) NULL,
 "COMPLETED_BY_" VARCHAR(255) NULL,
 "IN_PROGRESS_DUE_DATE_" TIMESTAMP(0) NULL
);

CREATE TABLE "BLADEX"."ACT_HI_PROCINST"
(
 "ID_" VARCHAR(64) NOT NULL,
 "REV_" INT DEFAULT 1
 NULL,
 "PROC_INST_ID_" VARCHAR(64) NOT NULL,
 "BUSINESS_KEY_" VARCHAR(255) NULL,
 "PROC_DEF_ID_" VARCHAR(64) NOT NULL,
 "START_TIME_" TIMESTAMP(0) NOT NULL,
 "END_TIME_" TIMESTAMP(0) NULL,
 "DURATION_" BIGINT NULL,
 "START_USER_ID_" VARCHAR(255) NULL,
 "START_ACT_ID_" VARCHAR(255) NULL,
 "END_ACT_ID_" VARCHAR(255) NULL,
 "SUPER_PROCESS_INSTANCE_ID_" VARCHAR(64) NULL,
 "DELETE_REASON_" VARCHAR(3900) NULL,
 "TENANT_ID_" VARCHAR(255) DEFAULT ''
 NULL,
 "NAME_" VARCHAR(255) NULL,
 "CALLBACK_ID_" VARCHAR(255) NULL,
 "CALLBACK_TYPE_" VARCHAR(255) NULL,
 "REFERENCE_ID_" VARCHAR(255) NULL,
 "REFERENCE_TYPE_" VARCHAR(255) NULL,
 "PROPAGATED_STAGE_INST_ID_" VARCHAR(255) NULL,
 "BUSINESS_STATUS_" VARCHAR(255) NULL
);

CREATE TABLE "BLADEX"."ACT_HI_IDENTITYLINK"
(
 "ID_" VARCHAR(64) NOT NULL,
 "GROUP_ID_" VARCHAR(255) NULL,
 "TYPE_" VARCHAR(255) NULL,
 "USER_ID_" VARCHAR(255) NULL,
 "TASK_ID_" VARCHAR(64) NULL,
 "CREATE_TIME_" TIMESTAMP(0) NULL,
 "PROC_INST_ID_" VARCHAR(64) NULL,
 "SCOPE_ID_" VARCHAR(255) NULL,
 "SCOPE_TYPE_" VARCHAR(255) NULL,
 "SCOPE_DEFINITION_ID_" VARCHAR(255) NULL,
 "SUB_SCOPE_ID_" VARCHAR(255) NULL
);

CREATE TABLE "BLADEX"."ACT_HI_ENTITYLINK"
(
 "ID_" VARCHAR(64) NOT NULL,
 "LINK_TYPE_" VARCHAR(255) NULL,
 "CREATE_TIME_" TIMESTAMP(0) NULL,
 "SCOPE_ID_" VARCHAR(255) NULL,
 "SCOPE_TYPE_" VARCHAR(255) NULL,
 "SCOPE_DEFINITION_ID_" VARCHAR(255) NULL,
 "REF_SCOPE_ID_" VARCHAR(255) NULL,
 "REF_SCOPE_TYPE_" VARCHAR(255) NULL,
 "REF_SCOPE_DEFINITION_ID_" VARCHAR(255) NULL,
 "HIERARCHY_TYPE_" VARCHAR(255) NULL,
 "ROOT_SCOPE_ID_" VARCHAR(255) NULL,
 "ROOT_SCOPE_TYPE_" VARCHAR(255) NULL,
 "SUB_SCOPE_ID_" VARCHAR(255) NULL,
 "PARENT_ELEMENT_ID_" VARCHAR(255) NULL
);

CREATE TABLE "BLADEX"."ACT_HI_DETAIL"
(
 "ID_" VARCHAR(64) NOT NULL,
 "TYPE_" VARCHAR(255) NOT NULL,
 "PROC_INST_ID_" VARCHAR(64) NULL,
 "EXECUTION_ID_" VARCHAR(64) NULL,
 "TASK_ID_" VARCHAR(64) NULL,
 "ACT_INST_ID_" VARCHAR(64) NULL,
 "NAME_" VARCHAR(255) NOT NULL,
 "VAR_TYPE_" VARCHAR(255) NULL,
 "REV_" INT NULL,
 "TIME_" TIMESTAMP(0) NOT NULL,
 "BYTEARRAY_ID_" VARCHAR(64) NULL,
 "DOUBLE_" DOUBLE NULL,
 "LONG_" BIGINT NULL,
 "TEXT_" VARCHAR(3900) NULL,
 "TEXT2_" VARCHAR(3900) NULL
);

CREATE TABLE "BLADEX"."ACT_HI_COMMENT"
(
 "ID_" VARCHAR(64) NOT NULL,
 "TYPE_" VARCHAR(255) NULL,
 "TIME_" TIMESTAMP(0) NOT NULL,
 "USER_ID_" VARCHAR(255) NULL,
 "TASK_ID_" VARCHAR(64) NULL,
 "PROC_INST_ID_" VARCHAR(64) NULL,
 "ACTION_" VARCHAR(255) NULL,
 "MESSAGE_" VARCHAR(3900) NULL,
 "FULL_MSG_" BLOB NULL
);

CREATE TABLE "BLADEX"."ACT_HI_ATTACHMENT"
(
 "ID_" VARCHAR(64) NOT NULL,
 "REV_" INT NULL,
 "USER_ID_" VARCHAR(255) NULL,
 "NAME_" VARCHAR(255) NULL,
 "DESCRIPTION_" VARCHAR(3900) NULL,
 "TYPE_" VARCHAR(255) NULL,
 "TASK_ID_" VARCHAR(64) NULL,
 "PROC_INST_ID_" VARCHAR(64) NULL,
 "URL_" VARCHAR(3900) NULL,
 "CONTENT_ID_" VARCHAR(64) NULL,
 "TIME_" TIMESTAMP(0) NULL
);

CREATE TABLE "BLADEX"."ACT_HI_ACTINST"
(
 "ID_" VARCHAR(64) NOT NULL,
 "REV_" INT DEFAULT 1
 NULL,
 "PROC_DEF_ID_" VARCHAR(64) NOT NULL,
 "PROC_INST_ID_" VARCHAR(64) NOT NULL,
 "EXECUTION_ID_" VARCHAR(64) NOT NULL,
 "ACT_ID_" VARCHAR(255) NOT NULL,
 "TASK_ID_" VARCHAR(64) NULL,
 "CALL_PROC_INST_ID_" VARCHAR(64) NULL,
 "ACT_NAME_" VARCHAR(255) NULL,
 "ACT_TYPE_" VARCHAR(255) NOT NULL,
 "ASSIGNEE_" VARCHAR(255) NULL,
 "START_TIME_" TIMESTAMP(0) NOT NULL,
 "END_TIME_" TIMESTAMP(0) NULL,
 "DURATION_" BIGINT NULL,
 "DELETE_REASON_" VARCHAR(3900) NULL,
 "TENANT_ID_" VARCHAR(255) DEFAULT ''
 NULL,
 "TRANSACTION_ORDER_" INT NULL
);

CREATE TABLE "BLADEX"."ACT_GE_PROPERTY"
(
 "NAME_" VARCHAR(64) NOT NULL,
 "VALUE_" VARCHAR(300) NULL,
 "REV_" INT NULL
);

CREATE TABLE "BLADEX"."ACT_GE_BYTEARRAY"
(
 "ID_" VARCHAR(64) NOT NULL,
 "REV_" INT NULL,
 "NAME_" VARCHAR(255) NULL,
 "DEPLOYMENT_ID_" VARCHAR(64) NULL,
 "BYTES_" BLOB NULL,
 "GENERATED_" TINYINT NULL
);

CREATE TABLE "BLADEX"."ACT_FO_FORM_RESOURCE"
(
 "ID_" VARCHAR(255) NOT NULL,
 "NAME_" VARCHAR(255) NULL,
 "DEPLOYMENT_ID_" VARCHAR(255) NULL,
 "RESOURCE_BYTES_" BLOB NULL
);

CREATE TABLE "BLADEX"."ACT_FO_FORM_INSTANCE"
(
 "ID_" VARCHAR(255) NOT NULL,
 "FORM_DEFINITION_ID_" VARCHAR(255) NOT NULL,
 "TASK_ID_" VARCHAR(255) NULL,
 "PROC_INST_ID_" VARCHAR(255) NULL,
 "PROC_DEF_ID_" VARCHAR(255) NULL,
 "SUBMITTED_DATE_" TIMESTAMP(0) NULL,
 "SUBMITTED_BY_" VARCHAR(255) NULL,
 "FORM_VALUES_ID_" VARCHAR(255) NULL,
 "TENANT_ID_" VARCHAR(255) NULL,
 "SCOPE_ID_" VARCHAR(255) NULL,
 "SCOPE_TYPE_" VARCHAR(255) NULL,
 "SCOPE_DEFINITION_ID_" VARCHAR(255) NULL
);

CREATE TABLE "BLADEX"."ACT_FO_FORM_DEPLOYMENT"
(
 "ID_" VARCHAR(255) NOT NULL,
 "NAME_" VARCHAR(255) NULL,
 "CATEGORY_" VARCHAR(255) NULL,
 "DEPLOY_TIME_" TIMESTAMP(0) NULL,
 "TENANT_ID_" VARCHAR(255) NULL,
 "PARENT_DEPLOYMENT_ID_" VARCHAR(255) NULL
);

CREATE TABLE "BLADEX"."ACT_FO_FORM_DEFINITION"
(
 "ID_" VARCHAR(255) NOT NULL,
 "NAME_" VARCHAR(255) NULL,
 "VERSION_" INT NULL,
 "KEY_" VARCHAR(255) NULL,
 "CATEGORY_" VARCHAR(255) NULL,
 "DEPLOYMENT_ID_" VARCHAR(255) NULL,
 "TENANT_ID_" VARCHAR(255) NULL,
 "RESOURCE_NAME_" VARCHAR(255) NULL,
 "DESCRIPTION_" VARCHAR(255) NULL
);

CREATE TABLE "BLADEX"."ACT_FO_DATABASECHANGELOGLOCK"
(
 "ID" INT NOT NULL,
 "LOCKED" BIT NOT NULL,
 "LOCKGRANTED" TIMESTAMP(0) NULL,
 "LOCKEDBY" VARCHAR(255) NULL
);

CREATE TABLE "BLADEX"."ACT_FO_DATABASECHANGELOG"
(
 "ID" VARCHAR(255) NOT NULL,
 "AUTHOR" VARCHAR(255) NOT NULL,
 "FILENAME" VARCHAR(255) NOT NULL,
 "DATEEXECUTED" TIMESTAMP(0) NOT NULL,
 "ORDEREXECUTED" INT NOT NULL,
 "EXECTYPE" VARCHAR(10) NOT NULL,
 "MD5SUM" VARCHAR(35) NULL,
 "DESCRIPTION" VARCHAR(255) NULL,
 "COMMENTS" VARCHAR(255) NULL,
 "TAG" VARCHAR(255) NULL,
 "LIQUIBASE" VARCHAR(20) NULL,
 "CONTEXTS" VARCHAR(255) NULL,
 "LABELS" VARCHAR(255) NULL,
 "DEPLOYMENT_ID" VARCHAR(10) NULL
);

CREATE TABLE "BLADEX"."ACT_EVT_LOG"
(
 "LOG_NR_" BIGINT IDENTITY(1,1) NOT NULL,
 "TYPE_" VARCHAR(64) NULL,
 "PROC_DEF_ID_" VARCHAR(64) NULL,
 "PROC_INST_ID_" VARCHAR(64) NULL,
 "EXECUTION_ID_" VARCHAR(64) NULL,
 "TASK_ID_" VARCHAR(64) NULL,
 "TIME_STAMP_" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "USER_ID_" VARCHAR(255) NULL,
 "DATA_" BLOB NULL,
 "LOCK_OWNER_" VARCHAR(255) NULL,
 "LOCK_TIME_" TIMESTAMP(0) NULL,
 "IS_PROCESSED_" TINYINT DEFAULT 0
 NULL
);

CREATE TABLE "BLADEX"."ACT_DMN_HI_DECISION_EXECUTION"
(
 "ID_" VARCHAR(255) NOT NULL,
 "DECISION_DEFINITION_ID_" VARCHAR(255) NULL,
 "DEPLOYMENT_ID_" VARCHAR(255) NULL,
 "START_TIME_" TIMESTAMP(0) NULL,
 "END_TIME_" TIMESTAMP(0) NULL,
 "INSTANCE_ID_" VARCHAR(255) NULL,
 "EXECUTION_ID_" VARCHAR(255) NULL,
 "ACTIVITY_ID_" VARCHAR(255) NULL,
 "FAILED_" BIT DEFAULT '0'
 NULL,
 "TENANT_ID_" VARCHAR(255) NULL,
 "EXECUTION_JSON_" CLOB NULL,
 "SCOPE_TYPE_" VARCHAR(255) NULL
);

CREATE TABLE "BLADEX"."ACT_DMN_DEPLOYMENT_RESOURCE"
(
 "ID_" VARCHAR(255) NOT NULL,
 "NAME_" VARCHAR(255) NULL,
 "DEPLOYMENT_ID_" VARCHAR(255) NULL,
 "RESOURCE_BYTES_" BLOB NULL
);

CREATE TABLE "BLADEX"."ACT_DMN_DEPLOYMENT"
(
 "ID_" VARCHAR(255) NOT NULL,
 "NAME_" VARCHAR(255) NULL,
 "CATEGORY_" VARCHAR(255) NULL,
 "DEPLOY_TIME_" TIMESTAMP(0) NULL,
 "TENANT_ID_" VARCHAR(255) NULL,
 "PARENT_DEPLOYMENT_ID_" VARCHAR(255) NULL
);

CREATE TABLE "BLADEX"."ACT_DMN_DECISION"
(
 "ID_" VARCHAR(255) NOT NULL,
 "NAME_" VARCHAR(255) NULL,
 "VERSION_" INT NULL,
 "KEY_" VARCHAR(255) NULL,
 "CATEGORY_" VARCHAR(255) NULL,
 "DEPLOYMENT_ID_" VARCHAR(255) NULL,
 "TENANT_ID_" VARCHAR(255) NULL,
 "RESOURCE_NAME_" VARCHAR(255) NULL,
 "DESCRIPTION_" VARCHAR(255) NULL,
 "DECISION_TYPE_" VARCHAR(255) NULL
);

CREATE TABLE "BLADEX"."ACT_DMN_DATABASECHANGELOGLOCK"
(
 "ID" INT NOT NULL,
 "LOCKED" BIT NOT NULL,
 "LOCKGRANTED" TIMESTAMP(0) NULL,
 "LOCKEDBY" VARCHAR(255) NULL
);

CREATE TABLE "BLADEX"."ACT_DMN_DATABASECHANGELOG"
(
 "ID" VARCHAR(255) NOT NULL,
 "AUTHOR" VARCHAR(255) NOT NULL,
 "FILENAME" VARCHAR(255) NOT NULL,
 "DATEEXECUTED" TIMESTAMP(0) NOT NULL,
 "ORDEREXECUTED" INT NOT NULL,
 "EXECTYPE" VARCHAR(10) NOT NULL,
 "MD5SUM" VARCHAR(35) NULL,
 "DESCRIPTION" VARCHAR(255) NULL,
 "COMMENTS" VARCHAR(255) NULL,
 "TAG" VARCHAR(255) NULL,
 "LIQUIBASE" VARCHAR(20) NULL,
 "CONTEXTS" VARCHAR(255) NULL,
 "LABELS" VARCHAR(255) NULL,
 "DEPLOYMENT_ID" VARCHAR(10) NULL
);

CREATE TABLE "BLADEX"."ACT_DE_MODEL_RELATION"
(
 "ID" VARCHAR(255) NOT NULL,
 "PARENT_MODEL_ID" VARCHAR(255) NULL,
 "MODEL_ID" VARCHAR(255) NULL,
 "RELATION_TYPE" VARCHAR(255) NULL
);

CREATE TABLE "BLADEX"."ACT_DE_MODEL_HISTORY"
(
 "ID" VARCHAR(255) NOT NULL,
 "NAME" VARCHAR(400) NOT NULL,
 "MODEL_KEY" VARCHAR(400) NOT NULL,
 "DESCRIPTION" VARCHAR(3900) NULL,
 "MODEL_COMMENT" VARCHAR(3900) NULL,
 "CREATED" TIMESTAMP(0) NULL,
 "CREATED_BY" VARCHAR(255) NULL,
 "LAST_UPDATED" TIMESTAMP(0) NULL,
 "LAST_UPDATED_BY" VARCHAR(255) NULL,
 "REMOVAL_DATE" TIMESTAMP(0) NULL,
 "VERSION" INT NULL,
 "MODEL_EDITOR_JSON" CLOB NULL,
 "MODEL_ID" VARCHAR(255) NOT NULL,
 "MODEL_TYPE" INT NULL,
 "TENANT_ID" VARCHAR(255) NULL
);

CREATE TABLE "BLADEX"."ACT_DE_MODEL"
(
 "ID" VARCHAR(255) NOT NULL,
 "NAME" VARCHAR(400) NOT NULL,
 "MODEL_KEY" VARCHAR(400) NOT NULL,
 "DESCRIPTION" VARCHAR(3900) NULL,
 "MODEL_COMMENT" VARCHAR(3900) NULL,
 "CREATED" TIMESTAMP(0) NULL,
 "CREATED_BY" VARCHAR(255) NULL,
 "LAST_UPDATED" TIMESTAMP(0) NULL,
 "LAST_UPDATED_BY" VARCHAR(255) NULL,
 "VERSION" INT NULL,
 "MODEL_EDITOR_JSON" CLOB NULL,
 "MODEL_EDITOR_XML" CLOB NULL,
 "THUMBNAIL" BLOB NULL,
 "MODEL_TYPE" INT NULL,
 "TENANT_ID" VARCHAR(255) NULL,
 "XML" CLOB NULL
);

CREATE TABLE "BLADEX"."ACT_DE_DATABASECHANGELOGLOCK"
(
 "ID" INT NOT NULL,
 "LOCKED" BIT NOT NULL,
 "LOCKGRANTED" TIMESTAMP(0) NULL,
 "LOCKEDBY" VARCHAR(255) NULL
);

CREATE TABLE "BLADEX"."ACT_DE_DATABASECHANGELOG"
(
 "ID" VARCHAR(255) NOT NULL,
 "AUTHOR" VARCHAR(255) NOT NULL,
 "FILENAME" VARCHAR(255) NOT NULL,
 "DATEEXECUTED" TIMESTAMP(0) NOT NULL,
 "ORDEREXECUTED" INT NOT NULL,
 "EXECTYPE" VARCHAR(10) NOT NULL,
 "MD5SUM" VARCHAR(35) NULL,
 "DESCRIPTION" VARCHAR(255) NULL,
 "COMMENTS" VARCHAR(255) NULL,
 "TAG" VARCHAR(255) NULL,
 "LIQUIBASE" VARCHAR(20) NULL,
 "CONTEXTS" VARCHAR(255) NULL,
 "LABELS" VARCHAR(255) NULL,
 "DEPLOYMENT_ID" VARCHAR(10) NULL
);

CREATE TABLE "BLADEX"."ACT_CO_DATABASECHANGELOGLOCK"
(
 "ID" INT NOT NULL,
 "LOCKED" BIT NOT NULL,
 "LOCKGRANTED" TIMESTAMP(0) NULL,
 "LOCKEDBY" VARCHAR(255) NULL
);

CREATE TABLE "BLADEX"."ACT_CO_DATABASECHANGELOG"
(
 "ID" VARCHAR(255) NOT NULL,
 "AUTHOR" VARCHAR(255) NOT NULL,
 "FILENAME" VARCHAR(255) NOT NULL,
 "DATEEXECUTED" TIMESTAMP(0) NOT NULL,
 "ORDEREXECUTED" INT NOT NULL,
 "EXECTYPE" VARCHAR(10) NOT NULL,
 "MD5SUM" VARCHAR(35) NULL,
 "DESCRIPTION" VARCHAR(255) NULL,
 "COMMENTS" VARCHAR(255) NULL,
 "TAG" VARCHAR(255) NULL,
 "LIQUIBASE" VARCHAR(20) NULL,
 "CONTEXTS" VARCHAR(255) NULL,
 "LABELS" VARCHAR(255) NULL,
 "DEPLOYMENT_ID" VARCHAR(10) NULL
);

CREATE TABLE "BLADEX"."ACT_CO_CONTENT_ITEM"
(
 "ID_" VARCHAR(255) NOT NULL,
 "NAME_" VARCHAR(255) NOT NULL,
 "MIME_TYPE_" VARCHAR(255) NULL,
 "TASK_ID_" VARCHAR(255) NULL,
 "PROC_INST_ID_" VARCHAR(255) NULL,
 "CONTENT_STORE_ID_" VARCHAR(255) NULL,
 "CONTENT_STORE_NAME_" VARCHAR(255) NULL,
 "FIELD_" VARCHAR(400) NULL,
 "CONTENT_AVAILABLE_" BIT DEFAULT '0'
 NULL,
 "CREATED_" TIMESTAMP(0) NULL,
 "CREATED_BY_" VARCHAR(255) NULL,
 "LAST_MODIFIED_" TIMESTAMP(0) NULL,
 "LAST_MODIFIED_BY_" VARCHAR(255) NULL,
 "CONTENT_SIZE_" BIGINT DEFAULT 0
 NULL,
 "TENANT_ID_" VARCHAR(255) NULL,
 "SCOPE_ID_" VARCHAR(255) NULL,
 "SCOPE_TYPE_" VARCHAR(255) NULL
);

CREATE TABLE "BLADEX"."ACT_CMMN_RU_SENTRY_PART_INST"
(
 "ID_" VARCHAR(255) NOT NULL,
 "REV_" INT NOT NULL,
 "CASE_DEF_ID_" VARCHAR(255) NULL,
 "CASE_INST_ID_" VARCHAR(255) NULL,
 "PLAN_ITEM_INST_ID_" VARCHAR(255) NULL,
 "ON_PART_ID_" VARCHAR(255) NULL,
 "IF_PART_ID_" VARCHAR(255) NULL,
 "TIME_STAMP_" TIMESTAMP(0) NULL
);

CREATE TABLE "BLADEX"."ACT_CMMN_RU_PLAN_ITEM_INST"
(
 "ID_" VARCHAR(255) NOT NULL,
 "REV_" INT NOT NULL,
 "CASE_DEF_ID_" VARCHAR(255) NULL,
 "CASE_INST_ID_" VARCHAR(255) NULL,
 "STAGE_INST_ID_" VARCHAR(255) NULL,
 "IS_STAGE_" BIT NULL,
 "ELEMENT_ID_" VARCHAR(255) NULL,
 "NAME_" VARCHAR(255) NULL,
 "STATE_" VARCHAR(255) NULL,
 "CREATE_TIME_" TIMESTAMP(0) NULL,
 "START_USER_ID_" VARCHAR(255) NULL,
 "REFERENCE_ID_" VARCHAR(255) NULL,
 "REFERENCE_TYPE_" VARCHAR(255) NULL,
 "TENANT_ID_" VARCHAR(255) DEFAULT ''
 NULL,
 "ITEM_DEFINITION_ID_" VARCHAR(255) NULL,
 "ITEM_DEFINITION_TYPE_" VARCHAR(255) NULL,
 "IS_COMPLETEABLE_" BIT NULL,
 "IS_COUNT_ENABLED_" BIT NULL,
 "VAR_COUNT_" INT NULL,
 "SENTRY_PART_INST_COUNT_" INT NULL,
 "LAST_AVAILABLE_TIME_" TIMESTAMP(0) NULL,
 "LAST_ENABLED_TIME_" TIMESTAMP(0) NULL,
 "LAST_DISABLED_TIME_" TIMESTAMP(0) NULL,
 "LAST_STARTED_TIME_" TIMESTAMP(0) NULL,
 "LAST_SUSPENDED_TIME_" TIMESTAMP(0) NULL,
 "COMPLETED_TIME_" TIMESTAMP(0) NULL,
 "OCCURRED_TIME_" TIMESTAMP(0) NULL,
 "TERMINATED_TIME_" TIMESTAMP(0) NULL,
 "EXIT_TIME_" TIMESTAMP(0) NULL,
 "ENDED_TIME_" TIMESTAMP(0) NULL,
 "ENTRY_CRITERION_ID_" VARCHAR(255) NULL,
 "EXIT_CRITERION_ID_" VARCHAR(255) NULL,
 "EXTRA_VALUE_" VARCHAR(255) NULL,
 "DERIVED_CASE_DEF_ID_" VARCHAR(255) NULL,
 "LAST_UNAVAILABLE_TIME_" TIMESTAMP(0) NULL
);

CREATE TABLE "BLADEX"."ACT_CMMN_RU_MIL_INST"
(
 "ID_" VARCHAR(255) NOT NULL,
 "NAME_" VARCHAR(255) NOT NULL,
 "TIME_STAMP_" TIMESTAMP(0) NULL,
 "CASE_INST_ID_" VARCHAR(255) NOT NULL,
 "CASE_DEF_ID_" VARCHAR(255) NOT NULL,
 "ELEMENT_ID_" VARCHAR(255) NOT NULL,
 "TENANT_ID_" VARCHAR(255) DEFAULT ''
 NULL
);

CREATE TABLE "BLADEX"."ACT_CMMN_RU_CASE_INST"
(
 "ID_" VARCHAR(255) NOT NULL,
 "REV_" INT NOT NULL,
 "BUSINESS_KEY_" VARCHAR(255) NULL,
 "NAME_" VARCHAR(255) NULL,
 "PARENT_ID_" VARCHAR(255) NULL,
 "CASE_DEF_ID_" VARCHAR(255) NULL,
 "STATE_" VARCHAR(255) NULL,
 "START_TIME_" TIMESTAMP(0) NULL,
 "START_USER_ID_" VARCHAR(255) NULL,
 "CALLBACK_ID_" VARCHAR(255) NULL,
 "CALLBACK_TYPE_" VARCHAR(255) NULL,
 "TENANT_ID_" VARCHAR(255) DEFAULT ''
 NULL,
 "LOCK_TIME_" TIMESTAMP(0) NULL,
 "IS_COMPLETEABLE_" BIT NULL,
 "REFERENCE_ID_" VARCHAR(255) NULL,
 "REFERENCE_TYPE_" VARCHAR(255) NULL,
 "LOCK_OWNER_" VARCHAR(255) NULL,
 "LAST_REACTIVATION_TIME_" TIMESTAMP(0) NULL,
 "LAST_REACTIVATION_USER_ID_" VARCHAR(255) NULL,
 "BUSINESS_STATUS_" VARCHAR(255) NULL
);

CREATE TABLE "BLADEX"."ACT_CMMN_HI_PLAN_ITEM_INST"
(
 "ID_" VARCHAR(255) NOT NULL,
 "REV_" INT NOT NULL,
 "NAME_" VARCHAR(255) NULL,
 "STATE_" VARCHAR(255) NULL,
 "CASE_DEF_ID_" VARCHAR(255) NULL,
 "CASE_INST_ID_" VARCHAR(255) NULL,
 "STAGE_INST_ID_" VARCHAR(255) NULL,
 "IS_STAGE_" BIT NULL,
 "ELEMENT_ID_" VARCHAR(255) NULL,
 "ITEM_DEFINITION_ID_" VARCHAR(255) NULL,
 "ITEM_DEFINITION_TYPE_" VARCHAR(255) NULL,
 "CREATE_TIME_" TIMESTAMP(0) NULL,
 "LAST_AVAILABLE_TIME_" TIMESTAMP(0) NULL,
 "LAST_ENABLED_TIME_" TIMESTAMP(0) NULL,
 "LAST_DISABLED_TIME_" TIMESTAMP(0) NULL,
 "LAST_STARTED_TIME_" TIMESTAMP(0) NULL,
 "LAST_SUSPENDED_TIME_" TIMESTAMP(0) NULL,
 "COMPLETED_TIME_" TIMESTAMP(0) NULL,
 "OCCURRED_TIME_" TIMESTAMP(0) NULL,
 "TERMINATED_TIME_" TIMESTAMP(0) NULL,
 "EXIT_TIME_" TIMESTAMP(0) NULL,
 "ENDED_TIME_" TIMESTAMP(0) NULL,
 "LAST_UPDATED_TIME_" TIMESTAMP(0) NULL,
 "START_USER_ID_" VARCHAR(255) NULL,
 "REFERENCE_ID_" VARCHAR(255) NULL,
 "REFERENCE_TYPE_" VARCHAR(255) NULL,
 "TENANT_ID_" VARCHAR(255) DEFAULT ''
 NULL,
 "ENTRY_CRITERION_ID_" VARCHAR(255) NULL,
 "EXIT_CRITERION_ID_" VARCHAR(255) NULL,
 "SHOW_IN_OVERVIEW_" TINYINT NULL,
 "EXTRA_VALUE_" VARCHAR(255) NULL,
 "DERIVED_CASE_DEF_ID_" VARCHAR(255) NULL,
 "LAST_UNAVAILABLE_TIME_" TIMESTAMP(0) NULL
);

CREATE TABLE "BLADEX"."ACT_CMMN_HI_MIL_INST"
(
 "ID_" VARCHAR(255) NOT NULL,
 "REV_" INT NOT NULL,
 "NAME_" VARCHAR(255) NOT NULL,
 "TIME_STAMP_" TIMESTAMP(0) NULL,
 "CASE_INST_ID_" VARCHAR(255) NOT NULL,
 "CASE_DEF_ID_" VARCHAR(255) NOT NULL,
 "ELEMENT_ID_" VARCHAR(255) NOT NULL,
 "TENANT_ID_" VARCHAR(255) DEFAULT ''
 NULL
);

CREATE TABLE "BLADEX"."ACT_CMMN_HI_CASE_INST"
(
 "ID_" VARCHAR(255) NOT NULL,
 "REV_" INT NOT NULL,
 "BUSINESS_KEY_" VARCHAR(255) NULL,
 "NAME_" VARCHAR(255) NULL,
 "PARENT_ID_" VARCHAR(255) NULL,
 "CASE_DEF_ID_" VARCHAR(255) NULL,
 "STATE_" VARCHAR(255) NULL,
 "START_TIME_" TIMESTAMP(0) NULL,
 "END_TIME_" TIMESTAMP(0) NULL,
 "START_USER_ID_" VARCHAR(255) NULL,
 "CALLBACK_ID_" VARCHAR(255) NULL,
 "CALLBACK_TYPE_" VARCHAR(255) NULL,
 "TENANT_ID_" VARCHAR(255) DEFAULT ''
 NULL,
 "REFERENCE_ID_" VARCHAR(255) NULL,
 "REFERENCE_TYPE_" VARCHAR(255) NULL,
 "LAST_REACTIVATION_TIME_" TIMESTAMP(0) NULL,
 "LAST_REACTIVATION_USER_ID_" VARCHAR(255) NULL,
 "BUSINESS_STATUS_" VARCHAR(255) NULL
);

CREATE TABLE "BLADEX"."ACT_CMMN_DEPLOYMENT_RESOURCE"
(
 "ID_" VARCHAR(255) NOT NULL,
 "NAME_" VARCHAR(255) NULL,
 "DEPLOYMENT_ID_" VARCHAR(255) NULL,
 "RESOURCE_BYTES_" BLOB NULL,
 "GENERATED_" BIT NULL
);

CREATE TABLE "BLADEX"."ACT_CMMN_DEPLOYMENT"
(
 "ID_" VARCHAR(255) NOT NULL,
 "NAME_" VARCHAR(255) NULL,
 "CATEGORY_" VARCHAR(255) NULL,
 "KEY_" VARCHAR(255) NULL,
 "DEPLOY_TIME_" TIMESTAMP(0) NULL,
 "PARENT_DEPLOYMENT_ID_" VARCHAR(255) NULL,
 "TENANT_ID_" VARCHAR(255) DEFAULT ''
 NULL
);

CREATE TABLE "BLADEX"."ACT_CMMN_DATABASECHANGELOGLOCK"
(
 "ID" INT NOT NULL,
 "LOCKED" BIT NOT NULL,
 "LOCKGRANTED" TIMESTAMP(0) NULL,
 "LOCKEDBY" VARCHAR(255) NULL
);

CREATE TABLE "BLADEX"."ACT_CMMN_DATABASECHANGELOG"
(
 "ID" VARCHAR(255) NOT NULL,
 "AUTHOR" VARCHAR(255) NOT NULL,
 "FILENAME" VARCHAR(255) NOT NULL,
 "DATEEXECUTED" TIMESTAMP(0) NOT NULL,
 "ORDEREXECUTED" INT NOT NULL,
 "EXECTYPE" VARCHAR(10) NOT NULL,
 "MD5SUM" VARCHAR(35) NULL,
 "DESCRIPTION" VARCHAR(255) NULL,
 "COMMENTS" VARCHAR(255) NULL,
 "TAG" VARCHAR(255) NULL,
 "LIQUIBASE" VARCHAR(20) NULL,
 "CONTEXTS" VARCHAR(255) NULL,
 "LABELS" VARCHAR(255) NULL,
 "DEPLOYMENT_ID" VARCHAR(10) NULL
);

CREATE TABLE "BLADEX"."ACT_CMMN_CASEDEF"
(
 "ID_" VARCHAR(255) NOT NULL,
 "REV_" INT NOT NULL,
 "NAME_" VARCHAR(255) NULL,
 "KEY_" VARCHAR(255) NOT NULL,
 "VERSION_" INT NOT NULL,
 "CATEGORY_" VARCHAR(255) NULL,
 "DEPLOYMENT_ID_" VARCHAR(255) NULL,
 "RESOURCE_NAME_" VARCHAR(3900) NULL,
 "DESCRIPTION_" VARCHAR(3900) NULL,
 "HAS_GRAPHICAL_NOTATION_" BIT NULL,
 "TENANT_ID_" VARCHAR(255) DEFAULT ''
 NULL,
 "DGRM_RESOURCE_NAME_" VARCHAR(3900) NULL,
 "HAS_START_FORM_KEY_" BIT NULL
);

CREATE TABLE "BLADEX"."ACT_APP_DEPLOYMENT_RESOURCE"
(
 "ID_" VARCHAR(255) NOT NULL,
 "NAME_" VARCHAR(255) NULL,
 "DEPLOYMENT_ID_" VARCHAR(255) NULL,
 "RESOURCE_BYTES_" BLOB NULL
);

CREATE TABLE "BLADEX"."ACT_APP_DEPLOYMENT"
(
 "ID_" VARCHAR(255) NOT NULL,
 "NAME_" VARCHAR(255) NULL,
 "CATEGORY_" VARCHAR(255) NULL,
 "KEY_" VARCHAR(255) NULL,
 "DEPLOY_TIME_" TIMESTAMP(0) NULL,
 "TENANT_ID_" VARCHAR(255) DEFAULT ''
 NULL
);

CREATE TABLE "BLADEX"."ACT_APP_DATABASECHANGELOGLOCK"
(
 "ID" INT NOT NULL,
 "LOCKED" BIT NOT NULL,
 "LOCKGRANTED" TIMESTAMP(0) NULL,
 "LOCKEDBY" VARCHAR(255) NULL
);

CREATE TABLE "BLADEX"."ACT_APP_DATABASECHANGELOG"
(
 "ID" VARCHAR(255) NOT NULL,
 "AUTHOR" VARCHAR(255) NOT NULL,
 "FILENAME" VARCHAR(255) NOT NULL,
 "DATEEXECUTED" TIMESTAMP(0) NOT NULL,
 "ORDEREXECUTED" INT NOT NULL,
 "EXECTYPE" VARCHAR(10) NOT NULL,
 "MD5SUM" VARCHAR(35) NULL,
 "DESCRIPTION" VARCHAR(255) NULL,
 "COMMENTS" VARCHAR(255) NULL,
 "TAG" VARCHAR(255) NULL,
 "LIQUIBASE" VARCHAR(20) NULL,
 "CONTEXTS" VARCHAR(255) NULL,
 "LABELS" VARCHAR(255) NULL,
 "DEPLOYMENT_ID" VARCHAR(10) NULL
);

CREATE TABLE "BLADEX"."ACT_APP_APPDEF"
(
 "ID_" VARCHAR(255) NOT NULL,
 "REV_" INT NOT NULL,
 "NAME_" VARCHAR(255) NULL,
 "KEY_" VARCHAR(255) NOT NULL,
 "VERSION_" INT NOT NULL,
 "CATEGORY_" VARCHAR(255) NULL,
 "DEPLOYMENT_ID_" VARCHAR(255) NULL,
 "RESOURCE_NAME_" VARCHAR(3900) NULL,
 "DESCRIPTION_" VARCHAR(3900) NULL,
 "TENANT_ID_" VARCHAR(255) DEFAULT ''
 NULL
);

ALTER TABLE "BLADEX"."FLW_RU_BATCH_PART" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."FLW_RU_BATCH" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."FLW_EV_DATABASECHANGELOGLOCK" ADD CONSTRAINT  PRIMARY KEY("ID") ;

ALTER TABLE "BLADEX"."FLW_EVENT_RESOURCE" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."FLW_EVENT_DEPLOYMENT" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."FLW_EVENT_DEFINITION" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."FLW_EVENT_DEFINITION" ADD CONSTRAINT "ACT_IDX_EVENT_DEF_UNIQ" UNIQUE("KEY_","VERSION_","TENANT_ID_") ;

ALTER TABLE "BLADEX"."FLW_CHANNEL_DEFINITION" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."FLW_CHANNEL_DEFINITION" ADD CONSTRAINT "ACT_IDX_CHANNEL_DEF_UNIQ" UNIQUE("KEY_","VERSION_","TENANT_ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_VARIABLE" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_TIMER_JOB" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_TASK" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_SUSPENDED_JOB" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_JOB" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_IDENTITYLINK" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_HISTORY_JOB" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_EXTERNAL_JOB" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_EXECUTION" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_EVENT_SUBSCR" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_ENTITYLINK" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_DEADLETTER_JOB" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_ACTINST" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_RE_PROCDEF" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_RE_PROCDEF" ADD CONSTRAINT "ACT_UNIQ_PROCDEF" UNIQUE("KEY_","VERSION_","DERIVED_VERSION_","TENANT_ID_") ;

ALTER TABLE "BLADEX"."ACT_RE_MODEL" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_RE_DEPLOYMENT" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_PROCDEF_INFO" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_PROCDEF_INFO" ADD CONSTRAINT "ACT_UNIQ_INFO_PROCDEF" UNIQUE("PROC_DEF_ID_") ;

ALTER TABLE "BLADEX"."ACT_ID_USER" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_ID_TOKEN" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_ID_PROPERTY" ADD CONSTRAINT  PRIMARY KEY("NAME_") ;

ALTER TABLE "BLADEX"."ACT_ID_PRIV_MAPPING" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_ID_PRIV" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_ID_PRIV" ADD CONSTRAINT "ACT_UNIQ_PRIV_NAME" UNIQUE("NAME_") ;

ALTER TABLE "BLADEX"."ACT_ID_MEMBERSHIP" ADD CONSTRAINT  PRIMARY KEY("USER_ID_","GROUP_ID_") ;

ALTER TABLE "BLADEX"."ACT_ID_INFO" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_ID_GROUP" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_ID_BYTEARRAY" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_HI_VARINST" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_HI_TSK_LOG" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_HI_TASKINST" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_HI_PROCINST" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_HI_PROCINST" ADD CONSTRAINT "PROC_INST_ID_" UNIQUE("PROC_INST_ID_") ;

ALTER TABLE "BLADEX"."ACT_HI_IDENTITYLINK" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_HI_ENTITYLINK" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_HI_DETAIL" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_HI_COMMENT" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_HI_ATTACHMENT" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_HI_ACTINST" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_GE_PROPERTY" ADD CONSTRAINT  PRIMARY KEY("NAME_") ;

ALTER TABLE "BLADEX"."ACT_GE_BYTEARRAY" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_FO_FORM_RESOURCE" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_FO_FORM_INSTANCE" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_FO_FORM_DEPLOYMENT" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_FO_FORM_DEFINITION" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_FO_FORM_DEFINITION" ADD CONSTRAINT "ACT_IDX_FORM_DEF_UNIQ" UNIQUE("KEY_","VERSION_","TENANT_ID_") ;

ALTER TABLE "BLADEX"."ACT_FO_DATABASECHANGELOGLOCK" ADD CONSTRAINT  PRIMARY KEY("ID") ;

ALTER TABLE "BLADEX"."ACT_EVT_LOG" ADD CONSTRAINT  PRIMARY KEY("LOG_NR_") ;

ALTER TABLE "BLADEX"."ACT_DMN_HI_DECISION_EXECUTION" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_DMN_DEPLOYMENT_RESOURCE" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_DMN_DEPLOYMENT" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_DMN_DECISION" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_DMN_DECISION" ADD CONSTRAINT "ACT_IDX_DMN_DEC_UNIQ" UNIQUE("KEY_","VERSION_","TENANT_ID_") ;

ALTER TABLE "BLADEX"."ACT_DMN_DATABASECHANGELOGLOCK" ADD CONSTRAINT  PRIMARY KEY("ID") ;

ALTER TABLE "BLADEX"."ACT_DE_MODEL_RELATION" ADD CONSTRAINT  PRIMARY KEY("ID") ;

ALTER TABLE "BLADEX"."ACT_DE_MODEL_HISTORY" ADD CONSTRAINT  PRIMARY KEY("ID") ;

ALTER TABLE "BLADEX"."ACT_DE_MODEL" ADD CONSTRAINT  PRIMARY KEY("ID") ;

ALTER TABLE "BLADEX"."ACT_DE_DATABASECHANGELOGLOCK" ADD CONSTRAINT  PRIMARY KEY("ID") ;

ALTER TABLE "BLADEX"."ACT_CO_DATABASECHANGELOGLOCK" ADD CONSTRAINT  PRIMARY KEY("ID") ;

ALTER TABLE "BLADEX"."ACT_CO_CONTENT_ITEM" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_CMMN_RU_SENTRY_PART_INST" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_CMMN_RU_PLAN_ITEM_INST" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_CMMN_RU_MIL_INST" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_CMMN_RU_CASE_INST" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_CMMN_HI_PLAN_ITEM_INST" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_CMMN_HI_MIL_INST" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_CMMN_HI_CASE_INST" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_CMMN_DEPLOYMENT_RESOURCE" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_CMMN_DEPLOYMENT" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_CMMN_DATABASECHANGELOGLOCK" ADD CONSTRAINT  PRIMARY KEY("ID") ;

ALTER TABLE "BLADEX"."ACT_CMMN_CASEDEF" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_CMMN_CASEDEF" ADD CONSTRAINT "ACT_IDX_CASE_DEF_UNIQ" UNIQUE("KEY_","VERSION_","TENANT_ID_") ;

ALTER TABLE "BLADEX"."ACT_APP_DEPLOYMENT_RESOURCE" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_APP_DEPLOYMENT" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_APP_DATABASECHANGELOGLOCK" ADD CONSTRAINT  PRIMARY KEY("ID") ;

ALTER TABLE "BLADEX"."ACT_APP_APPDEF" ADD CONSTRAINT  PRIMARY KEY("ID_") ;

ALTER TABLE "BLADEX"."ACT_APP_APPDEF" ADD CONSTRAINT "ACT_IDX_APP_DEF_UNIQ" UNIQUE("KEY_","VERSION_","TENANT_ID_") ;

ALTER TABLE "BLADEX"."FLW_RU_BATCH_PART" ADD CONSTRAINT "FLW_FK_BATCH_PART_PARENT" FOREIGN KEY("BATCH_ID_") REFERENCES "BLADEX"."FLW_RU_BATCH"("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_VARIABLE" ADD CONSTRAINT "ACT_FK_VAR_PROCINST" FOREIGN KEY("PROC_INST_ID_") REFERENCES "BLADEX"."ACT_RU_EXECUTION"("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_VARIABLE" ADD CONSTRAINT "ACT_FK_VAR_BYTEARRAY" FOREIGN KEY("BYTEARRAY_ID_") REFERENCES "BLADEX"."ACT_GE_BYTEARRAY"("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_VARIABLE" ADD CONSTRAINT "ACT_FK_VAR_EXE" FOREIGN KEY("EXECUTION_ID_") REFERENCES "BLADEX"."ACT_RU_EXECUTION"("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_TIMER_JOB" ADD CONSTRAINT "ACT_FK_TIMER_JOB_PROCESS_INSTANCE" FOREIGN KEY("PROCESS_INSTANCE_ID_") REFERENCES "BLADEX"."ACT_RU_EXECUTION"("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_TIMER_JOB" ADD CONSTRAINT "ACT_FK_TIMER_JOB_CUSTOM_VALUES" FOREIGN KEY("CUSTOM_VALUES_ID_") REFERENCES "BLADEX"."ACT_GE_BYTEARRAY"("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_TIMER_JOB" ADD CONSTRAINT "ACT_FK_TIMER_JOB_EXECUTION" FOREIGN KEY("EXECUTION_ID_") REFERENCES "BLADEX"."ACT_RU_EXECUTION"("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_TIMER_JOB" ADD CONSTRAINT "ACT_FK_TIMER_JOB_PROC_DEF" FOREIGN KEY("PROC_DEF_ID_") REFERENCES "BLADEX"."ACT_RE_PROCDEF"("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_TIMER_JOB" ADD CONSTRAINT "ACT_FK_TIMER_JOB_EXCEPTION" FOREIGN KEY("EXCEPTION_STACK_ID_") REFERENCES "BLADEX"."ACT_GE_BYTEARRAY"("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_TASK" ADD CONSTRAINT "ACT_FK_TASK_PROCDEF" FOREIGN KEY("PROC_DEF_ID_") REFERENCES "BLADEX"."ACT_RE_PROCDEF"("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_TASK" ADD CONSTRAINT "ACT_FK_TASK_EXE" FOREIGN KEY("EXECUTION_ID_") REFERENCES "BLADEX"."ACT_RU_EXECUTION"("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_TASK" ADD CONSTRAINT "ACT_FK_TASK_PROCINST" FOREIGN KEY("PROC_INST_ID_") REFERENCES "BLADEX"."ACT_RU_EXECUTION"("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_SUSPENDED_JOB" ADD CONSTRAINT "ACT_FK_SUSPENDED_JOB_PROCESS_INSTANCE" FOREIGN KEY("PROCESS_INSTANCE_ID_") REFERENCES "BLADEX"."ACT_RU_EXECUTION"("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_SUSPENDED_JOB" ADD CONSTRAINT "ACT_FK_SUSPENDED_JOB_PROC_DEF" FOREIGN KEY("PROC_DEF_ID_") REFERENCES "BLADEX"."ACT_RE_PROCDEF"("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_SUSPENDED_JOB" ADD CONSTRAINT "ACT_FK_SUSPENDED_JOB_EXCEPTION" FOREIGN KEY("EXCEPTION_STACK_ID_") REFERENCES "BLADEX"."ACT_GE_BYTEARRAY"("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_SUSPENDED_JOB" ADD CONSTRAINT "ACT_FK_SUSPENDED_JOB_EXECUTION" FOREIGN KEY("EXECUTION_ID_") REFERENCES "BLADEX"."ACT_RU_EXECUTION"("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_SUSPENDED_JOB" ADD CONSTRAINT "ACT_FK_SUSPENDED_JOB_CUSTOM_VALUES" FOREIGN KEY("CUSTOM_VALUES_ID_") REFERENCES "BLADEX"."ACT_GE_BYTEARRAY"("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_JOB" ADD CONSTRAINT "ACT_FK_JOB_PROC_DEF" FOREIGN KEY("PROC_DEF_ID_") REFERENCES "BLADEX"."ACT_RE_PROCDEF"("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_JOB" ADD CONSTRAINT "ACT_FK_JOB_CUSTOM_VALUES" FOREIGN KEY("CUSTOM_VALUES_ID_") REFERENCES "BLADEX"."ACT_GE_BYTEARRAY"("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_JOB" ADD CONSTRAINT "ACT_FK_JOB_EXECUTION" FOREIGN KEY("EXECUTION_ID_") REFERENCES "BLADEX"."ACT_RU_EXECUTION"("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_JOB" ADD CONSTRAINT "ACT_FK_JOB_PROCESS_INSTANCE" FOREIGN KEY("PROCESS_INSTANCE_ID_") REFERENCES "BLADEX"."ACT_RU_EXECUTION"("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_JOB" ADD CONSTRAINT "ACT_FK_JOB_EXCEPTION" FOREIGN KEY("EXCEPTION_STACK_ID_") REFERENCES "BLADEX"."ACT_GE_BYTEARRAY"("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_IDENTITYLINK" ADD CONSTRAINT "ACT_FK_IDL_PROCINST" FOREIGN KEY("PROC_INST_ID_") REFERENCES "BLADEX"."ACT_RU_EXECUTION"("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_IDENTITYLINK" ADD CONSTRAINT "ACT_FK_TSKASS_TASK" FOREIGN KEY("TASK_ID_") REFERENCES "BLADEX"."ACT_RU_TASK"("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_IDENTITYLINK" ADD CONSTRAINT "ACT_FK_ATHRZ_PROCEDEF" FOREIGN KEY("PROC_DEF_ID_") REFERENCES "BLADEX"."ACT_RE_PROCDEF"("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_EXTERNAL_JOB" ADD CONSTRAINT "ACT_FK_EXTERNAL_JOB_CUSTOM_VALUES" FOREIGN KEY("CUSTOM_VALUES_ID_") REFERENCES "BLADEX"."ACT_GE_BYTEARRAY"("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_EXTERNAL_JOB" ADD CONSTRAINT "ACT_FK_EXTERNAL_JOB_EXCEPTION" FOREIGN KEY("EXCEPTION_STACK_ID_") REFERENCES "BLADEX"."ACT_GE_BYTEARRAY"("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_EXECUTION" ADD CONSTRAINT "ACT_FK_EXE_PROCINST" FOREIGN KEY("PROC_INST_ID_") REFERENCES "BLADEX"."ACT_RU_EXECUTION"("ID_") ON UPDATE CASCADE  ON DELETE CASCADE  with index ;

ALTER TABLE "BLADEX"."ACT_RU_EXECUTION" ADD CONSTRAINT "ACT_FK_EXE_PARENT" FOREIGN KEY("PARENT_ID_") REFERENCES "BLADEX"."ACT_RU_EXECUTION"("ID_") ON DELETE CASCADE  with index ;

ALTER TABLE "BLADEX"."ACT_RU_EXECUTION" ADD CONSTRAINT "ACT_FK_EXE_SUPER" FOREIGN KEY("SUPER_EXEC_") REFERENCES "BLADEX"."ACT_RU_EXECUTION"("ID_") ON DELETE CASCADE  with index ;

ALTER TABLE "BLADEX"."ACT_RU_EXECUTION" ADD CONSTRAINT "ACT_FK_EXE_PROCDEF" FOREIGN KEY("PROC_DEF_ID_") REFERENCES "BLADEX"."ACT_RE_PROCDEF"("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_EVENT_SUBSCR" ADD CONSTRAINT "ACT_FK_EVENT_EXEC" FOREIGN KEY("EXECUTION_ID_") REFERENCES "BLADEX"."ACT_RU_EXECUTION"("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_DEADLETTER_JOB" ADD CONSTRAINT "ACT_FK_DEADLETTER_JOB_PROC_DEF" FOREIGN KEY("PROC_DEF_ID_") REFERENCES "BLADEX"."ACT_RE_PROCDEF"("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_DEADLETTER_JOB" ADD CONSTRAINT "ACT_FK_DEADLETTER_JOB_EXECUTION" FOREIGN KEY("EXECUTION_ID_") REFERENCES "BLADEX"."ACT_RU_EXECUTION"("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_DEADLETTER_JOB" ADD CONSTRAINT "ACT_FK_DEADLETTER_JOB_CUSTOM_VALUES" FOREIGN KEY("CUSTOM_VALUES_ID_") REFERENCES "BLADEX"."ACT_GE_BYTEARRAY"("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_DEADLETTER_JOB" ADD CONSTRAINT "ACT_FK_DEADLETTER_JOB_PROCESS_INSTANCE" FOREIGN KEY("PROCESS_INSTANCE_ID_") REFERENCES "BLADEX"."ACT_RU_EXECUTION"("ID_") ;

ALTER TABLE "BLADEX"."ACT_RU_DEADLETTER_JOB" ADD CONSTRAINT "ACT_FK_DEADLETTER_JOB_EXCEPTION" FOREIGN KEY("EXCEPTION_STACK_ID_") REFERENCES "BLADEX"."ACT_GE_BYTEARRAY"("ID_") ;

ALTER TABLE "BLADEX"."ACT_RE_MODEL" ADD CONSTRAINT "ACT_FK_MODEL_SOURCE" FOREIGN KEY("EDITOR_SOURCE_VALUE_ID_") REFERENCES "BLADEX"."ACT_GE_BYTEARRAY"("ID_") ;

ALTER TABLE "BLADEX"."ACT_RE_MODEL" ADD CONSTRAINT "ACT_FK_MODEL_SOURCE_EXTRA" FOREIGN KEY("EDITOR_SOURCE_EXTRA_VALUE_ID_") REFERENCES "BLADEX"."ACT_GE_BYTEARRAY"("ID_") ;

ALTER TABLE "BLADEX"."ACT_RE_MODEL" ADD CONSTRAINT "ACT_FK_MODEL_DEPLOYMENT" FOREIGN KEY("DEPLOYMENT_ID_") REFERENCES "BLADEX"."ACT_RE_DEPLOYMENT"("ID_") ;

ALTER TABLE "BLADEX"."ACT_PROCDEF_INFO" ADD CONSTRAINT "ACT_FK_INFO_PROCDEF" FOREIGN KEY("PROC_DEF_ID_") REFERENCES "BLADEX"."ACT_RE_PROCDEF"("ID_") ;

ALTER TABLE "BLADEX"."ACT_PROCDEF_INFO" ADD CONSTRAINT "ACT_FK_INFO_JSON_BA" FOREIGN KEY("INFO_JSON_ID_") REFERENCES "BLADEX"."ACT_GE_BYTEARRAY"("ID_") ;

ALTER TABLE "BLADEX"."ACT_ID_PRIV_MAPPING" ADD CONSTRAINT "ACT_FK_PRIV_MAPPING" FOREIGN KEY("PRIV_ID_") REFERENCES "BLADEX"."ACT_ID_PRIV"("ID_") ;

ALTER TABLE "BLADEX"."ACT_ID_MEMBERSHIP" ADD CONSTRAINT "ACT_FK_MEMB_GROUP" FOREIGN KEY("GROUP_ID_") REFERENCES "BLADEX"."ACT_ID_GROUP"("ID_") ;

ALTER TABLE "BLADEX"."ACT_ID_MEMBERSHIP" ADD CONSTRAINT "ACT_FK_MEMB_USER" FOREIGN KEY("USER_ID_") REFERENCES "BLADEX"."ACT_ID_USER"("ID_") ;

ALTER TABLE "BLADEX"."ACT_GE_BYTEARRAY" ADD CONSTRAINT "ACT_FK_BYTEARR_DEPL" FOREIGN KEY("DEPLOYMENT_ID_") REFERENCES "BLADEX"."ACT_RE_DEPLOYMENT"("ID_") ;

ALTER TABLE "BLADEX"."ACT_DE_MODEL_RELATION" ADD CONSTRAINT "FK_RELATION_CHILD" FOREIGN KEY("MODEL_ID") REFERENCES "BLADEX"."ACT_DE_MODEL"("ID") ;

ALTER TABLE "BLADEX"."ACT_DE_MODEL_RELATION" ADD CONSTRAINT "FK_RELATION_PARENT" FOREIGN KEY("PARENT_MODEL_ID") REFERENCES "BLADEX"."ACT_DE_MODEL"("ID") ;

ALTER TABLE "BLADEX"."ACT_CMMN_RU_SENTRY_PART_INST" ADD CONSTRAINT "ACT_FK_SENTRY_CASE_INST" FOREIGN KEY("CASE_INST_ID_") REFERENCES "BLADEX"."ACT_CMMN_RU_CASE_INST"("ID_") ;

ALTER TABLE "BLADEX"."ACT_CMMN_RU_SENTRY_PART_INST" ADD CONSTRAINT "ACT_FK_SENTRY_CASE_DEF" FOREIGN KEY("CASE_DEF_ID_") REFERENCES "BLADEX"."ACT_CMMN_CASEDEF"("ID_") ;

ALTER TABLE "BLADEX"."ACT_CMMN_RU_SENTRY_PART_INST" ADD CONSTRAINT "ACT_FK_SENTRY_PLAN_ITEM" FOREIGN KEY("PLAN_ITEM_INST_ID_") REFERENCES "BLADEX"."ACT_CMMN_RU_PLAN_ITEM_INST"("ID_") ;

ALTER TABLE "BLADEX"."ACT_CMMN_RU_PLAN_ITEM_INST" ADD CONSTRAINT "ACT_FK_PLAN_ITEM_CASE_INST" FOREIGN KEY("CASE_INST_ID_") REFERENCES "BLADEX"."ACT_CMMN_RU_CASE_INST"("ID_") ;

ALTER TABLE "BLADEX"."ACT_CMMN_RU_PLAN_ITEM_INST" ADD CONSTRAINT "ACT_FK_PLAN_ITEM_CASE_DEF" FOREIGN KEY("CASE_DEF_ID_") REFERENCES "BLADEX"."ACT_CMMN_CASEDEF"("ID_") ;

ALTER TABLE "BLADEX"."ACT_CMMN_RU_MIL_INST" ADD CONSTRAINT "ACT_FK_MIL_CASE_DEF" FOREIGN KEY("CASE_DEF_ID_") REFERENCES "BLADEX"."ACT_CMMN_CASEDEF"("ID_") ;

ALTER TABLE "BLADEX"."ACT_CMMN_RU_MIL_INST" ADD CONSTRAINT "ACT_FK_MIL_CASE_INST" FOREIGN KEY("CASE_INST_ID_") REFERENCES "BLADEX"."ACT_CMMN_RU_CASE_INST"("ID_") ;

ALTER TABLE "BLADEX"."ACT_CMMN_RU_CASE_INST" ADD CONSTRAINT "ACT_FK_CASE_INST_CASE_DEF" FOREIGN KEY("CASE_DEF_ID_") REFERENCES "BLADEX"."ACT_CMMN_CASEDEF"("ID_") ;

ALTER TABLE "BLADEX"."ACT_CMMN_DEPLOYMENT_RESOURCE" ADD CONSTRAINT "ACT_FK_CMMN_RSRC_DPL" FOREIGN KEY("DEPLOYMENT_ID_") REFERENCES "BLADEX"."ACT_CMMN_DEPLOYMENT"("ID_") ;

ALTER TABLE "BLADEX"."ACT_CMMN_CASEDEF" ADD CONSTRAINT "ACT_FK_CASE_DEF_DPLY" FOREIGN KEY("DEPLOYMENT_ID_") REFERENCES "BLADEX"."ACT_CMMN_DEPLOYMENT"("ID_") ;

ALTER TABLE "BLADEX"."ACT_APP_DEPLOYMENT_RESOURCE" ADD CONSTRAINT "ACT_FK_APP_RSRC_DPL" FOREIGN KEY("DEPLOYMENT_ID_") REFERENCES "BLADEX"."ACT_APP_DEPLOYMENT"("ID_") ;

ALTER TABLE "BLADEX"."ACT_APP_APPDEF" ADD CONSTRAINT "ACT_FK_APP_DEF_DPLY" FOREIGN KEY("DEPLOYMENT_ID_") REFERENCES "BLADEX"."ACT_APP_DEPLOYMENT"("ID_") ;


CREATE INDEX "FLW_IDX_BATCH_PART"
ON "BLADEX"."FLW_RU_BATCH_PART"("BATCH_ID_");

CREATE INDEX "ACT_IDX_VARIABLE_TASK_ID"
ON "BLADEX"."ACT_RU_VARIABLE"("TASK_ID_");

CREATE INDEX "ACT_IDX_RU_VAR_SUB_ID_TYPE"
ON "BLADEX"."ACT_RU_VARIABLE"("SUB_SCOPE_ID_","SCOPE_TYPE_");

CREATE INDEX "ACT_IDX_RU_VAR_SCOPE_ID_TYPE"
ON "BLADEX"."ACT_RU_VARIABLE"("SCOPE_ID_","SCOPE_TYPE_");

CREATE INDEX "ACT_IDX_TIMER_JOB_DUEDATE"
ON "BLADEX"."ACT_RU_TIMER_JOB"("DUEDATE_");

CREATE INDEX "ACT_IDX_TIMER_JOB_CUSTOM_VALUES_ID"
ON "BLADEX"."ACT_RU_TIMER_JOB"("CUSTOM_VALUES_ID_");

CREATE INDEX "ACT_IDX_TIMER_JOB_CORRELATION_ID"
ON "BLADEX"."ACT_RU_TIMER_JOB"("CORRELATION_ID_");

CREATE INDEX "ACT_IDX_TJOB_SCOPE_DEF"
ON "BLADEX"."ACT_RU_TIMER_JOB"("SCOPE_DEFINITION_ID_","SCOPE_TYPE_");

CREATE INDEX "ACT_IDX_TIMER_JOB_EXCEPTION_STACK_ID"
ON "BLADEX"."ACT_RU_TIMER_JOB"("EXCEPTION_STACK_ID_");

CREATE INDEX "ACT_IDX_TJOB_SCOPE"
ON "BLADEX"."ACT_RU_TIMER_JOB"("SCOPE_ID_","SCOPE_TYPE_");

CREATE INDEX "ACT_IDX_TJOB_SUB_SCOPE"
ON "BLADEX"."ACT_RU_TIMER_JOB"("SUB_SCOPE_ID_","SCOPE_TYPE_");

CREATE INDEX "ACT_IDX_TASK_SCOPE_DEF"
ON "BLADEX"."ACT_RU_TASK"("SCOPE_DEFINITION_ID_","SCOPE_TYPE_");

CREATE INDEX "ACT_IDX_TASK_SCOPE"
ON "BLADEX"."ACT_RU_TASK"("SCOPE_ID_","SCOPE_TYPE_");

CREATE INDEX "ACT_IDX_TASK_CREATE"
ON "BLADEX"."ACT_RU_TASK"("CREATE_TIME_");

CREATE INDEX "ACT_IDX_TASK_SUB_SCOPE"
ON "BLADEX"."ACT_RU_TASK"("SUB_SCOPE_ID_","SCOPE_TYPE_");

CREATE INDEX "ACT_IDX_SUSPENDED_JOB_CORRELATION_ID"
ON "BLADEX"."ACT_RU_SUSPENDED_JOB"("CORRELATION_ID_");

CREATE INDEX "ACT_IDX_SJOB_SCOPE"
ON "BLADEX"."ACT_RU_SUSPENDED_JOB"("SCOPE_ID_","SCOPE_TYPE_");

CREATE INDEX "ACT_IDX_SUSPENDED_JOB_EXCEPTION_STACK_ID"
ON "BLADEX"."ACT_RU_SUSPENDED_JOB"("EXCEPTION_STACK_ID_");

CREATE INDEX "ACT_IDX_SUSPENDED_JOB_CUSTOM_VALUES_ID"
ON "BLADEX"."ACT_RU_SUSPENDED_JOB"("CUSTOM_VALUES_ID_");

CREATE INDEX "ACT_IDX_SJOB_SUB_SCOPE"
ON "BLADEX"."ACT_RU_SUSPENDED_JOB"("SUB_SCOPE_ID_","SCOPE_TYPE_");

CREATE INDEX "ACT_IDX_SJOB_SCOPE_DEF"
ON "BLADEX"."ACT_RU_SUSPENDED_JOB"("SCOPE_DEFINITION_ID_","SCOPE_TYPE_");

CREATE INDEX "ACT_IDX_JOB_SCOPE_DEF"
ON "BLADEX"."ACT_RU_JOB"("SCOPE_DEFINITION_ID_","SCOPE_TYPE_");

CREATE INDEX "ACT_IDX_JOB_CORRELATION_ID"
ON "BLADEX"."ACT_RU_JOB"("CORRELATION_ID_");

CREATE INDEX "ACT_IDX_JOB_EXCEPTION_STACK_ID"
ON "BLADEX"."ACT_RU_JOB"("EXCEPTION_STACK_ID_");

CREATE INDEX "ACT_IDX_JOB_SCOPE"
ON "BLADEX"."ACT_RU_JOB"("SCOPE_ID_","SCOPE_TYPE_");

CREATE INDEX "ACT_IDX_JOB_CUSTOM_VALUES_ID"
ON "BLADEX"."ACT_RU_JOB"("CUSTOM_VALUES_ID_");

CREATE INDEX "ACT_IDX_JOB_SUB_SCOPE"
ON "BLADEX"."ACT_RU_JOB"("SUB_SCOPE_ID_","SCOPE_TYPE_");

CREATE INDEX "ACT_IDX_IDENT_LNK_GROUP"
ON "BLADEX"."ACT_RU_IDENTITYLINK"("GROUP_ID_");

CREATE INDEX "ACT_IDX_IDENT_LNK_USER"
ON "BLADEX"."ACT_RU_IDENTITYLINK"("USER_ID_");

CREATE INDEX "ACT_IDX_IDENT_LNK_SUB_SCOPE"
ON "BLADEX"."ACT_RU_IDENTITYLINK"("SUB_SCOPE_ID_","SCOPE_TYPE_");

CREATE INDEX "ACT_IDX_IDENT_LNK_SCOPE_DEF"
ON "BLADEX"."ACT_RU_IDENTITYLINK"("SCOPE_DEFINITION_ID_","SCOPE_TYPE_");

CREATE INDEX "ACT_IDX_ATHRZ_PROCEDEF"
ON "BLADEX"."ACT_RU_IDENTITYLINK"("PROC_DEF_ID_");

CREATE INDEX "ACT_IDX_IDENT_LNK_SCOPE"
ON "BLADEX"."ACT_RU_IDENTITYLINK"("SCOPE_ID_","SCOPE_TYPE_");

CREATE INDEX "ACT_IDX_EJOB_SCOPE"
ON "BLADEX"."ACT_RU_EXTERNAL_JOB"("SCOPE_ID_","SCOPE_TYPE_");

CREATE INDEX "ACT_IDX_EXTERNAL_JOB_EXCEPTION_STACK_ID"
ON "BLADEX"."ACT_RU_EXTERNAL_JOB"("EXCEPTION_STACK_ID_");

CREATE INDEX "ACT_IDX_EXTERNAL_JOB_CORRELATION_ID"
ON "BLADEX"."ACT_RU_EXTERNAL_JOB"("CORRELATION_ID_");

CREATE INDEX "ACT_IDX_EJOB_SUB_SCOPE"
ON "BLADEX"."ACT_RU_EXTERNAL_JOB"("SUB_SCOPE_ID_","SCOPE_TYPE_");

CREATE INDEX "ACT_IDX_EXTERNAL_JOB_CUSTOM_VALUES_ID"
ON "BLADEX"."ACT_RU_EXTERNAL_JOB"("CUSTOM_VALUES_ID_");

CREATE INDEX "ACT_IDX_EJOB_SCOPE_DEF"
ON "BLADEX"."ACT_RU_EXTERNAL_JOB"("SCOPE_DEFINITION_ID_","SCOPE_TYPE_");

CREATE INDEX "ACT_IDX_EXEC_BUSKEY"
ON "BLADEX"."ACT_RU_EXECUTION"("BUSINESS_KEY_");

CREATE INDEX "ACT_IDX_EXEC_REF_ID_"
ON "BLADEX"."ACT_RU_EXECUTION"("REFERENCE_ID_");

CREATE INDEX "ACT_IDC_EXEC_ROOT"
ON "BLADEX"."ACT_RU_EXECUTION"("ROOT_PROC_INST_ID_");

CREATE INDEX "ACT_IDX_EVENT_SUBSCR_CONFIG_"
ON "BLADEX"."ACT_RU_EVENT_SUBSCR"("CONFIGURATION_");

CREATE INDEX "ACT_IDX_EVENT_SUBSCR_SCOPEREF_"
ON "BLADEX"."ACT_RU_EVENT_SUBSCR"("SCOPE_ID_","SCOPE_TYPE_");

CREATE INDEX "ACT_IDX_ENT_LNK_REF_SCOPE"
ON "BLADEX"."ACT_RU_ENTITYLINK"("REF_SCOPE_ID_","REF_SCOPE_TYPE_","LINK_TYPE_");

CREATE INDEX "ACT_IDX_ENT_LNK_SCOPE"
ON "BLADEX"."ACT_RU_ENTITYLINK"("SCOPE_ID_","SCOPE_TYPE_","LINK_TYPE_");

CREATE INDEX "ACT_IDX_ENT_LNK_ROOT_SCOPE"
ON "BLADEX"."ACT_RU_ENTITYLINK"("ROOT_SCOPE_ID_","ROOT_SCOPE_TYPE_","LINK_TYPE_");

CREATE INDEX "ACT_IDX_ENT_LNK_SCOPE_DEF"
ON "BLADEX"."ACT_RU_ENTITYLINK"("SCOPE_DEFINITION_ID_","SCOPE_TYPE_","LINK_TYPE_");

CREATE INDEX "ACT_IDX_DEADLETTER_JOB_CUSTOM_VALUES_ID"
ON "BLADEX"."ACT_RU_DEADLETTER_JOB"("CUSTOM_VALUES_ID_");

CREATE INDEX "ACT_IDX_DEADLETTER_JOB_EXCEPTION_STACK_ID"
ON "BLADEX"."ACT_RU_DEADLETTER_JOB"("EXCEPTION_STACK_ID_");

CREATE INDEX "ACT_IDX_DJOB_SCOPE_DEF"
ON "BLADEX"."ACT_RU_DEADLETTER_JOB"("SCOPE_DEFINITION_ID_","SCOPE_TYPE_");

CREATE INDEX "ACT_IDX_DJOB_SCOPE"
ON "BLADEX"."ACT_RU_DEADLETTER_JOB"("SCOPE_ID_","SCOPE_TYPE_");

CREATE INDEX "ACT_IDX_DJOB_SUB_SCOPE"
ON "BLADEX"."ACT_RU_DEADLETTER_JOB"("SUB_SCOPE_ID_","SCOPE_TYPE_");

CREATE INDEX "ACT_IDX_DEADLETTER_JOB_CORRELATION_ID"
ON "BLADEX"."ACT_RU_DEADLETTER_JOB"("CORRELATION_ID_");

CREATE INDEX "ACT_IDX_RU_ACTI_END"
ON "BLADEX"."ACT_RU_ACTINST"("END_TIME_");

CREATE INDEX "ACT_IDX_RU_ACTI_EXEC"
ON "BLADEX"."ACT_RU_ACTINST"("EXECUTION_ID_");

CREATE INDEX "ACT_IDX_RU_ACTI_EXEC_ACT"
ON "BLADEX"."ACT_RU_ACTINST"("EXECUTION_ID_","ACT_ID_");

CREATE INDEX "ACT_IDX_RU_ACTI_PROC"
ON "BLADEX"."ACT_RU_ACTINST"("PROC_INST_ID_");

CREATE INDEX "ACT_IDX_RU_ACTI_PROC_ACT"
ON "BLADEX"."ACT_RU_ACTINST"("PROC_INST_ID_","ACT_ID_");

CREATE INDEX "ACT_IDX_RU_ACTI_START"
ON "BLADEX"."ACT_RU_ACTINST"("START_TIME_");

CREATE INDEX "ACT_IDX_RU_ACTI_TASK"
ON "BLADEX"."ACT_RU_ACTINST"("TASK_ID_");

CREATE INDEX "ACT_IDX_INFO_PROCDEF"
ON "BLADEX"."ACT_PROCDEF_INFO"("PROC_DEF_ID_");

CREATE INDEX "ACT_IDX_PRIV_GROUP"
ON "BLADEX"."ACT_ID_PRIV_MAPPING"("GROUP_ID_");

CREATE INDEX "ACT_IDX_PRIV_USER"
ON "BLADEX"."ACT_ID_PRIV_MAPPING"("USER_ID_");

CREATE INDEX "ACT_IDX_HI_PROCVAR_EXE"
ON "BLADEX"."ACT_HI_VARINST"("EXECUTION_ID_");

CREATE INDEX "ACT_IDX_HI_PROCVAR_PROC_INST"
ON "BLADEX"."ACT_HI_VARINST"("PROC_INST_ID_");

CREATE INDEX "ACT_IDX_HI_VAR_SUB_ID_TYPE"
ON "BLADEX"."ACT_HI_VARINST"("SUB_SCOPE_ID_","SCOPE_TYPE_");

CREATE INDEX "ACT_IDX_HI_VAR_SCOPE_ID_TYPE"
ON "BLADEX"."ACT_HI_VARINST"("SCOPE_ID_","SCOPE_TYPE_");

CREATE INDEX "ACT_IDX_HI_PROCVAR_NAME_TYPE"
ON "BLADEX"."ACT_HI_VARINST"("NAME_","VAR_TYPE_");

CREATE INDEX "ACT_IDX_HI_PROCVAR_TASK_ID"
ON "BLADEX"."ACT_HI_VARINST"("TASK_ID_");

CREATE INDEX "ACT_IDX_HI_TASK_SCOPE"
ON "BLADEX"."ACT_HI_TASKINST"("SCOPE_ID_","SCOPE_TYPE_");

CREATE INDEX "ACT_IDX_HI_TASK_SUB_SCOPE"
ON "BLADEX"."ACT_HI_TASKINST"("SUB_SCOPE_ID_","SCOPE_TYPE_");

CREATE INDEX "ACT_IDX_HI_TASK_SCOPE_DEF"
ON "BLADEX"."ACT_HI_TASKINST"("SCOPE_DEFINITION_ID_","SCOPE_TYPE_");

CREATE INDEX "ACT_IDX_HI_TASK_INST_PROCINST"
ON "BLADEX"."ACT_HI_TASKINST"("PROC_INST_ID_");

CREATE INDEX "ACT_IDX_HI_PRO_INST_END"
ON "BLADEX"."ACT_HI_PROCINST"("END_TIME_");

CREATE INDEX "ACT_IDX_HI_PRO_I_BUSKEY"
ON "BLADEX"."ACT_HI_PROCINST"("BUSINESS_KEY_");

CREATE INDEX "ACT_IDX_HI_PRO_SUPER_PROCINST"
ON "BLADEX"."ACT_HI_PROCINST"("SUPER_PROCESS_INSTANCE_ID_");

CREATE INDEX "ACT_IDX_HI_IDENT_LNK_TASK"
ON "BLADEX"."ACT_HI_IDENTITYLINK"("TASK_ID_");

CREATE INDEX "ACT_IDX_HI_IDENT_LNK_SCOPE"
ON "BLADEX"."ACT_HI_IDENTITYLINK"("SCOPE_ID_","SCOPE_TYPE_");

CREATE INDEX "ACT_IDX_HI_IDENT_LNK_PROCINST"
ON "BLADEX"."ACT_HI_IDENTITYLINK"("PROC_INST_ID_");

CREATE INDEX "ACT_IDX_HI_IDENT_LNK_SCOPE_DEF"
ON "BLADEX"."ACT_HI_IDENTITYLINK"("SCOPE_DEFINITION_ID_","SCOPE_TYPE_");

CREATE INDEX "ACT_IDX_HI_IDENT_LNK_USER"
ON "BLADEX"."ACT_HI_IDENTITYLINK"("USER_ID_");

CREATE INDEX "ACT_IDX_HI_IDENT_LNK_SUB_SCOPE"
ON "BLADEX"."ACT_HI_IDENTITYLINK"("SUB_SCOPE_ID_","SCOPE_TYPE_");

CREATE INDEX "ACT_IDX_HI_ENT_LNK_SCOPE"
ON "BLADEX"."ACT_HI_ENTITYLINK"("SCOPE_ID_","SCOPE_TYPE_","LINK_TYPE_");

CREATE INDEX "ACT_IDX_HI_ENT_LNK_ROOT_SCOPE"
ON "BLADEX"."ACT_HI_ENTITYLINK"("ROOT_SCOPE_ID_","ROOT_SCOPE_TYPE_","LINK_TYPE_");

CREATE INDEX "ACT_IDX_HI_ENT_LNK_SCOPE_DEF"
ON "BLADEX"."ACT_HI_ENTITYLINK"("SCOPE_DEFINITION_ID_","SCOPE_TYPE_","LINK_TYPE_");

CREATE INDEX "ACT_IDX_HI_ENT_LNK_REF_SCOPE"
ON "BLADEX"."ACT_HI_ENTITYLINK"("REF_SCOPE_ID_","REF_SCOPE_TYPE_","LINK_TYPE_");

CREATE INDEX "ACT_IDX_HI_DETAIL_ACT_INST"
ON "BLADEX"."ACT_HI_DETAIL"("ACT_INST_ID_");

CREATE INDEX "ACT_IDX_HI_DETAIL_PROC_INST"
ON "BLADEX"."ACT_HI_DETAIL"("PROC_INST_ID_");

CREATE INDEX "ACT_IDX_HI_DETAIL_NAME"
ON "BLADEX"."ACT_HI_DETAIL"("NAME_");

CREATE INDEX "ACT_IDX_HI_DETAIL_TIME"
ON "BLADEX"."ACT_HI_DETAIL"("TIME_");

CREATE INDEX "ACT_IDX_HI_DETAIL_TASK_ID"
ON "BLADEX"."ACT_HI_DETAIL"("TASK_ID_");

CREATE INDEX "ACT_IDX_HI_ACT_INST_START"
ON "BLADEX"."ACT_HI_ACTINST"("START_TIME_");

CREATE INDEX "ACT_IDX_HI_ACT_INST_PROCINST"
ON "BLADEX"."ACT_HI_ACTINST"("PROC_INST_ID_","ACT_ID_");

CREATE INDEX "ACT_IDX_HI_ACT_INST_END"
ON "BLADEX"."ACT_HI_ACTINST"("END_TIME_");

CREATE INDEX "ACT_IDX_HI_ACT_INST_EXEC"
ON "BLADEX"."ACT_HI_ACTINST"("EXECUTION_ID_","ACT_ID_");

CREATE INDEX "ACT_IDX_DMN_INSTANCE_ID"
ON "BLADEX"."ACT_DMN_HI_DECISION_EXECUTION"("INSTANCE_ID_");

CREATE INDEX "IDX_PROC_MOD_HISTORY_PROC"
ON "BLADEX"."ACT_DE_MODEL_HISTORY"("MODEL_ID");

CREATE INDEX "IDX_PROC_MOD_CREATED"
ON "BLADEX"."ACT_DE_MODEL"("CREATED_BY");

CREATE INDEX "IDX_CONTITEM_SCOPE"
ON "BLADEX"."ACT_CO_CONTENT_ITEM"("SCOPE_ID_","SCOPE_TYPE_");

CREATE INDEX "IDX_CONTITEM_PROCID"
ON "BLADEX"."ACT_CO_CONTENT_ITEM"("PROC_INST_ID_");

CREATE INDEX "IDX_CONTITEM_TASKID"
ON "BLADEX"."ACT_CO_CONTENT_ITEM"("TASK_ID_");

CREATE INDEX "ACT_IDX_SENTRY_CASE_DEF"
ON "BLADEX"."ACT_CMMN_RU_SENTRY_PART_INST"("CASE_DEF_ID_");

CREATE INDEX "ACT_IDX_SENTRY_CASE_INST"
ON "BLADEX"."ACT_CMMN_RU_SENTRY_PART_INST"("CASE_INST_ID_");

CREATE INDEX "ACT_IDX_SENTRY_PLAN_ITEM"
ON "BLADEX"."ACT_CMMN_RU_SENTRY_PART_INST"("PLAN_ITEM_INST_ID_");

CREATE INDEX "ACT_IDX_PLAN_ITEM_CASE_INST"
ON "BLADEX"."ACT_CMMN_RU_PLAN_ITEM_INST"("CASE_INST_ID_");

CREATE INDEX "ACT_IDX_PLAN_ITEM_STAGE_INST"
ON "BLADEX"."ACT_CMMN_RU_PLAN_ITEM_INST"("STAGE_INST_ID_");

CREATE INDEX "ACT_IDX_PLAN_ITEM_CASE_DEF"
ON "BLADEX"."ACT_CMMN_RU_PLAN_ITEM_INST"("CASE_DEF_ID_");

CREATE INDEX "ACT_IDX_MIL_CASE_INST"
ON "BLADEX"."ACT_CMMN_RU_MIL_INST"("CASE_INST_ID_");

CREATE INDEX "ACT_IDX_MIL_CASE_DEF"
ON "BLADEX"."ACT_CMMN_RU_MIL_INST"("CASE_DEF_ID_");

CREATE INDEX "ACT_IDX_CASE_INST_REF_ID_"
ON "BLADEX"."ACT_CMMN_RU_CASE_INST"("REFERENCE_ID_");

CREATE INDEX "ACT_IDX_CASE_INST_PARENT"
ON "BLADEX"."ACT_CMMN_RU_CASE_INST"("PARENT_ID_");

CREATE INDEX "ACT_IDX_CASE_INST_CASE_DEF"
ON "BLADEX"."ACT_CMMN_RU_CASE_INST"("CASE_DEF_ID_");

CREATE INDEX "ACT_IDX_HI_PLAN_ITEM_INST_CASE"
ON "BLADEX"."ACT_CMMN_HI_PLAN_ITEM_INST"("CASE_INST_ID_");

CREATE INDEX "ACT_IDX_HI_CASE_INST_END"
ON "BLADEX"."ACT_CMMN_HI_CASE_INST"("END_TIME_");

CREATE INDEX "ACT_IDX_CMMN_RSRC_DPL"
ON "BLADEX"."ACT_CMMN_DEPLOYMENT_RESOURCE"("DEPLOYMENT_ID_");

CREATE INDEX "ACT_IDX_CASE_DEF_DPLY"
ON "BLADEX"."ACT_CMMN_CASEDEF"("DEPLOYMENT_ID_");

CREATE INDEX "ACT_IDX_APP_RSRC_DPL"
ON "BLADEX"."ACT_APP_DEPLOYMENT_RESOURCE"("DEPLOYMENT_ID_");

CREATE INDEX "ACT_IDX_APP_DEF_DPLY"
ON "BLADEX"."ACT_APP_APPDEF"("DEPLOYMENT_ID_");


INSERT INTO "BLADEX"."ACT_APP_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('1','flowable','org/flowable/app/db/liquibase/flowable-app-db-changelog.xml','2019-08-01 03:08:02.000000',1,'EXECUTED','9:959783069c0c7ce80320a0617aa48969','createTable tableName=ACT_APP_DEPLOYMENT; createTable tableName=ACT_APP_DEPLOYMENT_RESOURCE; addForeignKeyConstraint baseTableName=ACT_APP_DEPLOYMENT_RESOURCE, constraintName=ACT_FK_APP_RSRC_DPL, referencedTableName=ACT_APP_DEPLOYMENT; createIndex...','',null,'3.6.3',null,null,'4628882015');
INSERT INTO "BLADEX"."ACT_APP_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('2','flowable','org/flowable/app/db/liquibase/flowable-app-db-changelog.xml','2019-08-01 03:08:02.000000',2,'EXECUTED','9:c75407b1c0e16adf2a6db585c05a94c7','modifyDataType columnName=DEPLOY_TIME_, tableName=ACT_APP_DEPLOYMENT','',null,'3.6.3',null,null,'4628882015');
INSERT INTO "BLADEX"."ACT_APP_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('3','flowable','org/flowable/app/db/liquibase/flowable-app-db-changelog.xml','2019-08-01 03:08:02.000000',3,'EXECUTED','9:c05b79a3b00e95136533085718361208','createIndex indexName=ACT_IDX_APP_DEF_UNIQ, tableName=ACT_APP_APPDEF','',null,'3.6.3',null,null,'4628882015');

INSERT INTO "BLADEX"."ACT_APP_DATABASECHANGELOGLOCK"("ID","LOCKED","LOCKGRANTED","LOCKEDBY") VALUES(1,0,null,null);

INSERT INTO "BLADEX"."ACT_CMMN_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('1','flowable','org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml','2019-08-01 03:07:59.000000',1,'EXECUTED','9:d0cc0aaadf0e4ef70c5b412cd05fadc4','createTable tableName=ACT_CMMN_DEPLOYMENT; createTable tableName=ACT_CMMN_DEPLOYMENT_RESOURCE; addForeignKeyConstraint baseTableName=ACT_CMMN_DEPLOYMENT_RESOURCE, constraintName=ACT_FK_CMMN_RSRC_DPL, referencedTableName=ACT_CMMN_DEPLOYMENT; create...','',null,'3.6.3',null,null,'4628878437');
INSERT INTO "BLADEX"."ACT_CMMN_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('2','flowable','org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml','2019-08-01 03:07:59.000000',2,'EXECUTED','9:8095a5a8a222a100c2d0310cacbda5e7','addColumn tableName=ACT_CMMN_CASEDEF; addColumn tableName=ACT_CMMN_DEPLOYMENT_RESOURCE; addColumn tableName=ACT_CMMN_RU_CASE_INST; addColumn tableName=ACT_CMMN_RU_PLAN_ITEM_INST','',null,'3.6.3',null,null,'4628878437');
INSERT INTO "BLADEX"."ACT_CMMN_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('3','flowable','org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml','2019-08-01 03:08:00.000000',3,'EXECUTED','9:f031b4f0ae67bc5a640736b379049b12','addColumn tableName=ACT_CMMN_RU_PLAN_ITEM_INST; addColumn tableName=ACT_CMMN_RU_CASE_INST; createIndex indexName=ACT_IDX_PLAN_ITEM_STAGE_INST, tableName=ACT_CMMN_RU_PLAN_ITEM_INST; addColumn tableName=ACT_CMMN_RU_PLAN_ITEM_INST; addColumn tableNam...','',null,'3.6.3',null,null,'4628878437');
INSERT INTO "BLADEX"."ACT_CMMN_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('4','flowable','org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml','2019-08-01 03:08:00.000000',4,'EXECUTED','9:c484ecfb08719feccac2f80fc962dda9','createTable tableName=ACT_CMMN_HI_PLAN_ITEM_INST; addColumn tableName=ACT_CMMN_RU_MIL_INST; addColumn tableName=ACT_CMMN_HI_MIL_INST','',null,'3.6.3',null,null,'4628878437');
INSERT INTO "BLADEX"."ACT_CMMN_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('5','flowable','org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml','2019-08-01 03:08:01.000000',5,'EXECUTED','9:e6a67f8f0d16cd72117900442acfe6e0','modifyDataType columnName=DEPLOY_TIME_, tableName=ACT_CMMN_DEPLOYMENT; modifyDataType columnName=START_TIME_, tableName=ACT_CMMN_RU_CASE_INST; modifyDataType columnName=START_TIME_, tableName=ACT_CMMN_RU_PLAN_ITEM_INST; modifyDataType columnName=T...','',null,'3.6.3',null,null,'4628878437');
INSERT INTO "BLADEX"."ACT_CMMN_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('6','flowable','org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml','2019-08-01 03:08:01.000000',6,'EXECUTED','9:7343ab247d959e5add9278b5386de833','createIndex indexName=ACT_IDX_CASE_DEF_UNIQ, tableName=ACT_CMMN_CASEDEF','',null,'3.6.3',null,null,'4628878437');
INSERT INTO "BLADEX"."ACT_CMMN_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('7','flowable','org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml','2019-08-01 03:08:01.000000',7,'EXECUTED','9:d73200db684b6cdb748cc03570d5d2e9','renameColumn newColumnName=CREATE_TIME_, oldColumnName=START_TIME_, tableName=ACT_CMMN_RU_PLAN_ITEM_INST; renameColumn newColumnName=CREATE_TIME_, oldColumnName=CREATED_TIME_, tableName=ACT_CMMN_HI_PLAN_ITEM_INST; addColumn tableName=ACT_CMMN_RU_P...','',null,'3.6.3',null,null,'4628878437');
INSERT INTO "BLADEX"."ACT_CMMN_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('8','flowable','org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml','2024-03-14 17:42:56.000000',8,'EXECUTED','9:eda5e43816221f2d8554bfcc90f1c37e','addColumn tableName=ACT_CMMN_HI_PLAN_ITEM_INST','',null,'4.24.0',null,null,'0438176530');
INSERT INTO "BLADEX"."ACT_CMMN_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('9','flowable','org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml','2024-03-14 17:42:56.000000',9,'EXECUTED','9:c34685611779075a73caf8c380f078ea','addColumn tableName=ACT_CMMN_RU_PLAN_ITEM_INST; addColumn tableName=ACT_CMMN_HI_PLAN_ITEM_INST','',null,'4.24.0',null,null,'0438176530');
INSERT INTO "BLADEX"."ACT_CMMN_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('10','flowable','org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml','2024-03-14 17:42:56.000000',10,'EXECUTED','9:368e9472ad2348206205170d6c52d58e','addColumn tableName=ACT_CMMN_RU_CASE_INST; addColumn tableName=ACT_CMMN_RU_CASE_INST; createIndex indexName=ACT_IDX_CASE_INST_REF_ID_, tableName=ACT_CMMN_RU_CASE_INST; addColumn tableName=ACT_CMMN_HI_CASE_INST; addColumn tableName=ACT_CMMN_HI_CASE...','',null,'4.24.0',null,null,'0438176530');
INSERT INTO "BLADEX"."ACT_CMMN_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('11','flowable','org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml','2024-03-14 17:42:57.000000',11,'EXECUTED','9:e54b50ceb2bcd5355ae4dfb56d9ff3ad','addColumn tableName=ACT_CMMN_RU_PLAN_ITEM_INST; addColumn tableName=ACT_CMMN_HI_PLAN_ITEM_INST','',null,'4.24.0',null,null,'0438176530');
INSERT INTO "BLADEX"."ACT_CMMN_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('12','flowable','org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml','2024-03-14 17:42:57.000000',12,'EXECUTED','9:f53f262768d04e74529f43fcd93429b0','addColumn tableName=ACT_CMMN_RU_CASE_INST','',null,'4.24.0',null,null,'0438176530');
INSERT INTO "BLADEX"."ACT_CMMN_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('13','flowable','org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml','2024-03-14 17:42:57.000000',13,'EXECUTED','9:64e7eafbe97997094654e83caea99895','addColumn tableName=ACT_CMMN_RU_PLAN_ITEM_INST; addColumn tableName=ACT_CMMN_HI_PLAN_ITEM_INST','',null,'4.24.0',null,null,'0438176530');
INSERT INTO "BLADEX"."ACT_CMMN_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('14','flowable','org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml','2024-03-14 17:42:57.000000',14,'EXECUTED','9:ab7d934abde497eac034701542e0a281','addColumn tableName=ACT_CMMN_RU_CASE_INST; addColumn tableName=ACT_CMMN_HI_CASE_INST','',null,'4.24.0',null,null,'0438176530');
INSERT INTO "BLADEX"."ACT_CMMN_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('16','flowable','org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml','2024-03-14 17:42:57.000000',15,'EXECUTED','9:03928d422e510959770e7a9daa5a993f','addColumn tableName=ACT_CMMN_RU_CASE_INST; addColumn tableName=ACT_CMMN_HI_CASE_INST','',null,'4.24.0',null,null,'0438176530');
INSERT INTO "BLADEX"."ACT_CMMN_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('17','flowable','org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml','2024-03-14 17:42:57.000000',16,'EXECUTED','9:f30304cf001d6eac78c793ea88cd5781','createIndex indexName=ACT_IDX_HI_CASE_INST_END, tableName=ACT_CMMN_HI_CASE_INST','',null,'4.24.0',null,null,'0438176530');
INSERT INTO "BLADEX"."ACT_CMMN_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('18','flowable','org/flowable/cmmn/db/liquibase/flowable-cmmn-db-changelog.xml','2024-03-14 17:42:57.000000',17,'EXECUTED','9:d782865087d6c0c3dc033ac20e783008','createIndex indexName=ACT_IDX_HI_PLAN_ITEM_INST_CASE, tableName=ACT_CMMN_HI_PLAN_ITEM_INST','',null,'4.24.0',null,null,'0438176530');

INSERT INTO "BLADEX"."ACT_CMMN_DATABASECHANGELOGLOCK"("ID","LOCKED","LOCKGRANTED","LOCKEDBY") VALUES(1,0,null,null);

INSERT INTO "BLADEX"."ACT_CO_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('1','activiti','org/flowable/content/db/liquibase/flowable-content-db-changelog.xml','2019-08-01 03:07:58.000000',1,'EXECUTED','8:7644d7165cfe799200a2abdd3419e8b6','createTable tableName=ACT_CO_CONTENT_ITEM; createIndex indexName=idx_contitem_taskid, tableName=ACT_CO_CONTENT_ITEM; createIndex indexName=idx_contitem_procid, tableName=ACT_CO_CONTENT_ITEM','',null,'3.6.3',null,null,'4628877941');
INSERT INTO "BLADEX"."ACT_CO_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('2','flowable','org/flowable/content/db/liquibase/flowable-content-db-changelog.xml','2019-08-01 03:07:58.000000',2,'EXECUTED','8:fe7b11ac7dbbf9c43006b23bbab60bab','addColumn tableName=ACT_CO_CONTENT_ITEM; createIndex indexName=idx_contitem_scope, tableName=ACT_CO_CONTENT_ITEM','',null,'3.6.3',null,null,'4628877941');

INSERT INTO "BLADEX"."ACT_CO_DATABASECHANGELOGLOCK"("ID","LOCKED","LOCKGRANTED","LOCKEDBY") VALUES(1,0,null,null);

INSERT INTO "BLADEX"."ACT_DE_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('1','flowable','META-INF/liquibase/flowable-modeler-app-db-changelog.xml','2019-08-01 03:08:43.000000',1,'EXECUTED','8:e70d1d9d3899a734296b2514ccc71501','createTable tableName=ACT_DE_MODEL; createIndex indexName=idx_proc_mod_created, tableName=ACT_DE_MODEL; createTable tableName=ACT_DE_MODEL_HISTORY; createIndex indexName=idx_proc_mod_history_proc, tableName=ACT_DE_MODEL_HISTORY; createTable tableN...','',null,'3.6.3',null,null,'4628923400');
INSERT INTO "BLADEX"."ACT_DE_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('3','flowable','META-INF/liquibase/flowable-modeler-app-db-changelog.xml','2019-08-01 03:08:43.000000',2,'EXECUTED','8:3a9143bef2e45f2316231cc1369138b6','addColumn tableName=ACT_DE_MODEL; addColumn tableName=ACT_DE_MODEL_HISTORY','',null,'3.6.3',null,null,'4628923400');

INSERT INTO "BLADEX"."ACT_DE_DATABASECHANGELOGLOCK"("ID","LOCKED","LOCKGRANTED","LOCKEDBY") VALUES(1,0,null,null);

INSERT INTO "BLADEX"."ACT_DMN_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('1','activiti','org/flowable/dmn/db/liquibase/flowable-dmn-db-changelog.xml','2019-08-01 03:07:57.000000',1,'EXECUTED','9:5b36e70aee5a2e42f6e7a62ea5fa681b','createTable tableName=ACT_DMN_DEPLOYMENT; createTable tableName=ACT_DMN_DEPLOYMENT_RESOURCE; createTable tableName=ACT_DMN_DECISION_TABLE','',null,'3.6.3',null,null,'4628876950');
INSERT INTO "BLADEX"."ACT_DMN_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('2','flowable','org/flowable/dmn/db/liquibase/flowable-dmn-db-changelog.xml','2019-08-01 03:07:57.000000',2,'EXECUTED','9:fd13fa3f7af55d2b72f763fc261da30d','createTable tableName=ACT_DMN_HI_DECISION_EXECUTION','',null,'3.6.3',null,null,'4628876950');
INSERT INTO "BLADEX"."ACT_DMN_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('3','flowable','org/flowable/dmn/db/liquibase/flowable-dmn-db-changelog.xml','2019-08-01 03:07:57.000000',3,'EXECUTED','9:9f30e6a3557d4b4c713dbb2dcc141782','addColumn tableName=ACT_DMN_HI_DECISION_EXECUTION','',null,'3.6.3',null,null,'4628876950');
INSERT INTO "BLADEX"."ACT_DMN_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('4','flowable','org/flowable/dmn/db/liquibase/flowable-dmn-db-changelog.xml','2019-08-01 03:07:57.000000',4,'EXECUTED','9:41085fbde807dba96104ee75a2fcc4cc','dropColumn columnName=PARENT_DEPLOYMENT_ID_, tableName=ACT_DMN_DECISION_TABLE','',null,'3.6.3',null,null,'4628876950');
INSERT INTO "BLADEX"."ACT_DMN_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('5','flowable','org/flowable/dmn/db/liquibase/flowable-dmn-db-changelog.xml','2019-08-01 03:07:57.000000',5,'EXECUTED','9:169d906b6503ad6907b7e5cd0d70d004','modifyDataType columnName=DEPLOY_TIME_, tableName=ACT_DMN_DEPLOYMENT; modifyDataType columnName=START_TIME_, tableName=ACT_DMN_HI_DECISION_EXECUTION; modifyDataType columnName=END_TIME_, tableName=ACT_DMN_HI_DECISION_EXECUTION','',null,'3.6.3',null,null,'4628876950');
INSERT INTO "BLADEX"."ACT_DMN_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('6','flowable','org/flowable/dmn/db/liquibase/flowable-dmn-db-changelog.xml','2019-08-01 03:07:57.000000',6,'EXECUTED','9:f00f92f3ef1af3fc1604f0323630f9b1','createIndex indexName=ACT_IDX_DEC_TBL_UNIQ, tableName=ACT_DMN_DECISION_TABLE','',null,'3.6.3',null,null,'4628876950');
INSERT INTO "BLADEX"."ACT_DMN_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('7','flowable','org/flowable/dmn/db/liquibase/flowable-dmn-db-changelog.xml','2024-03-14 17:42:55.000000',7,'EXECUTED','9:d24d4c5f44083b4edf1231a7a682a2cd','dropIndex indexName=ACT_IDX_DEC_TBL_UNIQ, tableName=ACT_DMN_DECISION_TABLE; renameTable newTableName=ACT_DMN_DECISION, oldTableName=ACT_DMN_DECISION_TABLE; createIndex indexName=ACT_IDX_DMN_DEC_UNIQ, tableName=ACT_DMN_DECISION','',null,'4.24.0',null,null,'0438175582');
INSERT INTO "BLADEX"."ACT_DMN_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('8','flowable','org/flowable/dmn/db/liquibase/flowable-dmn-db-changelog.xml','2024-03-14 17:42:55.000000',8,'EXECUTED','9:3998ef0958b46fe9c19458183952d2a0','addColumn tableName=ACT_DMN_DECISION','',null,'4.24.0',null,null,'0438175582');
INSERT INTO "BLADEX"."ACT_DMN_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('9','flowable','org/flowable/dmn/db/liquibase/flowable-dmn-db-changelog.xml','2024-03-14 17:42:55.000000',9,'EXECUTED','9:5c9dc65601456faa1aa12f8d3afe0e9e','createIndex indexName=ACT_IDX_DMN_INSTANCE_ID, tableName=ACT_DMN_HI_DECISION_EXECUTION','',null,'4.24.0',null,null,'0438175582');

INSERT INTO "BLADEX"."ACT_DMN_DATABASECHANGELOGLOCK"("ID","LOCKED","LOCKGRANTED","LOCKEDBY") VALUES(1,0,null,null);

SET IDENTITY_INSERT "BLADEX"."ACT_EVT_LOG" ON;
SET IDENTITY_INSERT "BLADEX"."ACT_EVT_LOG" OFF;
INSERT INTO "BLADEX"."ACT_FO_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('1','activiti','org/flowable/form/db/liquibase/flowable-form-db-changelog.xml','2019-08-01 03:07:57.000000',1,'EXECUTED','8:033ebf9380889aed7c453927ecc3250d','createTable tableName=ACT_FO_FORM_DEPLOYMENT; createTable tableName=ACT_FO_FORM_RESOURCE; createTable tableName=ACT_FO_FORM_DEFINITION; createTable tableName=ACT_FO_FORM_INSTANCE','',null,'3.6.3',null,null,'4628877478');
INSERT INTO "BLADEX"."ACT_FO_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('2','flowable','org/flowable/form/db/liquibase/flowable-form-db-changelog.xml','2019-08-01 03:07:57.000000',2,'EXECUTED','8:986365ceb40445ce3b27a8e6b40f159b','addColumn tableName=ACT_FO_FORM_INSTANCE','',null,'3.6.3',null,null,'4628877478');
INSERT INTO "BLADEX"."ACT_FO_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('3','flowable','org/flowable/form/db/liquibase/flowable-form-db-changelog.xml','2019-08-01 03:07:57.000000',3,'EXECUTED','8:abf482518ceb09830ef674e52c06bf15','dropColumn columnName=PARENT_DEPLOYMENT_ID_, tableName=ACT_FO_FORM_DEFINITION','',null,'3.6.3',null,null,'4628877478');
INSERT INTO "BLADEX"."ACT_FO_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('4','flowable','org/flowable/form/db/liquibase/flowable-form-db-changelog.xml','2019-08-01 03:07:57.000000',4,'EXECUTED','8:2087829f22a4b2298dbf530681c74854','modifyDataType columnName=DEPLOY_TIME_, tableName=ACT_FO_FORM_DEPLOYMENT; modifyDataType columnName=SUBMITTED_DATE_, tableName=ACT_FO_FORM_INSTANCE','',null,'3.6.3',null,null,'4628877478');
INSERT INTO "BLADEX"."ACT_FO_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('5','flowable','org/flowable/form/db/liquibase/flowable-form-db-changelog.xml','2019-08-01 03:07:57.000000',5,'EXECUTED','8:b4be732b89e5ca028bdd520c6ad4d446','createIndex indexName=ACT_IDX_FORM_DEF_UNIQ, tableName=ACT_FO_FORM_DEFINITION','',null,'3.6.3',null,null,'4628877478');

INSERT INTO "BLADEX"."ACT_FO_DATABASECHANGELOGLOCK"("ID","LOCKED","LOCKGRANTED","LOCKEDBY") VALUES(1,0,null,null);

INSERT INTO "BLADEX"."ACT_GE_PROPERTY"("NAME_","VALUE_","REV_") VALUES('batch.schema.version','*******',1);
INSERT INTO "BLADEX"."ACT_GE_PROPERTY"("NAME_","VALUE_","REV_") VALUES('cfg.execution-related-entities-count','true',1);
INSERT INTO "BLADEX"."ACT_GE_PROPERTY"("NAME_","VALUE_","REV_") VALUES('cfg.task-related-entities-count','true',1);
INSERT INTO "BLADEX"."ACT_GE_PROPERTY"("NAME_","VALUE_","REV_") VALUES('common.schema.version','*******',1);
INSERT INTO "BLADEX"."ACT_GE_PROPERTY"("NAME_","VALUE_","REV_") VALUES('entitylink.schema.version','*******',1);
INSERT INTO "BLADEX"."ACT_GE_PROPERTY"("NAME_","VALUE_","REV_") VALUES('eventsubscription.schema.version','*******',1);
INSERT INTO "BLADEX"."ACT_GE_PROPERTY"("NAME_","VALUE_","REV_") VALUES('identitylink.schema.version','*******',1);
INSERT INTO "BLADEX"."ACT_GE_PROPERTY"("NAME_","VALUE_","REV_") VALUES('job.schema.version','*******',1);
INSERT INTO "BLADEX"."ACT_GE_PROPERTY"("NAME_","VALUE_","REV_") VALUES('next.dbid','1',1);
INSERT INTO "BLADEX"."ACT_GE_PROPERTY"("NAME_","VALUE_","REV_") VALUES('schema.history','upgrade(*******->*******)',2);
INSERT INTO "BLADEX"."ACT_GE_PROPERTY"("NAME_","VALUE_","REV_") VALUES('schema.version','*******',2);
INSERT INTO "BLADEX"."ACT_GE_PROPERTY"("NAME_","VALUE_","REV_") VALUES('task.schema.version','*******',1);
INSERT INTO "BLADEX"."ACT_GE_PROPERTY"("NAME_","VALUE_","REV_") VALUES('variable.schema.version','*******',1);

SET IDENTITY_INSERT "BLADEX"."ACT_HI_TSK_LOG" ON;
SET IDENTITY_INSERT "BLADEX"."ACT_HI_TSK_LOG" OFF;
INSERT INTO "BLADEX"."ACT_ID_PROPERTY"("NAME_","VALUE_","REV_") VALUES('schema.version','*******',1);

INSERT INTO "BLADEX"."FLW_EV_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('1','flowable','org/flowable/eventregistry/db/liquibase/flowable-eventregistry-db-changelog.xml','2024-03-14 17:42:55.000000',1,'EXECUTED','9:63268f536c469325acef35970312551b','createTable tableName=FLW_EVENT_DEPLOYMENT; createTable tableName=FLW_EVENT_RESOURCE; createTable tableName=FLW_EVENT_DEFINITION; createIndex indexName=ACT_IDX_EVENT_DEF_UNIQ, tableName=FLW_EVENT_DEFINITION; createTable tableName=FLW_CHANNEL_DEFIN...','',null,'4.24.0',null,null,'0438174846');
INSERT INTO "BLADEX"."FLW_EV_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('2','flowable','org/flowable/eventregistry/db/liquibase/flowable-eventregistry-db-changelog.xml','2024-03-14 17:42:55.000000',2,'EXECUTED','9:dcb58b7dfd6dbda66939123a96985536','addColumn tableName=FLW_CHANNEL_DEFINITION; addColumn tableName=FLW_CHANNEL_DEFINITION','',null,'4.24.0',null,null,'0438174846');
INSERT INTO "BLADEX"."FLW_EV_DATABASECHANGELOG"("ID","AUTHOR","FILENAME","DATEEXECUTED","ORDEREXECUTED","EXECTYPE","MD5SUM","DESCRIPTION","COMMENTS","TAG","LIQUIBASE","CONTEXTS","LABELS","DEPLOYMENT_ID") VALUES('3','flowable','org/flowable/eventregistry/db/liquibase/flowable-eventregistry-db-changelog.xml','2024-03-14 17:42:55.000000',3,'EXECUTED','9:d0c05678d57af23ad93699991e3bf4f6','customChange','',null,'4.24.0',null,null,'0438174846');

INSERT INTO "BLADEX"."FLW_EV_DATABASECHANGELOGLOCK"("ID","LOCKED","LOCKGRANTED","LOCKEDBY") VALUES(1,0,null,null);

COMMIT;

