package org.springblade.ms.dingapp.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.*;
import com.dingtalk.api.response.*;
import com.taobao.api.ApiException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springblade.ms.dingapp.config.DingAppConfig;
import org.springblade.ms.dingapp.service.IDingAppUserService;
import org.springblade.ms.dingapp.service.IDingNotificationService;
import org.springblade.ms.dingapp.service.IMsDingUserService;
import org.springblade.ms.dingapp.vo.DingUserAccessTokenVO;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.Map;
import java.util.HashMap;

/**
 * 钉钉工作通知服务实现类
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Service
@AllArgsConstructor
@Slf4j
public class DingNotificationServiceImpl implements IDingNotificationService {

    private final DingAppConfig dingAppConfig;
    private final IDingAppUserService dingAppUserService;
    private final IMsDingUserService msDingUserService;

    /**
     * 获取访问令牌
     */
    private String getAccessToken() throws Exception {
        DingUserAccessTokenVO accessTokenVO = dingAppUserService.getAccessToken();
        if (accessTokenVO == null) {
            throw new RuntimeException("获取钉钉访问令牌失败");
        }
        return accessTokenVO.getAccessToken();
    }

    @Override
    public R<String> sendTextNotification(List<String> userIds, String title, String content) {
        if (CollUtil.isEmpty(userIds)) {
            return R.fail("接收者用户ID列表不能为空");
        }
        if (StrUtil.isBlank(content)) {
            return R.fail("消息内容不能为空");
        }

        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");
            OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
            request.setAgentId(Long.valueOf(dingAppConfig.getAgentId()));
            request.setUseridList(String.join(",", userIds));
            request.setToAllUser(false);

            OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
            msg.setMsgtype("text");
            msg.setText(new OapiMessageCorpconversationAsyncsendV2Request.Text());
            msg.getText().setContent(StrUtil.isBlank(title) ? content : title + "\n\n" + content);
            request.setMsg(msg);

            String accessToken = getAccessToken();
            OapiMessageCorpconversationAsyncsendV2Response response = client.execute(request, accessToken);

            if (response.isSuccess()) {
                return R.data(response.getTaskId().toString(), "发送成功");
            } else {
                log.error("发送钉钉工作通知失败: {}", response.getErrmsg());
                return R.fail("发送失败: " + response.getErrmsg());
            }
        } catch (ApiException e) {
            log.error("发送钉钉工作通知API异常", e);
            return R.fail("发送异常: " + e.getErrMsg());
        } catch (Exception e) {
            log.error("发送钉钉工作通知异常", e);
            return R.fail("发送异常: " + e.getMessage());
        }
    }

    @Override
    public R<String> sendTextNotificationToDept(List<String> deptIds, String title, String content) {
        if (CollUtil.isEmpty(deptIds)) {
            return R.fail("接收者部门ID列表不能为空");
        }
        if (StrUtil.isBlank(content)) {
            return R.fail("消息内容不能为空");
        }

        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");
            OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
            request.setAgentId(Long.valueOf(dingAppConfig.getAgentId()));
            request.setDeptIdList(String.join(",", deptIds));
            request.setToAllUser(false);

            OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
            msg.setMsgtype("text");
            msg.setText(new OapiMessageCorpconversationAsyncsendV2Request.Text());
            msg.getText().setContent(StrUtil.isBlank(title) ? content : title + "\n\n" + content);
            request.setMsg(msg);

            String accessToken = getAccessToken();
            OapiMessageCorpconversationAsyncsendV2Response response = client.execute(request, accessToken);

            if (response.isSuccess()) {
                return R.data(response.getTaskId().toString(), "发送成功");
            } else {
                log.error("发送钉钉工作通知失败: {}", response.getErrmsg());
                return R.fail("发送失败: " + response.getErrmsg());
            }
        } catch (ApiException e) {
            log.error("发送钉钉工作通知API异常", e);
            return R.fail("发送异常: " + e.getErrMsg());
        } catch (Exception e) {
            log.error("发送钉钉工作通知异常", e);
            return R.fail("发送异常: " + e.getMessage());
        }
    }

    @Override
    public R<String> sendLinkNotification(List<String> userIds, String title, String text, String messageUrl, String picUrl) {
        if (CollUtil.isEmpty(userIds)) {
            return R.fail("接收者用户ID列表不能为空");
        }
        if (StrUtil.isBlank(title) ) {
            return R.fail("消息标题不能为空");
        }

        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");
            OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
            request.setAgentId(Long.valueOf(dingAppConfig.getAgentId()));
            request.setUseridList(String.join(",", userIds));
            request.setToAllUser(false);

            OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
            msg.setMsgtype("link");
            msg.setLink(new OapiMessageCorpconversationAsyncsendV2Request.Link());
            msg.getLink().setTitle(title);
            msg.getLink().setText(text);

            String redirectUrl = null;
            if(StrUtil.isBlank(messageUrl)){
                redirectUrl = dingAppConfig.getBaseUrl();
            }else{
                redirectUrl = dingAppConfig.getBaseUrl() + messageUrl;
            }
            String encodedRedirectUrl = URLEncoder.encode(redirectUrl, StandardCharsets.UTF_8.toString());
            String url = "dingtalk://dingtalkclient/action/openapp?corpid="+dingAppConfig.getCorpId()+
                    "&container_type=work_platform&app_id=0_" +dingAppConfig.getAgentId() +
                    "&redirect_type=jump&redirect_url=" + encodedRedirectUrl;
            msg.getLink().setMessageUrl(url);
            if (StrUtil.isNotBlank(picUrl)) {
                msg.getLink().setPicUrl(picUrl);
            }
            request.setMsg(msg);

            String accessToken = getAccessToken();
            OapiMessageCorpconversationAsyncsendV2Response response = client.execute(request, accessToken);

            if (response.isSuccess()) {
                log.info("发送钉钉工作通知成功: {}", response.getBody());
                return R.data(response.getTaskId().toString(), "发送成功");
            } else {
                log.error("发送钉钉工作通知失败: {}", response.getErrmsg());
                return R.fail("发送失败: " + response.getErrmsg());
            }
        } catch (ApiException e) {
            log.error("发送钉钉工作通知API异常", e);
            return R.fail("发送异常: " + e.getErrMsg());
        } catch (Exception e) {
            log.error("发送钉钉工作通知异常", e);
            return R.fail("发送异常: " + e.getMessage());
        }
    }

    @Override
    public R<String> sendMarkdownNotification(List<String> userIds, String title, String markdownText) {
        if (CollUtil.isEmpty(userIds)) {
            return R.fail("接收者用户ID列表不能为空");
        }
        if (StrUtil.isBlank(title) || StrUtil.isBlank(markdownText)) {
            return R.fail("消息标题和Markdown内容不能为空");
        }

        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");
            OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
            request.setAgentId(Long.valueOf(dingAppConfig.getAgentId()));
            request.setUseridList(String.join(",", userIds));
            request.setToAllUser(false);

            OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
            msg.setMsgtype("markdown");
            msg.setMarkdown(new OapiMessageCorpconversationAsyncsendV2Request.Markdown());
            msg.getMarkdown().setTitle(title);
            msg.getMarkdown().setText(markdownText);
            request.setMsg(msg);

            String accessToken = getAccessToken();
            OapiMessageCorpconversationAsyncsendV2Response response = client.execute(request, accessToken);

            if (response.isSuccess()) {
                return R.data(response.getTaskId().toString(), "发送成功");
            } else {
                log.error("发送钉钉工作通知失败: {}", response.getErrmsg());
                return R.fail("发送失败: " + response.getErrmsg());
            }
        } catch (ApiException e) {
            log.error("发送钉钉工作通知API异常", e);
            return R.fail("发送异常: " + e.getErrMsg());
        } catch (Exception e) {
            log.error("发送钉钉工作通知异常", e);
            return R.fail("发送异常: " + e.getMessage());
        }
    }

    @Override
    public R<Object> getNotificationProgress(Long taskId) {
        if (taskId == null) {
            return R.fail("任务ID不能为空");
        }

        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/getsendprogress");
            OapiMessageCorpconversationGetsendprogressRequest request = new OapiMessageCorpconversationGetsendprogressRequest();
            request.setAgentId(Long.valueOf(dingAppConfig.getAgentId()));
            request.setTaskId(taskId);

            String accessToken = getAccessToken();
            OapiMessageCorpconversationGetsendprogressResponse response = client.execute(request, accessToken);

            if (response.isSuccess()) {
                return R.data(response, "查询成功");
            } else {
                log.error("查询钉钉工作通知进度失败: {}", response.getErrmsg());
                return R.fail("查询失败: " + response.getErrmsg());
            }
        } catch (ApiException e) {
            log.error("查询钉钉工作通知进度API异常", e);
            return R.fail("查询异常: " + e.getErrMsg());
        } catch (Exception e) {
            log.error("查询钉钉工作通知进度异常", e);
            return R.fail("查询异常: " + e.getMessage());
        }
    }

    @Override
    public R<Object> getNotificationResult(Long taskId) {
        if (taskId == null) {
            return R.fail("任务ID不能为空");
        }

        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/getsendresult");
            OapiMessageCorpconversationGetsendresultRequest request = new OapiMessageCorpconversationGetsendresultRequest();
            request.setAgentId(Long.valueOf(dingAppConfig.getAgentId()));
            request.setTaskId(taskId);

            String accessToken = getAccessToken();
            OapiMessageCorpconversationGetsendresultResponse response = client.execute(request, accessToken);

            if (response.isSuccess()) {
                return R.data(response, "查询成功");
            } else {
                log.error("查询钉钉工作通知结果失败: {}", response.getErrmsg());
                return R.fail("查询失败: " + response.getErrmsg());
            }
        } catch (ApiException e) {
            log.error("查询钉钉工作通知结果API异常", e);
            return R.fail("查询异常: " + e.getErrMsg());
        } catch (Exception e) {
            log.error("查询钉钉工作通知结果异常", e);
            return R.fail("查询异常: " + e.getMessage());
        }
    }

    @Override
    public R<Boolean> recallNotification(Long taskId) {
        if (taskId == null) {
            return R.fail("任务ID不能为空");
        }

        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/recall");
            OapiMessageCorpconversationRecallRequest request = new OapiMessageCorpconversationRecallRequest();
            request.setAgentId(Long.valueOf(dingAppConfig.getAgentId()));
            request.setMsgTaskId(taskId);

            String accessToken = getAccessToken();
            OapiMessageCorpconversationRecallResponse response = client.execute(request, accessToken);

            if (response.isSuccess()) {
                return R.data(true, "撤回成功");
            } else {
                log.error("撤回钉钉工作通知失败: {}", response.getErrmsg());
                return R.fail("撤回失败: " + response.getErrmsg());
            }
        } catch (ApiException e) {
            log.error("撤回钉钉工作通知API异常", e);
            return R.fail("撤回异常: " + e.getErrMsg());
        } catch (Exception e) {
            log.error("撤回钉钉工作通知异常", e);
            return R.fail("撤回异常: " + e.getMessage());
        }
    }

    @Override
    public R<Object> getDepartmentList() {
        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/department/listsub");
            OapiV2DepartmentListsubRequest request = new OapiV2DepartmentListsubRequest();
            request.setDeptId(1L); // 根部门ID为1
            request.setLanguage("zh_CN");

            String accessToken = getAccessToken();
            OapiV2DepartmentListsubResponse response = client.execute(request, accessToken);

            if (response.isSuccess()) {
                return R.data(response.getResult(), "获取部门列表成功");
            } else {
                log.error("获取钉钉部门列表失败: {}", response.getErrmsg());
                return R.fail("获取部门列表失败: " + response.getErrmsg());
            }
        } catch (ApiException e) {
            log.error("获取钉钉部门列表API异常", e);
            return R.fail("获取部门列表异常: " + e.getErrMsg());
        } catch (Exception e) {
            log.error("获取钉钉部门列表异常", e);
            return R.fail("获取部门列表异常: " + e.getMessage());
        }
    }





    @Override
    public R<Object> getDepartmentUserIds(String deptId) {
        if (StrUtil.isBlank(deptId)) {
            return R.fail("部门ID不能为空");
        }

        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/user/listid");
            OapiUserListidRequest request = new OapiUserListidRequest();
            request.setDeptId(Long.valueOf(deptId));

            String accessToken = getAccessToken();
            OapiUserListidResponse response = client.execute(request, accessToken);

            if (response.isSuccess()) {
                return R.data(response.getResult(), "获取部门用户ID列表成功");
            } else {
                log.error("获取钉钉部门用户ID列表失败: {}", response.getErrmsg());
                return R.fail("获取部门用户ID列表失败: " + response.getErrmsg());
            }
        } catch (ApiException e) {
            log.error("获取钉钉部门用户ID列表API异常", e);
            return R.fail("获取部门用户ID列表异常: " + e.getErrMsg());
        } catch (Exception e) {
            log.error("获取钉钉部门用户ID列表异常", e);
            return R.fail("获取部门用户ID列表异常: " + e.getMessage());
        }
    }

    @Override
    public R<Object> getAllDepartmentUserIds() {
        try {
            // 首先获取所有部门
            R<Object> deptResult = getDepartmentList();
            if (!deptResult.isSuccess()) {
                return R.fail("获取部门列表失败: " + deptResult.getMsg());
            }

            // 获取所有部门的用户ID
            Map<String, Object> result = new HashMap<>();
            List<Object> allDepartmentUserIds = CollUtil.newArrayList();

            // 递归获取所有部门及其用户ID
            getAllDepartmentUserIdsRecursive("1", allDepartmentUserIds);

            result.put("departments", allDepartmentUserIds);
            result.put("total", allDepartmentUserIds.size());

            return R.data(result, "获取所有部门用户ID列表成功");
        } catch (Exception e) {
            log.error("获取所有部门用户ID列表异常", e);
            return R.fail("获取所有部门用户ID列表异常: " + e.getMessage());
        }
    }

    /**
     * 递归获取所有部门的用户ID
     */
    private void getAllDepartmentUserIdsRecursive(String deptId, List<Object> allUserIds) {
        try {
            log.info("开始获取部门{}的用户ID", deptId);

            // 获取当前部门的用户ID
            R<Object> userIdsResult = getDepartmentUserIds(deptId);
            if (userIdsResult.isSuccess()) {
                Object userIds = userIdsResult.getData();
                if (userIds != null) {
                    Map<String, Object> deptUserIdInfo = new HashMap<>();
                    deptUserIdInfo.put("deptId", deptId);
                    deptUserIdInfo.put("userIds", userIds);
                    allUserIds.add(deptUserIdInfo);

                    // 记录当前部门的用户数量
                    if (userIds instanceof OapiUserListidResponse.ListUserByDeptResponse) {
                        OapiUserListidResponse.ListUserByDeptResponse response =
                            (OapiUserListidResponse.ListUserByDeptResponse) userIds;
                        int userCount = response.getUseridList() != null ? response.getUseridList().size() : 0;
                        log.info("部门{}有{}个用户", deptId, userCount);
                    }
                }
            } else {
                log.warn("获取部门{}用户ID失败: {}", deptId, userIdsResult.getMsg());
            }

            // 获取子部门列表
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/department/listsub");
            OapiV2DepartmentListsubRequest request = new OapiV2DepartmentListsubRequest();
            request.setDeptId(Long.valueOf(deptId));
            request.setLanguage("zh_CN");

            String accessToken = getAccessToken();
            OapiV2DepartmentListsubResponse response = client.execute(request, accessToken);

            if (response.isSuccess() && response.getResult() != null) {
                // 根据反编译的代码，getResult()直接返回List<DeptBaseResponse>
                List<OapiV2DepartmentListsubResponse.DeptBaseResponse> subDeptList = response.getResult();
                log.info("部门{}有{}个子部门", deptId, subDeptList.size());

                if (!subDeptList.isEmpty()) {
                    // 递归处理每个子部门
                    for (OapiV2DepartmentListsubResponse.DeptBaseResponse subDept : subDeptList) {
                        if (subDept.getDeptId() != null) {
                            log.info("递归处理子部门: {}, 名称: {}", subDept.getDeptId(), subDept.getName());
                            getAllDepartmentUserIdsRecursive(subDept.getDeptId().toString(), allUserIds);
                        }
                    }
                } else {
                    log.info("部门{}没有子部门", deptId);
                }
            } else {
                log.warn("获取部门{}子部门列表失败: {}", deptId, response.getErrmsg());
            }
        } catch (Exception e) {
            log.error("递归获取部门{}用户ID失败: {}", deptId, e.getMessage());
        }
    }

    @Override
    public R<Object> getUserDetail(String userId) {
        if (StrUtil.isBlank(userId)) {
            return R.fail("用户ID不能为空");
        }

        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/get");
            OapiV2UserGetRequest request = new OapiV2UserGetRequest();
            request.setUserid(userId);
            request.setLanguage("zh_CN");

            String accessToken = getAccessToken();
            OapiV2UserGetResponse response = client.execute(request, accessToken);

            if (response.isSuccess()) {
                return R.data(response.getResult(), "获取用户详细信息成功");
            } else {
                log.error("获取钉钉用户详细信息失败: {}", response.getErrmsg());
                return R.fail("获取用户详细信息失败: " + response.getErrmsg());
            }
        } catch (ApiException e) {
            log.error("获取钉钉用户详细信息API异常", e);
            return R.fail("获取用户详细信息异常: " + e.getErrMsg());
        } catch (Exception e) {
            log.error("获取钉钉用户详细信息异常", e);
            return R.fail("获取用户详细信息异常: " + e.getMessage());
        }
    }

    @Override
    public R<Object> getBatchUserDetails(List<String> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return R.fail("用户ID列表不能为空");
        }

        if (userIds.size() > 100) {
            return R.fail("批量查询用户数量不能超过100个");
        }

        try {
            List<Object> userDetails = CollUtil.newArrayList();
            Map<String, Object> result = new HashMap<>();

            // 批量获取用户详细信息
            for (String userId : userIds) {
                R<Object> userDetailResult = getUserDetail(userId);
                if (userDetailResult.isSuccess()) {
                    Map<String, Object> userInfo = new HashMap<>();
                    userInfo.put("userId", userId);
                    userInfo.put("userDetail", userDetailResult.getData());
                    userDetails.add(userInfo);
                } else {
                    // 记录失败的用户ID
                    Map<String, Object> failedUser = new HashMap<>();
                    failedUser.put("userId", userId);
                    failedUser.put("error", userDetailResult.getMsg());
                    userDetails.add(failedUser);
                }
            }

            result.put("users", userDetails);
            result.put("total", userDetails.size());
            result.put("requestCount", userIds.size());

            return R.data(result, "批量获取用户详细信息完成");
        } catch (Exception e) {
            log.error("批量获取用户详细信息异常", e);
            return R.fail("批量获取用户详细信息异常: " + e.getMessage());
        }
    }

    @Override
    public R<Object> getAllUsersWithDetails() {
        try {
            log.info("开始获取所有用户基本信息");

            // 使用递归方法获取所有部门的用户ID
            R<Object> allDeptUserIdsResult = getAllDepartmentUserIds();
            if (!allDeptUserIdsResult.isSuccess()) {
                return R.fail("获取所有部门用户ID失败: " + allDeptUserIdsResult.getMsg());
            }

            // 从所有部门中提取用户ID列表并去重
            List<String> allUserIds = CollUtil.newArrayList();
            Object allDeptData = allDeptUserIdsResult.getData();

            if (allDeptData instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> deptDataMap = (Map<String, Object>) allDeptData;
                Object departmentsObj = deptDataMap.get("departments");

                if (departmentsObj instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<Object> departments = (List<Object>) departmentsObj;

                    for (Object deptObj : departments) {
                        if (deptObj instanceof Map) {
                            @SuppressWarnings("unchecked")
                            Map<String, Object> deptInfo = (Map<String, Object>) deptObj;
                            Object userIdsObj = deptInfo.get("userIds");

                            if (userIdsObj instanceof OapiUserListidResponse.ListUserByDeptResponse) {
                                OapiUserListidResponse.ListUserByDeptResponse response =
                                    (OapiUserListidResponse.ListUserByDeptResponse) userIdsObj;
                                if (response.getUseridList() != null) {
                                    allUserIds.addAll(response.getUseridList());
                                }
                            }
                        }
                    }
                }
            }

            // 去重用户ID
            List<String> uniqueUserIds = allUserIds.stream().distinct().collect(Collectors.toList());
            log.info("从所有部门获取到{}个用户ID，去重后{}个", allUserIds.size(), uniqueUserIds.size());

            if (CollUtil.isEmpty(uniqueUserIds)) {
                Map<String, Object> emptyResult = new HashMap<>();
                emptyResult.put("users", CollUtil.newArrayList());
                emptyResult.put("totalUsers", 0);
                return R.data(emptyResult, "未找到任何用户");
            }

            // 获取用户基本信息
            List<Map<String, Object>> allUsers = CollUtil.newArrayList();

            for (String userId : uniqueUserIds) {
                try {
                    R<Object> userDetailResult = getUserDetail(userId);
                    if (userDetailResult.isSuccess()) {
                        Object userDetailData = userDetailResult.getData();
                        if (userDetailData instanceof OapiV2UserGetResponse.UserGetResponse) {
                            OapiV2UserGetResponse.UserGetResponse userDetail =
                                (OapiV2UserGetResponse.UserGetResponse) userDetailData;

                            // 只提取基本信息
                            Map<String, Object> basicUserInfo = new HashMap<>();
                            basicUserInfo.put("userid", userDetail.getUserid());
                            basicUserInfo.put("name", userDetail.getName());
                            basicUserInfo.put("mobile", userDetail.getMobile());
                            basicUserInfo.put("title", userDetail.getTitle());
                            basicUserInfo.put("email", userDetail.getEmail());
                            basicUserInfo.put("deptIdList", userDetail.getDeptIdList());
                            allUsers.add(basicUserInfo);
                        }
                    } else {
                        log.warn("获取用户{}详细信息失败: {}", userId, userDetailResult.getMsg());
                    }
                } catch (Exception e) {
                    log.error("获取用户{}详细信息异常: {}", userId, e.getMessage());
                }
            }

            Map<String, Object> result = new HashMap<>();
            result.put("users", allUsers);
            result.put("totalUsers", allUsers.size());

            log.info("获取所有用户基本信息完成，总用户数: {}", allUsers.size());

            return R.data(result, String.format("获取所有用户基本信息完成，共%d个用户", allUsers.size()));

        } catch (Exception e) {
            log.error("获取所有用户基本信息异常", e);
            return R.fail("获取所有用户基本信息异常: " + e.getMessage());
        }
    }

    @Override
    public R<Object> getSpecificDepartmentsUsers() {
        try {
            // 硬编码指定的部门ID列表 - 请根据需要修改这些部门ID
            List<String> targetDeptIds = List.of(
                "910580500",  // 请替换为实际的部门ID
                "644375942",  // 请替换为实际的部门ID
                "911034169",
                "964433628",
                "697986507"   // 请替换为实际的部门ID
            );

            log.info("开始获取指定部门的用户信息，部门ID列表: {}", targetDeptIds);

            // 收集所有用户ID
            List<String> allUserIds = new ArrayList<>();
            Map<String, Object> departmentInfo = new HashMap<>();

            for (String deptId : targetDeptIds) {
                log.info("正在获取部门{}的用户ID列表", deptId);
                R<Object> userIdsResult = getDepartmentUserIds(deptId);

                if (userIdsResult.isSuccess()) {
                    Object userIdsData = userIdsResult.getData();
                    if (userIdsData instanceof OapiUserListidResponse.ListUserByDeptResponse) {
                        OapiUserListidResponse.ListUserByDeptResponse response =
                            (OapiUserListidResponse.ListUserByDeptResponse) userIdsData;
                        if (response.getUseridList() != null) {
                            allUserIds.addAll(response.getUseridList());
                            departmentInfo.put(deptId, response.getUseridList().size());
                            log.info("部门{}有{}个用户", deptId, response.getUseridList().size());
                        }
                    }
                } else {
                    log.warn("获取部门{}用户ID失败: {}", deptId, userIdsResult.getMsg());
                    departmentInfo.put(deptId, "获取失败: " + userIdsResult.getMsg());
                }
            }

            // 去重用户ID
            List<String> uniqueUserIds = allUserIds.stream().distinct().collect(Collectors.toList());
            log.info("从{}个部门获取到{}个用户ID，去重后{}个", targetDeptIds.size(), allUserIds.size(), uniqueUserIds.size());

            if (CollUtil.isEmpty(uniqueUserIds)) {
                Map<String, Object> emptyResult = new HashMap<>();
                emptyResult.put("users", CollUtil.newArrayList());
                emptyResult.put("totalUsers", 0);
                emptyResult.put("departmentInfo", departmentInfo);
                emptyResult.put("targetDeptIds", targetDeptIds);
                return R.data(emptyResult, "指定部门中未找到任何用户");
            }

            // 批量获取用户详细信息
            List<Map<String, Object>> allUsers = new ArrayList<>();
            for (String userId : uniqueUserIds) {
                try {
                    R<Object> userDetailResult = getUserDetail(userId);
                    if (userDetailResult.isSuccess()) {
                        Object userDetailData = userDetailResult.getData();
                        if (userDetailData instanceof OapiV2UserGetResponse.UserGetResponse) {
                            OapiV2UserGetResponse.UserGetResponse userDetail =
                                (OapiV2UserGetResponse.UserGetResponse) userDetailData;

                            // 只提取基本信息
                            Map<String, Object> basicUserInfo = new HashMap<>();
                            basicUserInfo.put("userid", userDetail.getUserid());
                            basicUserInfo.put("name", userDetail.getName());
                            basicUserInfo.put("mobile", userDetail.getMobile());
                            basicUserInfo.put("title", userDetail.getTitle());
                            basicUserInfo.put("email", userDetail.getEmail());
                            basicUserInfo.put("deptIdList", userDetail.getDeptIdList());


                            allUsers.add(basicUserInfo);
                        }
                    } else {
                        log.warn("获取用户{}详细信息失败: {}", userId, userDetailResult.getMsg());
                    }
                } catch (Exception e) {
                    log.error("获取用户{}详细信息异常: {}", userId, e.getMessage());
                }
            }

            Map<String, Object> result = new HashMap<>();
            result.put("users", allUsers);
            result.put("totalUsers", allUsers.size());
            result.put("departmentInfo", departmentInfo);
            result.put("targetDeptIds", targetDeptIds);

            log.info("获取指定部门用户信息完成，总用户数: {}", allUsers.size());

            return R.data(result, String.format("获取指定部门用户信息完成，共%d个用户", allUsers.size()));

        } catch (Exception e) {
            log.error("获取指定部门用户信息异常", e);
            return R.fail("获取指定部门用户信息异常: " + e.getMessage());
        }
    }

    @Override
    public R<Object> getSpecificDepartmentsUsers(List<String> targetDeptIds) {
        try {
            // 参数校验
            if (CollUtil.isEmpty(targetDeptIds)) {
                return R.fail("部门ID列表不能为空");
            }

            log.info("开始获取指定部门的用户信息，部门ID列表: {}", targetDeptIds);

            // 收集所有用户ID
            List<String> allUserIds = new ArrayList<>();
            Map<String, Object> departmentInfo = new HashMap<>();
            Map<String, String> deptIdToNameMap = new HashMap<>(); // 部门ID到名称的映射

            // 先获取所有部门的名称和父级部门ID（通过单独的API调用）
            Map<String, String> deptIdToParentIdMap = new HashMap<>(); // 部门ID到父级部门ID的映射
            for (String deptId : targetDeptIds) {
                try {
                    DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/department/get");
                    OapiV2DepartmentGetRequest request = new OapiV2DepartmentGetRequest();
                    request.setDeptId(Long.valueOf(deptId));
                    request.setLanguage("zh_CN");

                    String accessToken = getAccessToken();
                    OapiV2DepartmentGetResponse response = client.execute(request, accessToken);

                    if (response.isSuccess() && response.getResult() != null) {
                        String deptName = response.getResult().getName();
                        if (deptName != null) {
                            deptIdToNameMap.put(deptId, deptName);
                        }

                        // 获取父级部门ID（如果有的话）
                        Long parentId = response.getResult().getParentId();
                        if (parentId != null && parentId != 1L) { // 1是根部门，不需要记录父级
                            deptIdToParentIdMap.put(deptId, parentId.toString());
                        }
                    } else {
                        log.warn("获取部门{}名称失败: {}", deptId, response.getErrmsg());
                    }
                } catch (Exception e) {
                    log.error("获取部门{}名称异常: {}", deptId, e.getMessage());
                }
            }

            for (String deptId : targetDeptIds) {
                log.info("正在获取部门{}的用户ID列表", deptId);
                R<Object> userIdsResult = getDepartmentUserIds(deptId);

                if (userIdsResult.isSuccess()) {
                    Object userIdsData = userIdsResult.getData();
                    if (userIdsData instanceof OapiUserListidResponse.ListUserByDeptResponse) {
                        OapiUserListidResponse.ListUserByDeptResponse response =
                            (OapiUserListidResponse.ListUserByDeptResponse) userIdsData;
                        if (response.getUseridList() != null) {
                            allUserIds.addAll(response.getUseridList());
                            departmentInfo.put(deptId, response.getUseridList().size());
                            log.info("部门{}有{}个用户", deptId, response.getUseridList().size());
                        }
                    }
                } else {
                    log.warn("获取部门{}用户ID失败: {}", deptId, userIdsResult.getMsg());
                    departmentInfo.put(deptId, "获取失败: " + userIdsResult.getMsg());
                }
            }

            // 去重用户ID
            List<String> uniqueUserIds = allUserIds.stream().distinct().collect(Collectors.toList());
            log.info("从{}个部门获取到{}个用户ID，去重后{}个", targetDeptIds.size(), allUserIds.size(), uniqueUserIds.size());

            if (CollUtil.isEmpty(uniqueUserIds)) {
                Map<String, Object> emptyResult = new HashMap<>();
                emptyResult.put("users", CollUtil.newArrayList());
                emptyResult.put("totalUsers", 0);
                emptyResult.put("departmentInfo", departmentInfo);
                emptyResult.put("targetDeptIds", targetDeptIds);
                return R.data(emptyResult, "指定部门中未找到任何用户");
            }

            // 批量获取用户详细信息
            List<Map<String, Object>> allUsers = new ArrayList<>();
            for (String userId : uniqueUserIds) {
                try {
                    R<Object> userDetailResult = getUserDetail(userId);
                    if (userDetailResult.isSuccess()) {
                        Object userDetailData = userDetailResult.getData();
                        if (userDetailData instanceof OapiV2UserGetResponse.UserGetResponse) {
                            OapiV2UserGetResponse.UserGetResponse userDetail =
                                (OapiV2UserGetResponse.UserGetResponse) userDetailData;

                            // 只提取基本信息
                            Map<String, Object> basicUserInfo = new HashMap<>();
                            basicUserInfo.put("userid", userDetail.getUserid());
                            basicUserInfo.put("name", userDetail.getName());
                            basicUserInfo.put("mobile", userDetail.getMobile());
                            basicUserInfo.put("title", userDetail.getTitle());
                            basicUserInfo.put("email", userDetail.getEmail());
                            basicUserInfo.put("deptIdList", userDetail.getDeptIdList());

                            // 获取部门名称和父级部门ID（使用已有的映射）
                            List<String> deptNames = new ArrayList<>();
                            List<String> parentDeptIds = new ArrayList<>();
                            if (userDetail.getDeptIdList() != null) {
                                for (Long deptId : userDetail.getDeptIdList()) {
                                    String deptName = deptIdToNameMap.get(deptId.toString());
                                    if (deptName != null) {
                                        deptNames.add(deptName);
                                    }

                                    // 获取父级部门ID
                                    String parentDeptId = deptIdToParentIdMap.get(deptId.toString());
                                    if (parentDeptId != null) {
                                        parentDeptIds.add(parentDeptId);
                                    } else {
                                        // 如果没有父级部门ID，可能是根部门，添加null
                                        parentDeptIds.add(null);
                                    }
                                }
                            }
                            basicUserInfo.put("deptNames", deptNames);
                            basicUserInfo.put("parentDeptIds", parentDeptIds);

                            allUsers.add(basicUserInfo);
                        }
                    } else {
                        log.warn("获取用户{}详细信息失败: {}", userId, userDetailResult.getMsg());
                    }
                } catch (Exception e) {
                    log.error("获取用户{}详细信息异常: {}", userId, e.getMessage());
                }
            }

            Map<String, Object> result = new HashMap<>();
            result.put("users", allUsers);
            result.put("totalUsers", allUsers.size());
            result.put("departmentInfo", departmentInfo);
            result.put("targetDeptIds", targetDeptIds);

            log.info("获取指定部门用户信息完成，总用户数: {}", allUsers.size());

            return R.data(result, String.format("获取指定部门用户信息完成，共%d个用户", allUsers.size()));

        } catch (Exception e) {
            log.error("获取指定部门用户信息异常", e);
            return R.fail("获取指定部门用户信息异常: " + e.getMessage());
        }
    }

    @Override
    public R<Object> getTargetDepartmentsWithSubUsersDetails(List<String> targetDeptIds) {
        try {
            // 参数校验
            if (CollUtil.isEmpty(targetDeptIds)) {
                return R.fail("部门ID列表不能为空");
            }

            log.info("开始获取目标部门及其下级部门的用户信息，目标部门ID列表: {}", targetDeptIds);

            // 收集所有部门ID（包括目标部门和所有下级部门）
            List<String> allDeptIds = new ArrayList<>();
            Map<String, Object> departmentStructure = new HashMap<>();
            Map<String, String> deptIdToNameMap = new HashMap<>(); // 部门ID到名称的映射
            Map<String, String> deptIdToParentIdMap = new HashMap<>(); // 部门ID到父级部门ID的映射

            // 对每个目标部门，递归获取所有下级部门
            for (String targetDeptId : targetDeptIds) {
                log.info("正在递归获取部门{}及其所有下级部门", targetDeptId);
                List<String> deptAndSubDepts = new ArrayList<>();

                // 添加目标部门本身
                deptAndSubDepts.add(targetDeptId);

                // 递归获取所有下级部门（同时收集部门名称和父级关系，包括目标部门本身）
                getAllSubDepartmentsRecursiveWithNames(targetDeptId, deptAndSubDepts, deptIdToNameMap, deptIdToParentIdMap, 0);

                allDeptIds.addAll(deptAndSubDepts);
                departmentStructure.put(targetDeptId, deptAndSubDepts);

                log.info("部门{}及其下级部门总数: {}", targetDeptId, deptAndSubDepts.size());
            }

            // 去重部门ID（可能有重复的下级部门）
            List<String> uniqueDeptIds = allDeptIds.stream().distinct().collect(Collectors.toList());
            log.info("从{}个目标部门获取到{}个部门（含下级），去重后{}个部门",
                targetDeptIds.size(), allDeptIds.size(), uniqueDeptIds.size());

            // 收集所有用户ID
            List<String> allUserIds = new ArrayList<>();
            Map<String, Object> departmentUserInfo = new HashMap<>();

            for (String deptId : uniqueDeptIds) {
                R<Object> userIdsResult = getDepartmentUserIds(deptId);

                if (userIdsResult.isSuccess()) {
                    Object userIdsData = userIdsResult.getData();
                    if (userIdsData instanceof OapiUserListidResponse.ListUserByDeptResponse) {
                        OapiUserListidResponse.ListUserByDeptResponse response =
                            (OapiUserListidResponse.ListUserByDeptResponse) userIdsData;
                        if (response.getUseridList() != null) {
                            allUserIds.addAll(response.getUseridList());
                            departmentUserInfo.put(deptId, response.getUseridList().size());
                            log.info("部门{}有{}个用户", deptId, response.getUseridList().size());
                        } else {
                            departmentUserInfo.put(deptId, 0);
                        }
                    }
                } else {
                    log.warn("获取部门{}用户ID失败: {}", deptId, userIdsResult.getMsg());
                    departmentUserInfo.put(deptId, "获取失败: " + userIdsResult.getMsg());
                }
            }

            // 去重用户ID
            List<String> uniqueUserIds = allUserIds.stream().distinct().collect(Collectors.toList());
            log.info("从{}个部门获取到{}个用户ID，去重后{}个", uniqueDeptIds.size(), allUserIds.size(), uniqueUserIds.size());

            if (CollUtil.isEmpty(uniqueUserIds)) {
                Map<String, Object> emptyResult = new HashMap<>();
                emptyResult.put("users", CollUtil.newArrayList());
                emptyResult.put("totalUsers", 0);
                emptyResult.put("departmentStructure", departmentStructure);
                emptyResult.put("departmentUserInfo", departmentUserInfo);
                emptyResult.put("targetDeptIds", targetDeptIds);
                emptyResult.put("totalDepartments", uniqueDeptIds.size());
                return R.data(emptyResult, "目标部门及下级部门中未找到任何用户");
            }

            // 批量获取用户详细信息
            List<Map<String, Object>> allUsers = new ArrayList<>();
            for (String userId : uniqueUserIds) {
                try {
                    R<Object> userDetailResult = getUserDetail(userId);
                    if (userDetailResult.isSuccess()) {
                        Object userDetailData = userDetailResult.getData();
                        if (userDetailData instanceof OapiV2UserGetResponse.UserGetResponse) {
                            OapiV2UserGetResponse.UserGetResponse userDetail =
                                (OapiV2UserGetResponse.UserGetResponse) userDetailData;

                            // 只提取基本信息
                            Map<String, Object> basicUserInfo = new HashMap<>();
                            basicUserInfo.put("userid", userDetail.getUserid());
                            basicUserInfo.put("name", userDetail.getName());
                            basicUserInfo.put("mobile", userDetail.getMobile());
                            basicUserInfo.put("title", userDetail.getTitle());
                            basicUserInfo.put("email", userDetail.getEmail());
                            basicUserInfo.put("deptIdList", userDetail.getDeptIdList());

                            // 获取部门名称和父级部门ID（使用已有的映射）
                            List<String> deptNames = new ArrayList<>();
                            List<String> parentDeptIds = new ArrayList<>();
                            if (userDetail.getDeptIdList() != null) {
                                for (Long deptId : userDetail.getDeptIdList()) {
                                    String deptName = deptIdToNameMap.get(deptId.toString());
                                    if (deptName != null) {
                                        deptNames.add(deptName);
                                    }

                                    // 获取父级部门ID
                                    String parentDeptId = deptIdToParentIdMap.get(deptId.toString());
                                    if (parentDeptId != null) {
                                        parentDeptIds.add(parentDeptId);
                                    } else {
                                        // 如果没有父级部门ID，可能是根部门，添加null或空字符串
                                        parentDeptIds.add(null);
                                    }
                                }
                            }
                            basicUserInfo.put("deptNames", deptNames);
                            basicUserInfo.put("parentDeptIds", parentDeptIds);

                            allUsers.add(basicUserInfo);
                        }
                    } else {
                        log.warn("获取用户{}详细信息失败: {}", userId, userDetailResult.getMsg());
                    }
                } catch (Exception e) {
                    log.error("获取用户{}详细信息异常: {}", userId, e.getMessage());
                }
            }

            // 保存用户信息到数据库
            try {
                if (CollUtil.isNotEmpty(allUsers)) {
                    log.info("开始保存{}个用户信息到数据库", allUsers.size());
                    boolean saveResult = msDingUserService.saveOrUpdateBatch(allUsers);
                    if (saveResult) {
                        log.info("成功保存{}个用户信息到数据库", allUsers.size());
                    } else {
                        log.warn("保存用户信息到数据库失败");
                    }
                }
            } catch (Exception e) {
                log.error("保存用户信息到数据库异常", e);
                // 不影响主流程，继续返回结果
            }

            Map<String, Object> result = new HashMap<>();
            result.put("users", allUsers);
            result.put("totalUsers", allUsers.size());
            result.put("departmentStructure", departmentStructure);
            result.put("departmentUserInfo", departmentUserInfo);
            result.put("targetDeptIds", targetDeptIds);
            result.put("totalDepartments", uniqueDeptIds.size());

            log.info("获取目标部门及下级部门用户信息完成，总用户数: {}，总部门数: {}", allUsers.size(), uniqueDeptIds.size());

            return R.data(result, String.format("获取目标部门及下级部门用户信息完成，共%d个用户，涉及%d个部门",
                allUsers.size(), uniqueDeptIds.size()));

        } catch (Exception e) {
            log.error("获取目标部门及下级部门用户信息异常", e);
            return R.fail("获取目标部门及下级部门用户信息异常: " + e.getMessage());
        }
    }

    /**
     * 递归获取所有下级部门ID（同时收集部门名称和父级关系）
     */
    private void getAllSubDepartmentsRecursiveWithNames(String deptId, List<String> allDeptIds, Map<String, String> deptIdToNameMap, Map<String, String> deptIdToParentIdMap, int currentLevel) {
        try {
            // 防止递归过深
            if (currentLevel > 10) {
                log.warn("⚠️ 递归深度超过10级，停止搜索部门: {}", deptId);
                return;
            }

            log.info("🔎 [第{}级] 获取部门{}的子部门", currentLevel, deptId);

            // 只有目标部门（第0级）需要单独获取名称，因为listsub API只返回子部门信息，不包括当前部门本身
            if (currentLevel == 0 && !deptIdToNameMap.containsKey(deptId)) {
                try {
                    DingTalkClient deptClient = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/department/get");
                    OapiV2DepartmentGetRequest deptRequest = new OapiV2DepartmentGetRequest();
                    deptRequest.setDeptId(Long.valueOf(deptId));
                    deptRequest.setLanguage("zh_CN");

                    String accessToken = getAccessToken();
                    OapiV2DepartmentGetResponse deptResponse = deptClient.execute(deptRequest, accessToken);

                    if (deptResponse.isSuccess() && deptResponse.getResult() != null) {
                        String targetDeptName = deptResponse.getResult().getName();
                        if (targetDeptName != null) {
                            deptIdToNameMap.put(deptId, targetDeptName);
                            log.info("📂 [第{}级] 目标部门名称: ID={}, 名称={}", currentLevel, deptId, targetDeptName);
                        }
                    }
                } catch (Exception e) {
                    log.error("获取目标部门{}名称异常: {}", deptId, e.getMessage());
                }
            }

            // 获取子部门列表（子部门的名称直接从响应中获取，无需额外API调用）
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/department/listsub");
            OapiV2DepartmentListsubRequest request = new OapiV2DepartmentListsubRequest();
            request.setDeptId(Long.valueOf(deptId));
            request.setLanguage("zh_CN");

            String accessToken = getAccessToken();
            OapiV2DepartmentListsubResponse response = client.execute(request, accessToken);

            if (response.isSuccess() && response.getResult() != null) {
                List<OapiV2DepartmentListsubResponse.DeptBaseResponse> subDeptList = response.getResult();

                log.info("📁 [第{}级] 部门{}有{}个子部门", currentLevel, deptId, subDeptList.size());

                if (subDeptList.isEmpty()) {
                    log.info("🍃 [第{}级] 部门{}是叶子节点", currentLevel, deptId);
                    return;
                }

                // 遍历所有子部门
                for (OapiV2DepartmentListsubResponse.DeptBaseResponse subDept : subDeptList) {
                    if (subDept.getDeptId() != null) {
                        String subDeptId = subDept.getDeptId().toString();
                        String deptName = subDept.getName();

                        log.info("📂 [第{}级] 添加子部门: ID={}, 名称={}", currentLevel + 1, subDeptId, deptName);

                        // 添加子部门ID
                        allDeptIds.add(subDeptId);

                        // 保存部门名称映射
                        if (deptName != null) {
                            deptIdToNameMap.put(subDeptId, deptName);
                        }

                        // 保存父级部门ID映射
                        deptIdToParentIdMap.put(subDeptId, deptId);

                        // 递归搜索子部门的子部门
                        getAllSubDepartmentsRecursiveWithNames(subDeptId, allDeptIds, deptIdToNameMap, deptIdToParentIdMap, currentLevel + 1);
                    }
                }

                log.info("🏁 [第{}级] 完成部门{}的子部门遍历", currentLevel, deptId);

            } else {
                log.error("❌ [第{}级] 获取部门{}子部门失败: {}", currentLevel, deptId,
                    response != null ? response.getErrmsg() : "response为null");
            }

        } catch (Exception e) {
            log.error("💥 [第{}级] 获取部门{}子部门异常: {}", currentLevel, deptId, e.getMessage());
        }
    }

    /**
     * 递归获取所有下级部门ID
     */
    private void getAllSubDepartmentsRecursive(String deptId, List<String> allDeptIds, int currentLevel) {
        try {
            // 防止递归过深
            if (currentLevel > 10) {
                log.warn("⚠️ 递归深度超过10级，停止搜索部门: {}", deptId);
                return;
            }

            log.info("🔎 [第{}级] 获取部门{}的子部门", currentLevel, deptId);

            // 获取子部门列表
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/department/listsub");
            OapiV2DepartmentListsubRequest request = new OapiV2DepartmentListsubRequest();
            request.setDeptId(Long.valueOf(deptId));
            request.setLanguage("zh_CN");

            String accessToken = getAccessToken();
            OapiV2DepartmentListsubResponse response = client.execute(request, accessToken);

            if (response.isSuccess() && response.getResult() != null) {
                List<OapiV2DepartmentListsubResponse.DeptBaseResponse> subDeptList = response.getResult();

                log.info("📁 [第{}级] 部门{}有{}个子部门", currentLevel, deptId, subDeptList.size());

                if (subDeptList.isEmpty()) {
                    log.info("🍃 [第{}级] 部门{}是叶子节点", currentLevel, deptId);
                    return;
                }

                // 遍历所有子部门
                for (OapiV2DepartmentListsubResponse.DeptBaseResponse subDept : subDeptList) {
                    if (subDept.getDeptId() != null) {
                        String subDeptId = subDept.getDeptId().toString();
                        String deptName = subDept.getName();

                        log.info("📂 [第{}级] 添加子部门: ID={}, 名称={}", currentLevel + 1, subDeptId, deptName);

                        // 添加子部门ID
                        allDeptIds.add(subDeptId);

                        // 递归搜索子部门的子部门
                        getAllSubDepartmentsRecursive(subDeptId, allDeptIds, currentLevel + 1);
                    }
                }

                log.info("🏁 [第{}级] 完成部门{}的子部门遍历", currentLevel, deptId);

            } else {
                log.error("❌ [第{}级] 获取部门{}子部门失败: {}", currentLevel, deptId,
                    response != null ? response.getErrmsg() : "response为null");
            }

        } catch (Exception e) {
            log.error("💥 [第{}级] 获取部门{}子部门异常: {}", currentLevel, deptId, e.getMessage());
        }
    }

    @Override
    public R<Object> getZhanjiangDepartments(String startDeptId) {
        try {
            // 如果没有指定起始部门ID，使用默认值641568739
            if (StrUtil.isBlank(startDeptId)) {
                startDeptId = "641568739";
            }

            log.info("🔍 开始从部门ID {}深度递归查找包含'湛江'的部门", startDeptId);
            log.info("📊 搜索统计信息将实时更新...");
            log.info("🎯 目标：查找所有包含'湛江'字样的部门");
            log.info("🌳 搜索策略：深度优先遍历，无层级限制");
            log.info("========================================");

            List<Map<String, Object>> zhanjiangDepts = new ArrayList<>();
            SearchStatistics stats = new SearchStatistics();

            // 深度递归查找包含"湛江"的部门
            log.info("🚀 开始从根部门{}进行深度搜索...", startDeptId);
            findZhanjiangDepartmentsRecursive(startDeptId, zhanjiangDepts, stats, 0);
            log.info("🏁 深度搜索完成，开始汇总结果...");

            Map<String, Object> result = new HashMap<>();
            result.put("departments", zhanjiangDepts);
            result.put("total", zhanjiangDepts.size());
            result.put("startDeptId", startDeptId);
            result.put("searchStatistics", stats.toMap());

            log.info("========================================");
            log.info("🎉 深度搜索完成！");
            log.info("📊 搜索统计：");
            log.info("  - 总共检查了 {} 个部门", stats.totalChecked);
            log.info("  - 最大搜索深度：{} 级", stats.maxDepth);
            log.info("  - 找到湛江部门：{} 个", zhanjiangDepts.size());

            // 输出所有找到的湛江部门汇总
            if (!zhanjiangDepts.isEmpty()) {
                log.info("📋 湛江部门详细列表：");
                for (int i = 0; i < zhanjiangDepts.size(); i++) {
                    Map<String, Object> dept = zhanjiangDepts.get(i);
                    log.info("  {}. 部门ID: {}, 部门名称: {}, 层级: {}",
                        i + 1, dept.get("deptId"), dept.get("deptName"), dept.get("level"));
                }
            } else {
                log.info("⚠️ 在所有 {} 个部门中未找到任何包含'湛江'的部门", stats.totalChecked);
            }
            log.info("========================================");

            return R.data(result, "深度搜索湛江相关部门完成");
        } catch (Exception e) {
            log.error("获取湛江相关部门列表异常", e);
            return R.fail("获取湛江相关部门列表异常: " + e.getMessage());
        }
    }

    /**
     * 搜索统计信息类
     */
    private static class SearchStatistics {
        int totalChecked = 0;
        int maxDepth = 0;
        int currentDepth = 0;

        public Map<String, Object> toMap() {
            Map<String, Object> map = new HashMap<>();
            map.put("totalChecked", totalChecked);
            map.put("maxDepth", maxDepth);
            return map;
        }
    }

    /**
     * 递归查找包含"湛江"的部门（增强版）
     */
    private void findZhanjiangDepartmentsRecursive(String deptId, List<Map<String, Object>> zhanjiangDepts, SearchStatistics stats, int currentLevel) {
        try {
            // 防止递归过深
            if (currentLevel > 10) {
                log.warn("⚠️ 递归深度超过10级，停止搜索部门: {}", deptId);
                return;
            }

            // 更新深度统计
            if (currentLevel > stats.maxDepth) {
                stats.maxDepth = currentLevel;
            }

            // 更新检查计数
            stats.totalChecked++;

            log.info("🔎 [第{}级] 检查部门ID: {} (总计已检查{}个)", currentLevel, deptId, stats.totalChecked);

            // 每检查25个部门输出一次进度
            if (stats.totalChecked % 25 == 0) {
                log.info("📊 搜索进度：已检查 {} 个部门，当前深度 {} 级，已找到 {} 个湛江部门",
                    stats.totalChecked, currentLevel, zhanjiangDepts.size());
            }

            // 获取子部门列表
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/department/listsub");
            OapiV2DepartmentListsubRequest request = new OapiV2DepartmentListsubRequest();
            request.setDeptId(Long.valueOf(deptId));
            request.setLanguage("zh_CN");

            String accessToken = getAccessToken();
            OapiV2DepartmentListsubResponse response = client.execute(request, accessToken);

            if (response.isSuccess() && response.getResult() != null) {
                List<OapiV2DepartmentListsubResponse.DeptBaseResponse> subDeptList = response.getResult();

                log.info("📁 [第{}级] 部门{}有{}个子部门", currentLevel, deptId, subDeptList.size());

                if (subDeptList.isEmpty()) {
                    log.info("🍃 [第{}级] 部门{}是叶子节点", currentLevel, deptId);
                    return;
                }

                // 遍历所有子部门
                for (OapiV2DepartmentListsubResponse.DeptBaseResponse subDept : subDeptList) {
                    if (subDept.getDeptId() != null && subDept.getName() != null) {
                        String deptName = subDept.getName();
                        Long subDeptId = subDept.getDeptId();

                        log.info("🔍 [第{}级] 检查子部门: ID={}, 名称={}", currentLevel + 1, subDeptId, deptName);

                        // 检查部门名称是否包含"湛江"
                        if (deptName.contains("湛江")) {
                            Map<String, Object> deptInfo = new HashMap<>();
                            deptInfo.put("deptId", subDeptId);
                            deptInfo.put("deptName", deptName);
                            deptInfo.put("parentId", Long.valueOf(deptId));
                            deptInfo.put("level", currentLevel + 1);
                            zhanjiangDepts.add(deptInfo);

                            log.info("=== 🎯 找到湛江相关部门 ===");
                            log.info("部门ID: {}", subDeptId);
                            log.info("部门名称: {}", deptName);
                            log.info("父部门ID: {}", deptId);
                            log.info("所在层级: 第 {} 级", currentLevel + 1);
                            log.info("当前已找到湛江部门总数: {}", zhanjiangDepts.size());
                            log.info("========================");
                        }

                        // 递归搜索子部门
                        log.info("🔄 递归搜索子部门: {}", subDeptId);
                        findZhanjiangDepartmentsRecursive(subDeptId.toString(), zhanjiangDepts, stats, currentLevel + 1);
                        log.info("✅ 完成子部门搜索: {}", subDeptId);
                    }
                }

                log.info("🏁 [第{}级] 完成部门{}的子部门遍历", currentLevel, deptId);

            } else {
                log.error("❌ [第{}级] 获取部门{}子部门失败: {}", currentLevel, deptId,
                    response != null ? response.getErrmsg() : "response为null");
            }

        } catch (Exception e) {
            log.error("💥 [第{}级] 搜索部门{}异常: {}", currentLevel, deptId, e.getMessage());
        }
    }

}
