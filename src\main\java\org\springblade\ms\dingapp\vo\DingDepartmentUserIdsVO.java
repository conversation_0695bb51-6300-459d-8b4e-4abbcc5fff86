package org.springblade.ms.dingapp.vo;

import lombok.Data;

import java.util.List;

/**
 * 钉钉部门用户ID列表VO
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Data
public class DingDepartmentUserIdsVO {

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 用户ID列表
     */
    private List<String> userIds;

    /**
     * 用户数量
     */
    private Integer userCount;

    /**
     * 构造函数
     */
    public DingDepartmentUserIdsVO() {
    }

    /**
     * 构造函数
     *
     * @param deptId 部门ID
     * @param userIds 用户ID列表
     */
    public DingDepartmentUserIdsVO(String deptId, List<String> userIds) {
        this.deptId = deptId;
        this.userIds = userIds;
        this.userCount = userIds != null ? userIds.size() : 0;
    }

    /**
     * 构造函数
     *
     * @param deptId 部门ID
     * @param deptName 部门名称
     * @param userIds 用户ID列表
     */
    public DingDepartmentUserIdsVO(String deptId, String deptName, List<String> userIds) {
        this.deptId = deptId;
        this.deptName = deptName;
        this.userIds = userIds;
        this.userCount = userIds != null ? userIds.size() : 0;
    }
}
