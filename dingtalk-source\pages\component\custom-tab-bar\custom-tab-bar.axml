<view class="tabBarMain">
  <view class="mapTab" data-tabName="map" onTap="tabClick">
    <view class="mapTabImg" style="{{currentTab === 'map' ? 'background-image: url(/image/map_hl.png)' : ''}}"/>
    <view class="mapTabFont" style="{{currentTab === 'map' ? 'color: #1486FF' : ''}}">
      首 页
    </view>
  </view>
  <view class="createTab" data-tabName="create" onTap="tabClick">
    <view class="createTabImg"/>
    <view class="createTabFont">
      勘 查
    </view>
  </view>
  <view class="mineTab" data-tabName="mine" onTap="tabClick">
    <view class="mineTabImg" style="{{currentTab === 'mine' ? 'background-image: url(/image/wode_hl.png)' : ''}}"/>
    <view class="mineTabFont" style="{{currentTab === 'mine' ? 'color: #1486FF' : ''}}">
      我 的
    </view>
  </view>
</view>
<ant-dialog 
    title="开始勘查"
    visible={{isShowExplorationDialog}}
    onClose="handleExplorationDialogClose"
    >
        <view
            slot="footer"
            class="activity-dialog-footer"
        >
            <ant-input 
                className="searchBar" 
                style="background: #f7f9fa;"
                placeholder="请输入许可证号"
                confirm-type="search"
                onChange="handleLicChange"
                value="{{inputLic}}"
            >
                <ant-icon slot="prefix" type="SearchOutline" style="color: black;" />
                <ant-icon slot="suffix" type="ScanningOutline" style="color: #1486FF; font-size: 30px;" onTap="handleSearchScanTap" />
            </ant-input>
            <view class="searchBtn" onTap="handleSearchBtnTap">
              搜 索
            </view>
        </view>
</ant-dialog>
<ant-toast
  content="请输入许可证号或使用扫码功能"
  visible="{{toastShow}}"
  duration="{{3000}}"
  showMask="{{false}}"
  onClose="handleCloseToast"
/>