package org.springblade.ms.itemidentify.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.Func;
import org.springblade.ms.common.utils.ApiCenterUtil;
import org.springblade.ms.common.utils.ThreeTierDataFetcher;
import org.springblade.ms.itemidentify.mapper.MsRecentRetailerOrderProductMapper;
import org.springblade.ms.itemidentify.pojo.entity.MsRecentRetailerOrderProduct;
import org.springblade.ms.itemidentify.service.MsRecentRetailerOrderProductService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> @description 针对表【ms_recent_retailer_order_product(近一年零售户订货品规)】的数据库操作Service实现 - 使用通用三级查询工具
 * @createDate 2025-03-11 19:15:51
 */
@Service("msRecentRetailerOrderProductService")
@Slf4j
public class MsRecentRetailerOrderProductServiceImpl extends BaseServiceImpl<MsRecentRetailerOrderProductMapper, MsRecentRetailerOrderProduct> implements MsRecentRetailerOrderProductService {

    private static final String REDIS_KEY_PREFIX = "ms:recentRetailerOrderProduct:customerCode:";
    private static final int REDIS_EXPIRE_DAYS = 1; // 1天过期
    private static final long CACHE_REFRESH_INTERVAL = 60 * 60 * 1000; // 1小时刷新间隔

    private final ThreeTierDataFetcher<MsRecentRetailerOrderProduct, String> dataFetcher;

    /**
     * 构造方法，初始化数据获取器
     */
    public MsRecentRetailerOrderProductServiceImpl(RedisTemplate<String, Object> redisTemplate) {
        // 初始化缓存配置
        ThreeTierDataFetcher.CacheConfig cacheConfig = ThreeTierDataFetcher.CacheConfig.builder()
                .keyPrefix(REDIS_KEY_PREFIX)
                .expireDays(REDIS_EXPIRE_DAYS)
                .refreshIntervalMs(CACHE_REFRESH_INTERVAL)
                .apiTimeoutSeconds(5)
                .dbTimeoutSeconds(3)
                .build();

        // 初始化数据获取器
        this.dataFetcher = new ThreeTierDataFetcher<>(
                redisTemplate,
                cacheConfig,
                customerCode -> REDIS_KEY_PREFIX + customerCode,  // 键生成器
                this::getFromApi,                                // API数据获取器
                this::getFromDatabase                            // 数据库数据获取器
        );
    }

    @Override
    public List<MsRecentRetailerOrderProduct> listByCustomerCode(String customerCode) {
        if (Func.isEmpty(customerCode)) {
            return new ArrayList<>();
        }

        // 使用通用数据获取器获取数据
        return dataFetcher.fetchData(customerCode);
    }

    /**
     * 从 API 中获取数据
     */
    private List<MsRecentRetailerOrderProduct> getFromApi(String customerCode) {
        List<MsRecentRetailerOrderProduct> resultList = new ArrayList<>();
        try {
            Map<String, Object> requestParams = new HashMap<>();
            requestParams.put("KHBM", customerCode);
            requestParams.put("PAGENUM", 1);
            requestParams.put("PAGESIZE", 1000);
            String serviceId = "sv25149QEZK2";
            String response = ApiCenterUtil.send(serviceId, requestParams);

            if (Func.isNotEmpty(response)) {
                JSONObject jsonObject = JSONUtil.parseObj(response);
                JSONObject dataObj = jsonObject.getJSONObject("data");
                Integer errcode = jsonObject.getInt("errcode");

                if (dataObj != null && errcode == 0) {
                    JSONArray dataArray = dataObj.getJSONArray("data");
                    if (dataArray != null && !dataArray.isEmpty()) {
                        for (int i = 0; i < dataArray.size(); i++) {
                            JSONObject item = dataArray.getJSONObject(i);
                            MsRecentRetailerOrderProduct product = new MsRecentRetailerOrderProduct();

                            product.setCustomerUuid(item.getStr("KHBS"));
                            product.setCustomerCode(item.getStr("KHBM"));
                            product.setCustomerName(item.getStr("KHMC"));
                            product.setProductUuid(item.getStr("SPBS"));
                            product.setProductCode(item.getStr("SPBM"));
                            product.setProductName(item.getStr("SPMC"));

                            product.setIsRecentArrived(item.getInt("JYQSFDH"));     // 近一期是否到货
                            product.setRecentQty(item.getInt("JYQDDSL")); // 近一期订单数量
                            product.setRecentBizDate(item.getStr("JYQYWRQ"));  // 近一期业务日期
                            product.setRecentWithTaxAmount(item.getDouble("JYQHSJE")); // 近一期含税金额
                            product.setRecentReqQty(item.getInt("JYQYHSL")); // 近一期要货数量

                            product.setTotalOrdTimes(item.getInt("LJDHCS"));     // 累计订货次数
                            product.setTotalQty(item.getInt("LJDDSL"));// 累计订单数量
                            product.setTotalWithTaxAmount(item.getDouble("LJHSJE")); // 累计含税金额
                            product.setTotalReqQty(item.getInt("LJYHSL")); // 累计要货数量

                            resultList.add(product);
                        }
                    }
                } else {
                    log.warn("API返回错误代码: {}, 错误信息: {}",
                            dataObj != null ? dataObj.getInt("errcode") : "unknown",
                            dataObj != null ? dataObj.getStr("errmsg") : "unknown");
                }
            }
        } catch (Exception e) {
            log.error("从API获取数据失败，customerCode: {}", customerCode, e);
        }
        return resultList;
    }

    /**
     * 从数据库中获取数据
     */
    private List<MsRecentRetailerOrderProduct> getFromDatabase(String customerCode) {
        try {
            LambdaQueryWrapper<MsRecentRetailerOrderProduct> queryWrapper = Wrappers.<MsRecentRetailerOrderProduct>lambdaQuery()
                    .eq(MsRecentRetailerOrderProduct::getCustomerCode, customerCode);
            return this.list(queryWrapper);
        } catch (Exception e) {
            log.error("从数据库获取数据失败，customerCode: {}", customerCode, e);
            return new ArrayList<>();
        }
    }
}
