import httpApi from "/utils/http/httpApi";
import {getWeekByDate} from '/utils/luch-request/utils';

Component({
  mixins: [],
  data: {
    pgsbList:[
      {
        itemNum:5,
        outlier:1,
      },
      {
        itemNum:5,
        outlier:1,
      },
      {
        itemNum:5,
        outlier:1,
      },
      {
        itemNum:5,
        outlier:1,
      },
    ],
    todayData:{
      itemNum:0,
      outlier:0,
    },
    lshhxIsView:false, 
    formPage: {
      pageNo: 1,
      pageSize: 15,
      total: 10,
    },
    gotoScrollTop: 0,
    scrollToast:'',
    dataList:[],
    todayData:{
      identifyDate:new Date().toISOString().split('T')[0] + ' ' +getWeekByDate(new Date().toISOString().split('T')[0]),
      errorNum:0,
      identifyNum:0,
    }

  },
  props: {
    isView:'',
    licData:{},
  },
  async didMount() {
    const isView = this.props.isView=='true'
    this.setData({lshhxIsView:isView})
    await this.getList()
    
    if(!isView && this.data.dataList.length>0 && this.data.dataList[0].identifyDate == this.data.todayData.identifyDate ){ //第一条日期是今天且是勘察
      const newDataList = [...this.data.dataList];
      const todayDataItem = newDataList[0];
      newDataList.splice(0, 1);
      this.setData({
        todayData: todayDataItem,
        dataList: newDataList
      });
    }
  },
  didUpdate() {},
  didUnmount() {},

  methods: {
    getList(){
      return new Promise((resolve) => {
        httpApi.get('/api/dingapp/itemIdentifyResults/countList', {
          params: {
            licId: this.props.licData.licId,
          }
        }).then(res => {
          console.log(res);
          if (res && res.data) {
            let data = res.data;
            // 其他逻辑...
            
            // 填充数据
            let list = [];
            if (this.data.formPage.pageNo > 1) {
              list = this.data.dataList;
            }
            let resData = res.data.records.map(item => ({
              identifyDate: item.identifyDate + ' ' + getWeekByDate(item.identifyDate),
              identifyNum: item.identifyNum,
              errorNum: item.errorNum,
              explorationId: item.explorationId
            }));
            
            list.push(...resData);
            this.setData({
              'formPage.total': data.total,
              'formPage.pageSize': data.size,
              'formPage.pageNo': data.current,
              dataList: list
            }, () => {
              resolve();
            });
          }
        });
      });
    },
    handleStartRecognition(){
      dd.navigateTo({
        url: '/pages/pgsb-info/pgsb-info?type=survey&date='+this.data.todayData.identifyDate +'&explorationId=' + this.props.licData.explorationId
      });
    },
    handleViewClick(e){
      const item = e.target.dataset.item
      dd.navigateTo({
        url: '/pages/pgsb-info/pgsb-info?explorationId='+item.explorationId+"&type=view&date="+item.identifyDate,
      });
    },
    click(){
      console.log(this.props.isView)
    }
  },
  scrollToLowerLoad() {
    if (this.data.formPage.pageNo * this.data.formPage.pageSize >= this.data.formPage.total) {
        if (this.data.formPage.pageNo == 1 && this.data.formPage.total < 15) {
            this.setData({
                scrollToast: ''
            })
            return
        }
        this.setData({
            scrollToast: '已经到底啦'
        })
        return;
    }
    this.setData({
        'formPage.pageNo': this.data.formPage.pageNo + 1
    });
    this.getList();
  }
});
