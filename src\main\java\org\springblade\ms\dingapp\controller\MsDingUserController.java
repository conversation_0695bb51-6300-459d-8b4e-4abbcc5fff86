package org.springblade.ms.dingapp.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.ms.dingapp.entity.MsDingUserEntity;
import org.springblade.ms.dingapp.service.IMsDingUserService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 钉钉用户信息控制器
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@RestController
@AllArgsConstructor
@RequestMapping("/dingapp/user")
@Tag(name = "钉钉用户信息接口")
public class MsDingUserController extends BladeController {

    private final IMsDingUserService msDingUserService;

    /**
     * 根据钉钉用户ID查询用户信息
     */
    @GetMapping("/detail/{userid}")
    @Operation(summary = "根据钉钉用户ID查询用户信息", description = "根据钉钉用户ID查询用户信息")
    public R<MsDingUserEntity> getByUserid(@PathVariable @Parameter(description = "钉钉用户ID") String userid) {
        MsDingUserEntity user = msDingUserService.getByUserid(userid);
        if (user != null) {
            return R.data(user, "查询成功");
        } else {
            return R.fail("用户不存在");
        }
    }

    /**
     * 查询所有钉钉用户信息
     */
    @GetMapping("/list")
    @Operation(summary = "查询所有钉钉用户信息", description = "查询所有钉钉用户信息")
    public R<List<MsDingUserEntity>> list() {
        List<MsDingUserEntity> list = msDingUserService.list();
        return R.data(list, "查询成功");
    }

    /**
     * 根据ID删除用户信息
     */
    @DeleteMapping("/remove/{id}")
    @Operation(summary = "删除用户信息", description = "根据ID删除用户信息")
    public R<Boolean> removeById(@PathVariable @Parameter(description = "用户ID") Long id) {
        boolean result = msDingUserService.removeById(id);
        return R.status(result);
    }

}
