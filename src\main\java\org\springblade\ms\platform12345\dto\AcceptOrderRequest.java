package org.springblade.ms.platform12345.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 提交受理请求参数
 */
@Data
@Schema(description = "提交受理请求参数")
public class AcceptOrderRequest {
    
    @Schema(description = "加密校验串")
    private String signature;
    
    @Schema(description = "时间戳")
    private String timestamp;
    
    @Schema(description = "应用ID")
    private String appid;
    
    @Schema(description = "热线系统工单ID")
    private String proWoId;
    
    @Schema(description = "热线系统工单编号")
    private String proWoCode;
    
    @Schema(description = "主办单位")
    private String assignOrgName;
    
    @Schema(description = "处理意见")
    private String appContent;
    
    @Schema(description = "受理时间：YYYY-MM-DD HH24:MI:SS")
    private String appTime;
    
    @Schema(description = "第三方系统处理人帐号")
    private String dealUserNo;
}
