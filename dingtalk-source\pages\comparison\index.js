
Page({
  data: {
    columns: [
      {
        title: ' ',
        dataIndex: 'item',
        key: 'name',
        width: 180,
        // fixed: true,
      },
      {
        title: '企业名称1',
        dataIndex: 'company1',
        key: 'name',
        width: 260,
        // fixed: true,
      },
      {
        title: '企业名称2',
        dataIndex: 'company2',
        key: 'age',
        width: 260,
      },
    ],
    dataSource:[
      {
        "item":"许可证号",
        "company1":"*********",
        "company2":"*********",
      },
      {
        "item":"企业名称",
        "company1":"************************************",
        "company2":"************************************",
      },
      {
        "item":"企业类型",
        "company1":"************************************",
        "company2":"************************************",
      }
    ]
  },
  onLoad() {
    let data;
    dd.getStorage({
      key: 'comparisionData',
      success: (res) => {
         data = JSON.parse(res.data);
         console.log(data)
         
      },
      fail: () => {
      },
      complete: () => {},
    });
  },
  onUnload() {
    dd.removeStorage({
      key: 'comparisionData',
    });
    console.log("数据没咯");
  }
});
