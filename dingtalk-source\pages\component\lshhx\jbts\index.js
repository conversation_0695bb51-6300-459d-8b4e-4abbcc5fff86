import httpApi from "/utils/http/httpApi";
import {formatTimestamp} from '/utils/luch-request/utils';

Component({
  mixins: [],
  data: {
    withinOneYear:[{
      "reason":"XXXXXXX案由",
      "penaltyTime":"2025-02-122025-02-122025-02-122025-02-122025-02-122025-02-12"
    },{
      "reason":"XXXXXXX案由",
      "penaltyTime":"2025-02-122025-02-122025-02-122025-02-122025-02-122025-02-12"
    }],
    formPage: {
      pageNo: 1,
      pageSize: 15,
      total: 10,
    },
    gotoScrollTop: 0,
    scrollToast:'',
    dataList:[],
    scrollVisible:false,
    popupData:{},
  },
  props: {
    licNo:'',
  },
  didMount() {
    console.log(this.props.licNo)
    this.getData();
  },
  didUpdate() {},
  didUnmount() {},
  methods: {
    getData(){
      httpApi.get('/api/dingapp/reportcomplaint/selectPage', {
        params: {
            licNo: this.props.licNo,
            current: this.data.formPage.pageNo,
            size: this.data.formPage.pageSize,
        }
      }).then(res => {
          console.log(res);
          if (res && res.data) {
              let data = res.data;
              if (data.current == 1 && data.total < 10) {
                  this.setData({
                      scrollToast: ''
                  })
              } else {
                  this.setData({
                      scrollToast: "向上拉动显示更多内容"
                  });
              }
              // 填充数据
              let list = [];
              if (this.data.formPage.pageNo > 1) {
                  list = this.data.searchResultList;
              }
              list.push(...res.data.records);
              this.setData({
                  'formPage.total': data.total,
                  'formPage.pageSize': data.size,
                  'formPage.pageNo': data.current,
                  dataList: list
              });
          }
      })
    },
    scrollToLowerSearch() {
      if (this.data.formPage.pageNo * this.data.formPage.pageSize >= this.data.formPage.total) {
          if (this.data.formPage.pageNo == 1 && this.data.formPage.total < 15) {
              this.setData({
                  scrollToast: ''
              })
              return
          }
          this.setData({
              scrollToast: '已经到底啦'
          })
          return;
      }
      this.setData({
          'formPage.pageNo': this.data.formPage.pageNo + 1
      });
      this.getData();
    },
    handlePopupClose() {
      this.setData({
          scrollVisible: false,
      });
    },
    handleOpenPopup(e){
      this.setData({
        scrollVisible: true,
        popupData:e.target.dataset.item,
      });
    },
  },
    
});
