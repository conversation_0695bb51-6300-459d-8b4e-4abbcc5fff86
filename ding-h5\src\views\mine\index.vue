<template>
  <div class="mainApp">
    <div class="userView">
      <div class="personalBox">
        <div class="photo">
          <img src="/src/assets/user_photo.png" alt="avatar">
        </div>
        <div class="info">
          <div>
            <text class="name">{{ userName }}</text>
          </div>
          <div class="region">所属单位: {{ '湛江市烟草专卖局城区管理中心' || deptInfo?.deptName || userStore.userInfo?.dept_name || '-' }}</div>
          <div class="region">岗位: {{ postInfo?.postName || userStore.userInfo?.post_name || '-' }}</div>
          <div class="dept">执法证号码: {{ userStore.userInfo?.detail.law_enforcement_certificate_no || '-' }}</div>
        </div>
      </div>
    </div>
    <div class="functionView">
      <div class="functionTitle">我的功能</div>
      <van-grid :gutter="20" :column-num="2" :border="false" class="custom-grid">
        <van-grid-item class="grid-card" icon="todo-list-o" text="举报投诉处理" @click="toTodo"/>
        <van-grid-item class="grid-card" icon="bill-o" text="涉案物品管理" @click="toEvidenceItems"/>
        <van-grid-item class="grid-card" icon="clock-o" text="勘查历史记录" @click="toSurveyHistory"/>
        <van-grid-item v-if="canAccessMonitoring" class="grid-card" icon="chart-trending-o" text="使用情况监控" @click="toMonitoring"/>
        <van-grid-item v-if="isAdministrator" class="grid-card" icon="contact" text="接口调试" @click="toDebugApi"/>
      </van-grid>
    </div>
    <!-- <van-button type="danger" block class="logout-btn" @click="handleLogout">退出登录</van-button> -->
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { showToast } from 'vant'
import * as deptApi from '@/api/dept';
import * as postApi from '@/api/post';


const router = useRouter()
const userStore = useUserStore()

// 部门信息
const deptInfo = ref(null)
// 岗位信息
const postInfo = ref(null)

// 使用存储的用户信息
const userName = computed(() => userStore.userInfo?.real_name || '未知用户')

// 获取部门信息
const fetchDeptInfo = async () => {
  try {
    // 确保用户信息已加载
    if (!userStore.userInfo?.user_id) {
      console.log('用户信息未加载，无法获取部门信息')
      return
    }

    const userId = userStore.userInfo.user_id
    const deptId = userStore.userInfo.dept_id

   // 调用API获取部门信息
    const response = await deptApi.getDeptList({
      userId: userId,
      deptId: deptId
    });

    // 根据API返回格式处理数据
    if (response.code === 200 && response.data) {
      deptInfo.value = response.data[0]
      console.log('部门信息获取成功:', response.data)
    } else {
      console.error('获取部门信息失败:', response.msg || '未知错误')
      showToast('获取部门信息失败')
    }
  } catch (error) {
    console.error('获取部门信息异常:', error)
    showToast('获取部门信息异常')
  }
}

// 获取岗位信息
const fetchPostInfo = async () => {
  try {
    // 确保用户信息已加载
    if (!userStore.userInfo?.user_id) {
      console.log('用户信息未加载，无法获取岗位信息')
      return
    }

    const userId = userStore.userInfo.user_id
    const postId = userStore.userInfo.post_id

   // 调用API获取部门信息
    const response = await postApi.getPostList({
      userId: userId,
      postId: postId
    });

    // 根据API返回格式处理数据
    if (response.code === 200 && response.data) {
      postInfo.value = response.data[0]
      console.log('岗位信息获取成功:', response.data)
    } else {
      console.error('获取岗位信息失败:', response.msg || '未知错误')
      showToast('获取岗位信息失败')
    }
  } catch (error) {
    console.error('获取岗位信息异常:', error)
    showToast('获取岗位信息异常')
  }
}

// 判断用户是否可以访问考勤监控功能
const canAccessMonitoring = computed(() => {
  console.info(userStore.userInfo)
  // 获取用户角色信息
  const roleName = userStore.userInfo?.role_name

  // 如果没有角色信息，则无权访问
  if (!roleName) return false

  // 定义可以访问考勤监控的角色列表
  // 根据需求配置可以访问考勤监控的角色
  const monitoringAccessRoles = ['monitoring', 'manager', 'administrator']

  // 将角色字符串拆分为数组（处理可能的多角色情况）
  const userRoles = roleName.split(',')

  // 调试信息
  console.log('用户角色:', userRoles)

  // 检查用户的任一角色是否在允许访问的角色列表中
  return userRoles.some(role => monitoringAccessRoles.includes(role.trim()))
})

// 判断用户是否为管理员
const isAdministrator = computed(() => {
  // 获取用户角色信息
  const roleName = userStore.userInfo?.role_name

  // 如果没有角色信息，则不是管理员
  if (!roleName) return false

  // 将角色字符串拆分为数组（处理可能的多角色情况）
  const userRoles = roleName.split(',')

  // 调试信息
  console.log('检查管理员权限，用户角色:', userRoles)

  // 检查用户的任一角色是否为管理员
  return userRoles.some(role => ['administrator', 'admin'].includes(role.trim().toLowerCase()))
})

const toTodo = () => {
  router.push('/todo')
}

const toEvidenceItems = () => {
  router.push('/evidence-items')
}

const toMonitoring = () => {
  router.push('/monitoring')
}

const toSurveyHistory = () => {
  router.push('/survey-history')
}

const toDebugApi = () => {
  router.push('/debug-api')
}

// 组件挂载时获取部门信息
onMounted(() => {
  fetchDeptInfo()
  fetchPostInfo()
})

</script>

<style lang="scss" scoped>
.mainApp {
  flex: 1;
  flex-direction: column;
  overflow: hidden;
  min-height: 100vh;
  background-color: #f7f8fa;

  .userView {
    background: rgb(2, 0, 36);
    background: linear-gradient(135deg, rgba(2, 0, 36, 1) 0%, rgba(9, 9, 121, 1) 41%, rgba(0, 212, 255, 1) 100%);
    width: 100%;
    height: 25vh;
    display: flex;
    align-items: center;
  }

  .personalBox {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
    margin-top: 5vh;
    margin-left: 30px;
  }

  .photo {
    width: 70px;
    height: 70px;
    border-radius: 80px;
    border: 3px solid #FFFFFF;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05), 0px 2px 4px 2px rgba(0, 0, 0, 0.05);
    margin-right: 16px;
    overflow: hidden;

    img {
      width: 60px;
      height: 60px;
      margin-left: 5px;
      margin-top: 5px;
    }
  }

  .info {
    color: #FFFFFF;
    display: flex;
    flex-direction: column;
    height: 80px;
    justify-content: space-around;
    flex: 1;

    .name {
      margin-right: 10px;
      font-size: 20px;
    }

    .region, .dept {
      font-size: 14px;
    }
  }

  .functionView {
    width: 100%;

    .functionTitle {
      margin: 15px;
      font-size: 16px;
      font-weight: 500;
    }
  }

  .logout-btn {
    width: calc(100% - 32px);
    margin: 24px 16px;
    border-radius: 8px;
    height: 44px;
    font-size: 16px;
  }
}
</style>