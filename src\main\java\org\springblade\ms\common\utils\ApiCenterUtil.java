package org.springblade.ms.common.utils;

import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustAllStrategy;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * 共享中心工具类
 */
@Service
@Slf4j
public class ApiCenterUtil {
    private static String PAASID;//网关用户 paasid
    private static String TOKEN;//网关用户 token
    private static String URL; //网关url

    @Value("${share.api.paasid}")
    public void setPAASID(String paasid) {
        ApiCenterUtil.PAASID = paasid;
    }

    @Value("${share.api.token}")
    public void setTOKEN(String token) {
        ApiCenterUtil.TOKEN = token;
    }

    @Value("${share.api.url}")
    public void setURL(String url) {
        ApiCenterUtil.URL = url;
    }

    private static void validateConfig() {
        if (PAASID == null || TOKEN == null || URL == null) {
            throw new IllegalStateException("API配置参数不能为空，请检查share.api相关配置");
        }
    }

    public static String send(String serviceId, Map<String, Object> requestBody) {
        try {
            // 获取当前时间戳
            long currentTime = Instant.now().getEpochSecond();

            // 生成随机nonce
            String nonce = generateNonce();

            validateConfig();

            // 生成签名
            String signature = generateSignature(currentTime, nonce);

            // 构建请求数据
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("query", requestBody);

            // 发送请求
            String response = sendRequest(serviceId, currentTime, nonce, signature, new JSONObject(requestData).toString());
            log.info("接口查询数据返回成功："  + response);

            // 将响应字符串转换为DsmResponseBody对象
//            com.google.gson.Gson gson = new com.google.gson.Gson();
//            DsmResponseBody responseBody = gson.fromJson(response, DsmResponseBody.class);
            return response;

        } catch (Exception e) {
            log.error("API请求失败: " + e.getMessage());
            return "";
        }
    }

    private static String generateNonce() {
        Random random = new Random();
        int randomNum = random.nextInt(1679616); // 36^4
        return String.format("%04x", randomNum).toLowerCase();
    }

    private static String generateSignature(long timestamp, String nonce) throws Exception {
        String signStr = timestamp + TOKEN + nonce + timestamp;
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] hash = digest.digest(signStr.getBytes(StandardCharsets.UTF_8));
        StringBuilder hexString = new StringBuilder();
        for (byte b : hash) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) hexString.append('0');
            hexString.append(hex);
        }
        return hexString.toString();
    }

    private static String sendRequest(String serviceId, long timestamp, String nonce, String signature, String requestBody) throws Exception {
        // 创建支持SSL的HttpClient
        SSLContextBuilder builder = new SSLContextBuilder();
        builder.loadTrustMaterial(null, new TrustAllStrategy());
        SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(
                builder.build(), NoopHostnameVerifier.INSTANCE);

        try (CloseableHttpClient client = HttpClients.custom().setSSLSocketFactory(sslsf).build()) {
            HttpPost httpPost = new HttpPost(URL);

            // 设置请求头
            httpPost.setHeader("x-tobacco-nonce", nonce);
            httpPost.setHeader("x-tobacco-signature", signature);
            httpPost.setHeader("x-tobacco-timestamp", String.valueOf(timestamp));
            httpPost.setHeader("x-tobacco-paasid", PAASID);
            httpPost.setHeader("x-tobacco-serviceId", serviceId);
            httpPost.setHeader("Content-Type", "application/json");

            // 设置请求体
            httpPost.setEntity(new StringEntity(requestBody));

            // 发送请求并获取响应
            try (CloseableHttpResponse response = client.execute(httpPost)) {
                HttpEntity entity = response.getEntity();
                return EntityUtils.toString(entity);
                // JSONObject jsonResponse = new JSONObject(responseStr);
                // return jsonResponse.getJSONObject("data").toString();
            }
        }
    }

//    @Data
//    public class DsmResponseBody {
//        private Integer errcode;
//        private String errmsg;
//        private DataList data;
//    }
//
//    @Data
//    public class DataList {
//        private JsonArray data;
//        private JsonObject metadata;
//    }
}
