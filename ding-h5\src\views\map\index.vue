<template>
    <div class="map-container">
        <!-- 添加头部搜索框 -->
        <div class="header-panel">
            <div class="search-box">
                <van-search v-model="searchKeyword" placeholder="搜索法人姓名、许可证号、名称、地址" shape="round" class="search-bar"
                            @click="handleSearchInputClick" @search="handleSearchBtnClick">
                    <template #right-icon>
                        <van-icon name="scan" size="20" @click.stop="handleSearchBarScan" />
                    </template>
                </van-search>
                <div class="search-btn" @click="handleSearchBtnClick">搜索</div>
            </div>
        </div>

        <div id="map" class="map"></div>

        <!-- 添加固定在地图中心的标记图标 -->
        <div class="center-marker-container">
            <img src="/custom_location.png" class="center-marker-icon" alt="位置标记" />
        </div>

        <div v-if="isShowDetailBox" class="detail-box">
            <div class="detail-header">
                <div class="header-left">
                    <h3>{{ currentLicense.companyName || '暂无数据' }}</h3>
                    <!-- 导航按钮 -->
                    <div class="header-nav-btn" @click="handleNavigation">
                        <van-icon name="guide-o" class="nav-icon" />
                        <span class="nav-text">导航</span>
                    </div>
                </div>
                <img src="@/assets/close-icon.svg" class="close-icon" @click="handleCloseDetailBox" alt="关闭" />
            </div>
            <div class="detail-content">
                <!-- 如果是学校信息 -->
                <div v-if="currentLicense.isSchool" class="detail-info">
                    <!-- 学校图标 -->
                    <div class="left-image">
                        <img v-if="currentLicense.photoPathList && currentLicense.photoPathList.length > 0"
                             :src="currentLicense.photoPathList[0].filthPath" class="store-image" alt="学校图标" />
                        <img v-else src="@/assets/picture-icon.svg" class="store-image" alt="默认学校图标" />
                    </div>

                    <!-- 学校信息区域 -->
                    <div class="right-info">
                        <div class="info-item">
                            <span class="label">地址：</span>
                            <span class="value">{{ currentLicense.businessAddr || '暂无数据' }}</span>
                        </div>
                        <div class="info-item" v-if="currentLicense.schoolInfo && currentLicense.schoolInfo.schoolCode">
                            <span class="label">学校代码：</span>
                            <span class="value">{{ currentLicense.schoolInfo.schoolCode }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">社会信用代码：</span>
                            <span class="value">{{ currentLicense.schoolInfo.socialCreditCode || '--' }}</span>
                        </div>
                        <div class="info-item" v-if="currentLicense.schoolInfo && currentLicense.schoolInfo.industryCategoryName">
                            <span class="label">学校类型：</span>
                            <span class="value">{{ currentLicense.schoolInfo.industryCategoryName }}</span>
                        </div>
                        <div class="info-item" v-if="currentLicense.schoolInfo && currentLicense.schoolInfo.officePhone">
                            <span class="label">办公电话：</span>
                            <span class="value">{{ currentLicense.schoolInfo.officePhone }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">距您</span>
                            <span class="value distance">{{ currentLicense.distance || '--' }}米</span>
                        </div>
                    </div>

                    <!-- 学校详情按钮区域 -->
                    <div class="detail-buttons">
                        <!-- 可以根据需要添加其他学校相关按钮 -->
                    </div>
                </div>

                <!-- 如果是零售户信息 -->
                <div v-else class="detail-info">
                    <!-- 照片区域 -->
                    <div class="left-image">
                        <img v-if="currentLicense.photoPathList && currentLicense.photoPathList.length > 0"
                             :src="currentLicense.photoPathList[0].filthPath" class="store-image" alt="门店照片" />
                        <img v-else src="@/assets/picture-icon.svg" class="store-image" alt="默认门店照片" />
                    </div>

                    <!-- 信息区域 -->
                    <div class="right-info">
                        <div class="info-item">
                            <span class="label">地址：</span>
                            <span class="value">{{ currentLicense.businessAddr || '暂无数据' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">许可证号：</span>
                            <span class="value">{{ currentLicense.licNo || '暂无数据' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">近一年案件数：</span>
                            <span class="value">{{ currentLicense.illegalRecordCount }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">近一年工单数：</span>
                            <span class="value">{{ currentLicense.reportComplaintCount }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">距您</span>
                            <span class="value distance">{{ currentLicense.distance || '--' }}米</span>
                        </div>
                    </div>

                    <!-- 按钮区域 -->
                    <div class="detail-buttons">
                        <!-- 零售户画像按钮 -->
                        <div class="detail-btn portrait-btn" @click="handleToLshhx">
                            <img src="@/assets/retailers-icon.svg" class="btn-icon" alt="用户" />
                            <span class="btn-text">零售户画像</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 添加业态选择器 -->
        <div class="format-param-box">
            <van-cell
                    title="业态"
                    :value="getFormatDisplayText()"
                    is-link
                    center
                    @click="showFormatPickerPopup"
                    :border="false"
                    class="format-cell"
            />
        </div>

        <!-- 添加定位按钮 -->
        <div class="position-box" :class="{ 'position-box-up': isShowDetailBox }" @click="getUserLocation">
            <img src="@/assets/position.svg" class="position-icon" alt="定位" />
        </div>

        <!-- 添加合理化布局按钮 - 调整到右侧 -->
        <div class="layout-box" :class="{ 'layout-box-selected': isCircleVisible, 'layout-box-up': isShowDetailBox }"
             @click="handleLayoutOptimization">
            <img src="@/assets/layout.svg" class="layout-icon" alt="合理化布局" />
        </div>

        <!-- 业态选择弹出层 -->
        <van-action-sheet
                v-model:show="showFormatPicker"
                title="请选择业态"
                close-on-click-overlay
                cancel-text="取消"
                teleport="body"
                :actions="formatParamList"
                @select="handleFormatParamPickerOK"
                @cancel="() => { showFormatPicker = false }"
                class="map-format-action-sheet"
        />

        <!-- 导航选项弹出层 -->
        <van-action-sheet
                v-model:show="showNavigationPicker"
                title="请选择导航应用"
                close-on-click-overlay
                cancel-text="取消"
                teleport="body"
                :actions="navigationActions"
                @select="handleNavigationSelect"
                @cancel="() => { showNavigationPicker = false }"
        />
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import * as dd from 'dingtalk-jsapi'
import { http } from '@/utils/http'
import { showFailToast, showToast, Icon as VanIcon } from 'vant'
import { useLicenseStore } from '@/stores/license'
import { gcj02ToWgs84, wgs84ToGcj02 } from '@/utils/coordinate'

const router = useRouter()
const licenseStore = useLicenseStore()

// 注意：userLocation 变量已不再使用，使用 currentLon, currentLat 和 currentAddress 代替

// 地图相关数据
const currentLon = ref(null) //存储的是转换后的坐标
const currentLat = ref(null)
const GCJ02Lon = ref(null)
const GCJ02Lat = ref(null)


const currentAddress = ref('')
const mapLicenseList = ref([])
const mapSchoolList = ref([]) // 学校列表
const map = ref(null)
const markers = ref([])
const schoolMarkers = ref([]) // 学校标记点
const selectedMarker = ref(null)
const currentCircle = ref(null) // 当前圆形覆盖物
const isCircleVisible = ref(false) // 控制圆形显示/隐藏
const locationMarker = ref(null) // 当前位置marker
const isSchoolMarker = ref(false) // 标记当前选中的是否为学校标记点

// 详情框相关数据
const isShowDetailBox = ref(false)
const currentLicense = ref(null)

// 搜索框相关数据
const searchKeyword = ref('')
const showFormatPicker = ref(false)
// 初始化为只有"全部"选项的数组
const formatParamList = ref([{ name: '全部', value: '', icon: 'apps-o' }])
const formatParamValue = ref('')

// 获取标记点图标
const getMarkerIcon = (format, isSelected = false, isSchool = false) => {
    // 如果是学校标记点，返回学校图标
    if (isSchool) {
        return new T.Icon({
            iconUrl: isSelected ? '/school-selected.png' : '/school.png',
            iconSize: new T.Point(48, 48),
            iconAnchor: new T.Point(24, 24)
        })
    }

    // 根据业态返回对应的图标URL
    let iconUrl = '/green.png' // 默认图标

    // 记录日志，便于调试
    // console.log('获取图标，业态:', format, '是否选中:', isSelected);

    switch(format) {
        case '商场':
            iconUrl = isSelected ? '/green2-selected.png' : '/green2.png'
            break
        case '超市':
            iconUrl = isSelected ? '/yellow-selected.png' : '/yellow.png'
            break
        case '其他':
            iconUrl = isSelected ? '/brown-selected.png' : '/brown.png'
            break
        case '娱乐服务类':
            iconUrl = isSelected ? '/blue2-selected.png' : '/blue2.png'
            break
        case '便利店':
            iconUrl = isSelected ? '/green-selected.png' : '/green.png'
            break
        case '烟草专业店':
            iconUrl = isSelected ? '/red-selected.png' : '/red.png'
            break
        case 'default': // 显式处理 default 情况
            iconUrl = '/grey2.png' // 使用灰色图标作为默认
            break
        case '无证零售户':
            iconUrl = isSelected ? '/grey-selected.png' : '/grey.png'
            break
        default:
            // 如果业态为空或未知，使用灰色图标
            iconUrl = isSelected ? '/grey2-selected.png' : '/grey2.png'
    }

    return new T.Icon({
        iconUrl,
        iconSize: new T.Point(48, 48),
        iconAnchor: new T.Point(24, 24)
    })
}

// 初始化地图
const initMap = () => {
    map.value = new T.Map('map', {
        zoom: 17,
        center: new T.LngLat(currentLon.value, currentLat.value),
        projection: 'EPSG:4326',
        dragEnable: true  // 明确启用拖拽功能
    })

    // 添加地图控件
    //map.value.addControl(new T.Control.Zoom())
    //map.value.addControl(new T.Control.Scale())

    map.value.enableDrag();  // 显式启用拖拽
    map.value.enableScrollWheelZoom();

    // 添加地图拖动结束事件
    map.value.addEventListener('dragend', async () => {
        console.log('地图拖动结束');
        await updateCenterPosition();
        // 拖动结束后加载周边零售户
        loadMapLicenseList();
    });
}

//获取用户定位
const getUserLocation = async () => {
    // const res = await dd.device.geolocation.get({
    //     targetAccuracy: 200,
    //     coordinate: 1,
    //     withReGeocode: true
    // })
    // const locationData = {
    //     longitude: res.longitude,
    //     latitude: res.latitude,
    //     markerId: res.id,
    //     address: res.address
    // }
    // localStorage.setItem('targetLocation', JSON.stringify(locationData))
    startLocation()
}
// 开始定位
const startLocation = async () => {
    try {
        // prod
        const res = await dd.device.geolocation.get({
          targetAccuracy: 200,
          coordinate: 1,
          withReGeocode: true
        })

        // 测试
        // const res = {
        //     longitude: 110.387653,
        //     latitude: 21.260024,
        //     address: '湛江市市场'
        // }

        GCJ02Lat.value = res.latitude
        GCJ02Lon.value = res.longitude
        // 转换坐标系
        const [gcjLng, gcjLat] = gcj02ToWgs84(res.longitude, res.latitude)
        console.log('转换后的坐标：', gcjLng, gcjLat)
        const userLocation = {
            longitude: gcjLng,
            latitude: gcjLat,
            address: res.address
        }

        // 设置当前位置（用于距离计算）
        currentLon.value = userLocation.longitude
        currentLat.value = userLocation.latitude
        currentAddress.value = userLocation.address

        if (map.value) {
            const position = new T.LngLat(currentLon.value, currentLat.value)

            const targetLocation = localStorage.getItem('targetLocation')
            if (!targetLocation) { //无需加载目标位置时定位当前位置
                map.value.panTo(position,18)
            }

            // 更新或创建位置marker
            if (locationMarker.value) {
                locationMarker.value.setLngLat(position)
            } else {
                locationMarker.value = new T.Marker(position, {
                    icon: new T.Icon({
                        iconUrl: '/location-marker.svg',
                        iconSize: new T.Point(32, 32),
                        iconAnchor: new T.Point(16, 16)
                    })
                })
                map.value.addOverLay(locationMarker.value)
            }

            await loadMapLicenseList()
        }
    } catch (error) {
        // showToast.fail('获取位置信息失败')
        console.error('定位失败:', error)
    }
}

// 加载地图标记点
const loadMapLicenseList = async () => {
    let targetLocation = localStorage.getItem('targetLocation')
    if (targetLocation) {
        targetLocation = JSON.parse(targetLocation)
    }
    try {
        // 使用地图中心点坐标加载周边零售户
        const res = await http.get('/api/dingapp/license/getDingMapLicenseList', {
            params: {
                longitude: targetLocation ? targetLocation.GCJ02Lon : GCJ02Lon.value,
                latitude: targetLocation ? targetLocation.GCJ02Lat : GCJ02Lat.value,
                formatParam: formatParamValue.value || '',
                searchParam: '' // 添加空的搜索参数，与原生应用保持一致
            }
        })
        console.log(res)
        if (res.data && Object.keys(res.data).length > 0) {
            mapLicenseList.value = res.data
            updateMapMarkers()
        }else{
            mapLicenseList.value = []
            updateMapMarkers()
        }

        // 加载学校标记点
        await loadNearbySchools()
    } catch (error) {
        // showToast.fail('获取零售户列表失败')
        console.error('获取零售户列表失败:', error)
    }
}

// 加载附近学校
const loadNearbySchools = async () => {
    let targetLocation = localStorage.getItem('targetLocation')
    if (targetLocation) {
        targetLocation = JSON.parse(targetLocation)
    }
    try {
        // 使用地图中心点坐标加载附近学校
        const res = await http.get('/api/dingapp/schoolInfo/nearby', {
            params: {
                longitude: targetLocation ? targetLocation.GCJ02Lon : GCJ02Lon.value,
                latitude: targetLocation ? targetLocation.GCJ02Lat : GCJ02Lat.value,
            }
        })

        if (res.data && res.data.length > 0) {
            mapSchoolList.value = res.data
            updateSchoolMarkers()
        } else {
            mapSchoolList.value = []
            // 清除学校标记点
            schoolMarkers.value.forEach(marker => map.value.removeOverLay(marker))
            schoolMarkers.value = []
        }
    } catch (error) {
        console.error('获取附近学校失败:', error)
    }
}

// 更新地图标记点
const updateMapMarkers = () => {
    // 清除旧的标记点
    markers.value.forEach(marker => map.value.removeOverLay(marker))
    markers.value = []

    // 添加新的标记点
    mapLicenseList.value.forEach((license) => {
        // 添加判空逻辑，确保经纬度有效
        if (license && license.longitude && license.latitude) {
            const marker = new T.Marker(new T.LngLat(license.longitude, license.latitude), {
                icon: getMarkerIcon(license.bizFormat)
            })

            // 将业态信息保存到标记点对象中，便于后续重置
            marker.bizFormat = license.bizFormat

            marker.addEventListener('click', () => handleMarkerClick(marker, license, false))
            map.value.addOverLay(marker)
            markers.value.push(marker)
        } else {
            // console.warn('跳过无效的标记点数据:', license)
        }
    })
}

// 更新学校标记点
const updateSchoolMarkers = () => {
    // 清除旧的学校标记点
    schoolMarkers.value.forEach(marker => map.value.removeOverLay(marker))
    schoolMarkers.value = []

    // 添加新的学校标记点
    mapSchoolList.value.forEach((school) => {
        if (school && school.longitude && school.latitude) {
            const marker = new T.Marker(new T.LngLat(school.longitude, school.latitude), {
                icon: getMarkerIcon('', false, true) // 使用学校图标
            })

            // 标记为学校标记点
            marker.isSchool = true

            marker.addEventListener('click', () => handleMarkerClick(marker, school, true))
            map.value.addOverLay(marker)
            schoolMarkers.value.push(marker)
        }
    })
}

// 点击锁定机制，防止快速点击
let isProcessingClick = false;

// 处理标记点点击
const handleMarkerClick = async (marker, data, isSchool = false) => {
    // 如果正在处理点击，则忽略新的点击
    if (isProcessingClick) {
        console.log('正在处理上一次点击，忽略当前点击');
        return;
    }

    // 设置锁定状态
    isProcessingClick = true;

    // 记录当前选中的是否为学校标记点
    isSchoolMarker.value = isSchool;

    try {
    // 如果当前显示合理化布局圆形，则关闭它
    if (isCircleVisible.value) {
        isCircleVisible.value = false
        if (currentCircle.value) {
            currentCircle.value.forEach(item => {
                map.value.removeOverLay(item.circle)
                map.value.removeOverLay(item.label)
            })
            currentCircle.value = null
        }
    }

    // 重置之前选中的标记点图标
    // 创建一个函数来重置标记点，便于管理
    const resetPreviousMarker = async () => {
        if (!selectedMarker.value || selectedMarker.value === marker) {
            return; // 没有选中的标记点或者点击的就是当前选中的，不需要重置
        }

        try {
            // 保存当前选中的标记点的引用，防止在重置过程中被改变
            const markerToReset = selectedMarker.value;

            // 如果是学校标记点
            if (markerToReset.isSchool) {
                markerToReset.setIcon(getMarkerIcon('', false, true));
                return;
            }

            // 使用标记点上保存的业态信息，如果有的话
            if (markerToReset.bizFormat) {
                console.log('使用标记点保存的业态信息重置图标:', markerToReset.bizFormat);
                markerToReset.setIcon(getMarkerIcon(markerToReset.bizFormat, false));
                return; // 重置成功，直接返回
            }

            // 如果标记点没有保存业态信息，则使用原来的方法查找
            const markerLng = markerToReset.getLngLat().lng;
            const markerLat = markerToReset.getLngLat().lat;
            const EPSILON = 0.0001; // 增大误差范围

            // 在地图数据中查找匹配的许可证
            const prevLicense = mapLicenseList.value.find(l => {
                if (!l || !l.longitude || !l.latitude) return false;
                const lngDiff = Math.abs(Number(l.longitude) - markerLng);
                const latDiff = Math.abs(Number(l.latitude) - markerLat);
                return lngDiff < EPSILON && latDiff < EPSILON;
            });

            // 记录详细日志
            console.log('查找到的许可证:', prevLicense ? prevLicense.bizFormat : '未找到');
            console.log('标记点坐标:', markerLng, markerLat);

            if (prevLicense && prevLicense.bizFormat) {
                console.log('使用查找到的业态重置图标:', prevLicense.bizFormat);
                markerToReset.setIcon(getMarkerIcon(prevLicense.bizFormat, false));
                // 将业态信息保存到标记点中，便于下次使用
                markerToReset.bizFormat = prevLicense.bizFormat;
            } else {
                // 如果仍然找不到匹配的许可证，使用默认图标
                console.log('未找到匹配的许可证，使用默认图标');
                markerToReset.setIcon(getMarkerIcon('default', false));
                // 将默认业态信息保存到标记点中
                markerToReset.bizFormat = 'default';
            }
        } catch (error) {
            console.error('重置标记点图标时出错:', error);
            // 出错时使用默认图标
            if (selectedMarker.value) {
                selectedMarker.value.setIcon(getMarkerIcon('default', false));
            }
        }
    };

    // 先重置之前的标记点，然后再设置新的选中标记点
    await resetPreviousMarker();

    // 设置当前选中的标记点图标
    if (isSchool) {
        // 如果是学校标记点
        marker.setIcon(getMarkerIcon('', true, true))
        console.log(data, '学校标记点')

        // 设置学校信息
        const school = data
        const distance = calculateDistance(
            currentLat.value,
            currentLon.value,
            school.latitude,
            school.longitude
        )

        // 创建学校信息对象
        currentLicense.value = {
            companyName: school.schoolName || '学校',
            businessAddr: school.addressName || '暂无地址',
            distance: Math.round(distance),
            photoPathList: school.photoPathList || [],
            isSchool: true,
            schoolInfo: school
        }
    } else {
        // 如果是零售户标记点
        const license = data
        marker.setIcon(getMarkerIcon(license.bizFormat, true))
        console.log(license, '目标标点')

        // 确保标记点保存了业态信息
        if (!marker.bizFormat && license.bizFormat) {
            marker.bizFormat = license.bizFormat;
            console.log('点击时保存业态信息到标记点:', license.bizFormat);
        }

        // 获取完整的许可证信息
        try {
            const res = await http.get('/api/dingapp/license/getDingMapLicense', {
                params: {
                    yhytId: license.id,
                    licNo: license.licNo
                }
            })
            const res1 = await http.get('/api/dingapp/illegalrecords/getLastYearTotalCount', {
                params: {
                    yhytId: license.id,
                }
            })

            if (res.data) {
                // 计算距离
                const distance = calculateDistance(
                    currentLat.value,
                    currentLon.value,
                    res.data.latitude,
                    res.data.longitude
                )

                // 更新许可证信息
                res.data.distance = Math.round(distance)

                // 设置默认值，防止未定义错误
                if (!res.data.photoPathList) {
                    res.data.photoPathList = []
                }

                currentLicense.value = res.data
                currentLicense.value.isSchool = false
            } else {
                // 设置默认的照片列表
                license.photoPathList = []
                license.isSchool = false
                currentLicense.value = license
            }

            if(res1.data){
                currentLicense.value.illegalRecordCount = res1.data.illegalRecordCount
                currentLicense.value.reportComplaintCount = res1.data.reportComplaintCount
            }

        } catch (error) {
            console.error('获取许可证详情失败:', error)
            // 设置默认的照片列表
            license.photoPathList = []
            license.isSchool = false
            currentLicense.value = license
        }
    }

    selectedMarker.value = marker
    isShowDetailBox.value = true
    } catch (error) {
        console.error('处理标记点点击时出错:', error);
    } finally {
        // 释放锁定状态
        isProcessingClick = false;
    }
}

// 计算两点之间的距离（米）
const calculateDistance = (lat1, lon1, lat2, lon2) => {
    const R = 6371000 // 地球半径，单位米
    const dLat = (lat2 - lat1) * Math.PI / 180
    const dLon = (lon2 - lon1) * Math.PI / 180
    const a =
        Math.sin(dLat/2) * Math.sin(dLat/2) +
        Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
        Math.sin(dLon/2) * Math.sin(dLon/2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
    return R * c
}

// 关闭详情框
const handleCloseDetailBox = () => {
    isShowDetailBox.value = false
    currentLicense.value = null
}

// 跳转到零售户画像页面
const handleToLshhx = () => {
    if (currentLicense.value) {
        licenseStore.setCurrentLicense(currentLicense.value)
        router.push(`/lshhx?view=true&xkzh=${currentLicense.value.licNo}`)
    }
}

/**
 * 打开地图应用进行导航
 * @param {Object} options 导航参数
 * @param {string} options.mapType 地图类型（gaode/baidu/tencent）
 * @param {number} options.latitude 目的地纬度
 * @param {number} options.longitude 目的地经度
 * @param {string} options.name 目的地名称
 */
const openMapNavigation = (options) => {
    const { mapType, latitude, longitude, name } = options;

    // 检测设备类型
    const isAndroid = navigator.userAgent.indexOf('Android') > -1;
    const isIOS = /iPhone|iPad|iPod/i.test(navigator.userAgent);

    // 构造导航链接
    let navigationUrl = '';

    // 根据选择的地图类型构造URL
    switch (mapType) {
        case 'gaode':
            // 高德地图
            if (isIOS) {
                navigationUrl = `iosamap://path?sourceApplication=dingtalk&dlat=${latitude}&dlon=${longitude}&dname=${encodeURIComponent(name)}&dev=0&t=0`;
            } else if (isAndroid) {
                navigationUrl = `androidamap://route?sourceApplication=dingtalk&dlat=${latitude}&dlon=${longitude}&dname=${encodeURIComponent(name)}&dev=0&t=0`;
            }
            break;

        case 'baidu':
            // 百度地图
            if (isIOS) {
                navigationUrl = `baidumap://map/direction?destination=name:${encodeURIComponent(name)}|latlng:${latitude},${longitude}&coord_type=gcj02&mode=driving&src=dingtalk`;
            } else if (isAndroid) {
                navigationUrl = `bdapp://map/direction?destination=name:${encodeURIComponent(name)}|latlng:${latitude},${longitude}&coord_type=gcj02&mode=driving&src=dingtalk`;
            }
            break;

        case 'tencent':
            // 腾讯地图
            navigationUrl = `qqmap://map/routeplan?type=drive&from=我的位置&to=${encodeURIComponent(name)}&tocoord=${latitude},${longitude}`;
            break;
    }

    // 打开地图应用
    if (navigationUrl) {
        window.location.href = navigationUrl;
    } else {
        showFailToast('不支持的设备或地图类型');
    }
}

// 导航选项列表
const navigationActions = ref([
    { name: '高德地图', value: 'gaode'},
    { name: '百度地图', value: 'baidu'},
    { name: '腾讯地图', value: 'tencent' }
]);

// 是否显示导航选项
const showNavigationPicker = ref(false);

// 当前导航目的地
const navigationDestination = ref(null);

// 处理导航按钮点击
const handleNavigation = () => {
    if (!currentLicense.value) return;

    // 获取目的地信息
    const destination = {
        name: currentLicense.value.companyName || '目的地',
        address: currentLicense.value.businessAddr || '',
        // 使用GCJ02坐标系（高德坐标系）
        latitude: currentLicense.value.latitude ? wgs84ToGcj02(currentLicense.value.longitude, currentLicense.value.latitude)[1] : 0,
        longitude: currentLicense.value.longitude ? wgs84ToGcj02(currentLicense.value.longitude, currentLicense.value.latitude)[0] : 0
    };

    // 如果是学校，使用学校的坐标
    if (currentLicense.value.isSchool && currentLicense.value.schoolInfo) {
        destination.latitude = currentLicense.value.schoolInfo.latitude ?
            wgs84ToGcj02(currentLicense.value.schoolInfo.longitude, currentLicense.value.schoolInfo.latitude)[1] : 0;
        destination.longitude = currentLicense.value.schoolInfo.longitude ?
            wgs84ToGcj02(currentLicense.value.schoolInfo.longitude, currentLicense.value.schoolInfo.latitude)[0] : 0;
    }

    // 检查坐标是否有效
    if (!destination.latitude || !destination.longitude) {
        showFailToast('无法获取目的地坐标，导航失败');
        return;
    }

    // 保存导航目的地
    navigationDestination.value = destination;

    // 显示导航选项
    showNavigationPicker.value = true;
}

// 处理导航选项选择
const handleNavigationSelect = (action) => {
    if (!navigationDestination.value) return;

    // 打开选择的地图应用进行导航
    openMapNavigation({
        mapType: action.value,
        ...navigationDestination.value
    });
}

// 显示业态选择器
const showFormatPickerPopup = () => {
    console.log('点击了业态选择器按钮');

    // 如果还未加载数据，先加载
    if (formatParamList.value.length <= 1) {
        loadFormatParamList()
            .then(() => {
                showFormatPicker.value = true;
            })
            .catch(() => {
                // 即使加载失败也显示选择器（将使用默认的全部选项）
                showFormatPicker.value = true;
            });
    } else {
        // 使用nextTick确保DOM更新
        nextTick(() => {
            showFormatPicker.value = true;
        });
    }
}

// 业态选择确认
const handleFormatParamPickerOK = (action, index) => {
    console.log('选择的业态:', action, index);

    // 确保action是对象
    if (action && typeof action === 'object') {
        // 保存选项的value值供API请求使用
        formatParamValue.value = action.value;

        // 打印选择后的值
        console.log('设置formatParamValue为:', formatParamValue.value);

        // 保存选择到本地存储
        localStorage.setItem('formatParamValue', formatParamValue.value);

        // 关闭弹出框
        showFormatPicker.value = false;

        // 重新加载地图数据
        loadMapLicenseList();
    } else {
        console.error('选择的action不是对象:', action);
    }
}

// 添加获取已保存的业务选择
const getFormatParamFromStorage = () => {
    const savedValue = localStorage.getItem('formatParamValue')
    if (savedValue) {
        console.log('从本地存储读取到的业态值:', savedValue);
        formatParamValue.value = savedValue;

        // 记录日志看看获取的文本是否正确
        console.log('从存储恢复后getFormatDisplayText():', getFormatDisplayText());
    }
}

// 获取业态显示文本
const getFormatDisplayText = () => {
    // 如果没有选择值，返回全部
    if (!formatParamValue.value) return '全部';

    // 在formatParamList中查找匹配项
    const selected = formatParamList.value.find(item =>
        item.value === formatParamValue.value
    );

    // 如果找到匹配项返回其文本，否则返回全部
    return selected ? selected.name : '全部';
}

// 搜索框点击
const handleSearchInputClick = () => {
    router.push('/search?currentAddress=' + currentAddress.value + '&currentLon=' + currentLon.value + '&currentLat=' + currentLat.value)
}

// 扫码搜索


const handleSearchBarScan = async () => {

    try {
        await dd.scan({
            type: 'qr',
            onSuccess: (res) => {
            const text = res.text
            if (!text) {
                showFailToast('未识别到二维码')
                return
            }

            const licenseNumberMatch = text.match(/许可证号[:：]\s*(\d+)/)

            if (licenseNumberMatch) {
                searchKeyword.value = licenseNumberMatch[1]
                showToast(searchKeyword.value)
                handleSearchBtnClick()
            } else {
                showFailToast('未识别许可证号')
            }
            },
            onFail: (err) => {
                showFailToast('扫码失败')
                console.error('扫码失败:', err)
            }
    })
    } catch (error) {
        console.error('扫码失败:', error)
    }
}

// 搜索按钮点击
const handleSearchBtnClick = () => {
    if (searchKeyword.value) {
        router.push('/search?currentAddress=' + currentAddress.value + '&currentLon=' + currentLon.value + '&currentLat=' + currentLat.value + '&searchParam=' + searchKeyword.value)

    }
}

// 添加加载业务列表的方法
const loadFormatParamList = async () => {
    try {
        const res = await http.get('/api/dingapp/license/getFormatList')
        // console.log('获取到的业态列表原始数据:', res.data);

        if (res.data && Array.isArray(res.data)) {
            // 转换为Picker需要的格式，只添加一个全部选项
            const formattedList = [
                { name: '全部', value: '', icon: 'apps-o' }
            ];

            // 添加API返回的数据，并为每个选项添加图标
            res.data.forEach(item => {
                // 排除可能的重复"全部"选项
                if (item && item !== '全部') {
                    // 根据业态类型选择对应的图标
                    let icon = '/green.png';

                    switch(item) {
                        case '商场':
                            icon = '/green2.png';
                            break;
                        case '超市':
                            icon = '/yellow.png';
                            break;
                        case '其他':
                            icon = '/brown.png';
                            break;
                        case '娱乐服务类':
                            icon = '/blue2.png';
                            break;
                        case '便利店':
                            icon = '/green.png';
                            break;
                        case '烟草专业店':
                            icon = '/red.png';
                            break;
                        case '无证零售户':
                            icon = '/grey.png';
                            break;
                        default:
                            icon = '/green.png';
                    }

                    formattedList.push({
                        name: item,
                        value: item,
                        icon: icon
                    });
                }
            });

            // 保存格式化后的列表
            formatParamList.value = formattedList;
            // console.log('转换后的业态列表:', formatParamList.value);

            // 检查从本地存储中获取的值是否有效
            if (formatParamValue.value) {
                // 检查当前值是否在新列表中存在
                const exists = formattedList.some(item => item.value === formatParamValue.value);
                if (!exists) {
                    // 如果当前值在新列表中不存在，重置为空（全部）
                    // console.log('当前业态值在新列表中不存在，重置为全部');
                    formatParamValue.value = '';
                    localStorage.setItem('formatParamValue', '');
                }
            }

            // 调用一次getFormatDisplayText确保显示更新
            console.log('API加载后getFormatDisplayText():', getFormatDisplayText());
        }
        return Promise.resolve(); // 返回一个已解析的Promise
    } catch (error) {
        console.error('获取业务列表失败:', error)
        // 保留默认值，至少有一个"全部"选项
        formatParamList.value = [{ name: '全部', value: '' }];
        return Promise.reject(error); // 返回一个拒绝的Promise
    }
}

onMounted(() => {
    // 加载天地图 SDK
    const script = document.createElement('script')
    script.src = 'https://api.tianditu.gov.cn/api?v=4.0&tk=e7d5714afd6f840366a1e9b4e7675236'
    script.onload = async () => {
        // 确保有初始坐标，避免地图因坐标未定义而不能正常初始化
        if (!currentLon.value || !currentLat.value) {
            // 设置默认坐标（可以使用湛江市的默认坐标）
            currentLon.value = 110.387653
            currentLat.value = 21.260024
        }
        initMap()
        await startLocation()
        checkTargetLocation()
    }
    document.head.appendChild(script)

    // 从本地存储获取业务选择
    getFormatParamFromStorage()
    // 加载业务列表
    loadFormatParamList()
})

// 检查是否有目标位置需要触发点击
const checkTargetLocation = () => {
    const targetLocation = localStorage.getItem('targetLocation')
    if (targetLocation) {
        const target = JSON.parse(targetLocation)

        let targetMarker = null
        let targetLicense = null

        // 如果有markerId，优先使用ID匹配
        if (target.markerId) {
            const licenseIndex = mapLicenseList.value.findIndex(license => license.id === target.markerId)
            if (licenseIndex !== -1) {
                targetMarker = markers.value[licenseIndex]
                targetLicense = mapLicenseList.value[licenseIndex]
            }
        }

        // 如果ID匹配失败，回退到坐标匹配
        if (!targetMarker) {
            // 使用更宽松的匹配条件，允许一定的误差范围
            // 由于浮点数精度问题，直接比较可能不准确
            const EPSILON = 0.000006 // 允许的误差范围

            // 查找最接近目标位置的标记点
            let closestMarker = null
            let closestLicense = null
            let minDistance = Number.MAX_VALUE

            markers.value.forEach((marker, index) => {
                const markerLngLat = marker.getLngLat()
                const license = mapLicenseList.value[index]

                // 计算标记点与目标位置的距离
                const distance = Math.sqrt(
                    Math.pow(markerLngLat.lng - target.longitude, 2) +
                    Math.pow(markerLngLat.lat - target.latitude, 2)
                )

                // 如果距离小于当前最小距离，更新最接近的标记点
                if (distance < minDistance) {
                    minDistance = distance
                    closestMarker = marker
                    closestLicense = license
                }
            })

            // 如果找到最接近的标记点，并且距离在可接受范围内
            if (closestMarker && minDistance < EPSILON) {
                targetMarker = closestMarker
                targetLicense = closestLicense
            } 
        }

        // 如果找到目标标记点，触发点击事件
        if (targetMarker && targetLicense) {
            handleMarkerClick(targetMarker, targetLicense)
            map.value.panTo(targetMarker.getLngLat(), 18)
        }

        // 处理完目标位置后移除它
        localStorage.removeItem('targetLocation')
    }
}





onUnmounted(() => {
    if (map.value) {
        // map.value.destroy()
    }
})

// 注意：handleMapMoveEnd 函数已移除，改为使用内联的事件处理函数

// 更新地图中心点位置
const updateCenterPosition = async () => {
    try {
        const center = map.value.getCenter();

        if (!center || typeof center.lng === 'undefined' || typeof center.lat === 'undefined') {
            console.error('获取地图中心点失败:', center);
            return Promise.reject('获取地图中心点失败');
        }

        // 更新中心点坐标（WGS84坐标系）
        currentLon.value = center.lng;
        currentLat.value = center.lat;

        // 转换为GCJ02坐标系（高德坐标系）用于API请求
        try {
            const [gcjLng, gcjLat] = wgs84ToGcj02(center.lng, center.lat);
            GCJ02Lon.value = gcjLng;
            GCJ02Lat.value = gcjLat;

            console.log('地图中心点位置更新:', {
                wgs84: { lng: currentLon.value, lat: currentLat.value },
                gcj02: { lng: GCJ02Lon.value, lat: GCJ02Lat.value }
            });
        } catch (error) {
            console.error('坐标转换失败:', error);
            // 如果转换失败，使用原始坐标
            GCJ02Lon.value = center.lng;
            GCJ02Lat.value = center.lat;
        }

        // 返回一个Promise，确保异步操作完成
        return Promise.resolve();
    } catch (error) {
        console.error('更新中心点位置时出错:', error);
        return Promise.reject(error);
    }
};

// 处理合理化布局
const handleLayoutOptimization = () => {
    // 如果没有选中的标记点，则不执行操作
    if (!selectedMarker.value) return

    // 切换圆形显示状态
    isCircleVisible.value = !isCircleVisible.value

    // 清除之前的圆形覆盖物和标签
    if (currentCircle.value) {
        currentCircle.value.forEach(item => {
            map.value.removeOverLay(item.circle)
            map.value.removeOverLay(item.label)
        })
        currentCircle.value = null
    }

    // 如果需要显示圆形
    if (isCircleVisible.value) {
        const center = selectedMarker.value.getLngLat()
        currentCircle.value = []

        // 创建30米半径的红色圆和标签
        const redCircle = new T.Circle(center, 30, {
            color: '#FF0000',
            weight: 2,
            opacity: 0.8,
            fillColor: '#FF0000',
            fillOpacity: 0.3
        })
        const redLabel = new T.Label({
            text: '30米',
            position: new T.LngLat(center.lng, center.lat + 0.0003),
            offset: new T.Point(-35, 20)
        })
        redLabel.setBackgroundColor('transparent')
        redLabel.setBorderLine(0)
        map.value.addOverLay(redCircle)
        map.value.addOverLay(redLabel)
        currentCircle.value.push({ circle: redCircle, label: redLabel })

        // 创建50米半径的黄色圆和标签
        const yellowCircle = new T.Circle(center, 50, {
            color: '#FFFF00',
            weight: 2,
            opacity: 0.8,
            fillColor: '#FFFF00',
            fillOpacity: 0.3
        })
        const yellowLabel = new T.Label({
            text: '50米',
            position: new T.LngLat(center.lng, center.lat + 0.0005),
            offset: new T.Point(-35, 20)
        })
        yellowLabel.setBackgroundColor('transparent')
        yellowLabel.setBorderLine(0)
        map.value.addOverLay(yellowCircle)
        map.value.addOverLay(yellowLabel)
        currentCircle.value.push({ circle: yellowCircle, label: yellowLabel })
    }
}
</script>

<style lang="scss" scoped>
.map-container {
    height: calc(100vh - 50px);
    /* 减去tabbar的高度 */
    position: relative;
    padding-bottom: 50px;
    /* 为tabbar预留空间 */

    .map {
        height: 100%;
    }

    /* 头部搜索框样式 */
    .header-panel {
        position: absolute;
        z-index: 1002;
        left: 0;
        right: 0;
        top: 0;
        padding: 16px 7px 0 7px;
        display: flex;
        flex-direction: column;
    }

    .search-box {
        display: flex;
        height: 80px;
        width: 100%;
        align-items: center;
    }

    .search-bar {
        flex: 1;
        border-radius: 20px 0 0 20px;
        height: 40px;
        :deep(.van-search__content) {
            border-radius: 20px 0 0 20px;
            background-color: #ffffff;
        }
    }

    .search-btn {
        width: 60px;
        height: 40px;
        line-height: 40px;
        border-radius: 0 10px 10px 0;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #FFFFFF;
        background-color: #4285F4;
        font-size: 16px;
    }

    /* 业态选择器样式 */
    .format-param-box {
        position: absolute;
        z-index: 1001;
        right: 8px;
        top: 80px;
        width: auto;
        min-width: 120px;
        height: 36px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 8px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        background-color: #fff;
        overflow: hidden;

        :deep(.van-cell) {
            padding: 5px 10px;
            height: 36px;
            line-height: 26px;
            font-size: 14px;
        }

        :deep(.van-cell__title) {
            flex: 0;
            min-width: 40px;
        }

        :deep(.van-cell__value) {
            flex: 1;
            text-align: center;
            color: #333;
        }
    }

    /* 定位按钮样式 */
    .position-box {
        position: absolute;
        z-index: 1001;
        left: 16px;
        bottom: 120px;
        width: 36px;
        height: 36px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 8px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        background-color: #fff;
        transition: bottom 0.3s ease; /* 添加过渡效果 */

        &.position-box-up {
            bottom: 330px; /* 详情框显示时上移的位置 */
        }

        .position-icon {
            width: 20px;
            height: 20px;
        }
    }

    /* 业态选择器样式已移至外部 */

    /* 合理化布局按钮样式 */
    .layout-box {
        position: absolute;
        z-index: 1001;
        right: 16px; /* 改为右侧定位 */
        bottom: 120px; /* 与定位按钮在同一水平线上 */
        width: 36px;
        height: 36px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 8px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        background-color: #fff;
        transition: all 0.3s ease, bottom 0.3s ease; /* 添加底部位置的过渡效果 */

        &.layout-box-up {
            bottom: 330px; /* 详情框显示时上移的位置，与定位按钮保持一致 */
        }

        &.layout-box-selected {
            background-color: #4285F4;
        }

        .layout-icon {
            width: 20px;
            height: 20px;
            transition: all 0.3s ease;
        }

        &.layout-box-selected .layout-icon {
            filter: brightness(0) invert(1);
        }
    }

    .detail-box {
        position: absolute;
        bottom: 95px;
        left: 10px;
        right: 10px;
        background: #fff;
        padding: 15px 15px 0;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        z-index: 1001;

        .detail-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start; /* 改为顶部对齐，以便标题换行时布局正确 */
            margin-bottom: 10px;
            padding-top: 5px; /* 添加顶部内边距，使布局更加美观 */

            .header-left {
                display: flex;
                flex-direction: row; /* 水平排列 */
                align-items: flex-start; /* 顶部对齐 */
                width: calc(100% - 26px); /* 减去关闭按钮的宽度和间距 */
            }

            h3 {
                margin: 0;
                font-size: 16px;
                font-weight: bold;
                color: #333;
                word-break: break-word; /* 允许在任何字符间换行 */
                line-height: 1.3; /* 增加行高，使多行文本看起来更好 */
                flex: 1; /* 占据剩余空间 */
                padding-right: 8px; /* 与导航按钮保持间距 */
                min-width: 0; /* 确保flex项可以收缩 */
            }

            .header-nav-btn {
                display: flex;
                align-items: center;
                background-color: #4285F4; 
                color: white;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                cursor: pointer;
                flex-shrink: 0; /* 防止按钮被压缩 */
                white-space: nowrap; /* 防止文字换行 */
                align-self: flex-start; /* 顶部对齐 */
                margin-top: 2px; /* 微调垂直位置，使其与标题第一行对齐 */
            }

            .nav-icon {
                margin-right: 4px;
                font-size: 14px;
            }

            .close-icon {
                width: 16px;
                height: 16px;
                cursor: pointer;
                flex-shrink: 0; /* 防止按钮被压缩 */
                margin-left: 10px; /* 确保与标题区域有一定间距 */
            }
        }

        .detail-content {
            margin-bottom: 10px;

            .detail-info {
                display: flex;
                position: relative;

                .left-image {
                    flex: 0 0 60px;
                    height: 90px;
                    margin-right: 15px;
                    border-radius: 4px;
                    overflow: hidden;

                    .store-image {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                    }
                }

                .right-info {
                    flex: 1;

                    .info-item {
                        margin-bottom: 4px;
                        font-size: 14px;
                        line-height: 22px;
                        padding-right: 30px;

                        .label {
                            color: #666;
                        }

                        .value {
                            color: #333;

                            &.distance {
                                color: #ff6600;
                            }
                        }
                    }
                }

                /* 详情按钮区域 */
                .detail-buttons {
                    display: flex;
                    justify-content: flex-end;
                    gap: 10px;
                    margin-top: 10px;
                    margin-bottom: 10px;
                }

                .detail-btn {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    width: 50px;
                    cursor: pointer;
                }

                .portrait-btn .btn-icon,
                .navigation-btn .btn-icon {
                    width: 30px;
                    height: 30px;
                    margin-bottom: 5px;
                    border-radius: 50%;
                    background-color: #fff;
                    padding: 5px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }

                .navigation-btn .btn-icon {
                    color: #4285F4; /* 导航图标颜色为绿色 */
                    font-size: 20px;
                }

                .btn-text {
                    font-size: 12px;
                    color: #666;
                    text-align: center;
                    white-space: nowrap;
                }
            }
        }
    }
}

</style>

<!-- 业态选择器特定样式 - 选项居中显示，内容左对齐 -->
<style>
.map-format-action-sheet .van-action-sheet__item {
    width: 8.75rem !important; /* 设置固定宽度 */
    margin: 0 auto !important; /* 水平居中 */
    justify-content: flex-start !important; /* 内容左对齐 */
}

.map-format-action-sheet .van-action-sheet__name {
    text-align: left !important;
    margin: 0 !important;
    flex: 1 !important;
}

.map-format-action-sheet .van-action-sheet__item .van-icon,
.map-navigation-action-sheet .van-action-sheet__item .van-icon {
    margin-right: 8px !important;
    font-size: 18px !important;
}

/* 导航选项样式 */
.map-navigation-action-sheet .van-action-sheet__item {
    width: 8.75rem !important; /* 设置固定宽度 */
    margin: 0 auto !important; /* 水平居中 */
    justify-content: flex-start !important; /* 内容左对齐 */
}

.map-navigation-action-sheet .van-action-sheet__name {
    text-align: left !important;
    margin: 0 !important;
    flex: 1 !important;
}

/* 中心标记容器样式 */
.center-marker-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none; /* 允许点击穿透到地图 */
    z-index: 1000; /* 确保在标记点之上，但在详情框之下 */
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 中心标记图标样式 */
.center-marker-icon {
    width: 36px; /* 图标宽度 */
    height: 36px; /* 图标高度 */
    transform: translateY(-40px); /* 向上偏移半个图标高度，使底部对准中心点 */
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3)); /* 添加阴影增强可见性 */
}
</style>