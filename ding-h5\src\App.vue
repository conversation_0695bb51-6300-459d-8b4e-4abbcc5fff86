<template>
    <div id="app">
        <router-view style="z-index: 1;"></router-view>

        <!-- 调试控制台开关 -->
<!--        <DebugToggle />-->

        <!-- 添加返回首页按钮 -->
        <div v-if="showHomeButton"
            class="home-button"
            :style="homeButtonStyle"
            @click="goToHome"
            @touchstart="startDrag"
            @touchmove.prevent="onDrag"
            @touchend="endDrag"
        >
            <van-icon name="home-o" size="24" />
            <span>首页</span>
        </div>

        <van-tabbar v-model="currentTab" v-if="showTabBar" class="custom-tabbar">
            <van-tabbar-item name="map" to="/map">
                <template #icon="props">
                    <div class="tabbar-icon-wrapper">
                        <img v-if="props.active" src="@/assets/map_hl.png" class="tab-icon" />
                        <img v-else src="@/assets/map.png" class="tab-icon" />
                        <span :class="{ active: props.active }">首页</span>
                    </div>
                </template>
            </van-tabbar-item>
            <div class="center-button">
                <div class="tabbar-icon-wrapper survey-button" @click="handleCreateExploration">
                    <div class="survey-icon-wrapper">
                        <div class="survey-text">勘查</div>
                    </div>
                </div>
            </div>
            <van-tabbar-item name="mine" to="/mine">
                <template #icon="props">
                    <div class="tabbar-icon-wrapper">
                        <img v-if="props.active" src="@/assets/wode_hl.png" class="tab-icon" />
                        <img v-else src="@/assets/wode.png" class="tab-icon" />
                        <span :class="{ active: props.active }">我的</span>
                    </div>
                </template>
            </van-tabbar-item>
        </van-tabbar>

        <!-- 勘查弹窗 -->
        <van-dialog v-model:show="explorationDialogVisible" :close-on-click-overlay="true"
            :show-confirm-button="false" @close="handleExplorationDialogClose">
            <template #title>
                <div class="dialog-title-container">
                    <span>开始勘查</span>
                    <van-popover
                        v-model:show="showPopover"
                        theme="dark"
                        placement="bottom"
                        :offset="[0, 10]"
                    >
                        <template #reference>
                            <van-icon name="question-o" class="help-icon" />
                        </template>
                        <div class="popover-content">
                            <div class="popover-title">获取许可证号方式：</div>
                            <div class="popover-steps">
                                <div class="popover-step">
                                    <span class="step-number">1</span>
                                    <span class="step-text">点击右侧扫码按钮，扫描许可证二维码</span>
                                </div>
                                <div class="popover-step">
                                    <span class="step-number">2</span>
                                    <span class="step-text">扫码失败时，可上传许可证照片识别</span>
                                </div>
                                <div class="popover-step">
                                    <span class="step-number">3</span>
                                    <span class="step-text">以上方式均失败时，可手动输入许可证号</span>
                                </div>
                            </div>
                        </div>
                    </van-popover>
                </div>
            </template>
            <div class="exploration-dialog-content">
                <van-search v-model="licenseNumber" placeholder="请输入许可证号" @search="handleSearchBtnClick">
                    <template #right-icon>
                        <van-icon name="scan" size="24" @click="handleSearchBarScan" />
                    </template>
                </van-search>

                <div class="upload-container">
                    <div class="upload-title">
                        <van-icon name="photograph" class="upload-icon" />
                        上传许可证照片识别
                    </div>
                    <van-uploader
                        v-model="uploadedImages"
                        :max-count="1"
                        :after-read="handleImageUpload"
                        @delete="handleImageDelete"
                    />
                </div>

                <div class="search-btn-container">
                    <van-button type="primary" block @click="handleSearchBtnClick">搜索</van-button>
                </div>
            </div>
        </van-dialog>

        <!-- 提示信息 -->
        <van-toast v-model:show="toastShow" type="text" position="middle" :duration="2500">{{ toastMessage }}</van-toast>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import * as dd from 'dingtalk-jsapi'
import { showToast } from 'vant'
import { http } from '@/utils/http'

const route = useRoute()
const router = useRouter()
const currentTab = computed(() => {
    const path = route.path
    if (path === '/map') return 'map'
    if (path === '/mine') return 'mine'
    return 'map'
})

// 控制底部标签栏的显示
const showTabBar = computed(() => {
    const TabBarRoutes = ['/map', '/mine', "/http://127.0.0.1:3000/"]
    return TabBarRoutes.includes(route.path)
})

// 控制返回首页按钮的显示
const showHomeButton = computed(() => {
    const hideRoutes = ['/map', '/mine','/login','/evidence-items/detail-table','/monitoring']
    return !hideRoutes.includes(route.path)
})

// 勘查弹窗相关状态
const explorationDialogVisible = ref(false)
const licenseNumber = ref('')
const toastShow = ref(false)
const toastMessage = ref('')
const uploadedImages = ref([])
const isUploading = ref(false)
const showPopover = ref(false)

// 处理创建勘查
const handleCreateExploration = () => {
    const currentPath = route.path

    // 如果当前在地图页面，显示弹窗
    if (currentPath === '/map') {
        explorationDialogVisible.value = true
    } else {
        // 如果在其他页面，先返回地图页面
        router.push('/map')
        explorationDialogVisible.value = true

    }
}

// 处理弹窗关闭
const handleExplorationDialogClose = () => {
    licenseNumber.value = ''
    explorationDialogVisible.value = false
    showPopover.value = false
}

// 处理搜索按钮点击
const handleSearchBtnClick = () => {
    if (!licenseNumber.value) {
        showToast('请通过扫码、上传照片或手动输入获取许可证号')
        return
    }

    // 先调用接口获取数据，只有在成功获取到数据后才跳转
    http.get(`/api/dingapp/exploration/getTodayExploration?licNo=${licenseNumber.value}`)
        .then(res => {
            if (res.data && Object.keys(res.data).length > 0) {
                // 获取到数据，关闭弹窗并跳转
                explorationDialogVisible.value = false
                handleImageDelete();
                router.push(`/survey?type=create&xkzh=${licenseNumber.value}`)
            } else {
                // 未找到数据
                showToast('未找到零售户数据')
            }
        })
        .catch(err => {
            console.error('获取数据失败:', err)
            showToast('获取数据失败')
        })
}

// 处理扫码
const handleSearchBarScan = () => {
    dd.scan({
        type: 'qr',
        onSuccess: (res) => {
            const text = res.text

            if (!text) {
                showToast('未识别到二维码，请尝试上传许可证照片')
                return
            }

            const licenseNumberMatch = text.match(/许可证号[:：]\s*(\d+)/)
            if (licenseNumberMatch) {
                const scannedLicenseNumber = licenseNumberMatch[1]
                // 先调用接口获取数据，只有在成功获取到数据后才跳转
                http.get(`/api/dingapp/exploration/getTodayExploration?licNo=${scannedLicenseNumber}`)
                    .then(res => {
                        if (res.data && Object.keys(res.data).length > 0) {
                            // 获取到数据，设置许可证号并跳转
                            licenseNumber.value = scannedLicenseNumber
                            explorationDialogVisible.value = false
                            router.push(`/survey?type=create&xkzh=${scannedLicenseNumber}`)
                        } else {
                            // 未找到数据
                            showToast('未找到零售户数据')
                            licenseNumber.value = ''
                        }
                    })
                    .catch(err => {
                        console.error('获取数据失败:', err)
                        showToast('获取数据失败')
                    })
            } else {
                showToast('未识别许可证号，请尝试上传许可证照片或手动输入')
            }
        },
        fail: (err) => {
            console.error('扫码失败:', err)
            showToast('扫码失败，请尝试上传许可证照片或手动输入')
        }
    })
}

// 处理图片上传
const handleImageUpload = async (file) => {
    try {
        isUploading.value = true
        file.status = 'uploading'
        file.message = '上传中...'

        // 创建FormData对象
        const formData = new FormData()
        formData.append('file', file.file)
        formData.append('type', 'blicense')

        // 调用OCR接口识别许可证号
        const res = await http.post('/api/dingapp/file/ocr', formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        })
        if (res.data && res.data.result && res.data.result.data) {
            const parsedData = JSON.parse(res.data.result.data);
            console.log(parsedData)
            // 识别成功，设置许可证号
            if(parsedData["统一社会信用代码"]){
                licenseNumber.value = parsedData["统一社会信用代码"]
                file.status = 'done'
                showToast('识别成功')
            }else{
                file.status = 'done'
                showToast('未能识别许可证号，请手动输入许可证号')
            }
        } else {
            // 识别失败
            file.status = 'failed'
            file.message = '识别失败'
            showToast('未能识别许可证号，请手动输入许可证号')
        }
    } catch (error) {
        console.error('图片上传或识别失败:', error)
        file.status = 'failed'
        file.message = '上传失败'
        showToast('图片上传或识别失败，请手动输入许可证号')
    } finally {
        isUploading.value = false
    }
}

// 处理图片删除
const handleImageDelete = () => {
    // 清空已上传的图片
    uploadedImages.value = []
}

// 返回首页方法
const goToHome = () => {
    router.push('/map')
}

// 按钮拖动相关状态
const buttonPosition = ref({ x: 0, y: 0 })
const isDragging = ref(false)
const startPosition = ref({ x: 0, y: 0 })

// 计算按钮样式
const homeButtonStyle = computed(() => {
    return {
        right: buttonPosition.value.x + 'px',
        bottom: buttonPosition.value.y + 'px',
    }
})

// 开始拖动
const startDrag = (event) => {
    // 防止点击事件触发
    if (event.type === 'touchstart') {
        isDragging.value = true
        startPosition.value = {
            x: event.touches[0].clientX,
            y: event.touches[0].clientY
        }
    }
}

// 拖动中
const onDrag = (event) => {
    if (!isDragging.value) return

    // 获取触摸点位置
    const touch = event.touches[0]

    // 计算移动距离
    const deltaX = startPosition.value.x - touch.clientX
    const deltaY = startPosition.value.y - touch.clientY

    // 更新按钮位置
    buttonPosition.value = {
        x: Math.max(0, Math.min(window.innerWidth - 60, buttonPosition.value.x + deltaX)),
        y: Math.max(0, Math.min(window.innerHeight - 120, buttonPosition.value.y + deltaY))
    }

    // 更新起始位置
    startPosition.value = {
        x: touch.clientX,
        y: touch.clientY
    }
}

// 结束拖动
const endDrag = () => {
    if (isDragging.value) {
        isDragging.value = false
        // 保存位置到本地存储
        localStorage.setItem('homeButtonPosition', JSON.stringify(buttonPosition.value))
    }
}

// 在组件挂载时加载保存的位置
onMounted(() => {
    const savedPosition = localStorage.getItem('homeButtonPosition')
    if (savedPosition) {
        try {
            buttonPosition.value = JSON.parse(savedPosition)
        } catch (e) {
            console.error('解析保存的按钮位置失败:', e)
        }
    }
})
</script>

<style>
#app {
    font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica,
        Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei',
        sans-serif;
}

.exploration-dialog-content {
    padding: 16px;
}

.dialog-title-container {
    display: flex;
    align-items: center;
    justify-content: center;
}

.help-icon {
    font-size: 18px;
    color: #4285F4;
    margin-left: 6px;
}

.popover-content {
    padding: 8px;
    max-width: 280px;
}

.popover-title {
    font-size: 14px;
    font-weight: 500;
    color: #fff;
    margin-bottom: 8px;
}

.popover-steps {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.popover-step {
    display: flex;
    align-items: center;
    font-size: 13px;
}

.step-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    background-color: #fff;
    color: #4285F4;
    border-radius: 50%;
    font-size: 12px;
    margin-right: 8px;
}

.step-text {
    color: #fff;
}

.upload-container {
    margin-top: 16px;
    margin-bottom: 16px;
    border-top: 1px solid #ebedf0;
    padding-top: 16px;
}

.upload-title {
    font-size: 14px;
    color: #646566;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
}

.upload-icon {
    margin-right: 4px;
    color: #4285F4;
}

.search-btn-container {
    margin-top: 16px;
}

/* 新增的 tabbar 样式 */
.custom-tabbar {
    /* box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.1); */
    padding: 4px 0;
    height: 85px !important;
    position: relative;
    z-index: 999;
}
.van-tabbar-item__icon img {
    display: block;
    height: 28px !important;
}

.tabbar-icon-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 28px;
}
.van-tabbar-item__icon img {
    display: block;
    height: 28px;
}


/* 中间凸起按钮样式 */
.center-button {
    background-color: transparent;
    margin-top: -30px;
    z-index: 1000;
}

.survey-button {
    height: auto !important;
    position: relative;
    z-index: 1000;
}

.survey-icon-wrapper {
    width: 80px;
    height: 80px;
    background: url('@/assets/scan_hl.svg') no-repeat center center;
    background-size: contain;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 1000;
}

.survey-text {
    color: #4285F4;
    font-size: 14px;
    font-weight: 500;
    position: absolute;

    bottom: -10px;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

/* 其他已有样式保持不变 */
.tab-icon {
    width: 28px;
    height: 28px;
    margin-bottom: 4px;

}

.tab-icon-active {
    opacity: 1;
}

.tabbar-icon-wrapper .van-icon {
    font-size: 24px;
    margin-bottom: 4px;
    color: #9295A0;
}

.tabbar-icon-wrapper span {
    font-size: 12px;
    color: #9295A0;
}

.tabbar-icon-wrapper .van-icon.active {
    color: #4285F4;
}

.tabbar-icon-wrapper span.active {
    color: #4285F4;
}

/* 覆盖 vant 默认样式 */
:deep(.van-tabbar-item) {
    height: 85px;
}

:deep(.van-tabbar-item__icon) {
    margin-bottom: 0;
}


.van-tabbar-item--active{
    background-color: transparent !important;
}

/* 返回首页按钮样式 */
.home-button {
    position: fixed;
    right: 16px;
    bottom: 120px;
    width: 44px;
    height: 44px;
    background-color: #fff;
    border-radius: 22px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 999;
    touch-action: none; /* 防止浏览器默认行为干扰拖动 */
    user-select: none; /* 防止文本选中 */
}

.home-button .van-icon {
    color: #4285F4;
    margin-bottom: 2px;
}

.home-button span {
    font-size: 10px;
    color: #4285F4;
    line-height: 1;
}

.home-button:active {
    background-color: #f5f5f5;
}
</style>