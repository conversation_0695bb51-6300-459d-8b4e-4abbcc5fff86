package org.springblade.ms.dingapp.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.modules.system.pojo.entity.Dept;
import org.springblade.modules.system.service.IDeptService;
import org.springblade.ms.dingapp.service.IDingReportComplaintService;
import org.springblade.ms.dingapp.vo.DingMapLicenseVO;
import org.springblade.ms.reportcomplaint.pojo.entity.ReportComplaintEntity;
import org.springblade.ms.reportcomplaint.pojo.entity.ReportIllegalLabelEntity;
import org.springblade.ms.reportcomplaint.pojo.vo.ReportComplaintVO;
import org.springblade.ms.reportcomplaint.pojo.vo.ReportIllegalLabelVO;
import org.springblade.ms.reportcomplaint.service.IReportComplaintService;
import org.springblade.ms.reportcomplaint.service.IReportIllegalLabelService;
import org.springblade.ms.reportcomplaint.wrapper.ReportComplaintWrapper;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-02-16 18:36
 * 举报投诉
 */
@RestController
@AllArgsConstructor
@RequestMapping("/dingapp/reportcomplaint")
public class DingReportComplaintController extends BladeController {

    private final IDingReportComplaintService dingReportComplaintService;
    private final IReportComplaintService reportComplaintService;
    private final IReportIllegalLabelService reportIllegalLabelService;
    private final IDeptService deptService;


    @GetMapping("/selectPage")
    public R<IPage<ReportComplaintVO>> selectPage(
            @RequestParam(name = "licId", required = false) Long yhytId,
            Query query
    ) {
        QueryWrapper<ReportIllegalLabelEntity> qw = new QueryWrapper<>();
        qw.eq("is_deleted",0);


        List<ReportIllegalLabelVO> list = reportIllegalLabelService.listByLabelId(yhytId);
        List<Long> ids = list.stream().map(ReportIllegalLabelEntity::getObjId).toList();
        if(ids.isEmpty()){
            return R.data(null);
        }
        QueryWrapper<ReportComplaintEntity> qw1 = new QueryWrapper<>();
        qw1.in("id",ids);
        IPage<ReportComplaintEntity> page = reportComplaintService.page(Condition.getPage(query), qw1);
        IPage<ReportComplaintVO> convert = page.convert(entity -> BeanUtil.copyProperties(entity, ReportComplaintVO.class));
        return R.data(convert);
    }

    @GetMapping("/detail")
    public R<ReportComplaintVO> detail(ReportComplaintEntity reportComplaint) {
        ReportComplaintEntity detail = reportComplaintService.getOne(Condition.getQueryWrapper(reportComplaint));
        if(ObjectUtil.isNull(detail)){
            return R.data(null);
        }


        ReportComplaintVO reportComplaintVO = ReportComplaintWrapper.build().entityVO(detail);
        if(detail.getDeptId() != null){
            Dept byId = deptService.getById(detail.getDeptId());
            reportComplaintVO.setDeptName(byId.getDeptName());
        }
        List<ReportIllegalLabelVO> reportIllegalLabelVOS = reportIllegalLabelService.selectListByObjId(detail.getId());
        reportComplaintVO.setReportIllegalLabelList(reportIllegalLabelVOS);
        return R.data(reportComplaintVO);
    }

    /**
     * 根据指定用户获取待办分页数据
     * @param userId
     * @param query
     * @return
     */

    @GetMapping("/page-user")
    public R<IPage<ReportComplaintVO>> selectPageByUser(
            @RequestParam(name = "userId", required = false) Long userId,
            Query query
    ) {
        IPage<ReportComplaintVO> page = dingReportComplaintService.selectPageByUserId(Condition.getPage(query), userId);
        return R.data(page);
    }

    /**
     * 待办
     * @param deptId
     * @param query
     * @return
     */
    @GetMapping("/page-dept-todo")
    public R<IPage<ReportComplaintVO>> selectPageByDept(
            @RequestParam(name = "deptId", required = false) Long deptId,
            @RequestParam(name = "keyword", required = false) String title,
            Query query
    ) {
        IPage<ReportComplaintVO> page = dingReportComplaintService.selectPageByDeptId(Condition.getPage(query), deptId, title);
        return R.data(page);
    }

    /**
     * 已办
     * @param deptId
     * @param query
     * @return
     */
    @GetMapping("/page-dept-completed")
    public R<IPage<ReportComplaintVO>> selectCompletedPageByDeptId(
            @RequestParam(name = "userId", required = false) Long userId,
            @RequestParam(name = "keyword", required = false) String title,

            Query query
    ) {
        IPage<ReportComplaintVO> page = dingReportComplaintService.selectCompletedPageByDeptId(Condition.getPage(query), userId, title);
        return R.data(page);
    }
}
