/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.ms.basic.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.ms.basic.pojo.entity.FakeCigarettesEntity;
import org.springblade.ms.basic.pojo.vo.FakeCigarettesVO;
import org.springblade.ms.basic.excel.FakeCigarettesExcel;
import org.springblade.ms.basic.wrapper.FakeCigarettesWrapper;
import org.springblade.ms.basic.service.IFakeCigarettesService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 查获假烟统计表 控制器
 *
 * <AUTHOR>
 * @since 2025-04-20
 */
@RestController
@AllArgsConstructor
@RequestMapping("ms-fakecigarettes/fakeCigarettes")
@Tag(name = "查获假烟统计表", description = "查获假烟统计表接口")
public class FakeCigarettesController extends BladeController {

	private final IFakeCigarettesService fakeCigarettesService;

	/**
	 * 查获假烟统计表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入fakeCigarettes")
	public R<FakeCigarettesVO> detail(FakeCigarettesEntity fakeCigarettes) {
		FakeCigarettesEntity detail = fakeCigarettesService.getOne(Condition.getQueryWrapper(fakeCigarettes));
		return R.data(FakeCigarettesWrapper.build().entityVO(detail));
	}
	/**
	 * 查获假烟统计表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入fakeCigarettes")
	public R<IPage<FakeCigarettesVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> fakeCigarettes, Query query) {
		IPage<FakeCigarettesEntity> pages = fakeCigarettesService.page(Condition.getPage(query), Condition.getQueryWrapper(fakeCigarettes, FakeCigarettesEntity.class));
		return R.data(FakeCigarettesWrapper.build().pageVO(pages));
	}

	/**
	 * 查获假烟统计表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入fakeCigarettes")
	public R<IPage<FakeCigarettesVO>> page(FakeCigarettesVO fakeCigarettes, Query query) {
		IPage<FakeCigarettesVO> pages = fakeCigarettesService.selectFakeCigarettesPage(Condition.getPage(query), fakeCigarettes);
		return R.data(pages);
	}

	/**
	 * 查获假烟统计表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入fakeCigarettes")
	public R save(@Valid @RequestBody FakeCigarettesEntity fakeCigarettes) {
		return R.status(fakeCigarettesService.save(fakeCigarettes));
	}

	/**
	 * 查获假烟统计表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入fakeCigarettes")
	public R update(@Valid @RequestBody FakeCigarettesEntity fakeCigarettes) {
		return R.status(fakeCigarettesService.updateById(fakeCigarettes));
	}

	/**
	 * 查获假烟统计表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入fakeCigarettes")
	public R submit(@Valid @RequestBody FakeCigarettesEntity fakeCigarettes) {
		return R.status(fakeCigarettesService.saveOrUpdate(fakeCigarettes));
	}

	/**
	 * 查获假烟统计表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(fakeCigarettesService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@GetMapping("/export-fakeCigarettes")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入fakeCigarettes")
	public void exportFakeCigarettes(@Parameter(hidden = true) @RequestParam Map<String, Object> fakeCigarettes, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<FakeCigarettesEntity> queryWrapper = Condition.getQueryWrapper(fakeCigarettes, FakeCigarettesEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(FakeCigarettes::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(FakeCigarettesEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<FakeCigarettesExcel> list = fakeCigarettesService.exportFakeCigarettes(queryWrapper);
		ExcelUtil.export(response, "查获假烟统计表数据" + DateUtil.time(), "查获假烟统计表数据表", list, FakeCigarettesExcel.class);
	}

}
