package org.springblade.ms.dingapp.controller;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiGettokenRequest;
import com.dingtalk.api.request.OapiOcrStructuredRecognizeRequest;
import com.dingtalk.api.response.OapiGettokenResponse;
import com.dingtalk.api.response.OapiOcrStructuredRecognizeResponse;
import org.springblade.ms.dingapp.vo.DingUserAccessTokenVO;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.launch.constant.AppConstant;
import org.springblade.core.oss.MinioTemplate;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.ms.basic.pojo.entity.RetailerStorePhotoEntity;
import org.springblade.ms.basic.service.IRetailerStorePhotoService;
import org.springblade.ms.basic.service.IUploadFileService;
import org.springblade.ms.dingapp.config.DingAppConfig;
import org.springblade.ms.dingapp.service.IDingAppUserService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-02-12 19:57
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("dingapp/file")
@Slf4j
public class DingFileController {

    private final IUploadFileService uploadFileService;
    private final IRetailerStorePhotoService retailerStorePhotoService;
    private final IDingAppUserService dingAppUserService;
    private final Environment environment;

    @Value("${oss.transform-endpoint}")
    private String transformEndpoint;

    /**
     * 文件上传结构
     * @param file
     * @param request
     * @return
     */
    @PostMapping("/upload")
    public String upload(
            @RequestParam(name = "objId") Long objId,
            @RequestParam(name = "objName") String objName,
            @RequestParam(name = "extName", required = false) String extName,
            @RequestParam(name = "file", required = false) MultipartFile file,
            HttpServletRequest request
    ) {
        JSONObject res = null;
        try {
            res = uploadFileService.dingUploadFile(objId, objName, file, request, extName);
        } catch (Exception e) {
            e.printStackTrace();
            res = JSONUtil.createObj()
                .set("code", 0)
                .set("msg", "上传失败");
        }
        return res.toString();
    }

    @GetMapping("/remove")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "删除图片", description  = "传入ids")
    public R<Boolean> remove(@RequestParam  String ids) {
        Boolean b = uploadFileService.deleteLogic(Func.toLongList(ids));
        return R.data(b);
    }

    /**
     * 调用钉钉OCR文字识别
     *
     * @param file 需要识别的图片文件
     * @param type 识别类型，可选值：ocr_business_license(营业执照)、ocr_idcard(身份证)、ocr_bank_card(银行卡)、ocr_business_card(名片)、ocr_passport(护照)、ocr_general(通用文字)
     * @return 识别结果
     */
    @PostMapping("/ocr")
    @Operation(summary = "OCR文字识别", description = "上传图片进行OCR文字识别")
    public JSONObject ocrRecognition(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "type", defaultValue = "blicense") String type,
            HttpServletRequest request) {
//        log.info("开始调用钉钉OCR文字识别，类型: {}", type);

        try {
            // 获取钉钉访问令牌
            String accessToken = getAccessToken();
            if (StrUtil.isBlank(accessToken)) {
                log.error("获取钉钉访问令牌失败");
                return JSONUtil.createObj()
                    .set("code", 500)
                    .set("msg", "获取钉钉访问令牌失败");
            }

            // 1. 上传图片到MinIO
            Long objId = System.currentTimeMillis(); // 使用时间戳作为对象ID
            String objName = "ocr"; // 对象名称为ocr
            String extName = type; // 使用OCR类型作为额外名称

            // 调用上传服务将图片上传到MinIO
            JSONObject uploadResult = uploadFileService.dingUploadFile(objId, objName, file, request, extName);
            if (uploadResult.getInt("code", 0) != 200) {
                log.error("上传图片到MinIO失败");
                return JSONUtil.createObj()
                    .set("code", 500)
                    .set("msg", "上传图片到MinIO失败");
            }

            // 获取上传后的图片URL
            String imageUrl = uploadResult.getByPath("data.path", String.class);
            if (StrUtil.isBlank(imageUrl)) {
                log.error("获取上传图片URL失败");
                return JSONUtil.createObj()
                    .set("code", 500)
                    .set("msg", "获取上传图片URL失败");
            }

            // 处理图片URL
            // 1. 如果是dev环境，去除/minio前缀
            String[] activeProfiles = environment.getActiveProfiles();
            boolean isDevEnv = false;
            for (String profile : activeProfiles) {
                if ("dev".equals(profile)) {
                    isDevEnv = true;
                    break;
                }
            }

            if (isDevEnv && imageUrl.startsWith("/minio")) {
                imageUrl = imageUrl.substring("/minio".length());
            }

            // 2. 拼接transform-endpoint
            if (StrUtil.isNotBlank(transformEndpoint)) {
                imageUrl = transformEndpoint + imageUrl;
            }

            log.info("图片已上传到MinIO，处理后的URL: {}", imageUrl);

            // 2. 调用钉钉OCR API
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/ocr/structured/recognize");
            OapiOcrStructuredRecognizeRequest req = new OapiOcrStructuredRecognizeRequest();

            req.setType(type);


            // 设置图片URL
            req.setImageUrl(imageUrl);

            // 执行请求
            OapiOcrStructuredRecognizeResponse rsp = client.execute(req, accessToken);

            // 解析响应
            String responseBody = rsp.getBody();
            JSONObject responseJson = JSONUtil.parseObj(responseBody);

            // 检查响应状态
            if (responseJson.getInt("errcode", -1) != 0) {
                log.error("钉钉OCR识别失败: {}", responseJson.getStr("errmsg"));
                return JSONUtil.createObj()
                    .set("code", 500)
                    .set("msg", "钉钉OCR识别失败: " + responseJson.getStr("errmsg"));
            }

            // 返回识别结果
            return JSONUtil.createObj()
                .set("code", 200)
                .set("msg", "识别成功")
                .set("data", responseJson)
                .set("imageUrl", imageUrl); // 返回图片URL，方便前端展示

        } catch (Exception e) {
            log.error("OCR识别过程中发生错误", e);
            return JSONUtil.createObj()
                .set("code", 500)
                .set("msg", "OCR识别过程中发生错误: " + e.getMessage());
        }
    }

    /**
     * 获取钉钉访问令牌
     *
     * @return 访问令牌
     */
    private String getAccessToken() {
        try {
            // 从钉钉用户服务获取访问令牌
            DingUserAccessTokenVO tokenResponse = dingAppUserService.getAccessToken();
            if (tokenResponse != null) {
                return tokenResponse.getAccessToken();
            }
            return null;
        } catch (Exception e) {
            log.error("获取钉钉访问令牌失败", e);
            return null;
        }
    }
}
