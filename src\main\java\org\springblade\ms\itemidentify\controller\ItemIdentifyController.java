/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.ms.itemidentify.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.ms.itemidentify.pojo.entity.ItemIdentifyEntity;
import org.springblade.ms.itemidentify.pojo.vo.ItemIdentifyVO;
import org.springblade.ms.itemidentify.excel.ItemIdentifyExcel;
import org.springblade.ms.itemidentify.wrapper.ItemIdentifyWrapper;
import org.springblade.ms.itemidentify.service.IItemIdentifyService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 品规识别记录 控制器
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@RestController
@AllArgsConstructor
@RequestMapping("ms-itemidentify/itemIdentify")
@Tag(name = "品规识别记录", description = "品规识别记录接口")
public class ItemIdentifyController extends BladeController {

	private final IItemIdentifyService itemIdentifyService;

	/**
	 * 品规识别记录 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入itemIdentify")
	public R<ItemIdentifyVO> detail(ItemIdentifyEntity itemIdentify) {
		ItemIdentifyEntity detail = itemIdentifyService.getOne(Condition.getQueryWrapper(itemIdentify));
		return R.data(ItemIdentifyWrapper.build().entityVO(detail));
	}
	/**
	 * 品规识别记录 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入itemIdentify")
	public R<IPage<ItemIdentifyVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> itemIdentify, Query query) {
		IPage<ItemIdentifyEntity> pages = itemIdentifyService.page(Condition.getPage(query),Condition.getQueryWrapper(itemIdentify, ItemIdentifyEntity.class) );
		return R.data(ItemIdentifyWrapper.build().pageVO(pages));
	}

	/**
	 * 品规识别记录 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入itemIdentify")
	public R<IPage<ItemIdentifyVO>> page(ItemIdentifyVO itemIdentify, Query query) {
		IPage<ItemIdentifyVO> pages = itemIdentifyService.selectItemIdentifyPage(Condition.getPage(query), itemIdentify);
		return R.data(pages);
	}

	/**
	 * 品规识别记录 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入itemIdentify")
	public R save(@Valid @RequestBody ItemIdentifyEntity itemIdentify) {
		return R.status(itemIdentifyService.save(itemIdentify));
	}

	/**
	 * 品规识别记录 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入itemIdentify")
	public R update(@Valid @RequestBody ItemIdentifyEntity itemIdentify) {
		return R.status(itemIdentifyService.updateById(itemIdentify));
	}

	/**
	 * 品规识别记录 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入itemIdentify")
	public R submit(@Valid @RequestBody ItemIdentifyEntity itemIdentify) {
		return R.status(itemIdentifyService.saveOrUpdate(itemIdentify));
	}

	/**
	 * 品规识别记录 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(itemIdentifyService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@GetMapping("/export-itemIdentify")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入itemIdentify")
	public void exportItemIdentify(@Parameter(hidden = true) @RequestParam Map<String, Object> itemIdentify, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<ItemIdentifyEntity> queryWrapper = Condition.getQueryWrapper(itemIdentify, ItemIdentifyEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(ItemIdentify::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(ItemIdentifyEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<ItemIdentifyExcel> list = itemIdentifyService.exportItemIdentify(queryWrapper);
		ExcelUtil.export(response, "品规识别记录数据" + DateUtil.time(), "品规识别记录数据表", list, ItemIdentifyExcel.class);
	}

	}
