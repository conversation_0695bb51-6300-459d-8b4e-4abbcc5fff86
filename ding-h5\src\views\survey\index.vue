<template>
  <div class="survey-container">
    <div class="lsh-info-card">
      <h3 class="title blue">{{ companyName }}</h3>
      <div class="info-row">
        <span class="label">许可证号：</span>
        <span class="value">{{ licNo }}</span>
      </div>
      <div class="info-row">
        <span class="label">详细地址：</span>
        <span class="value">{{ address }}</span>
      </div>
      <img class="images" :src="filePath" @click="previewPic" />
    </div>

    <div class="photo-card">
      <div class="photo-header">
        <van-checkbox v-model="needPhoto" class="blue">
          <span class="blue">是否需要更新门面照片</span>
        </van-checkbox>
      </div>
      <p class="photo-tip">{{ isSchoolType ? '请拍摄1张正面照片' : '请拍摄3张门面照片，按照左、中、右顺序上传' }}</p>
      <div class="photo-container" :class="{ 'disabled': !needPhoto, 'school-mode': isSchoolType }">
        <div class="photo-item" v-if="!isSchoolType">
          <h4>左侧照片</h4>
          <van-uploader
            v-model="fileListOne"
            :max-count="1"
            @delete="handleRemoveOne"
            :after-read="afterRead"
            :disabled="!needPhoto"
            :upload-text="!needPhoto?'仅允许查看':''"
          />
        </div>
        <div class="photo-item">
          <h4>正面照片</h4>
          <van-uploader
            v-model="fileListTwo"
            :max-count="1"
            @delete="handleRemoveTwo"
            :after-read="afterRead"
            :disabled="!needPhoto"
            :upload-text="!needPhoto?'仅允许查看':''"
          />
        </div>
        <div class="photo-item" v-if="!isSchoolType">
          <h4>右侧照片</h4>
          <van-uploader
            v-model="fileListThree"
            :max-count="1"
            @delete="handleRemoveThree"
            :after-read="afterRead"
            :disabled="!needPhoto"
            :upload-text="!needPhoto?'仅允许查看':''"
          />
        </div>
      </div>
    </div>

    <!-- 添加小地图组件 -->
    <div class="distance-map-card" v-if="showDistanceMap">
      <div class="map-header">
        <h3>新旧地址距离</h3>
        <div class="distance-info" v-if="distance">
          <span class="distance-value">{{ distance }}米</span>
        </div>
      </div>
      <div id="mini-map" class="mini-map-container"></div>
    </div>

    <div class="survey-btn" v-if="isUnlicensed">
      <van-button
        type="primary"
        block
        @click="handleSubmitClick"
      >确定</van-button>
    </div>
    <div class="survey-btn" v-else >
      <van-button
        type="primary"
        block
        @click="handleNextClick"
      >下一步</van-button>
    </div>

  </div>
</template>

<script setup>
import { ref, onMounted, watch, onUnmounted, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { http } from '@/utils/http'
import { showSuccessToast, showFailToast,showToast, showImagePreview } from 'vant'
import * as dd from 'dingtalk-jsapi'
import { useRouter } from 'vue-router'
import { useLicenseStore } from '@/stores/license'
import { gcj02ToWgs84, wgs84ToGcj02 } from '@/utils/coordinate'

const route = useRoute()

const licNo = ref('')
const licId = ref('')
const companyName = ref('')
const filePath = ref('')
const address = ref('')
const needPhoto = ref(false)
const isUnlicensed = ref(false)

// 地图相关变量
const showDistanceMap = ref(false) // 默认显示地图
const miniMap = ref(null)
const oldLocationMarker = ref(null)
const newLocationMarker = ref(null)
const oldLocation = ref({ lat: null, lng: null })
const newLocation = ref({ lat: null, lng: null })
const distance = ref(null)

// 监听 needPhoto 变化，当取消勾选时清空图片
watch(needPhoto, (newVal) => {
  if (!newVal) {
    // 如果取消勾选，清空所有图片
    fileListOne.value = []
    fileListTwo.value = []
    fileListThree.value = []
  }
})


const toastShow = ref(false)
const explorationId = ref('')
const dingMapLicenseVoBasic = ref({})
const explorationVO = ref({})
const isSchoolType = ref(false)

const fileListOne = ref([])
const fileListTwo = ref([])
const fileListThree = ref([])



// 加载天地图SDK
const loadTianDiTuSDK = () => {
  return new Promise((resolve, reject) => {
    if (window.T) {
      console.log('天地图SDK已加载');
      resolve();
      return;
    }

    console.log('开始加载天地图SDK');
    const script = document.createElement('script');
    script.src = 'https://api.tianditu.gov.cn/api?v=4.0&tk=e7d5714afd6f840366a1e9b4e7675236';

    script.onload = () => {
      console.log('天地图SDK加载成功');
      if (window.T) {
        resolve();
      } else {
        console.error('天地图SDK加载成功但全局对象T不存在');
        reject(new Error('天地图SDK加载成功但全局对象T不存在'));
      }
    };

    script.onerror = (err) => {
      console.error('天地图SDK加载失败:', err);
      reject(err);
    };

    document.head.appendChild(script);
  });
};

// 初始化小地图
const initMiniMap = async () => {
  try {
    console.log('开始初始化小地图');

    // 确保天地图SDK已加载
    if (!window.T) {
      console.log('加载天地图SDK');
      await loadTianDiTuSDK();
    }

    // 确保DOM元素存在
    const mapContainer = document.getElementById('mini-map');
    if (!mapContainer) {
      console.error('找不到地图容器元素，ID: mini-map');
      console.log('当前DOM结构:', document.body.innerHTML);

      // 尝试再次检查DOM是否已渲染
      showToast('正在加载地图...');
      return;
    }

    console.log('地图容器尺寸:', mapContainer.clientWidth, 'x', mapContainer.clientHeight);

    // 初始化地图
    try {
      miniMap.value = new T.Map('mini-map', {
        zoom: 16,
        center: new T.LngLat(110.387653, 21.260024), // 默认中心点（湛江市）
        projection: 'EPSG:4326',
        dragging: true,  // 允许拖动
        doubleClickZoom: false,  // 禁止双击缩放
        touchZoom: false,  // 禁止触摸缩放
        pinchZoom: false   // 禁止捏合缩放
      });

      // 禁用缩放相关控件，但允许拖动
      miniMap.value.disableDoubleClickZoom();
      miniMap.value.disableScrollWheelZoom();

      console.log('地图初始化成功，已禁用缩放但允许拖动');
    } catch (mapError) {
      console.error('地图初始化失败:', mapError);
      return;
    }

    // 获取新旧位置坐标
    console.log('获取位置坐标');
    await getLocations();

    // 添加标记和计算距离
    console.log('添加标记');
    addMarkers();

    console.log('计算距离');
    calculateDistance();

    // 调整地图视野以包含所有标记
    console.log('调整地图视野');
    adjustMapView();

    console.log('小地图初始化完成');
  } catch (error) {
    console.error('初始化小地图时出错:', error);
    showToast('地图加载失败，请稍后再试');
  }
};

// 获取新旧位置坐标
const getLocations = async () => {
  try {
    console.log('开始获取位置坐标');

    // 获取当前位置（新位置）
    try {
      const res = await dd.device.geolocation.get({
        targetAccuracy: 200,
        coordinate: 1,
        withReGeocode: true
      });

      console.log('获取到当前位置:', res);

      // 转换坐标系（高德坐标系GCJ-02 -> WGS84）
      const [wgsLng, wgsLat] = gcj02ToWgs84(res.longitude, res.latitude);
      newLocation.value = { lng: wgsLng, lat: wgsLat };
      console.log('转换后的当前位置(WGS84):', newLocation.value);
    } catch (locError) {
      console.error('获取当前位置失败:', locError);
      // 使用默认位置（湛江市）
      newLocation.value = { lng: 110.387653, lat: 21.260024 };
    }

    // 获取旧位置坐标
    try {
      // 尝试从API获取旧位置坐标
      const licenseRes = await http.get(`/api/dingapp/exploration/getLicenseDetail?licNo=${licNo.value}`);
      console.log('许可证详情:', licenseRes.data);

      if (licenseRes.data && licenseRes.data.longitude && licenseRes.data.latitude) {
        console.log('使用实际的旧位置坐标:', licenseRes.data.longitude, licenseRes.data.latitude);
        // 转换坐标系（高德坐标系GCJ-02 -> WGS84）
        const [oldWgsLng, oldWgsLat] = gcj02ToWgs84(
          licenseRes.data.longitude,
          licenseRes.data.latitude
        );
        oldLocation.value = { lng: oldWgsLng, lat: oldWgsLat };
        console.log('转换后的旧位置(WGS84):', oldLocation.value);
      } else {
        console.log('API中没有位置信息，使用模拟的旧位置坐标');
        // 使用当前位置偏移500米作为旧位置
        oldLocation.value = {
          lng: newLocation.value.lng + 0.005,
          lat: newLocation.value.lat + 0.005
        };
      }
    } catch (error) {
      console.error('获取许可证详情失败:', error);
      console.log('使用模拟的旧位置坐标');
      // 使用当前位置偏移500米作为旧位置
      oldLocation.value = {
        lng: newLocation.value.lng + 0.005,
        lat: newLocation.value.lat + 0.005
      };
    }

    // 显示地图
    showDistanceMap.value = true;
    console.log('位置坐标获取完成');
  } catch (error) {
    console.error('获取位置坐标时出错:', error);
    // 使用默认位置，确保地图能显示
    newLocation.value = { lng: 110.387653, lat: 21.260024 };
    oldLocation.value = { lng: 110.392653, lat: 21.265024 }; // 偏移约500米
    showDistanceMap.value = true;
  }
};

// 添加标记
const addMarkers = () => {
  if (!miniMap.value) return;

  // 添加旧位置标记
  if (oldLocation.value.lng && oldLocation.value.lat) {
    const oldPosition = new T.LngLat(oldLocation.value.lng, oldLocation.value.lat);
    oldLocationMarker.value = new T.Marker(oldPosition, {
      icon: new T.Icon({
        iconUrl: '/green.png', // 使用绿色图标表示旧位置
        iconSize: new T.Point(32, 32),
        iconAnchor: new T.Point(16, 16)
      })
    });
    miniMap.value.addOverLay(oldLocationMarker.value);

    // 添加旧位置标签
    const oldLabel = new T.Label({
      text: '原位置',
      position: oldPosition,
      offset: new T.Point(0, -40)
    });
    miniMap.value.addOverLay(oldLabel);
  }

  // 添加新位置标记
  if (newLocation.value.lng && newLocation.value.lat) {
    const newPosition = new T.LngLat(newLocation.value.lng, newLocation.value.lat);
    newLocationMarker.value = new T.Marker(newPosition, {
      icon: new T.Icon({
        iconUrl: '/location-marker.svg', // 使用蓝色图标表示新位置
        iconSize: new T.Point(32, 32),
        iconAnchor: new T.Point(16, 16)
      })
    });
    miniMap.value.addOverLay(newLocationMarker.value);

    // 添加新位置标签
    const newLabel = new T.Label({
      text: '当前位置',
      position: newPosition,
      offset: new T.Point(0, -40)
    });
    miniMap.value.addOverLay(newLabel);
  }

  // 添加连线
  if (oldLocation.value.lng && oldLocation.value.lat && newLocation.value.lng && newLocation.value.lat) {
    const line = new T.Polyline([
      new T.LngLat(oldLocation.value.lng, oldLocation.value.lat),
      new T.LngLat(newLocation.value.lng, newLocation.value.lat)
    ], {
      color: '#1989fa',
      weight: 3,
      opacity: 0.8,
      lineStyle: 'solid'
    });
    miniMap.value.addOverLay(line);
  }
};

// 调整地图视野
const adjustMapView = () => {
  if (!miniMap.value || !oldLocation.value.lng || !newLocation.value.lng) return;

  // 创建边界对象
  const bounds = new T.LngLatBounds(
    new T.LngLat(
      Math.min(oldLocation.value.lng, newLocation.value.lng),
      Math.min(oldLocation.value.lat, newLocation.value.lat)
    ),
    new T.LngLat(
      Math.max(oldLocation.value.lng, newLocation.value.lng),
      Math.max(oldLocation.value.lat, newLocation.value.lat)
    )
  );

  // 扩大边界，留出更多边距以确保两点都在视野内
  const padding = 0.003; // 增加边距
  bounds.extend(new T.LngLat(bounds.getEast() + padding, bounds.getNorth() + padding));
  bounds.extend(new T.LngLat(bounds.getWest() - padding, bounds.getSouth() - padding));

  // 设置地图视野
  miniMap.value.setViewport(bounds);

  // 固定缩放级别，确保地图不会过度缩放
  setTimeout(() => {
    const currentZoom = miniMap.value.getZoom();
    // 如果缩放级别太小（地图显示范围太大），则设置为固定值
    if (currentZoom < 14) {
      miniMap.value.setZoom(14);
    }
    // 如果缩放级别太大（地图显示范围太小），则设置为固定值
    if (currentZoom > 17) {
      miniMap.value.setZoom(17);
    }

    console.log('地图视野已调整，缩放级别:', miniMap.value.getZoom());
  }, 100);
};

// 计算两点之间的距离（米）
const calculateDistance = () => {
  if (!oldLocation.value.lat || !newLocation.value.lat) return;

  const R = 6371000; // 地球半径，单位米
  const dLat = (newLocation.value.lat - oldLocation.value.lat) * Math.PI / 180;
  const dLon = (newLocation.value.lng - oldLocation.value.lng) * Math.PI / 180;
  const a =
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(oldLocation.value.lat * Math.PI / 180) * Math.cos(newLocation.value.lat * Math.PI / 180) *
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  distance.value = Math.round(R * c);
};

// 组件卸载时清理资源
onUnmounted(() => {
  if (miniMap.value) {
    try {
      // 移除标记
      if (oldLocationMarker.value) {
        miniMap.value.removeOverLay(oldLocationMarker.value);
      }
      if (newLocationMarker.value) {
        miniMap.value.removeOverLay(newLocationMarker.value);
      }
    } catch (error) {
      console.error('清理地图资源时出错:', error);
    }
  }
});

onMounted(async () => {
  if (route.query.xkzh) {
    licNo.value = route.query.xkzh
    await loadLicenseInfo()

    // 使用nextTick确保DOM已经渲染，然后初始化小地图
    // await nextTick();
    // // 增加延迟时间，确保DOM完全渲染
    // setTimeout(() => {
    //   initMiniMap();
    // }, 1000);
  }
})


const loadLicenseInfo = async () => {
  try {
    const res = await http.get(`/api/dingapp/exploration/getTodayExploration?licNo=${licNo.value}`)
    if (Object.keys(res.data).length !== 0) {
      const { dingMapLicenseVO, explorationVO: explorationData } = res.data
      licNo.value = dingMapLicenseVO.licNo
      if(dingMapLicenseVO.licNo == ''){
        isUnlicensed.value = true;
      }
      companyName.value = dingMapLicenseVO.companyName
      address.value = dingMapLicenseVO.businessAddr
      filePath.value = dingMapLicenseVO.lastCenterPoho && dingMapLicenseVO.lastCenterPoho.length > 0 ? dingMapLicenseVO.lastCenterPoho[0].filthPath : ''
      explorationId.value = explorationData.id
      licId.value = dingMapLicenseVO.id
      dingMapLicenseVoBasic.value = dingMapLicenseVO
      explorationVO.value = explorationData
      // 判断是否为学校类型
      isSchoolType.value = explorationData.objectType === '学校'
      // 加载已有照片
      const resFileList = dingMapLicenseVO.photoPathList || []
      resFileList.forEach(file => {
        const fileItem = {
          uid: file.id,
          status: 'done',
          url: file.filthPath,
          fileName: file.fileName
        }

        if (fileItem.fileName.includes('left')) {
          fileListOne.value = [fileItem]
        } else if (fileItem.fileName.includes('center')) {
          fileListTwo.value = [fileItem]
        } else if (fileItem.fileName.includes('right')) {
          fileListThree.value = [fileItem]
        }
      })
    }
  } catch (error) {
    showFailToast('获取数据失败')
    console.error(error)
  }
}

const afterRead = async (file) => {
  console.log(file,'after')
  try {
    file.status = 'uploading'
    file.message = '上传中...'

    // 上传文件
    const formData = new FormData()
    formData.append('file', file.file)
    formData.append('objId', explorationId.value)
    formData.append('objName', 'exploration')

    // 根据上传组件判断添加不同的extName
    let extName = ''
    if (fileListOne.value.includes(file)) {
      extName = '_left'
    } else if (fileListTwo.value.includes(file)) {
      extName = '_center'
    } else if (fileListThree.value.includes(file)) {
      extName = '_right'
    }
    formData.append('extName', extName)

    const res = await http.post('/api/dingapp/file/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })

    if (res.data) {
      file.status = 'done'
      file.url = res.data.path
      file.uid = res.data.id
      // 上传位置信息
      await uploadLocation()
    } else {
      file.status = 'failed'
      file.message = '上传失败'
      showFailToast('上传失败')
    }
  } catch (error) {
    file.status = 'failed'
    file.message = '上传失败'
    showFailToast('上传失败')
    console.error(error)
  }
}

const handleRemoveOne = async (file) => {
  try {
    const res = await http.get('/api/dingapp/file/remove', {
      params: { ids: file.uid }
    })
    if (res.data) {
      fileListOne.value = []
      showSuccessToast('删除成功')
    } else {
      showFailToast('删除失败')
    }
  } catch (error) {
    showFailToast('删除失败')
    console.error(error)
  }
}

const handleRemoveTwo = async (file) => {
  try {
    const res = await http.get('/api/dingapp/file/remove', {
      params: { ids: file.uid }
    })
    if (res.data) {
      fileListTwo.value = []
      showSuccessToast('删除成功')
    } else {
      showFailToast('删除失败')
    }
  } catch (error) {
    showFailToast('删除失败')
    console.error(error)
  }
}

const handleRemoveThree = async (file) => {
  try {
    const res = await http.get('/api/dingapp/file/remove', {
      params: { ids: file.uid }
    })
    if (res.data) {
      fileListThree.value = []
      showSuccessToast('删除成功')
    } else {
      showFailToast('删除失败')
    }
  } catch (error) {
    showFailToast('删除失败')
    console.error(error)
  }
}

const uploadLocation = async () => {
  try {
    const res = await dd.device.geolocation.get({
        targetAccuracy: 200,
        coordinate: 1,
        withReGeocode: true
    })
    await http.post('/api/dingapp/exploration/submitExplorationCoordinate', {
      explorationId: explorationId.value,
      licenseId: licNo.value,
      longitude: res.longitude,
      latitude: res.latitude,
    })
  } catch (error) {
    console.error('上传位置信息失败:', error)
    showFailToast('上传位置信息失败')
  }
}



const router = useRouter()
const licenseStore = useLicenseStore()

const handleNextClick = async () => {
    if(needPhoto.value){
      // 根据对象类型验证照片数量
      if(isSchoolType.value){
        // 学校类型只需要正面照片
        if(fileListTwo.value.length !== 1){
          showToast("请先拍摄正面照片");
          return;
        }
      } else {
        // 其他类型需要三张照片
        if(fileListOne.value.length + fileListTwo.value.length + fileListThree.value.length !== 3){
          showToast("请先拍摄三张门面照片");
          return;
        }
      }

      // 提交中间照片
      if (fileListTwo.value.length > 0 && needPhoto.value) {
        const res = await http.post('/api/dingapp/exploration/submitCenterPhoto', {
          explorationId: explorationId.value,
          licenseId: licId.value,
          fileId: fileListTwo.value[0].uid
        })
      }
    }


    // 将当前许可证信息存入store
    licenseStore.setCurrentLicense(dingMapLicenseVoBasic.value)
    // 设置explorationId到store中
    licenseStore.setExplorationId(explorationId.value)
    // 跳转到lshhx页面，设置view=false表示非查看模式
    router.push(`/lshhx?view=false`)
}


const handleSubmitClick = async () => {
    if(needPhoto.value){
      // 根据对象类型验证照片数量
      if(isSchoolType.value){
        // 学校类型只需要正面照片
        if(fileListTwo.value.length !== 1){
          showToast("请先拍摄正面照片");
          return;
        }
      } else {
        // 其他类型需要三张照片
        if(fileListOne.value.length + fileListTwo.value.length + fileListThree.value.length !== 3){
          showToast("请先拍摄三张门面照片");
          return;
        }
      }

      // 提交中间照片
      if (fileListTwo.value.length > 0 && needPhoto.value) {
        const res = await http.post('/api/dingapp/exploration/submitCenterPhoto', {
          explorationId: explorationId.value,
          licenseId: licId.value,
          fileId: fileListTwo.value[0].uid
        })
      }
    }
    router.back();
}


const previewPic = () => {
    showImagePreview({
        images: [filePath.value],
        closeable: true
    })
}

</script>

<style lang="scss" scoped>
.survey-container {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .lsh-info-card {
    background: #fff;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;

    .title {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 12px;
    }

    .info-row {
      display: flex;
      margin-bottom: 8px;
      font-size: 14px;

      .label {
        color: #666;
        min-width: 80px;
      }

      .value {
        color: #333;
        flex: 1;
      }
    }
  }

  .photo-card {
    background: #fff;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;

    .photo-header {
      margin-bottom: 12px;
    }

    .photo-tip {
      color: #666;
      font-size: 14px;
      margin-bottom: 16px;
    }

    .photo-container {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 12px;
      transition: opacity 0.3s;

      &.school-mode {
        grid-template-columns: 1fr;
        justify-items: center;
        max-width: 200px;
        margin: 0 auto;
      }

      .photo-item {
        h4 {
          font-size: 14px;
          margin-bottom: 8px;
          color: #333;
        }
      }
    }
  }

  .survey-btn {
    padding: 16px;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
  }

  .blue {
    color: #1989fa;
  }
}
.images {
    width: 70%;
    height: 140px;
    border-radius: 5px;
    display: block;
    margin: 0 auto 15px;
    object-fit: contain;
}

/* 小地图样式 */
.distance-map-card {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  overflow: hidden;
}

.map-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.map-header h3 {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.distance-info {
  display: flex;
  align-items: center;
}

.distance-value {
  color: #333;
  font-weight: bold;
  font-size: 16px;
}

.mini-map-container {
  width: 100%;
  height: 180px; /* 固定高度 */
  border-radius: 4px;
  overflow: hidden;
  user-select: none; /* 禁止选择文本 */
}
</style>