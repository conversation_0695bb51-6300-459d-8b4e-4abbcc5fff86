<view class="jbtsMain">
  <scroll-view scroll-y="{{true}}" style="flex: 1;" scroll-top='{{gotoScrollTop}}' onScrollToLower="scrollToLowerSearch" lower-threshold='150'>
    <ant-empty a:if="{{ dataList.length==0 }}"
      title="数据为空"
    />
  <view a:for="{{ dataList }}">
    <view class="title">
      <text class="title-two">{{formatTimestamp(item.create_time)}}</text>
    </view>
    <view class="info_card" >
      <view class="flex ">
        <view class="text">
          所属类型：
        </view>
        <view class="text">
          {{item.event_type}}
        </view>
      </view >
      <view class="flex"  style="margin-top:10px;">
        <view class="text ">
          投诉内容：
        </view>
        <view class="text text-time">
          {{item.event_content}}
        </view>
      </view >
    </view>
  </view>
</scroll-view>
</view>
<ant-popup
  visible="{{scrollVisible}}"
  position="bottom"
  title="{{popupData.regTime.split(' ')[0]}} 案由"
  showClose="{{true}}"
  onClose="handlePopupClose"
>
  <scroll-view
    scroll-y
    style="padding: 12px 14px 20px; height: 300px"
    disable-lower-scroll="out-of-bounds"
    disable-upper-scroll="out-of-bounds"
    >
     {{popupData.caseOfAction}}
  </scroll-view>
</ant-popup>