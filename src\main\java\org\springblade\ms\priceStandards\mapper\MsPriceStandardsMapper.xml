<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.ms.priceStandards.mapper.MsPriceStandardsMapper">

    <resultMap id="BaseResultMap" type="org.springblade.ms.priceStandards.pojo.entity.MsPriceStandards">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="stdType" column="std_type" jdbcType="VARCHAR"/>
            <result property="productName" column="product_name" jdbcType="VARCHAR"/>
            <result property="productCategory" column="product_category" jdbcType="VARCHAR"/>
            <result property="barcode" column="barcode" jdbcType="VARCHAR"/>
            <result property="price" column="price" jdbcType="NUMERIC"/>
            <result property="priceUnit" column="price_unit" jdbcType="VARCHAR"/>
            <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
            <result property="priceYear" column="price_year" jdbcType="INTEGER"/>
            <result property="createUser" column="create_user" jdbcType="BIGINT"/>
            <result property="createDept" column="create_dept" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateUser" column="update_user" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,std_type,product_name,
        product_category,barcode,price,
        price_unit,remarks,price_year,
        create_user,create_dept,create_time,
        update_user,update_time,status,
        is_deleted,tenant_id
    </sql>
</mapper>
