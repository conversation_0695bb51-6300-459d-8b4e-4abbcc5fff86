/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.ms.reportcomplaint.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.cache.UserCache;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.modules.system.pojo.entity.User;
import org.springblade.modules.system.service.IUserService;
import org.springblade.ms.dingapp.dto.DingNotificationDTO;
import org.springblade.ms.dingapp.entity.MsDingUserEntity;
import org.springblade.ms.dingapp.service.IDingNotificationService;
import org.springblade.ms.dingapp.service.IMsDingUserService;
import org.springblade.ms.platform12345.config.Platform12345Properties;
import org.springblade.ms.platform12345.dto.AcceptOrderRequest;
import org.springblade.ms.platform12345.service.Platform12345Service;
import org.springblade.ms.platform12345.utils.Platform12345Client;
import org.springblade.ms.reportcomplaint.pojo.entity.ReportComplaintEntity;
import org.springblade.ms.reportcomplaint.pojo.vo.ReportComplaintVO;
import org.springblade.ms.reportcomplaint.excel.ReportComplaintExcel;
import org.springblade.ms.reportcomplaint.mapper.ReportComplaintMapper;
import org.springblade.ms.reportcomplaint.pojo.vo.ReportIllegalLabelVO;
import org.springblade.ms.reportcomplaint.service.IReportComplaintService;
import org.springblade.ms.reportcomplaint.service.IReportIllegalLabelService;
import org.springblade.ms.reportcomplaint.wrapper.ReportComplaintWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.common.cache.SysCache;
import org.springframework.context.annotation.Lazy;
import org.springframework.transaction.annotation.Transactional;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 工单信息 服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Service
@Slf4j
public class ReportComplaintServiceImpl extends BaseServiceImpl<ReportComplaintMapper, ReportComplaintEntity> implements IReportComplaintService {

	@Lazy
	private final IReportIllegalLabelService reportIllegalLabelService;
	private final IUserService userService;
	private final IMsDingUserService dingUserService;
	private final IDingNotificationService dingNotificationService;
	private final Platform12345Service platform12345Service;
	private final Platform12345Client platform12345Client;
	private final Platform12345Properties properties;

	public ReportComplaintServiceImpl(@Lazy IReportIllegalLabelService reportIllegalLabelService,
									  IUserService userService,
									  IMsDingUserService dingUserService,
									  IDingNotificationService dingNotificationService,
									  Platform12345Service platform12345Service,
									  Platform12345Client platform12345Client,
									  Platform12345Properties properties) {
		this.reportIllegalLabelService = reportIllegalLabelService;
		this.userService = userService;
		this.dingUserService = dingUserService;
		this.dingNotificationService = dingNotificationService;
		this.platform12345Service = platform12345Service;
		this.platform12345Client = platform12345Client;
		this.properties = properties;
	}

	@Override
	public IPage<ReportComplaintVO> selectReportComplaintPage(IPage<ReportComplaintVO> page, ReportComplaintVO reportComplaint) {
		List<ReportComplaintVO> reportComplaintVOS = selectPage(page, reportComplaint);

		reportComplaintVOS.forEach(reportComplaintVO -> {
			if(ObjectUtil.isNotNull(reportComplaintVO.getUserId())){
				User updateUser = UserCache.getUser(reportComplaintVO.getUserId());
				if(ObjectUtil.isNotNull(updateUser)){
					reportComplaintVO.setUserName(updateUser.getName());
				}
			}
		});
		return page.setRecords(reportComplaintVOS);
	}

	@Override
	public List<ReportComplaintVO> selectPage(IPage<ReportComplaintVO> page, ReportComplaintVO reportComplaint) {
		List<ReportComplaintVO> reportComplaintVOS = baseMapper.selectReportComplaintPage(page, reportComplaint);
		// 获取标签
		for (ReportComplaintVO reportComplaintVO : reportComplaintVOS) {
			List<ReportIllegalLabelVO> reportIllegalLabelVOS = reportIllegalLabelService.selectListByObjId(reportComplaintVO.getId());
			reportComplaintVO.setReportIllegalLabelList(reportIllegalLabelVOS);
		}

		return reportComplaintVOS;
	}

	@Override
	public List<ReportComplaintExcel> exportReportComplaint(Wrapper<ReportComplaintEntity> queryWrapper) {
		List<ReportComplaintExcel> reportComplaintList = baseMapper.exportReportComplaint(queryWrapper);
		return reportComplaintList;
	}

	@Override
	public ReportComplaintEntity getReportComplaintByRollNumber(String rollNumber) {
		if (StrUtil.isBlank(rollNumber)) {
			return null;
		}

		return baseMapper.getReportComplaintByRollNumber(rollNumber);
	}

	@Override
	@Transactional
	public Boolean updateAndSendNotify(ReportComplaintEntity reportComplaint) {
		if(ObjectUtil.isNull(reportComplaint.getDeptId())){
			return false;
		}
		ReportComplaintEntity reportComplaint1 = baseMapper.selectById(reportComplaint.getId());
		if(StrUtil.isNotBlank(reportComplaint1.getComplaintStatus()) && reportComplaint1.getComplaintStatus().equals("待审派")){//待审派才进行受理
			if(StrUtil.isNotBlank(reportComplaint1.getDataSource()) && reportComplaint1.getDataSource().equals("12345")){ //12345受理工单
				AcceptOrderRequest acceptOrderRequest = new AcceptOrderRequest();

				// 生成签名信息
				Platform12345Client.SignatureInfo signatureInfo = platform12345Client.generateSignature();
				acceptOrderRequest.setSignature(signatureInfo.getSignature());
				acceptOrderRequest.setTimestamp(signatureInfo.getTimestamp());
				acceptOrderRequest.setAppid(signatureInfo.getAppid());
				//受理工单
				acceptOrderRequest.setProWoId(reportComplaint.getRollNumber());
				acceptOrderRequest.setProWoCode(String.valueOf(reportComplaint.getId()));

				acceptOrderRequest.setAssignOrgName("市烟草专卖局");
				acceptOrderRequest.setAppContent("下发核实");
				LocalDateTime now = LocalDateTime.now();
				// 定义日期时间格式
				DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
				// 格式化当前时间
				String formattedDate = now.format(formatter);
				acceptOrderRequest.setAppTime(formattedDate);
				acceptOrderRequest.setDealUserNo(properties.getDealUserNo());

				//调用受理
				String s = platform12345Service.acceptOrder(acceptOrderRequest);
				JSONObject entries = JSONUtil.parseObj(s);
				if(entries.getInt("resultCode") != 0){
					log.error("受理接口返回错误:{}",s);
					return false;
				}else{
					log.info("受理工单处理完成");
				}
			}
			String expireTimeStr = reportComplaint1.getHandleRelExpireTime();
			Date date = DateUtil.parse(expireTimeStr, "yyyy-MM-dd");
			Date newDate = DateUtil.offsetDay(date, 15);
			String newDateString = DateUtil.format(newDate, "yyyy-MM-dd");

			reportComplaint.setHandleRelExpireTime(newDateString);
			reportComplaint.setComplaintStatus("待处理");
		}

		baseMapper.updateById(reportComplaint);

		List<User> users =	userService.list(Wrappers.<User>query().lambda()
				.like(User::getDeptId, reportComplaint.getDeptId())
				.eq(User::getStatus, 1)
				.eq(User::getIsDeleted, BladeConstant.DB_NOT_DELETED)
				.orderByAsc(User::getId));

		List<String> userNames = users.stream()
				.map(User::getRealName)
				.toList();

		QueryWrapper<MsDingUserEntity> qw = new QueryWrapper<>();
		qw.in("name",userNames);
		List<MsDingUserEntity> list = dingUserService.list(qw);
		List<String> dingUserIds = list.stream()
				.map(MsDingUserEntity::getUserid)
				.toList();
		//发送通知
		dingNotificationService.sendLinkNotification(
				dingUserIds,
				"投诉举报处理提醒",
				"您有一条新的投诉举报处理信息，请尽快处理,"+reportComplaint1.getEventTitle(),
				"/todo",
				"@2312312"
		);
		return true;
	}

}
