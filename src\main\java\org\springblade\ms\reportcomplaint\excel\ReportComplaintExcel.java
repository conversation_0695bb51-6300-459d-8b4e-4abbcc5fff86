/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.ms.reportcomplaint.excel;


import lombok.Data;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;
import java.io.Serial;


/**
 * 工单信息 Excel实体类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class ReportComplaintExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("主键")
	private Long id;
	/**
	 * 受理号
	 */
	@ColumnWidth(20)
	@ExcelProperty("受理号")
	private String rollNumber;
	/**
	 * 受理方式
	 */
	@ColumnWidth(20)
	@ExcelProperty("受理方式")
	private String rollWay;
	/**
	 * 业务类别
	 */
	@ColumnWidth(20)
	@ExcelProperty("业务类别")
	private String rollType;
	/**
	 * 问题分类
	 */
	@ColumnWidth(20)
	@ExcelProperty("问题分类")
	private String eventType;
	/**
	 * 重要程度
	 */
	@ColumnWidth(20)
	@ExcelProperty("重要程度")
	private String rollDegree;
	/**
	 * 问题区域
	 */
	@ColumnWidth(20)
	@ExcelProperty("问题区域")
	private String eventRegion;
	/**
	 * 受理时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("受理时间")
	private Date rollTime;
	/**
	 * 受理日期
	 */
	@ColumnWidth(20)
	@ExcelProperty("受理日期")
	private String rollDate;
	/**
	 * 问题地址
	 */
	@ColumnWidth(20)
	@ExcelProperty("问题地址")
	private String eventAddr;
	/**
	 * 问题描述
	 */
	@ColumnWidth(20)
	@ExcelProperty("问题描述")
	private String eventContent;
	/**
	 * 办理截止
	 */
	@ColumnWidth(20)
	@ExcelProperty("办理截止")
	private String handleRelExpireTime;
	/**
	 * 工单状态
	 */
	@ColumnWidth(20)
	@ExcelProperty("工单状态")
	private String rollStatus;
	/**
	 * 办理类型
	 */
	@ColumnWidth(20)
	@ExcelProperty("办理类型")
	private String handleType;
	/**
	 * 许可证号
	 */
	@ColumnWidth(20)
	@ExcelProperty("许可证号")
	private String licenseCode;
	/**
	 * 工单提交处置结果
	 */
	@ColumnWidth(20)
	@ExcelProperty("工单提交处置结果")
	private String rollHandleResult;
	/**
	 * 工单提交时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("工单提交时间")
	private String handleNoteDate;
	/**
	 * 所在市局编码
	 */
	@ColumnWidth(20)
	@ExcelProperty("所在市局编码")
	private String cityOrgCode;
	/**
	 * 所在市局名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("所在市局名称")
	private String cityOrgName;

	/**
	 * 处理结果
	 */
	@ColumnWidth(20)
	@ExcelProperty("处理结果")
	private String handleResult;

	/**
	 * 工单状态
	 */
	@ColumnWidth(20)
	@ExcelProperty("工单状态")
	private String complaintStatus;
	/**
	 * 是否已删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否已删除")
	private Integer isDeleted;
	/**
	 * 租户ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户ID")
	private String tenantId;

}
