<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.ms.dingapp.mapper.DingAppUserMapper">
  <resultMap id="BaseResultMap" type="org.springblade.ms.dingapp.entity.DingAppUser">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="union_id" jdbcType="VARCHAR" property="unionId" />
    <result column="create_user" jdbcType="BIGINT" property="createUser" />
    <result column="create_dept" jdbcType="BIGINT" property="createDept" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="BIGINT" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
  </resultMap>

</mapper>
