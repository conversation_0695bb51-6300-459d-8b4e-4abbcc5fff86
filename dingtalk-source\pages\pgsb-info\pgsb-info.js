import httpApi from "/utils/http/httpApi";
import {options, env} from '/utils/http/config';
import {Base64} from '/utils/base64.js';
import {setting} from "/common/setting";
const app = getApp();


Page({
  data: {
    yanguiFileList:[
      // {
      //   uid:'1891408211578847234',
      //   status: 'done', 
      //   url: "http://localhost:9000/images/upload/20250217/57b03b75ec9cfcfc3c73a25b26ec7dc3.png"
      // },
    ],
    isView:false,
    yanjiaFileList:[],
    licData:{},
    date:{},
    adviceText:"无;",
    collisionResult:[
      // {
      //   name:"芙蓉王",
      //   type:"正常"
      // },

    ],
    collistionColumn:[
      {
        title: '品规码',
        dataIndex: 'itemCode',
        key: 'name',
        width: 180,
      },
      {
        title: '品规',
        dataIndex: 'itemName',
        key: 'name',
        width: 300,
      },
      {
        title: '类别',
        dataIndex: 'collisionType',
        key: 'name',
        width: 180,
      },
    ],
    identificationResult:[
      {
        name:"芙蓉王",
      },
      {
        name:"尼古丁",
      },
    ],
    identificationColumn:[
      {
        title: '品规码',
        dataIndex: 'itemCode',
        key: 'name',
        width: 200,
      },
      {
        title: '品规',
        dataIndex: 'itemName',
        key: 'name',
        width: 500,
      }
    ]
  },
  async onLoad(options) {
    dd.setNavigationBar({
      title: '品规识别详情',
    });
    console.log(options)
    if(options && options.type){
      await dd.getStorage(
        {
          key:"licData",
          success:res=>{
            this.setData({
              licData:JSON.parse(res.data)
            })
            
          }
      })
      this.setData({
        'licData.explorationId':options.explorationId ,
        date:options.date.split(" ")[0]
      })
      if(options.type == 'view'){
        this.setData({isView: true})        
      }else{
        this.setData({isView: false})
           
      }
      this.getListData(); 

      // console.log(this.data.isView)
    }
    
    
  },
  async getListData(){
    this.getImgData();
    this.getItemIdentifyResults();
  },

  async getImgData(){
    const res = await httpApi.request({
      url: `/api/dingapp/itemIdentify/listByExplorationId`,
      method: 'get',
      params:{
        explorationId:this.data.licData.explorationId,
        identifyDate:this.data.date,
      }
    })
    if(res.data){
      const filteredCabinet = res.data.filter(item => item.type === '烟柜').map(item=>({
        uid:item.id,
        url:item.fileUrl,
        resultFileUrl:item.resultFileUrl,
        // status:'done',
      }));
      const filteredRack = res.data.filter(item => item.type === '烟架').map(item=>({
        uid:item.id,
        url:item.fileUrl,
        resultFileUrl:item.resultFileUrl
        // status:'done',
      }));
      this.setData({
        yanguiFileList:filteredCabinet,
        yanjiaFileList:filteredRack
      })
    }
  },
  async getItemIdentifyResults(){
    const res1 = await httpApi.request({
      url: `/api/dingapp/itemIdentifyResults/list`,
      method: 'get',
      params:{
        explorationId:this.data.licData.explorationId,
        identifyDate:this.data.date,
      }
    })

    //对碰结果排序
    if(res1.data){
      this.setData({
        identificationResult:res1.data
      })
      let errorData = res1.data;
      errorData.sort((a, b) => {
        if (a.collisionType > b.collisionType) {
          return -1;
        }
        if (a.collisionType < b.collisionType) {
          return 1; // a 排在 b 之后
        }
        // collision_type 相同，按 item_code 排序
        if (a.itemCode < b.itemCode) {
          return -1;
        }
        if (a.itemCode > b.itemCode) {
          return 1;
        }
        return 0; // a 和 b 相等
      });
      this.setData({collisionResult:errorData})
      const hasNonSmoke = errorData.some(item => item.collisionType === '非烟');
      if(hasNonSmoke){
        this.setData({adviceText:"存在非烟嫌疑，请查处跟进；"})
      }
    }
  },

  async handleYanguiRemove(file){
    // console.log(file)
    const res1 = await httpApi.request({
      url: `/api/dingapp/itemIdentify/remove`,
      method: 'get',
      params:{
        itemIdentifyId:file.uid,
      }
    })
    if(res1.data==true){
      dd.showToast({
        type: 'success',
        content: '删除成功',
        duration: 2000,
      });
    }

    this.getListData();
    // const currentFileList = this.data.yanguiFileList;
    // // 过滤掉被删除的文件
    // const newFileList = currentFileList.filter((item) => item.uid !== file.uid);
    // // 更新 fileList
    // this.setData({
    //   yanguiFileList: newFileList,
    // });
  },
  onYanguiPreview(file){
    console.log(file)
    dd.navigateTo({
      url: `/pages/pgsb-picture-info/pgsb-picture-info?url=${file.resultFileUrl}&id=${file.uid}`
    });
  },
  onYanguiUpload(filePath){
    dd.showLoading({
      content: '图片上传中...',
    });
    
    return new Promise((resolve, reject) => {
      dd.uploadFile({
        url: `${options.baseURL}/dingapp/itemIdentify/upload?objName=itemIdentify&explorationId=${this.data.licData.explorationId}&type=yangui`, 
        fileType: 'image',
        name: 'file', // 根据后台服务需求替换
        header:{
          "Blade-Requested-With":"BladeHttpRequest",
          "Blade-Auth":'bearer ' + app.globalData.accessToken,
          "Authorization":Base64.encode(setting.clientId + ':' + setting.clientSecret)
        },
        filePath: filePath.path, // 传入 localFile.path
        success: res => {
          console.log(res)
          //  this.uploadLocation();
          // 根据后台返回，得到上传成功的图片 url
          
          let data = JSON.parse(res.data);
          let url = data.data.path
          resolve(url); // 调用 resolve 传入图片 url

          let currentFileList = this.data.yanguiFileList;
          dd.hideLoading();
          this.getListData();
          // 创建新的文件对象
          // let newFile = {
          //   uid: new Date().getTime().toString(),
          //   status: 'done', 
          //   url: url, 
          // };

          // // 将新文件追加到 fileList 中
          // this.setData({
          //   yanguiFileList: [...currentFileList, newFile],
          // });
        },
        fail: err => {
          console.log(err)
          reject(); // 上传错误调用 reject
        },
      });
    });
  },

  async handleYanjiaRemove(file){
    const res1 = await httpApi.request({
      url: `/api/dingapp/itemIdentify/remove`,
      method: 'get',
      params:{
        itemIdentifyId:file.uid,
      }
    })
    if(res1.data==true){
      dd.showToast({
        type: 'success',
        content: '删除成功',
        duration: 2000,
      });
    }
    this.getListData();
  },
  onYanjiaPreview(file){
    // console.log("onYanjiaPreview",file)
    dd.navigateTo({
      url: `/pages/pgsb-picture-info/pgsb-picture-info?url=${file.resultFileUrl}&id=${file.uid}`
    });
  },
  onYanjiaUpload(filePath){
    dd.showLoading({
      content: '图片上传中...',
    });
    return new Promise((resolve, reject) => {
      dd.uploadFile({
        url: `${options.baseURL}/dingapp/itemIdentify/upload?objName=itemIdentify&explorationId=${this.data.licData.explorationId}&type=yanjia`, //无法带中文参数
        fileType: 'image',
        name: 'file', // 根据后台服务需求替换
        header:{
          "Blade-Requested-With":"BladeHttpRequest",
          "Blade-Auth":'bearer ' + app.globalData.accessToken,
          "Authorization":Base64.encode(setting.clientId + ':' + setting.clientSecret)
        },
        filePath: filePath.path, // 传入 localFile.path
      
        success: res => {
           this.uploadLocation();
          // 根据后台返回，得到上传成功的图片 url
          
          let data = JSON.parse(res.data);
          let url = data.data.path
          resolve(url); // 调用 resolve 传入图片 url
          dd.hideLoading();
          this.getListData();
          // let currentFileList = this.data.yanguiFileList;

          // // 创建新的文件对象
          // let newFile = {
          //   uid: new Date().getTime().toString(),
          //   status: 'done', 
          //   url: url, 
          // };

          // // 将新文件追加到 fileList 中
          // this.setData({
          //   yanguiFileList: [...currentFileList, newFile],
          // });
        },
        fail: err => {
          reject(); // 上传错误调用 reject
        },
      });
    });
  },
  //定位
  uploadLocation(){
    dd.getLocation({
      type: 1,
      useCache: false,
      coordinate: '1',
      cacheTimeout: 1,
      withReGeocode: true,
      targetAccuracy: '100',
      success: (res) => {
        httpApi.request({
          url: '/api/dingapp/exploration/submitExplorationCoordinate',
          method: 'POST',
          data:{
            "explorationId": this.data.licData.explorationId,
            "licenseId": this.data.licData.licNo,
            "longitude": res.longitude,
            "latitude": res.latitude
          }
        })
      },
      fail: (err) => {
      }
    })
  },
  onChooseImageError(err){
    console.log(err)
  },
  onYanguiImgClick(e){
    let file = this.data.yanguiFileList[e.currentTarget.dataset.imgIndex]
    dd.navigateTo({
      url: `/pages/pgsb-picture-info/pgsb-picture-info?url=${file.resultFileUrl}&id=${file.uid}`
    });
    // console.log(e.currentTarget.dataset.imgIndex)
    // console.log(this.data.yanguiFileList[e.currentTarget.dataset.imgIndex])
  },
  onYanjiaImgClick(e){
    let file = this.data.yanjiaFileList[e.currentTarget.dataset.imgIndex]
    dd.navigateTo({
      url: `/pages/pgsb-picture-info/pgsb-picture-info?url=${file.resultFileUrl}&id=${file.uid}`
    });
    
  },
});
