/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.ms.basic.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.ms.basic.excel.YhytLicenseExcel;
import org.springblade.ms.basic.pojo.entity.YhytLicenseEntity;
import org.springblade.ms.basic.pojo.vo.YhytLicenseDailyCutVO;
import org.springblade.ms.basic.pojo.vo.YhytLicenseVO;

import java.util.List;

/**
 * 零售户信息 服务类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
public interface IYhytLicenseService extends BaseService<YhytLicenseEntity> {

	/**
	 * 初始化零售户坐标进缓存
	 */
	void init();

    /**
	 * 自定义分页
	 *
	 * @param page 分页参数
	 * @param yhytLicense 查询参数
	 * @return IPage<YhytLicenseVO>
	 */
	IPage<YhytLicenseVO> selectYhytLicensePage(IPage<YhytLicenseVO> page, YhytLicenseVO yhytLicense);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper 查询条件
	 * @return List<YhytLicenseExcel>
	 */
	List<YhytLicenseExcel> exportYhytLicense(Wrapper<YhytLicenseEntity> queryWrapper);


	List<YhytLicenseEntity> getSelectionList(String name);

    List<YhytLicenseEntity> getDingMapList(YhytLicenseEntity param);

	/**
	 * 获取业态列表
	 * @return
	 */
	List<String> getFormatList();

	Boolean updateLocation(YhytLicenseEntity entity);

	YhytLicenseDailyCutVO getYhytLicenseDailyCutVO(String licNo);
}
