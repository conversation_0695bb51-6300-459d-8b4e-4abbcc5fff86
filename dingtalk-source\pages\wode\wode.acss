.mainApp {
    flex: 1;
    flex-direction: column;
    overflow: hidden;
}

.userView {
    background: rgb(2, 0, 36);
    background: linear-gradient(135deg, rgba(2, 0, 36, 1) 0%, rgba(9, 9, 121, 1) 41%, rgba(0, 212, 255, 1) 100%);
    width: 100%;
    height: 25vh;
    display: flex;
    align-items: center;
}

.personalBox {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
    margin-top: 5vh;
    margin-left: 30rpx;
}

.photo {
    width: 70px;
    height: 70px;
    border-radius: 80px;
    border: 3px solid #FFFFFF;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05), 0px 2px 4px 2px rgba(0, 0, 0, 0.05);
    margin-right: 16px;
    overflow: hidden;
}

.photo image {
    width: 60px;
    height: 60px;
    margin-left: 5px;
    margin-top: 5px;
}

.info {
    color: #FFFFFF;
    display: flex;
    flex-direction: column;
    height: 200rpx;
    justify-content: space-around;
    flex: 1;
}
.region{
    font-size: 25rpx;
}
.name {
    margin-right: 10px;
}
.rule{
    font-size: 25rpx;
}
.dept{
    font-size: 25rpx;
}

.functionView {
    width: 100%;
}

.functionTitle {
    margin: 30rpx 30rpx;
}