package org.springblade.ms.reportcomplaint.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.ms.reportcomplaint.pojo.entity.ReportIllegalLabelEntity;
import org.springblade.ms.reportcomplaint.pojo.vo.ReportIllegalLabelVO;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-01-16 23:49
 */
public class ReportIllegalLabelWrapper extends BaseEntityWrapper<ReportIllegalLabelEntity, ReportIllegalLabelVO> {

    @Override
    public ReportIllegalLabelVO entityVO(ReportIllegalLabelEntity entity) {
        return null;
    }
}
