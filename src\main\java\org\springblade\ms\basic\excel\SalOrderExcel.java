/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.ms.basic.excel;


import lombok.Data;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;
import java.io.Serial;


/**
 * 订单信息 Excel实体类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class SalOrderExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("主键")
	private Long id;
	/**
	 * 
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private String orderUuid;
	/**
	 * 销售类型
	 */
	@ColumnWidth(20)
	@ExcelProperty("销售类型")
	private String saleType;
	/**
	 * 单据号
	 */
	@ColumnWidth(20)
	@ExcelProperty("单据号")
	private String billCode;
	/**
	 * 业务日期
	 */
	@ColumnWidth(20)
	@ExcelProperty("业务日期")
	private String bizDate;
	/**
	 * 明细数
	 */
	@ColumnWidth(20)
	@ExcelProperty("明细数")
	private Integer detailCount;
	/**
	 * 客户标识
	 */
	@ColumnWidth(20)
	@ExcelProperty("客户标识")
	private String customerUuid;
	/**
	 * 客户编码
	 */
	@ColumnWidth(20)
	@ExcelProperty("客户编码")
	private String customerCode;
	/**
	 * 客户名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("客户名称")
	private String customerName;
	/**
	 * 客户电话
	 */
	@ColumnWidth(20)
	@ExcelProperty("客户电话")
	private String custMasterTel;
	/**
	 * 营销部门标识
	 */
	@ColumnWidth(20)
	@ExcelProperty("营销部门标识")
	private String saleDepartUuid;
	/**
	 * 营销部门
	 */
	@ColumnWidth(20)
	@ExcelProperty("营销部门")
	private String saleDepartName;
	/**
	 * 营销分公司
	 */
	@ColumnWidth(20)
	@ExcelProperty("营销分公司")
	private String saleCountyName;
	/**
	 * 客户经理
	 */
	@ColumnWidth(20)
	@ExcelProperty("客户经理")
	private String salerName;
	/**
	 * 是否已审核
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否已审核")
	private String isAudit;
	/**
	 * 是否被退
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否被退")
	private String isBeReturn;
	/**
	 * 是否退货单
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否退货单")
	private String isReturn;
	/**
	 * 是否已付款
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否已付款")
	private String isPayed;
	/**
	 * 是否到货
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否到货")
	private String isArrived;
	/**
	 * 是否已开税票
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否已开税票")
	private String isTaxInv;
	/**
	 * 是否可用
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否可用")
	private String isActive;
	/**
	 * 是否已删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否已删除")
	private Integer isDeleted;
	/**
	 * 租户ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户ID")
	private String tenantId;

}
