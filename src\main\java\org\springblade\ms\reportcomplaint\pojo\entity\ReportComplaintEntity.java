/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.ms.reportcomplaint.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import java.io.Serial;

/**
 * 工单信息 实体类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@TableName("ms_report_complaint")
@Schema(description = "ReportComplaint对象")
@EqualsAndHashCode(callSuper = true)
public class ReportComplaintEntity extends TenantEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 受理号
	 */
	@Schema(description = "受理号")
	private String rollNumber;
	/**
	 * 受理方式
	 */
	@Schema(description = "受理方式")
	private String rollWay;
	/**
	 * 业务类别
	 */
	@Schema(description = "业务类别")
	private String rollType;
	/**
	 * 问题分类
	 */
	@Schema(description = "问题分类")
	private String eventType;
	/**
	 * 重要程度
	 */
	@Schema(description = "重要程度")
	private String rollDegree;
	/**
	 * 问题区域
	 */
	@Schema(description = "问题区域")
	private String eventRegion;
	/**
	 * 受理时间
	 */
	@Schema(description = "受理时间")
	private Date rollTime;
	/**
	 * 受理日期
	 */
	@Schema(description = "受理日期")
	private String rollDate;
	/**
	 * 问题地址
	 */
	@Schema(description = "问题地址")
	private String eventAddr;
	/**
	 * 问题描述
	 */
	@Schema(description = "问题描述")
	private String eventContent;
	/**
	 * 办理截止
	 */
	@Schema(description = "办理截止")
	private String handleRelExpireTime;
	/**
	 * 工单状态
	 */
	@Schema(description = "工单状态")
	private String rollStatus;
	/**
	 * 办理类型
	 */
	@Schema(description = "办理类型")
	private String handleType;
	/**
	 * 许可证号
	 */
	@Schema(description = "许可证号")
	private String licenseCode;
	/**
	 * 工单提交处置结果
	 */
	@Schema(description = "工单提交处置结果")
	private String rollHandleResult;
	/**
	 * 工单提交时间
	 */
	@Schema(description = "工单提交时间")
	private String handleNoteDate;
	/**
	 * 所在市局编码
	 */
	@Schema(description = "所在市局编码")
	private String cityOrgCode;
	/**
	 * 所在市局名称
	 */
	@Schema(description = "所在市局名称")
	private String cityOrgName;

	@Schema(description = "处理人id")
	private Long userId;

	@Schema(description = "涉事主体")
	private String eventTarget;

	@Schema(description = "诉求标题")
	private String eventTitle;

	/**
	 * 处理结果
	 */
	@Schema(description = "处理结果")
	private String handleResult;

	/**
	 * 工单状态
	 */
	@Schema(description = "工单状态")
	private String complaintStatus;


	@Schema(description = "数据来源 12345,12313")
	private String dataSource;

	@Schema(description = "办结人员")
	private Long auditUser;

	private Long deptId;

	@Schema(description = "是否涉及许可证")
	private Integer hasLicense;

	private String  contactPhone;
}
