package org.springblade.ms.dingapp.service;

import org.springblade.ms.dingapp.vo.ChartDataVO;
import org.springblade.ms.dingapp.vo.MonitoringDataVO;
import org.springblade.ms.dingapp.vo.PersonnelRankingVO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 监控服务接口
 *
 * <AUTHOR> Name
 * @since 2025-05-01
 */
public interface IMonitoringService {

    /**
     * 获取监控数据
     *
     * @param timeRange    时间范围：day, week, month, quarter, year
     * @param teamId       中队ID，为null时查询所有中队
     * @param userId  人员ID，为null时查询所有人员
     * @return 监控数据
     */
    MonitoringDataVO getMonitoringData(String timeRange, LocalDateTime startTime, LocalDateTime endTime, String teamId, String userId);

    /**
     * 获取检客户统计数据
     *
     * @param timeRange    时间范围：day, week, month, quarter, year
     * @param teamId       中队ID，为null时查询所有中队
     * @param userId  人员ID，为null时查询所有人员
     * @return 检客户统计数据
     */
    ChartDataVO getInspectionData(String timeRange, LocalDateTime startTime, LocalDateTime endTime, String teamId, String userId);

    /**
     * 获取品规识别统计数据
     *
     * @param timeRange    时间范围：day, week, month, quarter, year
     * @param teamId       中队ID，为null时查询所有中队
     * @param userId  人员ID，为null时查询所有人员
     * @return 品规识别统计数据
     */
    ChartDataVO getRecognitionData(String timeRange, LocalDateTime startTime, LocalDateTime endTime, String teamId, String userId);

    /**
     * 获取正常/异常烟统计数据
     *
     * @param timeRange    时间范围：day, week, month, quarter, year
     * @param teamId       中队ID，为null时查询所有中队
     * @param userId  人员ID，为null时查询所有人员
     * @return 正常/异常烟统计数据
     */
    ChartDataVO getCigaretteData(String timeRange, LocalDateTime startTime, LocalDateTime endTime, String teamId, String userId);

    /**
     * 获取人员排名数据
     *
     * @param timeRange    时间范围：day, week, month, quarter, year
     * @param deptId       部门ID，必须指定中队
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @return 人员排名数据列表
     */
    List<PersonnelRankingVO> getPersonnelRanking(String timeRange, LocalDateTime startTime, LocalDateTime endTime, String deptId);
}
