<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.ms.basic.mapper.MsProductInfoMapper">

    <resultMap id="BaseResultMap" type="org.springblade.ms.basic.pojo.entity.MsProductInfo">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="productUuid" column="product_uuid" jdbcType="VARCHAR"/>
            <result property="productCode" column="product_code" jdbcType="VARCHAR"/>
            <result property="productName" column="product_name" jdbcType="VARCHAR"/>
            <result property="gjProductUuid" column="gj_product_uuid" jdbcType="VARCHAR"/>
            <result property="gjProductName" column="gj_product_name" jdbcType="VARCHAR"/>
            <result property="productType" column="product_type" jdbcType="VARCHAR"/>
            <result property="supplyShortName" column="supply_short_name" jdbcType="VARCHAR"/>
            <result property="brandCode" column="brand_code" jdbcType="VARCHAR"/>
            <result property="brandName" column="brand_name" jdbcType="VARCHAR"/>
            <result property="brandType" column="brand_type" jdbcType="VARCHAR"/>
            <result property="length" column="length" jdbcType="NUMERIC"/>
            <result property="width" column="width" jdbcType="NUMERIC"/>
            <result property="height" column="height" jdbcType="NUMERIC"/>
            <result property="stripCode" column="strip_code" jdbcType="VARCHAR"/>
            <result property="isOfften" column="is_offten" jdbcType="NUMERIC"/>
            <result property="importFlag" column="import_flag" jdbcType="NUMERIC"/>
            <result property="isMainProduct" column="is_main_product" jdbcType="NUMERIC"/>
            <result property="isFamous" column="is_famous" jdbcType="NUMERIC"/>
            <result property="isProvince" column="is_province" jdbcType="NUMERIC"/>
            <result property="isLocSale" column="is_loc_sale" jdbcType="NUMERIC"/>
            <result property="isSeized" column="is_seized" jdbcType="NUMERIC"/>
            <result property="isSpecial" column="is_special" jdbcType="NUMERIC"/>
            <result property="isCigar" column="is_cigar" jdbcType="NUMERIC"/>
            <result property="isHighTierBrand" column="is_high_tier_brand" jdbcType="NUMERIC"/>
            <result property="isActive" column="is_active" jdbcType="NUMERIC"/>
            <result property="createUser" column="create_user" jdbcType="BIGINT"/>
            <result property="createDept" column="create_dept" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateUser" column="update_user" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,product_uuid,product_code,
        product_name,gj_product_uuid,gj_product_name,
        product_type,supply_short_name,brand_code,
        brand_name,brand_type,length,
        width,height,strip_code,
        is_offten,import_flag,is_main_product,
        is_famous,is_province,is_loc_sale,
        is_seized,is_special,is_cigar,
        is_high_tier_brand,is_active,create_user,
        create_dept,create_time,update_user,
        update_time,status,is_deleted
    </sql>
</mapper>
