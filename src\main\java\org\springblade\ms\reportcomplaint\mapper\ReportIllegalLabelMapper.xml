<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.ms.reportcomplaint.mapper.ReportIllegalLabelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="reportIllegalLabelResultMap" type="org.springblade.ms.reportcomplaint.pojo.entity.ReportIllegalLabelEntity">
        <result column="id" property="id"/>
        <result column="obj_id" property="objId"/>
        <result column="label_id" property="labelId"/>
        <result column="obj_type" property="objType"/>
        <result column="label_type" property="labelType"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <resultMap id="reportIllegalLabelVOMap" type="org.springblade.ms.reportcomplaint.pojo.vo.ReportIllegalLabelVO" extends="reportIllegalLabelResultMap">
    </resultMap>

    <select id="selectList" resultMap="reportIllegalLabelVOMap">
        select * from ms_report_lllegal_label
        <where>
            <if test="true">
                is_deleted = 0
            </if>
            <if test="param.objId != null">
                and obj_id = #{param.objId}
            </if>
            <if test="param.labelId != null">
                and label_id = #{param.labelId}
            </if>
        </where>
    </select>

    <select id="listByLabelId" resultMap="reportIllegalLabelVOMap">
        select * from ms_report_lllegal_label
        <where>
            <if test="true">
                is_deleted = 0
            </if>
            <if test="labelId != null and labelId !=''">
                and label_id = #{labelId}
            </if>
        </where>
    </select>


    <select id="listLegalPageBycustCode" resultMap="reportIllegalLabelVOMap">
        select * from ms_report_lllegal_label
        <where>
            <if test="true">
               and is_deleted = 0
            </if>
            <if test="true">
                and obj_type = '案件'
            </if>
            <if test="yhytId != null and yhytId != '' ">
                and label_id = CAST(#{yhytId} AS BIGINT)
            </if>
        </where>
    </select>

    <select id="getIllegalRecordsLastYearCount" resultType="long">
        SELECT count(*)
        FROM "ms_report_lllegal_label" mll
                 LEFT JOIN ms_illegal_records mir ON mll.obj_id = mir.id
        WHERE mll.label_id = #{yhytId}
          AND mll.obj_type = '案件'
          AND mir.reg_time >= NOW()- INTERVAL '1 year'
    </select>
</mapper>
