<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.ms.basic.mapper.UploadFileMapper">
    <resultMap id="BaseResultMap" type="org.springblade.ms.basic.pojo.entity.UploadFileEntity">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="obj_id" jdbcType="BIGINT" property="objId" />
        <result column="file_name" jdbcType="VARCHAR" property="fileName" />
        <result column="filth_path" jdbcType="VARCHAR" property="filthPath" />
        <result column="file_size" jdbcType="DECIMAL" property="fileSize" />
        <result column="obj_name" jdbcType="VARCHAR" property="objName" />
        <result column="create_user" jdbcType="BIGINT" property="createUser" />
        <result column="create_dept" jdbcType="BIGINT" property="createDept" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_user" jdbcType="BIGINT" property="updateUser" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
    </resultMap>

</mapper>
