/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.ms.itemidentify.pojo.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 品规识别结果 视图实体类（包含零售户信息）
 *
 * <AUTHOR>
 * @since 2025-04-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ItemIdentifyResultsWithRetailerVO extends ItemIdentifyResultsVO {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 零售户ID
     */
    private Long licenseId;

    /**
     * 零售户名称
     */
    private String retailerName;

    /**
     * 零售户地址
     */
    private String retailerAddress;

    /**
     * 零售户许可证号
     */
    private String retailerLicNo;

    /**
     * 创建者真实姓名
     */
    private String creatorRealName;
}
