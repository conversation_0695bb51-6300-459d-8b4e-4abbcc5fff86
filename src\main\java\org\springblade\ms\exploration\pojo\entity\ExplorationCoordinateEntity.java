package org.springblade.ms.exploration.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.math.BigDecimal;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-02-14 11:27
 */
@Data
@TableName("ms_exploration_coordinate")
@EqualsAndHashCode(callSuper = true)
public class ExplorationCoordinateEntity extends TenantEntity {

    /**
     *  勘查 ID
     */
    private Long explorationId;

    /**
     *  零售户 ID
     */
    private Long licenseId;

    /**
     *  经度
     */
    private BigDecimal longitude;

    /**
     *  纬度
     */
    private BigDecimal latitude;


}
