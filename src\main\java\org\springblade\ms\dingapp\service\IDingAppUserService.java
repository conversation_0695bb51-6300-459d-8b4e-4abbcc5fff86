package org.springblade.ms.dingapp.service;

import org.springblade.core.mp.base.BaseService;
import org.springblade.core.tool.support.Kv;
import org.springblade.ms.dingapp.entity.DingAppUser;
import org.springblade.ms.dingapp.vo.DingUserAccessTokenVO;
import org.springblade.ms.dingapp.vo.DingUserInfoVO;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-01-22 10:18
 */
public interface IDingAppUserService extends BaseService<DingAppUser> {

    DingUserAccessTokenVO getAccessToken() throws Exception;

    DingUserInfoVO getUserInfo(String authCode, String accessToken) throws Exception;

    Kv login(String authCode);

    String getJsapiTicket() throws Exception;

}
