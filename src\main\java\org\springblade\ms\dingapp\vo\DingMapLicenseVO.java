package org.springblade.ms.dingapp.vo;

import lombok.Data;
import org.springblade.ms.basic.pojo.entity.UploadFileEntity;
import org.springblade.ms.basic.pojo.entity.YhytLicenseEntity;
import org.springblade.ms.basic.pojo.vo.YhytLicenseVO;
import org.springblade.ms.dingapp.utils.CoordinateTransformUtil;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 地图零售户对象
 * <AUTHOR> [sijun.zeng]
 * @date 2025-01-23 23:51
 */
@Data
public class DingMapLicenseVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String companyName;

    private String licNo;

    private String businessAddr;

    private BigDecimal longitude;

    private BigDecimal latitude;

    private String bizFormat;

    private List<UploadFileEntity> photoPathList;

    //门面照片
    private List<UploadFileEntity> lastCenterPoho;

    private YhytLicenseVO yhytLicenseVO;

    private Double distance;

    public DingMapLicenseVO(YhytLicenseEntity entity) {
        this.id = entity.getId();
        this.companyName = entity.getCompanyName();
        this.licNo = entity.getLicNo();
        this.businessAddr = entity.getBusinessAddr();
        // 转换高德坐标系(GCJ-02)到天地图坐标系(CGCS2000)
        BigDecimal[] coordinates = CoordinateTransformUtil.gcj02ToCgcs2000(entity.getLatitude(), entity.getLongitude());
        this.latitude = coordinates[0];
        this.longitude = coordinates[1];
        this.bizFormat = entity.getBizFormat();
    }

    public DingMapLicenseVO(YhytLicenseVO entity) {
        this.id = entity.getId();
        this.companyName = entity.getCompanyName();
        this.licNo = entity.getLicNo();
        this.businessAddr = entity.getBusinessAddr();
        // 转换高德坐标系(GCJ-02)到天地图坐标系(CGCS2000)
        BigDecimal[] coordinates = CoordinateTransformUtil.gcj02ToCgcs2000(entity.getLatitude(), entity.getLongitude());
        this.latitude = coordinates[0];
        this.longitude = coordinates[1];
        this.bizFormat = entity.getBizFormat();
        this.yhytLicenseVO = entity;
        this.distance = entity.getDistance();

    }

}
