<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.ms.reportcomplaint.mapper.ComplaintYhytMapper">

    <resultMap id="BaseResultMap" type="org.springblade.ms.reportcomplaint.pojo.entity.ComplaintYhyt">
            <result property="id" column="id" jdbcType="BIGINT"/>
            <result property="yhytId" column="yhyt_id" jdbcType="BIGINT"/>
            <result property="complaintId" column="complaint_id" jdbcType="BIGINT"/>
            <result property="createUser" column="create_user" jdbcType="BIGINT"/>
            <result property="createDept" column="create_dept" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateUser" column="update_user" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,yhyt_id,complaint_id,
        create_user,create_dept,create_time,
        update_user,update_time,status,
        is_deleted,tenant_id
    </sql>
</mapper>
