<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.ms.priceStandards.mapper.ProductInfoYangjiangEntityMapper">

    <resultMap id="BaseResultMap" type="org.springblade.ms.priceStandards.pojo.entity.ProductInfoYangjiangEntity">
            <result property="company" column="company" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="photoname" column="photoname" jdbcType="VARCHAR"/>
            <result property="photo" column="photo" jdbcType="INTEGER"/>
            <result property="type1" column="type1" jdbcType="INTEGER"/>
            <result property="type2" column="type2" jdbcType="VARCHAR"/>
            <result property="barcode2" column="barcode2" jdbcType="VARCHAR"/>
            <result property="barcode" column="barcode" jdbcType="VARCHAR"/>
            <result property="lprice" column="lprice" jdbcType="DOUBLE"/>
            <result property="pprice" column="pprice" jdbcType="DOUBLE"/>
            <result property="type3" column="type3" jdbcType="VARCHAR"/>
            <result property="mulu" column="mulu" jdbcType="VARCHAR"/>
            <result property="xilie" column="xilie" jdbcType="INTEGER"/>
            <result property="ctime" column="ctime" jdbcType="VARCHAR"/>
            <result property="mlid" column="mlid" jdbcType="INTEGER"/>
            <result property="mulu1" column="mulu1" jdbcType="INTEGER"/>
            <result property="mulu2" column="mulu2" jdbcType="VARCHAR"/>
            <result property="zs" column="zs" jdbcType="VARCHAR"/>
            <result property="unit" column="unit" jdbcType="VARCHAR"/>
            <result property="type4" column="type4" jdbcType="VARCHAR"/>
            <result property="type5" column="type5" jdbcType="VARCHAR"/>
            <result property="danwei" column="danwei" jdbcType="VARCHAR"/>
            <result property="memo" column="memo" jdbcType="VARCHAR"/>
            <result property="jy1" column="jy1" jdbcType="VARCHAR"/>
            <result property="jy2" column="jy2" jdbcType="DOUBLE"/>
            <result property="jy3" column="jy3" jdbcType="DOUBLE"/>
            <result property="jy4" column="jy4" jdbcType="VARCHAR"/>
            <result property="jy5" column="jy5" jdbcType="VARCHAR"/>
            <result property="jy6" column="jy6" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        company,name,photoname,
        photo,type1,type2,
        barcode2,barcode,lprice,
        pprice,type3,mulu,
        xilie,ctime,mlid,
        mulu1,mulu2,
        zs,unit,type4,
        type5,danwei,memo,
        jy1,jy2,jy3,
        jy4,jy5,jy6
    </sql>
</mapper>
