const EARTH_RADIUS = 6371000;

export function calculateDistance(lat1, lon1, lat2, lon2) {
    const numLat1 = parseFloat(lat1);
    const numLon1 = parseFloat(lon1);
    const numLat2 = parseFloat(lat2);
    const numLon2 = parseFloat(lon2);
    // 将角度转换为弧度
    const radLat1 = numLat1 * Math.PI / 180;
    const radLat2 = numLat2 * Math.PI / 180;
    const radLon1 = numLon1 * Math.PI / 180;
    const radLon2 = numLon2 * Math.PI / 180;

    // 计算两个点之间的距离
    const deltaLat = radLat2 - radLat1;
    const deltaLon = radLon2 - radLon1;
    const a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
        Math.cos(radLat1) * Math.cos(radLat2) *
        Math.sin(deltaLon / 2) * Math.sin(deltaLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return EARTH_RADIUS * c;
}